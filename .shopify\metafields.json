{"article": [], "blog": [], "collection": [{"key": "desktop", "namespace": "banner", "name": "Banner Desktop", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "mobile", "namespace": "banner", "name": "Banner Mobile", "description": "", "type": {"name": "file_reference", "category": "REFERENCE"}}, {"key": "description", "namespace": "custom", "name": "Description", "description": "", "type": {"name": "multi_line_text_field", "category": "TEXT"}}, {"key": "custom_label", "namespace": "custom_data", "name": "Collection Custom Badge", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_background", "namespace": "custom_data", "name": "Collection Custom Badge Background", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_font_color", "namespace": "custom_data", "name": "Collection Custom Font Color", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}], "company": [], "company_location": [], "location": [], "market": [], "order": [{"key": "estimated_shipping_date", "namespace": "fulfillment_network", "name": "Estimated Shipping Date", "description": "Shopify Logistics: The estimated date that the order will be picked up by the carrier.", "type": {"name": "date", "category": "DATE_TIME"}}, {"key": "estimated_delivery_date", "namespace": "fulfillment_network", "name": "Estimated Delivery Date", "description": "Shopify Logistics: The estimated date that the order will be delivered by the carrier.", "type": {"name": "date", "category": "DATE_TIME"}}, {"key": "item_picked_up_by_the_carrier", "namespace": "fulfillment_network", "name": "Picked Up By The Carrier", "description": "Shopify Logistics: The date and time in which the package was picked up by the carrier.", "type": {"name": "date_time", "category": "DATE_TIME"}}, {"key": "product_bundlename", "namespace": "custom", "name": "Order Bundle Name", "description": "Do not touch this metafield. This is for ShipHero and it is automatically being filled.", "type": {"name": "single_line_text_field", "category": "TEXT"}}], "page": [], "product": [{"key": "custom_text", "namespace": "prod", "name": "Product Custom Text", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "product_bundles", "namespace": "custom", "name": "Product Bundles", "description": "Add product handles separated by a comma, no spaces", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "select_bundles", "namespace": "custom", "name": "\"Select\" Bundles", "description": "", "type": {"name": "list.product_reference", "category": "REFERENCE"}}, {"key": "redirect", "namespace": "custom", "name": "Redirect", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "label_bundles", "namespace": "custom", "name": "\"Label\" Bundles", "description": "", "type": {"name": "list.product_reference", "category": "REFERENCE"}}, {"key": "from_price", "namespace": "custom", "name": "From Price", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "min_qty", "namespace": "custom", "name": "Min Quantity", "description": "", "type": {"name": "number_integer", "category": "NUMBER"}}, {"key": "custom_label", "namespace": "custom", "name": "Product Custom Badge", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_background", "namespace": "custom", "name": "Product Custom Badge Background", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_font_color", "namespace": "custom", "name": "Product Badge Font Color", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "model_size", "namespace": "custom", "name": "Model Size", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "product_bundles2", "namespace": "custom", "name": "Product Bundles", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "product_bundlename", "namespace": "custom", "name": "Product Bundle Name", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_product", "namespace": "mm-google-shopping", "name": "Google: Custom Product", "description": "Use to indicate whether or not the unique product identifiers (UPIs) GTIN, MPN, and brand are available for your product.", "type": {"name": "boolean", "category": "TRUE_FALSE"}}, {"key": "artist_name", "namespace": "custom", "name": "artist_name", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "artist_id", "namespace": "custom", "name": "artist_id", "description": "", "type": {"name": "number_integer", "category": "NUMBER"}}, {"key": "products_swatches", "namespace": "custom", "name": "products_swatches", "description": "", "type": {"name": "list.product_reference", "category": "REFERENCE"}}, {"key": "product_colors", "namespace": "custom", "name": "product_colours", "description": "", "type": {"name": "list.color", "category": "COLOR"}}, {"key": "rating_count", "namespace": "reviews", "name": "Product rating count", "description": "Total number of ratings from customers", "type": {"name": "number_integer", "category": "NUMBER"}}, {"key": "rating", "namespace": "reviews", "name": "Product rating", "description": "Average rating from customers", "type": {"name": "rating", "category": "RATING"}}, {"key": "custom_button_link", "namespace": "c_f", "name": "Custom Button Link", "description": "", "type": {"name": "url", "category": "URL"}}], "variant": [{"key": "custom_label_4", "namespace": "mm-google-shopping", "name": "Google: Custom Label 4", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_3", "namespace": "mm-google-shopping", "name": "Google: Custom Label 3", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_2", "namespace": "mm-google-shopping", "name": "Google: Custom Label 2", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_1", "namespace": "mm-google-shopping", "name": "Google: Custom Label 1", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "custom_label_0", "namespace": "mm-google-shopping", "name": "Google: Custom Label 0", "description": "Label that you assign to a product to help organize bidding and reporting in Shopping campaigns", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "size_system", "namespace": "mm-google-shopping", "name": "Google: Size System", "description": "The country of the size system used by your product.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "size_type", "namespace": "mm-google-shopping", "name": "Google: Size Type", "description": "Your apparel product’s cut.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "mpn", "namespace": "mm-google-shopping", "name": "Google: MPN", "description": "Your product’s Manufacturer Part Number (MPN).", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "gender", "namespace": "mm-google-shopping", "name": "Google: Gender", "description": "The gender for which your product is intended.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "condition", "namespace": "mm-google-shopping", "name": "Google: Condition", "description": "The condition of your product at time of sale.", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "age_group", "namespace": "mm-google-shopping", "name": "Google: Age Group", "description": "The demographic for which your product is intended.", "type": {"name": "single_line_text_field", "category": "TEXT"}}], "shop": []}