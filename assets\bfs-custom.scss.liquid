/**
 * VARIABLES
 */
$black: #000;
$white: #fff;
$gray: #f8f8f8;
$medium-gray: #d6d6d6;
$teal: #156571;
$light-teal: #e3ecee;
$light-yellow: #faf6e5;
$yellow: #febf46;
$red: #a60000;
$brown: #c2b79b;
$sienna: #B68A2B;
$orange: #F2B840; 
$light-green: #b3e374;
$border-radius-button: 5px;
$border-radius-media: 10px;
$border-radius-card: 15px;
$transition: all 0.3s ease-in-out;
$breakpoint-sm: 480px;
$breakpoint-sm-md: 640px;
$breakpoint-md: 800px;
$breakpoint-md-lg: 960px;
$breakpoint-lg: 1140px;
$breakpoint-lg-xl: 1280px;
$breakpoint-xl: 1440px;
$breakpoint-xxl: 1920px;
$breakpoint-xxxl: 3840px;

/**
 * MIXINS
 */
@mixin mq($breakpoint: null) {
    @if $breakpoint != null {
        @media (max-width: $breakpoint) {
            @content;
        }
    }
}

@mixin hover() {
    @media (hover: hover) {
        &:hover {
            @content;
        }
    }
}

/**
 * UTILITY CLASSES
 */
.bfs-fs-a7 {
    font-family: 'Qualy', sans-serif;
    //font-size: 60px;
    font-size: clamp(45px, calc(-1.2981rem + 5.7692vw), 60px);
    font-style: normal;
    font-weight: normal;
    line-height: 120%;
}

.bfs-fs-a6 {
    font-family: 'Qualy', sans-serif;
    //font-size: 50px;
    font-size: clamp(35px, calc(-1.9231rem + 5.7692vw), 50px);
    font-style: normal;
    font-weight: normal;
    line-height: 120%;
}

.bfs-fs-a5 {
    font-family: 'Qualy', sans-serif;
    //font-size: 30px;
    font-size: clamp(25px, calc(0.1923rem + 1.9231vw), 30px);
    font-style: normal;
    font-weight: normal;
    line-height: 120%;
}

.bfs-fs-a4 {
    font-family: 'Qualy', sans-serif;
    //font-size: 22px;
    font-size: clamp(20px, calc(0.625vw + 1rem), 22px);
    font-style: normal;
    font-weight: normal;
    line-height: 120%;
}

.bfs-fs-a3 {
    //font-size: 18px;
    font-size: clamp(17px, calc(0.313vw + 0.938rem), 18px);
    font-style: normal;
    font-weight: 700;
    line-height: 120%;
}

.bfs-fs-a2 {
    //font-size: 17px;
    font-size: clamp(16px, calc(0.313vw + 0.875rem), 17px);
    font-style: normal;
    font-weight: normal;
    line-height: 120%;
}

.bfs-fs-a1 {
    //font-size: 16px;
    font-size: clamp(15px, calc(0.313vw + 0.813rem), 16px);
    font-style: normal;
    font-weight: 700;
    line-height: 120%;
}

.bfs-fs-b0 {
    //font-size: 16px;
    font-size: clamp(15px, calc(0.313vw + 0.813rem), 16px);
    font-style: normal;
    font-weight: normal;
    line-height: 140%;
}

.bfs-fs-b1 {
    font-size: 14px;
    font-style: normal;
    font-weight: normal;
    line-height: 140%;
}

.bfs-fs-b2 {
    font-family: 'Qualy', sans-serif;
    font-size: 14px;
    font-style: normal;
    line-height: 100%;
    letter-spacing: 0.7px;
    text-transform: uppercase;
}

.bfs-fs-b3 {
    font-family: 'Qualy', sans-serif;
    font-size: 12px;
    font-style: normal;
    line-height: 100%;
    letter-spacing: 0.6px;
    text-transform: uppercase;
}

.bfs-fs-b4 {
    font-size: 12px;
    font-style: normal;
    font-weight: normal;
    line-height: 120%;
}

.bfs-richtext {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75em;

    strong,
    b {
        font-weight: 700;
    }

    u {
        text-decoration: underline;
    }

    a {
        text-decoration: underline;
    }

    ul {
        list-style: disc;
        margin-left: 1em;
    }

    ol {
        list-style: decimal inside;
        margin-left: 1em;
    }

    li {
        list-style-position: outside;
        padding: 0;
        margin: 0;

        &::marker {
            color: currentColor;
        }
    }

    img {
        max-width: 100%;
        height: auto;
        border: none;
        margin: 0;
        padding: 0;
        vertical-align: top;
    }
}

.bfs-icon {
    display: inline-block;
    width: 1em;
    height: 1em;
    stroke-width: 0;
    stroke: currentColor;
    fill: currentColor;
}

/**
 * OBJECTS
 */
.bfs-section {
    position: relative;
    padding: 60px calc((1 / 24) * 100%);

    @include mq($breakpoint-lg) {
        padding-top: 40px;
        padding-bottom: 40px;
    }

    &.bfs-section--small {
        padding-top: 30px;
        padding-bottom: 30px;

        @include mq($breakpoint-lg) {
            padding-top: 30px;
            padding-bottom: 30px;
        }

        @include mq($breakpoint-md) {
            padding-top: 30px;
            padding-bottom: 30px;
        }
    }

    &.bfs-section--large {
        padding-top: 100px;
        padding-bottom: 100px;

        @include mq($breakpoint-lg) {
            padding-top: 80px;
            padding-bottom: 80px;
        }

        @include mq($breakpoint-md) {
            padding-top: 60px;
            padding-bottom: 60px;
        }
    }

    &.bfs-section--narrow {
        padding-left: calc((3 / 24) * 100%);
        padding-right: calc((3 / 24) * 100%);

        @include mq($breakpoint-lg-xl) {
            padding-left: calc((2 / 24) * 100%);
            padding-right: calc((2 / 24) * 100%);
        }

        @include mq($breakpoint-lg) {
            padding-left: calc((1 / 24) * 100%);
            padding-right: calc((1 / 24) * 100%);
        }
    }

    &.bfs-section--full {
        padding-left: 0;
        padding-right: 0;

        @include mq($breakpoint-lg-xl) {
            padding-left: 0;
            padding-right: 0;
        }

        @include mq($breakpoint-lg) {
            padding-left: 0;
            padding-right: 0;
        }
    }

    &.bfs-section--no-overflow {
        overflow: hidden;
    }

    &.bfs-section--theme-light,
    .bfs-section--theme-light {
        background-color: $white;
    }

    &.bfs-section--theme-off-light,
    .bfs-section--theme-off-light {
        background-color: $gray;
    }

    &.bfs-section--theme-off-dark,
    .bfs-section--theme-off-dark {
        background-color: $light-yellow;
    }

    &.bfs-section--color-dark,
    .bfs-section--color-dark {
        color: $black;
    }

    &.bfs-section--color-off-dark,
    .bfs-section--color-off-dark {
        color: $teal;
    }

    &.bfs-section--color-light,
    .bfs-section--color-light {
        color: $white;
    }

    &.bfs-section--color-off-light,
    .bfs-section--color-off-light {
        color: $yellow;
    }

    p, h1, h2, h3, h4, h5, h6 {
        margin: 0;
        padding: 0;
    }
}

.bfs-container {
    max-width: 1680px;
    margin: 0 auto;
}

/**
 * COMPONENTS
 */
.bfs-media-wrapper {
    position: relative;
    overflow: hidden;
    border-radius: $border-radius-media;

    &.bfs-media-wrapper--round {
        border-radius: 100%;
    }
}

.bfs-media-overlay {
    display: block;
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.20) 0%, rgba(0, 0, 0, 0.20) 100%);
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;

    &.bfs-media-overlay--bottom {
        height: 80%;
        top: auto;
        bottom: 0;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.70) 100%);
    }
}

.bfs-media-hover {
    .bfs-media {
        transition: $transition;
    }

    @include hover {
        .bfs-media {
            transform: scale(1.05);
        }
    }
}

.bfs-image-hover {
    overflow: hidden;

    .bfs-media {
        transition: $transition;

        + .bfs-media {
            opacity: 0;
            visibility: hidden;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            transform: scale(1.05);
        }
    }

    @include hover {
        .bfs-media:has(+ .bfs-media) {
            opacity: 0;
            visibility: hidden;
            transform: scale(1.05);

            + .bfs-media {
                opacity: 1;
                visibility: visible;
                transform: scale(1);
            }
        }
    }
}

.bfs-media {
    position: relative;
    overflow: hidden;
    width: 100%;
    margin: 0;

    &.bfs-media--background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    .bfs-media__inner {
        height: 0;
        width: 100%;
        padding-bottom: 100%;
        overflow: hidden;
        position: relative;
        display: block;
        margin: 0;

        &.bfs-media__inner--1-1 {
            padding-bottom: 100%;
        }

        &.bfs-media__inner--2-1 {
            padding-bottom: calc((1 / 2) * 100%);
        }

        &.bfs-media__inner--1-2 {
            padding-bottom: calc((2 / 1) * 100%);
        }

        &.bfs-media__inner--3-1 {
            padding-bottom: calc((1 / 3) * 100%);
        }

        &.bfs-media__inner--1-3 {
            padding-bottom: calc((3 / 1) * 100%);
        }

        &.bfs-media__inner--3-2 {
            padding-bottom: calc((2 / 3) * 100%);
        }

        &.bfs-media__inner--2-3 {
            padding-bottom: calc((3 / 2) * 100%);
        }

        &.bfs-media__inner--4-3 {
            padding-bottom: calc((3 / 4) * 100%);
        }

        &.bfs-media__inner--3-4 {
            padding-bottom: calc((4 / 3) * 100%);
        }

        &.bfs-media__inner--16-9 {
            padding-bottom: calc((9 / 16) * 100%);
        }

        &.bfs-media__inner--9-16 {
            padding-bottom: calc((16 / 9) * 100%);
        }

        &.bfs-media__inner--adopt {
            padding-bottom: unset;
            height: 100%;
        }
    }

    .bfs-media__img,
    .bfs-media__video,
    iframe,
    object,
    embed {
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;

        &.bfs-media--cover {
            object-fit: cover;
        }

        &.bfs-media--contain {
            object-fit: contain;
        }

        &.bfs-media--center {
            object-position: center;
        }

        &.bfs-media--top {
            object-position: top;
        }

        &.bfs-media--bottom {
            object-position: bottom;
        }

        &.bfs-media--left {
            object-position: left;
        }

        &.bfs-media--right {
            object-position: right;
        }
    }

    .bfs-media__play-button {
        width: 80px;
        height: 80px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        outline: 0;
        outline: none;
        border: none;
        border-radius: 100%;
        box-shadow: none;
        box-sizing: border-box;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background: rgba($white, 0.8);
        color: inherit;
        font-family: inherit;
        font-size: inherit;
        font-style: inherit;
        font-weight: inherit;
        line-height: normal;
        text-decoration: none;
        text-transform: none;
        text-align: center;
        cursor: pointer;
        vertical-align: top;
        -webkit-appearance: none;
        -moz-appearance: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        transition: $transition;
        z-index: 1;
        opacity: 1;
        visibility: visible;
        -webkit-font-smoothing: inherit;
        -moz-osx-font-smoothing: inherit;
        -webkit-tap-highlight-color: transparent;

        @include hover {
            &:not(:disabled) {
                background: $white;
            }
        }
    }

    &.is-playing {
        .bfs-media__play-button {
            opacity: 0;
            visibility: hidden;
        }
    }
}

.bfs-button {
    width: auto;
    height: auto;
    margin: 0;
    padding: 0;
    overflow: hidden;
    outline: 0;
    outline: none;
    border: none;
    border-radius: $border-radius-button;
    box-shadow: none;
    box-sizing: border-box;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    color: inherit;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    vertical-align: top;
    -webkit-appearance: none;
    -moz-appearance: none;
    position: relative;
    transition: $transition;
    -webkit-font-smoothing: inherit;
    -moz-osx-font-smoothing: inherit;
    -webkit-tap-highlight-color: transparent;

    &:disabled {
        cursor: not-allowed;
        pointer-events: none;
    }

    &.bfs-button--primary {
        background-color: $teal;
        color: $white;

        @include hover {
            &:not(:disabled) {
                background-color: darken($teal, 10%);
                color: $white;
            }
        }

        &:disabled {
            background-color: darken($teal, 15%);
            color: $white;
        }

        &.bfs-button--bordered {
            background-color: transparent;
            color: $teal;
            border: 1px solid $teal;

            @include hover {
                &:not(:disabled) {
                    background-color: $teal;
                    color: $white;
                    border: 1px solid $teal;
                }
            }

            &:disabled {
                background-color: transparent;
                color: darken($teal, 15%);
                border: 1px solid darken($teal, 15%);
            }
        }
    }

    &.bfs-button--secondary {
        background-color: $yellow;
        color: $black;

        @include hover {
            &:not(:disabled) {
                background-color: darken($yellow, 10%);
                color: $black;
            }
        }

        &:disabled {
            background-color: darken($yellow, 15%);
            color: $black;
        }

        &.bfs-button--bordered {
            background-color: transparent;
            color: $yellow;
            border: 1px solid $yellow;

            @include hover {
                &:not(:disabled) {
                    background-color: $yellow;
                    color: $black;
                    border: 1px solid $yellow;
                }
            }

            &:disabled {
                background-color: transparent;
                color: darken($yellow, 15%);
                border: 1px solid darken($yellow, 15%);
            }
        }
    }

    &.bfs-button--tertiary {
        background-color: $black;
        color: $white;

        @include hover {
            &:not(:disabled) {
                background-color: lighten($black, 20%);
                color: $white;
            }
        }

        &:disabled {
            background-color: lighten($black, 25%);
            color: $white;
        }

        &.bfs-button--bordered {
            background-color: transparent;
            color: $black;
            border: 1px solid $black;

            @include hover {
                &:not(:disabled) {
                    background-color: $black;
                    color: $white;
                    border: 1px solid $black;
                }
            }

            &:disabled {
                background-color: transparent;
                color: lighten($black, 25%);
                border: 1px solid lighten($black, 25%);
            }
        }
    }

    &.bfs-button--small {
        min-width: auto;
        min-height: 35px;
        padding-left: 15px;
        padding-right: 15px;
    }

    &.bfs-button--medium {
        min-width: 180px;
        min-height: 45px;
        padding-left: 20px;
        padding-right: 20px;
    }

    &.bfs-button--large {
        min-width: 220px;
        min-height: 55px;
        padding-left: 25px;
        padding-right: 25px;
    }

    &.bfs-button--full {
        width: 100%;
    }

    .bfs-button__loader {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 1;
    }

    &.is-loading {
        transition: none;
        cursor: not-allowed;
        pointer-events: none;
        color: transparent;

        .bfs-button__loader {
            display: flex;
        }
    }
}

.bfs-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: $border-radius-button;
    text-align: center;
    position: relative;
    overflow: hidden;
    padding: 5px;

    &.bfs-badge--black {
        background-color: $black;
        color: $white;
    }

    &.bfs-badge--red {
        background-color: $red;
        color: $white;
    }

    &.bfs-badge--yellow {
        background-color: $yellow;
        color: $black;
    }

    &.bfs-badge--teal {
        background-color: $teal;
        color: $white;
    }

    &.bfs-badge--brown {
        background-color: $brown;
        color: $white;
    }

    &.bfs-badge--sienna {
        background-color: $sienna;
        color: $white;
    }

    &.bfs-badge--orange {
        background-color: $orange;
        color: $white;
    }

    &.bfs-badge--light-green {
        background-color: $light-green;
        color: $white;
    }

    &.bfs-badge--full {
        width: 100%;
        padding: 10px;
    }
}

.bfs-notification {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border-radius: $border-radius-button;
    text-align: left;
    position: relative;
    overflow: hidden;
    padding: 10px 15px;

    &.bfs-notification--black {
        background-color: $black;
        color: $white;
    }

    &.bfs-notification--red {
        background-color: rgba($red, 0.1);
        color: $red;
    }

    &.bfs-notification--medium-gray {
        background-color: $medium-gray;
        color: $black;
    }
}

.bfs-accordion {
    .bfs-accordion__single {
        border-bottom: 1px solid rgba($black, 0.1);

        &:first-child {
            border-top: 1px solid rgba($black, 0.1);
        }
    }

    .bfs-accordion__header {
        width: 100%;
        height: auto;
        margin: 0;
        padding: 15px 0;
        overflow: hidden;
        outline: 0;
        outline: none;
        border: none;
        border-radius: 0;
        box-shadow: none;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 15px;
        background: transparent;
        color: inherit;
        text-decoration: none;
        text-align: left;
        cursor: pointer;
        vertical-align: top;
        -webkit-appearance: none;
        -moz-appearance: none;
        position: relative;
        -webkit-font-smoothing: inherit;
        -moz-osx-font-smoothing: inherit;
        -webkit-tap-highlight-color: transparent;

        &.is-active {
            .bfs-accordion__header-indicator {
                transform: rotate(180deg);
            }
        }
    }

    .bfs-accordion__header-indicator {
        font-size: 1.5em;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        flex-grow: 0;
        transition: $transition;
    }

    .bfs-accordion__panel {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.2s ease-out;
    }

    .bfs-accordion__panel-inner {
        padding-bottom: 15px;
        max-width: 540px;
    }
}

.bfs-slider {
    &.swiper {
        display: none;
    }

    &.swiper.swiper-initialized {
        display: block;
    }

    .swiper-wrapper {
        box-sizing: border-box;
    }

    .swiper-slide {
        height: auto;
    }
}

.bfs-slider__navigation {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
}

.bfs-slider__navigation:has(.swiper-button-lock) {
    display: none;
}

.bfs-slider__button {
    width: 40px;
    height: 40px;
    margin: 0;
    padding: 0;
    overflow: hidden;
    outline: 0;
    outline: none;
    border: 1px solid rgba($black, 0.1);
    border-radius: 100%;
    box-shadow: none;
    box-sizing: border-box;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: $gray;
    color: inherit;
    font-family: inherit;
    font-size: inherit;
    font-style: inherit;
    font-weight: inherit;
    line-height: normal;
    text-decoration: none;
    text-transform: none;
    text-align: center;
    cursor: pointer;
    vertical-align: top;
    -webkit-appearance: none;
    -moz-appearance: none;
    position: relative;
    transform: none;
    transition: $transition;
    z-index: 1;
    opacity: 1;
    visibility: visible;
    -webkit-font-smoothing: inherit;
    -moz-osx-font-smoothing: inherit;
    -webkit-tap-highlight-color: transparent;

    @include hover {
        &:not(:disabled) {
            background: $black;
            border-color: $black;
            color: $white;
        }
    }

    &:disabled {
        background-color: $gray;
        border-color: $gray;
        cursor: not-allowed;
        opacity: 0.5;
    }
}

.bfs-form {
    display: block;
    width: 100%;

    .bfs-form__fieldset {
        padding: 0;
        margin: 0 0 20px;
        border-width: 0;

        &:last-child {
            margin-bottom: 0;
        }

        &:last-of-type {
            margin-bottom: 0;
        }
    }

    .bfs-form__label,
    .bfs-form__legend {
        display: block;
        margin: 0 0 10px;
        cursor: pointer;
        width: 100%;
        color: $black;

        &.bfs-form__label--flex {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 10px;
        }

        a {
            color: rgba($black, 0.5);
            text-decoration: underline;
            text-transform: none;
        }
    }

    .bfs-form__input {
        display: block;
        width: 100%;
        height: 45px;
        box-sizing: border-box;
        padding: 0 15px;
        margin: 0;
        border: 1px solid $medium-gray;
        border-radius: 5px;
        background-color: transparent;
        box-shadow: none;
        outline: none;
        transition: $transition;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        color: $black;
        font-size: clamp(15px, calc(0.313vw + 0.813rem), 16px);
        font-style: normal;
        font-weight: normal;
        line-height: 140%;

        @include hover {
            outline: 0;
            box-shadow: none;
            background-color: transparent;
            color: $black;
            border-color: $teal;
        }

        &:focus {
            outline: 0;
            box-shadow: none;
            background-color: transparent;
            color: $black;
            border-color: $teal;
        }

        &:disabled {
            outline: 0;
            box-shadow: none;
            pointer-events: none;
            background-color: transparent;
            color: $medium-gray;
            border-color: $medium-gray;
        }

        &:-webkit-autofill,
        &:-webkit-autofill:focus {
            transition: background-color 600000s 0s,
            color 600000s 0s;
        }

        &[data-autocompleted] {
            background-color: transparent !important;
        }

        &::placeholder {
            color: $medium-gray;
            opacity: 1;
        }

        &:-ms-input-placeholder {
            color: $medium-gray;
        }

        &::-ms-input-placeholder {
            color: $medium-gray;
        }
    }

    .bfs-form__input.bfs-form__input--error {
        border-color: $red;

        @include hover {
            border-color: $red;
        }

        &:focus {
            border-color: $red;
        }
    }

    .bfs-form__input.bfs-form__input--select {
        padding-right: 60px;
        cursor: pointer;
    }

    .bfs-form__input.bfs-form__input--textarea {
        min-height: 150px;
        height: 150px;
        padding-top: 15px;
        padding-bottom: 15px;
        resize: none;
    }

    .bfs-form__radio-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }

    .bfs-form__radio {
        position: absolute;
        z-index: -1;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
        box-sizing: border-box;

        @include hover {
            &:not(:checked):not(:disabled) {
                & + .bfs-form__radio-label {
                    outline: 0;
                    box-shadow: none;
                    background-color: transparent;
                    color: $black;
                    border-color: $teal;
                }
            }
        }

        &:focus {
            &:not(:checked):not(:disabled) {
                & + .bfs-form__radio-label {
                    outline: 0;
                    box-shadow: none;
                    background-color: transparent;
                    color: $black;
                    border-color: $teal;
                }
            }
        }

        &:checked {
            & + .bfs-form__radio-label {
                outline: 0;
                box-shadow: none;
                background-color: $light-teal;
                color: $black;
                border-color: $teal;
            }
        }

        &:disabled {
            & + .bfs-form__radio-label {
                outline: 0;
                box-shadow: none;
                background-color: transparent;
                color: $medium-gray;
                border-color: $medium-gray;
            }

            &:checked {
                & + .bfs-form__radio-label {
                    background-color: transparent;
                    color: $medium-gray;
                    border-color: $light-teal;
                }
            }
        }
    }

    .bfs-form__radio-label {
        box-sizing: border-box;
        padding: 10px;
        margin: 0;
        min-width: 55px;
        font-family: 'Qualy', sans-serif;
        font-size: 12px;
        font-style: normal;
        line-height: 100%;
        letter-spacing: 0.6px;
        text-transform: uppercase;
        background-color: transparent;
        color: $black;
        border: 1px solid $medium-gray;
        border-radius: 5px;
        box-shadow: none;
        outline: none;
        transition: $transition;
        -webkit-appearance: none;
        -moz-appearance: none;
        position: relative;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        &:before,
        &:after {
            box-sizing: border-box;
            line-height: 1;
            transform-origin: center;
        }

        a {
            text-decoration: underline;
        }
    }

    .bfs-form__select-wrapper {
        position: relative;
        cursor: pointer;
    }

    .bfs-form__select-indicator {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 50%;
        right: 15px;
        transform: translateY(-50%);
        font-size: 1em;
        pointer-events: none;
    }

    .bfs-form__validation-message {
        margin-top: 5px;
        color: $red;
    }
}

.bfs-product-share {
    display: inline-flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    list-style: none;
    margin: 0;

    li,
    a {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin: 0;
        text-align: center;
    }

    a {
        width: 2em;
        height: 2em;
        background-color: transparent;
        color: $black;
        border: 1px solid $black;
        border-radius: 100%;
        overflow: hidden;
        transition: $transition;

        @include hover {
            background-color: $teal;
            color: $white;
            border-color: $teal;
        }
    }
}

.bfs-upsell-progress-bar {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
    text-align: center;

    .bfs-upsell-progress-bar__thresholds {
        margin: 0;
        padding: 0;
        list-style: none;
        display: flex;
        align-items: flex-start;
        gap: 5px;
    }

    .bfs-upsell-progress-bar__threshold {
        margin: 0;
        padding: 0;
        flex-grow: 1;
        flex-basis: 0;
        min-width: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;

        &.is-active {
            .bfs-upsell-progress-bar__threshold-indicator {
                background-color: $red;

                .bfs-icon--checkmark {
                    opacity: 1;
                    visibility: visible;
                }
            }
        }
    }

    .bfs-upsell-progress-bar__threshold-indicator {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        background-color: transparent;
        color: $white;
        border: 1px solid $red;
        border-radius: 100%;
        overflow: hidden;
        transition: $transition;

        .bfs-icon--checkmark {
            opacity: 0;
            visibility: hidden;
            transition: $transition;
        }
    }

    .bfs-upsell-progress-bar__progress-bar {
        width: 100%;
        height: 5px;
        border-radius: 5px;
        background-color: rgba($red, 0.1);
        overflow: hidden;

        span {
            display: block;
            width: 0;
            height: 100%;
            border-radius: 5px;
            background-color: $red;
            overflow: hidden;
            transition: $transition;
        }
    }

    .bfs-upsell-progress-bar__status {
        i,
        em {
            font-style: normal;
            color: $red;
        }
    }
}

/**
 * CARDS
 */
.bfs-product-card {
    display: block;

    @include hover {
        .bfs-product-card__variants {
            opacity: 1;
            visibility: visible;
        }
    }

    .bfs-product-card__media {
        overflow: hidden;
        position: relative;
    }

    .bfs-product-card__info {
        position: absolute;
        top: 0;
        left: 0;
        padding: 10px;
        display: flex;
        align-items: flex-start;
        gap: 10px;
        width: 100%;
        z-index: 3;
    }

    .bfs-product-card__badges {
        margin: 0;
        padding: 0;
        list-style: none;
        flex: 1 0 0;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 5px;

        &:empty {
            display: none;
        }

        li {
            margin: 0;
            padding: 0;
        }
    }

    .bfs-product-card__wishlist {
        flex-shrink: 0;
        flex-grow: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-left: auto;

        &:empty {
            display: none;
        }

        .swym-button.swym-add-to-wishlist-view-product {
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: $transition;

            &:after {
                transition: $transition;
            }

            @include hover {
                &:after {
                    color: $red !important;
                }
            }
        }
    }

    .bfs-product-card__bottom-info {
        position: absolute;
        bottom: 0;
        left: 0;
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        z-index: 3;

        &:empty {
            display: none;
        }
    }

    .bfs-product-card__title {
        &:not(:only-child) {
            margin-top: 15px;
        }
    }

    .bfs-product-card__brand {
        text-transform: uppercase;

        &:not(:only-child) {
            margin-top: 10px;
        }

        &:empty {
            display: none;
        }
    }

    .bfs-product-card__price {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        &:not(:only-child) {
            margin-top: 10px;
        }
    }

    .bfs-product-card__sale-price {
        color: $red;
    }

    .bfs-product-card__sale-compare-at-price {
        opacity: 0.5;
    }

    .bfs-product-card__variants {
        margin: 10px 0 0;
        padding: 0;
        display: flex;
        flex-wrap: wrap;
        list-style: none;
        gap: 5px;
        opacity: 0;
        visibility: hidden;
        transition: $transition;

        &:empty {
            display: none;
        }

        @include mq($breakpoint-lg) {
            opacity: 1;
            visibility: visible;
        }

        li {
            margin: 0;
            padding: 0;
            display: inline-flex;
            justify-content: center;

            &.is-unavailable {
                opacity: 0.5;
                text-decoration: line-through;
            }
        }
    }
}

.bfs-collection-card {
    text-align: center;

    .bfs-collection-card__media {
        overflow: hidden;
    }

    .bfs-collection-card__title {
        text-decoration: underline;

        &:not(:only-child) {
            margin-top: 10px;
        }
    }
}

.bfs-collection-cta-card {
    position: relative;

    .bfs-collection-cta-card__media {
        overflow: hidden;
    }

    .bfs-collection-cta-card__content {
        position: absolute;
        left: 0;
        bottom: 0;
        z-index: 2;
        color: $white;
        width: 100%;
        padding: 20px;
        display: grid;
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .bfs-collection-cta-card__label {
        text-decoration: underline;
    }
}

.bfs-artist-card {
    text-align: center;

    .bfs-artist-card__media {
        overflow: hidden;

        &:not(:only-child) {
            margin-bottom: 20px;
        }
    }

    .bfs-artist-card__subtitle {
        &:not(:only-child) {
            margin-top: 5px;
        }
    }

    .bfs-artist-card__description {
        &:not(:only-child) {
            margin-top: 10px;
        }
    }
}

.bfs-simple-content-card {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 40px 20px;
    border-radius: $border-radius-card;
    height: 100%;

    @include mq($breakpoint-sm-md) {
        padding: 20px;
    }
}

.bfs-testimonial-card {
    padding: 30px 20px;
    border-radius: $border-radius-card;
    height: 100%;
    display: flex;
    flex-direction: column;

    @include mq($breakpoint-sm-md) {
        padding: 20px;
    }

    .bfs-testimonial-card__rating {
        display: flex;
        align-items: center;
        gap: 5px;

        &:not(:only-child) {
            margin-bottom: 15px;
        }
    }

    .bfs-testimonial-card__rating-stars {
        margin: 0;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 2px;
        list-style: none;

        li {
            margin: 0;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
    }

    .bfs-testimonial-card__rating-label {
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .bfs-testimonial-card__subtitle {
        &:not(:only-child) {
            margin-top: 10px;
        }
    }

    .bfs-testimonial-card__author {
        &:not(:only-child) {
            margin-top: 10px;
        }
    }

    .bfs-testimonial-card__product {
        &:not(:only-child) {
            padding-top: 35px;
            margin-top: auto;
        }

        a {
            text-decoration: underline;
        }
    }
}

.bfs-article-card {
    display: flex;
    flex-direction: column;
    gap: 15px;

    .bfs-article-card__media {
        overflow: hidden;
    }

    .bfs-article-card__meta {
        margin: 0;
        padding: 0;
        list-style: none;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        li {
            margin: 0;
            padding: 0;
        }
    }

    .bfs-article-card__title {
    }

    .bfs-article-card__cta {
        text-decoration: underline;
    }
}

.bfs-feature-card {
    margin: 0;
    border-radius: $border-radius-card;
    padding: 40px 30px;
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: 20px;
    text-align: center;

    @include mq($breakpoint-md-lg) {
        padding: 20px 15px;
    }

    .bfs-feature-card__icon {
        width: 60px;
    }
}

.bfs-media-with-content-card {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .bfs-media-with-content-card__media {
        overflow: hidden;

        &:not(:only-child) {
            margin-bottom: 5px;
        }
    }
}

/**
 * ANIMATIONS
 */
@keyframes bfs-scroll-marquee {
    from {
        transform: translateX(0);
    }

    to {
        transform: translateX(calc(-100% - 20px));
    }
}

/**
 * SECTIONS
 */

/**
 * Product
 */
.bfs-product {
    display: grid;
    grid-template-columns: calc((14 / 22) * 100%) calc((7.5 / 22) * 100%);
    gap: calc((0.5 / 22) * 100%);
    color: $black;

    @include mq($breakpoint-lg) {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .bfs-product__gallery-desktop {
        @include mq($breakpoint-lg) {
            display: none;
        }
    }

    .bfs-product__gallery-desktop-initial {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;

        &:not(:only-child) {
            margin-bottom: 10px;
        }
    }

    .bfs-product__gallery-desktop-hidden {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.4s ease-out;
    }

    .bfs-product__gallery-desktop-hidden-inner {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }

    .bfs-product__gallery-desktop-trigger {
        margin-top: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .bfs-product__gallery-desktop-trigger-button {
        span:first-child {
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        span:last-child {
            display: none;
        }

        &.is-active {
            span:first-child {
                display: none;
            }

            span:last-child {
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }
        }
    }

    .bfs-product__gallery-mobile {
        display: none;
        min-width: 0;
        position: relative;

        @include mq($breakpoint-lg) {
            display: block;
        }
    }

    .bfs-product__gallery-desktop-media,
    .bfs-product__gallery-mobile-media {
        position: relative;
    }

    .bfs-product__media-infos {
        position: absolute;
        top: 0;
        left: 0;
        padding: 10px;
        display: flex;
        align-items: flex-start;
        gap: 10px;
        width: 100%;
        z-index: 3;
    }

    .bfs-product__media-top-badges {
        margin: 0;
        padding: 0;
        list-style: none;
        flex: 1 0 0;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 5px;

        &:empty {
            display: none;
        }
    }

    .bfs-product__media-wishlist {
        flex-shrink: 0;
        flex-grow: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-left: auto;

        &:empty {
            display: none;
        }

        .swym-button {
            padding: 0 !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            transition: $transition !important;
            width: 20px !important;
            height: 20px !important;
            line-height: 1 !important;

            &:after {
                transition: $transition !important;
                font-size: 20px !important;
                width: 100% !important;
                height: 100% !important;
            }

            @include hover {
                &:after {
                    color: $red !important;
                }
            }
        }

        .swym-fave-count {
            display: none !important;
        }

        .swym-btn-container[data-position=default] .swym-add-to-wishlist.swym-icon .swym-tooltip .swym-tooltip-text {
            left: -40px !important;
        }
    }

    .bfs-product__media-bottom-badges {
        position: absolute;
        bottom: 0;
        left: 0;
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        z-index: 3;

        &:empty {
            display: none;
        }
    }

    .bfs-product__main {
        min-width: 0;
        display: flex;
        flex-direction: column;
        gap: 30px;
    }

    .bfs-product__divider {
        width: 100%;
        height: 1px;
        display: block;
        background-color: rgba($black, 0.1);
    }

    .bfs-product__reviews {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        .stamped-product-reviews-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .stamped-badge {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .stamped-badge-starrating {
            margin: 0 !important;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .stamped-fa {
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .fa-star,
        .fa-star-checked,
        .fa-star-half-o,
        .fa-star-o,
        .stamped-fa-star,
        .stamped-fa-star-checked,
        .stamped-fa-star-half-o,
        .stamped-fa-star-o {
            color: $yellow;
        }

        .stamped-badge-caption {
            font-size: 14px;
            font-style: normal;
            font-weight: normal;
            line-height: 140%;
            color: $teal;
            text-decoration: underline;
        }
    }

    .bfs-product__price {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-end;
        gap: 10px;

        &.is-on-sale {
            .bfs-product__regular-price {
                color: $red;
            }
        }
    }

    .bfs-product__compare-at-price {
        color: $medium-gray;

        &:empty {
            display: none;
        }
    }

    .bfs-product__discount-percentage {
        &:empty {
            display: none;
        }
    }

    .bfs-product__form {
        .saso-bundle,
        .saso-volumes,
        .swym-button-bar,
        .klaviyo-bis-trigger {
            display: none !important;
        }
    }

    .bfs-product__selling-points {
        display: flex;
        gap: 20px;
        list-style: none;
        margin: 0;

        @include mq($breakpoint-lg) {
            gap: 10px;
        }
    }

    .bfs-product__selling-point {
        flex-grow: 1;
        flex-basis: 0;
        min-width: 0;
        margin: 0;
        text-align: center;
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .bfs-product__selling-point-icon {
        width: 20px;
        margin: 0 auto;
    }

    .bfs-product__corporate-cta-button {
        &.bfs-button {
            justify-content: space-between;
            padding-left: 15px;
            padding-right: 15px;
        }

        span {
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .bfs-icon {
            font-size: 1.5lh;
        }
    }

    .bfs-product__rewards {
        border-radius: 5px;
        overflow: hidden;
        background: $light-yellow;
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 20px;

        @include mq($breakpoint-sm) {
            flex-direction: column;
            align-items: unset;
        }

        .bfs-button {
            min-width: 140px;

            @include mq($breakpoint-sm) {
                min-width: auto;
                width: 100%;
            }
        }
    }

    .bfs-product__rewards-content {
        flex: 1 0 0;
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .bfs-product__rewards-subtitle {
        .yotpo-product-points-widget-potential-earning-points,
        .yotpo-product-points-widget-points-amount {
            color: $black !important;
        }
    }

    .bfs-product__rewards-subtitle.bfs-fs-b3 {
        text-transform: none;
    }

    .bfs-product__rewards-cta {
        flex-shrink: 0;
        flex-grow: 0;
    }

    #bfs-artist-inject:empty {
        display: none;
    }

    .bfs-product__artist {
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .bfs-product__artist-image {
        flex: 0 0 120px;

        @include mq($breakpoint-sm-md) {
            flex: 0 0 80px;
        }
    }

    .bfs-product__artist-content {
        flex: 1 0 0;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .bfs-product__artist-name.bfs-fs-b3 {
        text-transform: none;
    }

    .bfs-product__artist-quote {
        margin-top: 5px;
    }

    .bfs-product__artist-cta {
        color: $teal;
        text-decoration: underline;
        display: inline-flex;
    }

    .bfs-slider__button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;

        &:first-child {
            left: 10px;
        }

        &:last-child {
            right: 10px;
        }
    }
}

.bfs-product-reviews {
    #stamped-main-widget {
        margin: 0;
    }

    div.stamped-container[data-widget-style],
    div[data-widget-style] div.stamped-container {
        max-width: 100%;
        margin: 0 !important;
    }

    .stamped-container {
        font-family: inherit;
        color: $black;
    }

    .stamped-header-title {
        display: block !important;
        font-family: 'Qualy', sans-serif;
        //font-size: 30px;
        font-size: clamp(25px, calc(0.1923rem + 1.9231vw), 30px);
        font-style: normal;
        font-weight: normal;
        line-height: 120%;
        max-width: 600px;
        margin: 0 0 40px;
        text-align: left !important;
    }

    .stamped-header {
        padding-top: 20px;
        border-top: 1px solid $medium-gray;
        margin-bottom: 0 !important;
    }

    .stamped-summary {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 20px;

        &[data-count="0"] {
            justify-content: center;
        }

        &:before,
        &:after {
            display: none !important;
        }

        > div,
        > span {
            float: none !important;
            margin: 0 !important;
            padding: 0 !important;

            @include mq($breakpoint-md-lg) {
                width: 100% !important;
                max-width: 100%;
                min-width: 100%;
            }
        }
    }

    .summary-overview,
    div[data-widget-style*=standard] .stamped-summary-ratings,
    .stamped-summary-photos.stamped-summary-photos-container, {
        margin-bottom: 0 !important;
    }

    .summary-overview {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 10px;
    }

    div[data-count="0"] .summary-overview,
    div[data-widget-style*=standard][data-count="0"] .summary-overview {
        display: none;
    }

    .stamped-summary-text-1 {
        font-family: 'Qualy', sans-serif;
        //font-size: 30px;
        font-size: clamp(25px, calc(0.1923rem + 1.9231vw), 30px);
        font-style: normal;
        font-weight: normal;
        line-height: 120%;
        margin: 0;

        strong,
        b {
            font-weight: 400;
        }
    }

    .stamped-summary-caption .stamped-summary-text {
        //font-size: 16px;
        font-size: clamp(15px, calc(0.313vw + 0.813rem), 16px);
        font-style: normal;
        font-weight: 400;
        line-height: 120%;
        margin: 0;
    }

    [data-version="2"] .stamped-summary-starrating {
        display: inline-flex;
        vertical-align: super;
    }

    .stamped-fa {
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .fa-star,
    .fa-star-checked,
    .fa-star-half-o,
    .fa-star-o,
    .stamped-fa-star,
    .stamped-fa-star-checked,
    .stamped-fa-star-half-o,
    .stamped-fa-star-o {
        color: $yellow;
    }

    .stamped-summary-ratings {
        border: none;
        text-align: left !important;
    }

    .summary-rating {
        margin-bottom: 5px;
    }

    .summary-rating-bar {
        background-color: $medium-gray;
    }

    .summary-rating-title {
        color: $black;
    }

    .summary-rating-bar-content {
        background-color: $yellow;
    }

    div[data-widget-style] .summary-rating:first-child .summary-rating-title:before,
    div[data-widget-style] .summary-rating:nth-child(2) .summary-rating-title:before,
    div[data-widget-style] .summary-rating:nth-child(3) .summary-rating-title:before,
    div[data-widget-style] .summary-rating:nth-child(4) .summary-rating-title:before,
    div[data-widget-style] .summary-rating:nth-child(5) .summary-rating-title:before {
        color: inherit;
    }

    .stamped-photos-carousel {
        > div {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
    }

    .stamped-photos-carousel .photo.stamped-photo {
        position: relative;
        width: 70px;
        height: 70px;
        padding: 0;
        margin: 0;
        border: none;
        border-radius: $border-radius-button;
        overflow: hidden;
    }

    .stamped-summary-photos-container .stamped-photos-carousel .photo.stamped-photo img {
        width: 70px;
        height: 70px !important;
        border-radius: $border-radius-button;
        overflow: hidden;
    }

    .stamped-summary-actions {
        @include mq($breakpoint-lg) {
            width: 100%;
        }
    }

    .stamped-summary-actions-clear,
    .stamped-summary-actions-mobile-filter,
    .stamped-summary-actions-newquestion,
    .stamped-summary-actions-newreview {
        width: auto;
        height: auto;
        margin: 0;
        padding: 0 15px;
        overflow: hidden;
        outline: 0;
        outline: none;
        border: none;
        border-radius: $border-radius-button;
        box-shadow: none !important;
        box-sizing: border-box;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        text-align: center;
        cursor: pointer;
        vertical-align: top;
        -webkit-appearance: none;
        -moz-appearance: none;
        position: relative;
        transition: $transition;
        -webkit-font-smoothing: inherit;
        -moz-osx-font-smoothing: inherit;
        -webkit-tap-highlight-color: transparent;
        min-width: auto;
        min-height: 35px;
        font-family: 'Qualy', sans-serif;
        font-size: 12px;
        font-style: normal;
        line-height: 100%;
        letter-spacing: 0.6px;
        text-transform: uppercase;
        font-weight: 400;
        background-color: $teal;
        color: $white;

        @include hover {
            &:not(:disabled) {
                background-color: darken($teal, 10%);
                color: $white;
            }
        }

        &:disabled {
            background-color: darken($teal, 15%);
            color: $white;
            cursor: not-allowed;
            pointer-events: none;
        }

        @include mq($breakpoint-lg) {
            width: 100%;
        }
    }

    .stamped-container ul.stamped-tabs {
        border-bottom: 1px solid $medium-gray;
    }

    div[data-widget-style*=standard] .stamped-tab-container {
        height: 20px;
    }

    div[data-widget-style*=standard] .stamped-tab-container ul.stamped-tabs {
        height: 20px;
    }

    .stamped-reviews-filter[data-show-filters=part],
    .stamped-reviews-filter[data-show-filters=true] {
        padding-top: 20px;
        padding-bottom: 0;
        border: none;
    }

    .stamped-reviews-filter {
        margin: 0;
        display: block;
    }

    .stamped-reviews-filter-label {
        display: none !important;
    }

    .stamped-filters-wrapper {
        display: none !important;
    }

    #stamped-sort-select {
        margin: 0;
    }

    select#stamped-sort-select {
        border: 1px solid $medium-gray !important;
        font-size: 12px;
        font-style: normal;
        font-weight: normal;
        line-height: 120%;
        padding: 0 15px !important;
        border-radius: $border-radius-button !important;

        @include mq($breakpoint-sm) {
            border: 1px solid $medium-gray !important;
            -webkit-appearance: none;
            -moz-appearance: none;
            width: 100%;
            min-width: 100% !important;
            font-weight: 400;
            background-color: #fff;
            background-repeat: no-repeat;
            height: 35px;
            cursor: pointer;
            background-size: initial !important;
            padding: 0 15px !important;
            appearance: none;
        }
    }

    .stamped-review {
        margin-bottom: 20px;
        padding-top: 20px;
        border-top: 1px solid $medium-gray;

        &:first-child {
            border-top: none;
        }
    }

    .stamped-review-content {
        margin: 0 !important;
    }

    .stamped-review-header .created,
    .stamped-review-header-byline .created {
        font-size: 12px;
        font-style: normal;
        font-weight: normal;
        line-height: 120%;
    }

    .stamped-review-body {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .stamped-review-header-starratings {
        display: flex;
        margin-right: 0;
        margin-top: 10px;
    }

    .stamped-review-header-title {
        //font-size: 18px;
        font-size: clamp(17px, calc(0.313vw + 0.938rem), 18px);
        font-style: normal;
        font-weight: 700;
        line-height: 120%;
        max-width: 1200px;
    }

    p.stamped-review-content-body {
        //font-size: 16px;
        font-size: clamp(15px, calc(0.313vw + 0.813rem), 16px);
        font-style: normal;
        font-weight: normal;
        line-height: 140%;
        margin: 0;
        max-width: 1200px;
    }

    .stamped-review-image {
        margin: 5px 0 0;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        a {
            display: inline-flex;
            margin: 0;
        }

        img {
            border: none;
            border-radius: $border-radius-button;
            overflow: hidden;
        }
    }

    .stamped-review-footer {
        display: none;
    }

    .stamped-pagination a {
        //font-size: 18px;
        font-size: clamp(17px, calc(0.313vw + 0.938rem), 18px);
        font-style: normal;
        font-weight: 400;
        line-height: 120%;
        color: $teal;
    }

    .new-review-form.stamped-visible {
        border: none !important;
        padding: 40px 0 !important;
    }

    .stamped-form-title {
        display: block !important;
        font-family: 'Qualy', sans-serif;
        //font-size: 22px;
        font-size: clamp(20px, calc(0.625vw + 1rem), 22px);
        font-style: normal;
        font-weight: normal;
        line-height: 120%;
        max-width: 600px;
        margin: 0 0 40px;
    }

    fieldset {
        padding: 0;
        margin: 0 0 20px;
        border-width: 0;

        &:last-child {
            margin-bottom: 0;
        }

        &:last-of-type {
            margin-bottom: 0;
        }

        > div {
            padding: 0;
            margin: 0 0 20px;
            border-width: 0;
            width: 100%;
            max-width: 100%;
            float: none;

            &:last-child {
                margin-bottom: 0;
            }

            &:last-of-type {
                margin-bottom: 0;
            }
        }
    }

    .stamped-form-label {
        display: block;
        margin: 0 0 10px;
        cursor: pointer;
        width: 100%;
        color: $black;
        font-family: 'Qualy', sans-serif;
        font-size: 12px;
        font-style: normal;
        line-height: 100%;
        letter-spacing: 0.6px;
        text-transform: uppercase;
    }

    input.stamped-form-input,
    textarea.stamped-form-input {
        display: block;
        width: 100%;
        height: 45px;
        box-sizing: border-box;
        padding: 0 15px;
        margin: 0;
        border: 1px solid $medium-gray;
        border-radius: 5px;
        background-color: transparent;
        box-shadow: none;
        outline: none;
        transition: $transition;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        color: $black;
        font-size: clamp(15px, calc(0.313vw + 0.813rem), 16px);
        font-style: normal;
        font-weight: normal;
        line-height: 140%;

        @include hover {
            outline: 0;
            box-shadow: none;
            background-color: transparent;
            color: $black;
            border-color: $teal;
        }

        &:focus {
            outline: 0;
            box-shadow: none;
            background-color: transparent;
            color: $black;
            border-color: $teal;
        }

        &:disabled {
            outline: 0;
            box-shadow: none;
            pointer-events: none;
            background-color: transparent;
            color: $medium-gray;
            border-color: $medium-gray;
        }

        &:-webkit-autofill,
        &:-webkit-autofill:focus {
            transition: background-color 600000s 0s,
            color 600000s 0s;
        }

        &[data-autocompleted] {
            background-color: transparent !important;
        }

        &::placeholder {
            color: $medium-gray;
            opacity: 1;
        }

        &:-ms-input-placeholder {
            color: $medium-gray;
        }

        &::-ms-input-placeholder {
            color: $medium-gray;
        }

        &.stamped-form-input-textarea {
            min-height: 150px;
            height: 150px;
            padding-top: 15px;
            padding-bottom: 15px;
            resize: none;
        }
    }

    div.stamped-form-input {
        font-weight: 400;
        margin: 0;
        display: block;
        width: 100% !important;
        min-height: auto;
        line-height: inherit;
    }

    .stamped-form-custom-questions {
        display: none;
    }

    .stamped-form-actions {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        gap: 20px;

        &:before,
        &:after {
            display: none !important;
        }

        @include mq($breakpoint-lg) {
            flex-direction: column;
        }
    }

    .stamped-file-uploader-btn {
        width: auto;
        height: auto;
        margin: 0 !important;
        padding: 0 20px !important;
        overflow: hidden;
        outline: 0;
        outline: none;
        border-radius: $border-radius-button !important;
        box-shadow: none;
        box-sizing: border-box;
        display: inline-flex !important;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        text-align: center;
        cursor: pointer;
        vertical-align: top;
        -webkit-appearance: none;
        -moz-appearance: none;
        position: relative;
        transition: $transition;
        -webkit-font-smoothing: inherit;
        -moz-osx-font-smoothing: inherit;
        -webkit-tap-highlight-color: transparent;
        background-color: transparent !important;
        color: $teal !important;
        border: 1px solid $teal !important;
        min-width: 180px;
        min-height: 45px;
        font-family: 'Qualy', sans-serif !important;
        font-size: 14px !important;
        font-style: normal !important;
        line-height: 100% !important;
        letter-spacing: 0.7px !important;
        text-transform: uppercase !important;

        @include hover {
            &:not(:disabled) {
                background-color: $teal !important;
                color: $white !important;
                border: 1px solid $teal !important;
            }
        }

        &:disabled {
            background-color: transparent !important;
            color: darken($teal, 15%) !important;
            border: 1px solid darken($teal, 15%) !important;
            cursor: not-allowed;
            pointer-events: none;
        }

        .stamped-fa:before {
            margin-right: 10px;
        }

        @include mq($breakpoint-lg) {
            width: 100%;
        }
    }

    #stamped-button-submit,
    input.stamped-button.stamped-button-primary.button.button-primary.btn.btn-primary {
        width: auto;
        height: auto;
        margin: 0 !important;
        padding: 0 20px !important;
        overflow: hidden;
        outline: 0;
        outline: none;
        border-radius: $border-radius-button !important;
        box-shadow: none;
        box-sizing: border-box;
        display: inline-flex !important;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        text-align: center;
        cursor: pointer;
        vertical-align: top;
        -webkit-appearance: none;
        -moz-appearance: none;
        position: relative;
        transition: $transition;
        -webkit-font-smoothing: inherit;
        -moz-osx-font-smoothing: inherit;
        -webkit-tap-highlight-color: transparent;
        background-color: $teal !important;
        color: $white !important;
        border: none !important;
        min-width: 180px;
        min-height: 45px;
        font-family: 'Qualy', sans-serif !important;
        font-size: 14px !important;
        font-style: normal !important;
        line-height: 100% !important;
        letter-spacing: 0.7px !important;
        text-transform: uppercase !important;

        @include hover {
            &:not(:disabled) {
                background-color: darken($teal, 10%) !important;
                color: $white !important;
            }
        }

        &:disabled {
            background-color: darken($teal, 15%);
            color: $white !important;
            cursor: not-allowed;
            pointer-events: none;
        }

        @include mq($breakpoint-lg) {
            width: 100%;
        }
    }

    .stamped-file-uploader {
        @include mq($breakpoint-lg) {
            width: 100%;
        }
    }

    .stamped-file-holder {
        float: none !important;
        margin-top: 0;
        display: inline-flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-right: auto;
    }

    .stamped-file-photo {
        border-radius: $border-radius-button;
        overflow: hidden;

        img {
            border-radius: $border-radius-button;
            overflow: hidden;
        }
    }

    .stamped-messages .stamped-thank-you {
        padding: 20px 0;
        border: none;
        margin-top: 20px;
    }

    .stamped-messages .stamped-thank-you p:first-child {
        font-family: 'Qualy', sans-serif;
        //font-size: 22px;
        font-size: clamp(20px, calc(0.625vw + 1rem), 22px);
        font-style: normal;
        font-weight: normal;
        line-height: 120%;
        margin-bottom: 20px;
    }

    .stamped-messages .stamped-thank-you p:nth-child(2) {
        //font-size: 16px;
        font-size: clamp(15px, calc(0.313vw + 0.813rem), 16px);
        font-style: normal;
        font-weight: normal;
        line-height: 140%;
    }

    .stamped-share-links {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }

    .stamped-messages .stamped-share-links a {
        width: auto;
        height: auto;
        margin: 0;
        padding: 0 15px;
        overflow: hidden;
        outline: 0;
        outline: none;
        border-radius: $border-radius-button;
        box-shadow: none;
        box-sizing: border-box;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        text-align: center;
        cursor: pointer;
        vertical-align: top;
        -webkit-appearance: none;
        -moz-appearance: none;
        position: relative;
        transition: $transition;
        -webkit-font-smoothing: inherit;
        -moz-osx-font-smoothing: inherit;
        -webkit-tap-highlight-color: transparent;
        background-color: transparent;
        color: $teal;
        border: 1px solid $teal;
        min-width: auto;
        min-height: 35px;
        font-family: 'Qualy', sans-serif;
        font-size: 12px;
        font-style: normal;
        line-height: 100%;
        letter-spacing: 0.6px;
        text-transform: uppercase;

        @include hover {
            &:not(:disabled) {
                background-color: $teal;
                color: $white;
                border: 1px solid $teal;
            }
        }

        &:disabled {
            background-color: transparent;
            color: darken($teal, 15%);
            border: 1px solid darken($teal, 15%);
            cursor: not-allowed;
            pointer-events: none;
        }
    }

    .stamped-empty-state {
        margin-top: 40px;
    }

    .stamped-container[data-count="0"] .stamped-empty-state:before {
        color: $yellow;
    }

    .stamped-container[data-count="0"][data-widget-language=""] .stamped-empty-state > div,
    .stamped-container[data-count="0"][data-widget-language=en] .stamped-empty-state > div {
        color: $black;
        margin-top: 10px;
        font-size: 14px;
        font-style: normal;
        font-weight: normal;
        line-height: 140%;
    }
}

.bfs-product-rebuy {
    margin: 0 -15px;

    @include mq($breakpoint-sm-md) {
        margin: 0;
    }

    .rebuy-widget,
    .rebuy-recommended-products {
        padding: 0;
        color: $black;
        text-align: left;
    }

    .rebuy-widget.rebuy-widget,
    .rebuy-widget .checkout-promotion-wrapper,
    .rebuy-widget.rebuy-cart-subscription {
        background-color: transparent;
    }

    .rebuy-widget .primary-title {
        font-family: 'Qualy', sans-serif;
        //font-size: 30px;
        font-size: clamp(25px, calc(0.1923rem + 1.9231vw), 30px);
        font-style: normal;
        font-weight: normal;
        line-height: 120%;
        margin-bottom: 40px;
        text-transform: none;
        letter-spacing: normal;
        color: $black;
        width: 100%;
        max-width: 960px;
        margin-left: auto;
        margin-right: auto;
    }

    .rebuy-widget .rebuy-product-grid.large-carousel,
    .rebuy-recommended-products .rebuy-product-grid.large-carousel {
        padding: 0;
    }

    .rebuy-widget .rebuy-product-block,
    .rebuy-recommended-products .rebuy-product-block {
        padding: 0 15px;
        border: none;

        @include mq($breakpoint-sm-md) {
            padding: 0 5px;
        }
    }

    .rebuy-widget .rebuy-product-block .rebuy-product-media,
    .rebuy-recommended-products .rebuy-product-block .rebuy-product-media {
        position: relative;
        overflow: hidden;
        border-radius: $border-radius-media;
    }

    .rebuy-widget .rebuy-product-block .rebuy-product-media img,
    .rebuy-recommended-products .rebuy-product-block .rebuy-product-media img {
        width: 100%;
    }

    .rebuy-widget .rebuy-product-title,
    .rebuy-modal__product-title {
        //font-size: 17px;
        font-size: clamp(16px, calc(0.313vw + 0.875rem), 17px);
        font-style: normal;
        font-weight: 400;
        line-height: 120%;
        margin-top: 15px !important;
        color: $black;
        display: block;
    }

    .rebuy-widget .rebuy-product-block .rebuy-product-info .rebuy-product-price,
    .rebuy-recommended-products .rebuy-product-block .rebuy-product-info .rebuy-product-price {
        margin-top: 10px !important;
        //font-size: 17px;
        font-size: clamp(16px, calc(0.313vw + 0.875rem), 17px);
        font-style: normal;
        font-weight: normal;
        line-height: 120%;
        color: $black;
    }

    .rebuy-money,
    .rebuy-widget .rebuy-money {
        color: $black;
    }

    .rebuy-money.sale,
    .rebuy-widget .rebuy-money.sale,
    .rebuy-cart__flyout-subtotal-final-amount,
    .rebuy-reorder-cart-total-price-final-amount,
    [data-rebuy-component="cart-subtotal"] .rebuy-cart__flyout-subtotal-final-amount,
    [data-rebuy-component="cart-subtotal"] .rebuy-reorder-cart-total-price-final-amount {
        color: $red;
        font-weight: 400;
    }

    .rebuy-money.compare-at,
    .rebuy-widget .rebuy-money.compare-at {
        color: $black;
        opacity: 0.5;
    }

    .rebuy-widget .rebuy-product-block .rebuy-product-options,
    .rebuy-recommended-products .rebuy-product-block .rebuy-product-options {
        margin-top: 20px;
    }

    .rebuy-select,
    .rebuy-widget .rebuy-select,
    select.rebuy-select,
    .rebuy-widget select.rebuy-select {
        color: $black;
        background-color: $white;
        border-color: $medium-gray;
        border-radius: $border-radius-button;
        transition: $transition;
        //font-size: 16px;
        font-size: clamp(15px, calc(0.313vw + 0.813rem), 16px);
        font-style: normal;
        font-weight: normal;
        line-height: 140%;

        @include hover {
            outline: 0;
            box-shadow: none;
            background-color: transparent;
            color: $black;
            border-color: $teal;
        }

        &:focus {
            outline: 0;
            box-shadow: none;
            background-color: transparent;
            color: $black;
            border-color: $teal;
        }
    }

    .rebuy-widget .rebuy-product-block .rebuy-product-actions,
    .rebuy-recommended-products .rebuy-product-block .rebuy-product-actions {
        margin-top: 10px;
    }

    .rebuy-button,
    .rebuy-widget .rebuy-button,
    .rebuy-cart__flyout-empty-cart a,
    [data-rebuy-component=progress-bar] .rebuy-cart__progress-gift-variant-readd,
    .rebuy-cart__progress-free-product-variant-select-container {
        width: 100%;
        min-width: auto;
        height: auto;
        min-height: 35px;
        margin: 0;
        padding: 0 15px;
        overflow: hidden;
        outline: 0;
        outline: none;
        border: none;
        border-radius: $border-radius-button;
        box-shadow: none;
        box-sizing: border-box;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: $teal;
        color: $white;
        text-decoration: none;
        text-align: center;
        cursor: pointer;
        vertical-align: top;
        -webkit-appearance: none;
        -moz-appearance: none;
        position: relative;
        transition: $transition;
        -webkit-font-smoothing: inherit;
        -moz-osx-font-smoothing: inherit;
        -webkit-tap-highlight-color: transparent;
        font-family: 'Qualy', sans-serif;
        font-size: 12px;
        font-style: normal;
        line-height: 100%;
        letter-spacing: 0.6px;
        text-transform: uppercase;
        font-weight: 400;

        @include hover {
            &:not(:disabled) {
                background-color: darken($teal, 10%);
                color: $white;
            }
        }

        &:disabled {
            cursor: not-allowed;
            pointer-events: none;
            background-color: darken($teal, 15%);
            color: $white;
        }
    }

    .rebuy-widget .splide__pagination {
        display: none !important;
    }

    .rebuy-widget .rebuy-carousel__arrows .rebuy-carousel__arrow,
    .rebuy-widget .flickity-button {
        width: 40px;
        height: 40px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        outline: 0;
        outline: none;
        border: 1px solid rgba($black, 0.1);
        border-radius: 100%;
        box-shadow: none;
        box-sizing: border-box;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: $gray;
        color: $black;
        font-family: inherit;
        font-size: inherit;
        font-style: inherit;
        font-weight: inherit;
        line-height: normal;
        text-decoration: none;
        text-transform: none;
        text-align: center;
        cursor: pointer;
        vertical-align: top;
        -webkit-appearance: none;
        -moz-appearance: none;
        transform: none;
        transition: $transition;
        opacity: 1;
        visibility: visible;
        -webkit-font-smoothing: inherit;
        -moz-osx-font-smoothing: inherit;
        -webkit-tap-highlight-color: transparent;

        @include hover {
            &:not(:disabled) {
                background: $black;
                border-color: $black;
                color: $white;
            }
        }

        &:disabled {
            background-color: $gray;
            border-color: $gray;
            cursor: not-allowed;
            opacity: 0.5;
        }

        svg {
            fill: $black;
            width: 7px;
            height: 7px;
        }
    }

    .rebuy-widget .rebuy-carousel__arrows .rebuy-carousel__arrow--prev {
        left: 15px;
        transform: translateY(-50%) translateX(-125%);

        @include mq($breakpoint-lg-xl) {
            transform: translateY(-50%) translateX(-115%);
        }

        @include mq($breakpoint-lg) {
            transform: translateY(-50%);
        }

        @include mq($breakpoint-sm-md) {
            left: 0;
        }
    }

    .rebuy-widget .rebuy-carousel__arrows .rebuy-carousel__arrow--next {
        right: 15px;
        transform: translateY(-50%) translateX(125%);

        @include mq($breakpoint-lg-xl) {
            transform: translateY(-50%) translateX(115%);
        }

        @include mq($breakpoint-lg) {
            transform: translateY(-50%);
        }

        @include mq($breakpoint-sm-md) {
            right: 0;
        }
    }
}

/**
 * MODULES
 */
.bfs-media-with-content {
    display: grid;
    grid-template-areas: "content media";
    grid-template-columns: 1fr 1fr;
    gap: calc((2 / 22) * 100%);

    @include mq($breakpoint-lg) {
        grid-template-areas: "content" "media";
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .bfs-section--rtl & {
        grid-template-areas: "media content";

        @include mq($breakpoint-lg) {
            grid-template-areas: "content" "media";
        }
    }

    .bfs-media-with-content__content {
        grid-area: content;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 20px;
    }

    .bfs-media-with-content__title {
        max-width: 480px;
    }

    .bfs-media-with-content__media {
        grid-area: media;
        align-content: center;
    }
}

.bfs-media-with-content-columns {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    column-gap: calc((1 / 22) * 100%);
    row-gap: 40px;


    .bfs-media-with-content-columns__media {
        width: calc((6 / 22) * 100%);

        @include mq($breakpoint-lg) {
            width: calc(50% - ((0.5 / 22) * 100%));
            order: 2;
        }

        @include mq($breakpoint-sm-md) {
            width: 100%;
        }
    }

    .bfs-media-with-content-columns__content {
        width: calc((8 / 22) * 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 20px;
        text-align: center;

        @include mq($breakpoint-lg) {
            width: 100%;
            order: 1;
        }
    }

    .bfs-media-with-content-columns__eyebrow,
    .bfs-media-with-content-columns__title,
    .bfs-media-with-content-columns__subtitle {
        max-width: 480px;
    }

    .bfs-media-with-content-columns__actions {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        gap: 20px;
    }
}

.bfs-media-with-content-slider {
    display: grid;
    grid-template-areas: "content media";
    grid-template-columns: 1fr 1fr;
    gap: calc((2 / 22) * 100%);

    @include mq($breakpoint-lg) {
        grid-template-areas: "content" "media";
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .bfs-section--rtl & {
        grid-template-areas: "media content";

        @include mq($breakpoint-lg) {
            grid-template-areas: "content" "media";
        }
    }

    .bfs-media-with-content-slider__content {
        grid-area: content;
        display: flex;
        flex-direction: column;
        min-width: 0;
    }

    .bfs-media-with-content-slider__eyebrow {
        max-width: 480px;
        margin-bottom: 20px;
    }

    .bfs-media-with-content-slider__title {
        max-width: 480px;
    }

    .bfs-media-with-content-slider__counter {
        margin-top: 40px;

        @include mq($breakpoint-sm-md) {
            margin-top: 20px;
        }
    }

    .bfs-media-with-content-slider__subtitle {
        margin-top: 10px;
    }

    .bfs-media-with-content-slider__description {
        margin-top: 15px;
    }

    .bfs-media-with-content-slider__cta {
        margin-top: 20px;
    }

    .bfs-media-with-content-slider__media {
        grid-area: media;
        align-content: center;
        min-width: 0;
    }

    .bfs-slider__navigation {
        margin-top: auto;
        padding-top: 40px;
        justify-content: flex-start;

        @include mq($breakpoint-sm-md) {
            padding-top: 20px;
        }
    }
}

.bfs-full-width-media-with-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 40px;

    .bfs-full-width-media-with-content__content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 20px;
        text-align: center;
    }

    .bfs-full-width-media-with-content__eyebrow {
        max-width: 840px;
    }

    .bfs-full-width-media-with-content__title {
        max-width: 960px;
    }

    .bfs-full-width-media-with-content__subtitle {
        max-width: 840px;
    }
}

.bfs-content-with-background {
    padding: 80px calc((2 / 18) * 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
    text-align: center;
    border-radius: $border-radius-card;
    position: relative;
    z-index: 2;

    @include mq($breakpoint-lg) {
        padding: 60px calc((1 / 22) * 100%);
    }

    @include mq($breakpoint-md) {
        padding-top: 40px;
        padding-bottom: 40px;
    }

    .bfs-content-with-background__eyebrow,
    .bfs-content-with-background__title,
    .bfs-content-with-background__subtitle {
        max-width: 840px;
    }

    .bfs-content-with-background__actions {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        gap: 20px;
    }
}

.bfs-media-with-cards {
    display: flex;
    flex-direction: row;
    gap: calc((1 / 22) * 100%);

    &.bfs-media-with-cards--alignment-top {
        align-items: flex-start;
    }

    &.bfs-media-with-cards--alignment-center {
        align-items: center;
    }

    &.bfs-media-with-cards--alignment-bottom {
        align-items: flex-end;
    }

    @include mq($breakpoint-lg) {
        flex-direction: column-reverse;
        gap: 40px;
    }

    .bfs-section--rtl & {
        flex-direction: row-reverse;

        @include mq($breakpoint-lg) {
            flex-direction: column-reverse;
        }
    }

    .bfs-media-with-cards__content {
        width: calc((13 / 22) * 100%);
        position: relative;

        @include mq($breakpoint-lg) {
            width: 100%;
        }
    }

    .bfs-media-with-cards__media {
        width: calc((8 / 22) * 100%);

        @include mq($breakpoint-lg) {
            width: 100%;
        }
    }

    .bfs-media-with-cards__media-content {
        position: absolute;
        left: 0;
        bottom: 0;
        z-index: 2;
        color: $white;
        width: 100%;
        padding: 30px;
        display: grid;
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .bfs-media-with-cards__cta-url {
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 3;
    }

    .swiper {
        @include mq($breakpoint-lg) {
            overflow: visible;
        }
    }

    .bfs-slider__navigation {
        @include mq($breakpoint-lg) {
            display: none;
        }
    }

    .bfs-slider__button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;

        &:first-child {
            left: 0;
            transform: translateY(-50%) translateX(-125%);

            @include mq($breakpoint-lg-xl) {
                transform: translateY(-50%) translateX(-115%);
            }
        }

        &:last-child {
            right: 0;
            transform: translateY(-50%) translateX(125%);

            @include mq($breakpoint-lg-xl) {
                transform: translateY(-50%) translateX(115%);
            }
        }
    }
}

.bfs-featured-links {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
    text-align: center;
    position: relative;
    z-index: 2;

    .bfs-featured-links__subtitle {
        max-width: 840px;
    }

    .bfs-featured-links__links {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
        gap: 10px;
        margin: 0;
        padding: 0;
        list-style: none;

        &.bfs-featured-links__links--big-gap {
            gap: 20px;
        }

        li {
            flex-shrink: 0;
            flex-grow: 0;
            margin: 0;
            padding: 0;
        }
    }
}

.bfs-featured-collections {
    display: grid;
    grid-template-columns: 1fr;
    gap: 40px;

    .bfs-featured-collections__header {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 20px;
        text-align: center;
    }

    .bfs-featured-collections__eyebrow {
        max-width: 840px;
    }

    .bfs-featured-collections__title {
        max-width: 600px;
    }

    .bfs-featured-collections__subtitle {
        max-width: 840px;
    }

    .bfs-featured-collections__list {
        margin: 0;
        padding: 0;
        list-style: none;
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        row-gap: 50px;
        column-gap: 30px;

        @include mq($breakpoint-lg) {
            grid-template-columns: repeat(3, 1fr);
            row-gap: 30px;
            column-gap: 20px;
        }

        @include mq($breakpoint-md) {
            grid-template-columns: repeat(2, 1fr);
            column-gap: 15px;
        }

        @include mq($breakpoint-sm) {
            grid-template-columns: repeat(1, 1fr);
        }

        li {
            margin: 0;
            padding: 0;
        }
    }
}

.bfs-features {
    display: grid;
    grid-template-columns: 1fr;
    gap: 60px;

    @include mq($breakpoint-lg) {
        gap: 40px;
    }

    .bfs-features__header {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 20px;
        text-align: center;
    }

    .bfs-features__eyebrow {
        max-width: 840px;
    }

    .bfs-features__title {
        max-width: 600px;
    }

    .bfs-features__subtitle {
        max-width: 840px;
    }

    .bfs-features__list {
        margin: 0;
        padding: 0;
        list-style: none;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 30px;

        @include mq($breakpoint-md-lg) {
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        @include mq($breakpoint-sm-md) {
            grid-template-columns: repeat(1, 1fr);
        }
    }
}

.bfs-marquee {
    display: flex;
    overflow: hidden;
    user-select: none;
    gap: 20px;

    .bfs-marquee__content {
        flex-shrink: 0;
        display: flex;
        justify-content: space-around;
        min-width: 100%;
        gap: 20px;
        animation: bfs-scroll-marquee 20s linear infinite;
        margin: 0;
        padding: 0;
    }

    .bfs-marquee__item {
        margin: 0;
        padding: 0;
        display: inline-flex;
        align-items: center;
        gap: 20px;
    }

    .bfs-marquee__item-icon {
        width: 60px;

        @include mq($breakpoint-md-lg) {
            width: 40px;
        }
    }
}

.bfs-cards-slider {
    display: grid;
    grid-template-columns: 1fr;
    gap: 40px;

    .bfs-cards-slider__header {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 20px;
        min-width: 0;

        &.bfs-cards-slider__header--left {
            align-items: flex-start;
            text-align: left;
        }

        &.bfs-cards-slider__header--center {
            align-items: center;
            text-align: center;
        }

        &.bfs-cards-slider__header--right {
            align-items: flex-end;
            text-align: right;
        }
    }

    .bfs-cards-slider__eyebrow {
        max-width: 840px;
    }

    .bfs-cards-slider__title {
        max-width: 600px;

        &.bfs-cards-slider__title--large {
            max-width: 960px;
        }
    }

    .bfs-cards-slider__subtitle {
        max-width: 840px;
    }

    .bfs-cards-slider__slider {
        min-width: 0;
    }

    .bfs-cards-slider__slider-description {
        max-width: 600px;
        margin-top: 20px;

        &:not(:only-child) {
            margin-bottom: 20px;
        }
    }

    .bfs-cards-slider__slider-inner {
        position: relative;
    }

    .swiper {
        @include mq($breakpoint-lg) {
            overflow: visible;
        }
    }

    .bfs-slider__navigation {
        @include mq($breakpoint-lg) {
            display: none;
        }
    }

    .bfs-slider__button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;

        &:first-child {
            left: 0;
            transform: translateY(-50%) translateX(-125%);

            @include mq($breakpoint-lg-xl) {
                transform: translateY(-50%) translateX(-115%);
            }
        }

        &:last-child {
            right: 0;
            transform: translateY(-50%) translateX(125%);

            @include mq($breakpoint-lg-xl) {
                transform: translateY(-50%) translateX(115%);
            }
        }
    }
}

.bfs-tabs-slider {
    display: grid;
    grid-template-columns: 1fr;
    gap: 40px;

    .bfs-tabs-slider__header {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 20px;
        min-width: 0;

        &.bfs-tabs-slider__header--left {
            align-items: flex-start;
            text-align: left;

            .bfs-tabs-slider__navigation {
                justify-content: flex-start;
            }
        }

        &.bfs-tabs-slider__header--center {
            align-items: center;
            text-align: center;

            .bfs-tabs-slider__navigation {
                justify-content: center;

                @include mq($breakpoint-md-lg) {
                    justify-content: flex-start;
                }
            }
        }

        &.bfs-tabs-slider__header--right {
            align-items: flex-end;
            text-align: right;

            .bfs-tabs-slider__navigation {
                justify-content: flex-end;

                @include mq($breakpoint-md-lg) {
                    justify-content: flex-start;
                }
            }
        }
    }

    .bfs-tabs-slider__eyebrow {
        max-width: 840px;
    }

    .bfs-tabs-slider__title {
        max-width: 600px;

        &.bfs-tabs-slider__title--large {
            max-width: 960px;
        }
    }

    .bfs-tabs-slider__subtitle {
        max-width: 840px;
    }

    .bfs-tabs-slider__navigation-wrapper {
        overflow: hidden;

        @include mq($breakpoint-md-lg) {
            margin-left: calc((1 / 22) * -100%);
            margin-right: calc((1 / 22) * -100%);
            display: grid;
        }
    }

    .bfs-tabs-slider__navigation {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
        margin: 0;
        padding: 0;
        list-style: none;

        @include mq($breakpoint-md-lg) {
            flex-wrap: nowrap;
            overflow: scroll;
            -webkit-overflow-scrolling: touch;
            -ms-overflow-style: none;
            scrollbar-width: none;
            padding-left: calc((1 / 24) * 100%);
            padding-right: calc((1 / 24) * 100%);
            width: 100vw;

            &::-webkit-scrollbar {
                display: none;
            }
        }

        li {
            flex-shrink: 0;
            flex-grow: 0;
            margin: 0;
            padding: 0;
        }

        .bfs-button.is-active {
            background-color: $black;
            color: $white;
            border: 1px solid $black;
        }
    }

    .bfs-tabs-slider__slider {
        min-width: 0;
        display: none;
        position: relative;

        &.is-active {
            display: block;
        }
    }

    .bfs-tabs-slider__cta {
        margin-top: 20px;
        display: flex;
        align-content: center;
        justify-content: flex-start;

        &.bfs-tabs-slider__cta--left {
            justify-content: flex-start;
        }

        &.bfs-tabs-slider__cta--center {
            justify-content: center;
        }

        &.bfs-tabs-slider__cta--right {
            justify-content: flex-end;
        }
    }

    .swiper {
        @include mq($breakpoint-lg) {
            overflow: visible;
        }
    }

    .bfs-slider__navigation {
        @include mq($breakpoint-lg) {
            display: none;
        }
    }

    .bfs-slider__button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;

        &:first-child {
            left: 0;
            transform: translateY(-50%) translateX(-125%);

            @include mq($breakpoint-lg-xl) {
                transform: translateY(-50%) translateX(-115%);
            }
        }

        &:last-child {
            right: 0;
            transform: translateY(-50%) translateX(125%);

            @include mq($breakpoint-lg-xl) {
                transform: translateY(-50%) translateX(115%);
            }
        }
    }
}

/**
 * OUT OF STOCK VARIANT STYLING
 */
.bfs-form__radio-wrapper {
    input[type="radio"] + label {
        cursor: pointer;
        transition: $transition;
    }

    // Style for out-of-stock variants
    &.variant-unavailable {
        label {
            opacity: 0.4;
            color: #999;
            background-color: #f8f8f8;
            border-color: #ddd;
            cursor: pointer;
            position: relative;
            text-decoration: line-through;

            @include hover() {
                opacity: 0.6;
                background-color: #f0f0f0;
            }
        }

        input[type="radio"]:checked + label {
            opacity: 0.6;
            background-color: #e8e8e8;
            border-color: #ccc;
            color: #666;
            text-decoration: line-through;
        }
    }
}

// BIS Button styling
.bfs-product__oos-cta {
    margin-top: 0.75rem;

    .bis-button.BIS_trigger {
        width: 100%;
        background-color: #F2B840 !important;
        border-color: #F2B840 !important;
        color: $black !important;

        @include hover() {
            background-color: darken(#F2B840, 10%) !important;
            border-color: darken(#F2B840, 10%) !important;
            color: $white !important;
        }
    }
}

// Sold out button styling
.bfs-button[disabled] {
    &.bfs-button--primary {
        background-color: #ccc;
        border-color: #ccc;
        color: #666;
        cursor: not-allowed;

        &:hover {
            background-color: #ccc;
            border-color: #ccc;
            color: #666;
        }
    }
}

.bis-reset {
    display: none;
}
