{%- liquid
    assign current_variant = product.selected_or_first_available_variant
    assign price = current_variant.price
    assign money_price = price | money_without_trailing_zeros
    assign compare_at_price = current_variant.compare_at_price
    assign money_compare_at_price = compare_at_price | money_without_trailing_zeros
    assign product_share_url = shop.url | append: product.url
    assign has_size_chart = false
    assign has_artwork = false
    assign is_collection_gallery = false
    assign has_no_returns = false
    assign has_custom_badge = false
    assign is_on_sale = false
    assign is_preorder = false
    assign has_preorder_shipping_time = false
    assign is_personalised = false
    assign is_coming_soon = false
    for tag in product.tags
        if tag contains ':size-chart'
            assign size_chart_page = tag | split: ':' | last
            assign size_chart_page = pages[size_chart_page]
            assign has_size_chart = true
        endif
        if tag contains ':story'
            assign artwork_identifier = tag | split: ':' | last
            assign has_artwork = true
        endif
        if tag == 'COLLECTION_YarnGallery'
            assign is_collection_gallery = true
        endif
        if tag == 'NoReturn'
            assign has_no_returns = true
        endif
        if tag contains '__label'
            assign custom_badge_label = tag | split: '__label:' | last
            assign has_custom_badge = true
        endif
        if tag contains 'preorder'
            assign is_preorder = true
        endif
        if tag contains 'comingsoon'
            assign is_coming_soon = true
        endif
        if tag contains 'SHIP_TEXT'
            assign preorder_shipping_time = tag | split: 'SHIP_TEXT_' | last
            assign has_preorder_shipping_time = true
        endif
        if tag contains 'PRODUCT_PersonalisedName'
            assign is_personalised = true
        endif
    endfor

    assign artist_profile = null
    assign artistcollection = ""
    assign artist_tag_name = ""
    for collection in product.collections
        assign artistcollection = artistcollection | append: collection.handle | append: ' '
    endfor
    for tag in product.tags
        if tag contains 'ARTIST_'
            assign artist_tag_name = tag | remove_first: 'ARTIST_'
            break
        endif
    endfor
    if has_artwork
        if artist_tag_name != ""
            assign artist_handle = artist_tag_name | downcase | replace: ' ', '-' | replace: '(', '' | replace: ')', ''
            assign artist_profile = shop.metaobjects.artist_profile[artist_handle]
            if artist_profile == null
                assign artist_handle_alt = artist_tag_name | downcase | replace: ' ', '-' | append: '-bayley-mifsud'
                assign artist_profile = shop.metaobjects.artist_profile[artist_handle_alt]
            endif
            if artist_profile == null
                assign artist_handle_alt2 = artist_tag_name | downcase | replace: ' ', '-' | replace: '(', '' | replace: ')', '' | append: '-bayley-mifsud'
                assign artist_profile = shop.metaobjects.artist_profile[artist_handle_alt2]
            endif
            if artist_profile == null
                for artist in shop.metaobjects['artist_profile'].values limit: 50
                    assign artist_name_clean = artist.artist_name.value | downcase | replace: ' ', '-' | replace: '(', '-' | replace: ')', ''
                    assign tag_name_clean = artist_tag_name | downcase | replace: ' ', '-'
                    if artist_name_clean contains tag_name_clean
                        assign artist_profile = artist
                        break
                    endif
                endfor
            endif
        endif
        if artist_profile == null
            paginate shop.metaobjects['artist_profile'].values by 50
                for artist in shop.metaobjects['artist_profile'].values
                    assign artist_collection_handle = artist.artist_collection.value.handle | downcase
                    assign artistcollection_lower = artistcollection | downcase
                    if artistcollection_lower contains artist_collection_handle
                        assign artist_profile = artist
                        break
                    endif
                endfor
            endpaginate
        endif
    endif

    for variant in product.variants
        if variant.compare_at_price > variant.price
            assign is_on_sale = true
            break
        endif
    endfor
    capture top_badges
        if product.tags contains 'NEW'
            assign new_badge_label = 'product.labels.new' | t
            render 'bfs-badge', label: new_badge_label, style: 'teal'
        endif
        if is_on_sale
            assign sale_badge_label = 'product.labels.on_sale' | t
            render 'bfs-badge', label: sale_badge_label, style: 'red'
        endif
        unless product.available
            assign sold_out_badge_label = 'product.labels.sold_out' | t
            render 'bfs-badge', label: sold_out_badge_label, style: 'yellow'
        endunless
        if is_preorder
            assign preorder_badge_label = 'product.form.pre_order' | t
            render 'bfs-badge', label: preorder_badge_label, style: 'black'
        endif
        if is_coming_soon
            render 'bfs-badge', label: 'Coming Soon', style: 'black'
        endif
        if product.tags contains 'cotton' or product.tags contains "__badge:Cotton"
            render 'bfs-badge', label: 'cotton', style: 'brown'
        endif
        if product.tags contains 'recycled' or product.tags contains 'RECYCLED'
            render 'bfs-badge', label: '100% Recycled', style: 'light-green'
        endif
        if product.tags contains '__badge:2 Tees 3rd Free'
            render 'bfs-badge', label: '2 Tees 3rd Free', style: 'red'
        endif
        if has_custom_badge
            render 'bfs-badge', label: custom_badge_label, style: 'teal'
        endif
    endcapture
-%}

<!--BFS PRODUCT SECTION-->
<section class="bfs-section">
    <div class="bfs-container">
        <div class="bfs-product" data-product>
            {%- if product.media.size > 0 -%}
                <!--gallery desktop-->
                <aside class="bfs-product__gallery-desktop">
                    <!--initial media-->
                    <div class="bfs-product__gallery-desktop-initial">
                        {%- for media in product.media limit: 6 -%}
                            {%- liquid
                                assign media_position = media_position | default: 0 | plus: 1
                                assign lazy = false
                                assign priority = true
                                assign decoding = 'auto'
                                assign preload = 'auto'
                                if media_position > 2
                                    assign lazy = true
                                    assign priority = false
                                    assign decoding = 'async'
                                    assign preload = 'none'
                                endif
                            -%}
                            <!--single-->
                            <div class="bfs-product__gallery-desktop-single">
                                <!--media-->
                                <div class="bfs-product__gallery-desktop-media">
                                    {%- if media.media_type == 'image' -%}
                                        {%- liquid
                                            assign desktop_img_url = media | image_url: width: 450
                                            assign desktop_retina_img_url = media | image_url: width: 500
                                            assign tablet_img_url = media | image_url: width: 1
                                            assign tablet_retina_img_url = media | image_url: width: 1
                                            assign mobile_img_url = media | image_url: width: 1
                                            assign mobile_retina_img_url = media | image_url: width: 1
                                        -%}
                                        {%- render 'bfs-image',
                                            desktop_img_url: desktop_img_url,
                                            desktop_retina_img_url: desktop_retina_img_url,
                                            tablet_img_url: tablet_img_url,
                                            tablet_retina_img_url: tablet_retina_img_url,
                                            mobile_img_url: mobile_img_url,
                                            mobile_retina_img_url: mobile_retina_img_url,
                                            alt: media.alt,
                                            aspect_ratio: '2-3',
                                            object_fit: 'cover',
                                            object_position: 'center',
                                            is_background: false,
                                            lazy: lazy,
                                            priority: priority,
                                            decoding: decoding,
                                            modifier_class: 'bfs-media-wrapper'
                                        -%}
                                    {%- elsif media.media_type == 'video' -%}
                                        {%- liquid
                                            assign video_url = media.sources[1].url
                                            assign video_poster = media.preview_image | image_url: width: 500
                                            assign video_suffix = 'desktop-initial-' | append: forloop.index
                                        -%}
                                        {%- render 'bfs-video',
                                            video_url: video_url,
                                            video_poster: video_poster,
                                            alt: media.alt,
                                            autoplay: section.settings.video_autoplay,
                                            preload: preload,
                                            aspect_ratio: '2-3',
                                            object_fit: 'cover',
                                            object_position: 'center',
                                            is_background: false,
                                            lazy: lazy,
                                            priority: priority,
                                            modifier_class: 'bfs-media-wrapper',
                                            suffix: video_suffix
                                        -%}
                                    {%- elsif media.media_type == 'external_video' -%}
                                        {%- assign iframe_html = media | external_video_tag -%}
                                        {%- render 'bfs-iframe',
                                            iframe_html: iframe_html,
                                            aspect_ratio: '2-3',
                                            lazy: lazy,
                                            modifier_class: 'bfs-media-wrapper'
                                        -%}
                                    {%- endif -%}
                                    {%- if media_position == 1 -%}
                                        <!--infos-->
                                        <div class="bfs-product__media-infos">
                                            {%- if top_badges != blank -%}
                                                <!--top badges-->
                                                <div class="bfs-product__media-top-badges">
                                                    {{- top_badges -}}
                                                </div>
                                                <!--end top badges-->
                                            {%- endif -%}
                                            <!--wishlist-->
                                            <div class="bfs-product__media-wishlist">
                                                <div class="swym-wishlist-button-bar"></div>
                                            </div>
                                            <!--end wishlist-->
                                        </div>
                                        <!--end infos-->
                                        {%- if
                                            product.metafields.custom.custom_label or
                                            collection.metafields.custom_data.custom_label != blank
                                        -%}
                                            <!--bottom badges-->
                                            <div class="bfs-product__media-bottom-badges">
                                                {%- if product.metafields.custom.custom_label != blank -%}
                                                    <!--BFS BADGE-->
                                                    <span class="bfs-badge bfs-badge--full bfs-fs-b3"
                                                          style="background-color: {{- product.metafields.custom.custom_label_background -}}; color: {{- product.metafields.custom.custom_label_font_color -}};">
                                                        {{- product.metafields.custom.custom_label -}}
                                                    </span>
                                                    <!--end BFS BADGE-->
                                                {%- elsif collection.metafields.custom_data.custom_label != blank -%}
                                                    <!--BFS BADGE-->
                                                    <span class="bfs-badge bfs-badge--full bfs-fs-b3"
                                                          style="background-color: {{- collection.metafields.custom_data.custom_label_background -}}; color: {{- collection.metafields.custom_data.custom_label_font_color -}};">
                                                        {{- collection.metafields.custom_data.custom_label -}}
                                                    </span>
                                                    <!--end BFS BADGE-->
                                                {%- endif -%}
                                            </div>
                                            <!--end bottom badges-->
                                        {%- endif -%}
                                    {%- endif -%}
                                </div>
                                <!--end media-->
                            </div>
                            <!--end single-->
                        {%- endfor -%}
                    </div>
                    <!--end initial media-->
                    {%- if product.media.size > 6 -%}
                        <!--hidden media-->
                        <div class="bfs-product__gallery-desktop-hidden" id="bfs-product-desktop-gallery-hidden">
                            <!--hidden media inner-->
                            <div class="bfs-product__gallery-desktop-hidden-inner">
                                {%- for media in product.media offset: 6 -%}
                                    {%- liquid
                                        assign lazy = true
                                        assign priority = false
                                        assign decoding = 'async'
                                        assign preload = 'none'
                                    -%}
                                    <!--single-->
                                    <div class="bfs-product__gallery-desktop-single">
                                        <!--media-->
                                        <div class="bfs-product__gallery-desktop-media">
                                            {%- if media.media_type == 'image' -%}
                                                {%- liquid
                                                    assign desktop_img_url = media | image_url: width: 450
                                                    assign desktop_retina_img_url = media | image_url: width: 500
                                                    assign tablet_img_url = media | image_url: width: 1
                                                    assign tablet_retina_img_url = media | image_url: width: 1
                                                    assign mobile_img_url = media | image_url: width: 1
                                                    assign mobile_retina_img_url = media | image_url: width: 1
                                                -%}
                                                {%- render 'bfs-image',
                                                    desktop_img_url: desktop_img_url,
                                                    desktop_retina_img_url: desktop_retina_img_url,
                                                    tablet_img_url: tablet_img_url,
                                                    tablet_retina_img_url: tablet_retina_img_url,
                                                    mobile_img_url: mobile_img_url,
                                                    mobile_retina_img_url: mobile_retina_img_url,
                                                    alt: media.alt,
                                                    aspect_ratio: '2-3',
                                                    object_fit: 'cover',
                                                    object_position: 'center',
                                                    is_background: false,
                                                    lazy: lazy,
                                                    priority: priority,
                                                    decoding: decoding,
                                                    modifier_class: 'bfs-media-wrapper'
                                                -%}
                                            {%- elsif media.media_type == 'video' -%}
                                                {%- liquid
                                                    assign video_url = media.sources[1].url
                                                    assign video_poster = media.preview_image | image_url: width: 500
                                                    assign video_suffix = 'desktop-initial-' | append: forloop.index
                                                -%}
                                                {%- render 'bfs-video',
                                                    video_url: video_url,
                                                    video_poster: video_poster,
                                                    alt: media.alt,
                                                    autoplay: section.settings.video_autoplay,
                                                    preload: preload,
                                                    aspect_ratio: '2-3',
                                                    object_fit: 'cover',
                                                    object_position: 'center',
                                                    is_background: false,
                                                    lazy: lazy,
                                                    priority: priority,
                                                    modifier_class: 'bfs-media-wrapper',
                                                    suffix: video_suffix
                                                -%}
                                            {%- elsif media.media_type == 'external_video' -%}
                                                {%- assign iframe_html = media | external_video_tag -%}
                                                {%- render 'bfs-iframe',
                                                    iframe_html: iframe_html,
                                                    aspect_ratio: '2-3',
                                                    lazy: lazy,
                                                    modifier_class: 'bfs-media-wrapper'
                                                -%}
                                            {%- endif -%}
                                        </div>
                                        <!--end media-->
                                    </div>
                                    <!--end single-->
                                {%- endfor -%}
                            </div>
                            <!--end hidden media inner-->
                        </div>
                        <!--end hidden media-->
                        <!--gallery trigger-->
                        <div class="bfs-product__gallery-desktop-trigger">
                            <button
                                class="bfs-product__gallery-desktop-trigger-button bfs-button bfs-button--primary bfs-button--bordered bfs-button--medium bfs-fs-b2"
                                id="bfs-product-desktop-gallery-trigger">
                                <span>{{- 'product.slideshow.view_more' | t -}}</span>
                                <span>{{- 'product.slideshow.view_less' | t -}}</span>
                            </button>
                        </div>
                        <!--end gallery trigger-->
                    {%- endif -%}
                </aside>
                <!--end gallery desktop-->
                <!--gallery mobile-->
                <aside class="bfs-product__gallery-mobile">
                    <!--slider-->
                    <div class="bfs-product__gallery-mobile-slider swiper bfs-slider" id="bfs-product-mobile-gallery">
                        <div class="swiper-wrapper">
                            {%- for media in product.media -%}
                                {%- liquid
                                    assign media_position_mobile = media_position_mobile | default: 0 | plus: 1
                                    assign lazy = false
                                    assign priority = true
                                    assign decoding = 'auto'
                                    assign preload = 'auto'
                                    if media_position_mobile > 1
                                        assign lazy = true
                                        assign priority = false
                                        assign decoding = 'async'
                                        assign preload = 'none'
                                    endif
                                -%}
                                <!--single-->
                                <div class="bfs-product__gallery-mobile-single swiper-slide">
                                    <!--media-->
                                    <div class="bfs-product__gallery-mobile-media">
                                        {%- if media.media_type == 'image' -%}
                                            {%- liquid
                                                assign desktop_img_url = media | image_url: width: 1
                                                assign desktop_retina_img_url = media | image_url: width: 1
                                                assign tablet_img_url = media | image_url: width: 1000
                                                assign tablet_retina_img_url = media | image_url: width: 1100
                                                assign mobile_img_url = media | image_url: width: 500
                                                assign mobile_retina_img_url = media | image_url: width: 600
                                            -%}
                                            {%- render 'bfs-image',
                                                desktop_img_url: desktop_img_url,
                                                desktop_retina_img_url: desktop_retina_img_url,
                                                tablet_img_url: tablet_img_url,
                                                tablet_retina_img_url: tablet_retina_img_url,
                                                mobile_img_url: mobile_img_url,
                                                mobile_retina_img_url: mobile_retina_img_url,
                                                alt: media.alt,
                                                aspect_ratio: '2-3',
                                                object_fit: 'cover',
                                                object_position: 'center',
                                                is_background: false,
                                                lazy: lazy,
                                                priority: priority,
                                                decoding: decoding,
                                                modifier_class: 'bfs-media-wrapper'
                                            -%}
                                        {%- elsif media.media_type == 'video' -%}
                                            {%- liquid
                                                assign video_url = media.sources[1].url
                                                assign video_poster = media.preview_image | image_url: width: 500
                                                assign video_suffix = 'mobile-initial-' | append: forloop.index
                                            -%}
                                            {%- render 'bfs-video',
                                                video_url: video_url,
                                                video_poster: video_poster,
                                                alt: media.alt,
                                                autoplay: section.settings.video_autoplay,
                                                preload: preload,
                                                aspect_ratio: '2-3',
                                                object_fit: 'cover',
                                                object_position: 'center',
                                                is_background: false,
                                                lazy: lazy,
                                                priority: priority,
                                                modifier_class: 'bfs-media-wrapper',
                                                suffix: video_suffix
                                            -%}
                                        {%- elsif media.media_type == 'external_video' -%}
                                            {%- assign iframe_html = media | external_video_tag -%}
                                            {%- render 'bfs-iframe',
                                                iframe_html: iframe_html,
                                                aspect_ratio: '2-3',
                                                lazy: lazy,
                                                modifier_class: 'bfs-media-wrapper'
                                            -%}
                                        {%- endif -%}
                                        {%- if media_position_mobile == 1 -%}
                                            <!--infos-->
                                            <div class="bfs-product__media-infos">
                                                {%- if top_badges != blank -%}
                                                    <!--top badges-->
                                                    <div class="bfs-product__media-top-badges">
                                                        {{- top_badges -}}
                                                    </div>
                                                    <!--end top badges-->
                                                {%- endif -%}
                                                <!--wishlist-->
                                                <div class="bfs-product__media-wishlist">
                                                    <div class="swym-wishlist-button-bar"></div>
                                                </div>
                                                <!--end wishlist-->
                                            </div>
                                            <!--end infos-->
                                            {%- if
                                                product.metafields.custom.custom_label or
                                                collection.metafields.custom_data.custom_label != blank
                                            -%}
                                                <!--bottom badges-->
                                                <div class="bfs-product__media-bottom-badges">
                                                    {%- if product.metafields.custom.custom_label != blank -%}
                                                        <!--BFS BADGE-->
                                                        <span class="bfs-badge bfs-badge--full bfs-fs-b3"
                                                              style="background-color: {{- product.metafields.custom.custom_label_background -}}; color: {{- product.metafields.custom.custom_label_font_color -}};">
                                                            {{- product.metafields.custom.custom_label -}}
                                                        </span>
                                                        <!--end BFS BADGE-->
                                                    {%- elsif collection.metafields.custom_data.custom_label != blank -%}
                                                        <!--BFS BADGE-->
                                                        <span class="bfs-badge bfs-badge--full bfs-fs-b3"
                                                              style="background-color: {{- collection.metafields.custom_data.custom_label_background -}}; color: {{- collection.metafields.custom_data.custom_label_font_color -}};">
                                                            {{- collection.metafields.custom_data.custom_label -}}
                                                        </span>
                                                        <!--end BFS BADGE-->
                                                    {%- endif -%}
                                                </div>
                                                <!--end bottom badges-->
                                            {%- endif -%}
                                        {%- endif -%}
                                    </div>
                                    <!--end media-->
                                </div>
                                <!--end single-->
                            {%- endfor -%}
                        </div>
                    </div>
                    <!--end slider-->
                    <!--slider navigation-->
                    <div class="bfs-slider__navigation">
                        <button class="bfs-slider__button bfs-slider__button--prev bfs-slider-prev-{{- section.id -}}">
                            {%- render 'bfs-icon-arrow-left' -%}
                        </button>
                        <button class="bfs-slider__button bfs-slider__button--next bfs-slider-next-{{- section.id -}}">
                            {%- render 'bfs-icon-arrow-right' -%}
                        </button>
                    </div>
                    <!--end slider navigation-->
                </aside>
                <!--end gallery mobile-->
            {%- endif -%}
            {%- if section.blocks.size > 0 -%}
                <!--main-->
                <div class="bfs-product__main">
                    {%- for block in section.blocks -%}
                        {%- case block.type -%}
                            {%- when 'title' -%}
                                {%- if product.title != blank -%}
                                    <!--title-->
                                    <h1 class="bfs-product__title bfs-fs-a4">
                                        {{- product.title | escape -}}
                                    </h1>
                                    <!--end title-->
                                {%- endif -%}
                            {%- when 'reviews' -%}
                                <!--reviews-->
                                <div class="bfs-product__reviews">
                                    <span class="stamped-product-reviews-badge"
                                          data-id="{{- product.id -}}"
                                          data-product-sku="{{- product.handle -}}"
                                          data-product-type="{{- product.type -}}"
                                          data-product-title="{{- product.title -}}">
                                    </span>
                                </div>
                                <!--end reviews-->
                            {%- when 'price' -%}
                                <!--price-->
                                <p class="bfs-product__price {% if price < compare_at_price -%}is-on-sale{%- endif -%}"
                                   data-price>
                                    <span class="bfs-product__regular-price bfs-fs-a3" data-regular-price>
                                        {{- money_price -}}
                                    </span>
                                    <s class="bfs-product__compare-at-price bfs-fs-a3" data-compare-at-price>
                                        {%- if price < compare_at_price -%}
                                            {{- money_compare_at_price -}}
                                        {%- endif -%}
                                    </s>
                                    <span class="bfs-product__discount-percentage bfs-fs-b1" data-discount-percentage>
                                        {%- if price < compare_at_price -%}
                                            {{- compare_at_price | minus: price | times: 100.0 | divided_by: compare_at_price | round | append: "% OFF" -}}
                                        {%- endif -%}
                                    </span>
                                </p>
                                <!--end price-->
                            {%- when 'form' -%}
                                <!--form-->
                                <div class="bfs-product__form">
                                    {%- assign product_form_id = 'bfsProductForm-' | append: section.id -%}
                                    {%- form 'product',
                                        product,
                                        id: product_form_id,
                                        class: 'bfs-product-form bfs-form',
                                        data-form: '',
                                        data-type: 'add-to-cart-form',
                                        data-product-handle: product.handle,
                                        novalidate: 'novalidate'
                                    -%}
                                        {%- if product.has_only_default_variant -%}
                                            <select name="id" hidden>
                                                {%- for variant in product.variants -%}
                                                    <option value="{{- variant.id -}}"
                                                            {% if variant == current_variant -%}selected="selected"{%- endif -%}>
                                                        {{- variant.title -}}
                                                        {%- if variant.available == false %} - {{ 'product.form.sold_out' | t -}}{%- endif %}
                                                        - {{ variant.price | money | strip_html -}}
                                                    </option>
                                                {%- endfor -%}
                                            </select>
                                        {%- else -%}
                                            <!--fieldset-->
                                            <fieldset class="bfs-form__fieldset">
                                                <legend class="bfs-form__label bfs-form__label--flex">
                                                    <span class="bfs-fs-b3">
                                                        {{- product.options_with_values[0].name | capitalize -}}
                                                    </span>
                                                    {%- if has_size_chart -%}
                                                        <a id="bfs-product-size-chart-modal-trigger" class="bfs-fs-b1">
                                                            {{- 'product.trigger_labels.size_chart' | t -}}
                                                        </a>
                                                    {%- endif -%}
                                                </legend>
                                                <!--radio grid-->
                                                <div class="bfs-form__radio-grid">
                                                    {%- for variant in product.variants -%}
                                                        <!--radio-->
                                                        <div class="bfs-form__radio-wrapper{% unless variant.available %} variant-unavailable{% endunless %}">
                                                            <input class="bfs-form__radio"
                                                                   id="bfsProductForm-{{- section.id -}}-variant-{{- forloop.index0 -}}"
                                                                   type="radio"
                                                                   name="id"
                                                                   value="{{- variant.id -}}"
                                                                   data-value="{{- variant.title | handle -}}"
                                                                   {% if variant == current_variant -%}checked{%- endif -%}>
                                                            <label class="bfs-form__radio-label"
                                                                   for="bfsProductForm-{{- section.id -}}-variant-{{- forloop.index0 -}}">
                                                                {{- variant.title -}}
                                                            </label>
                                                        </div>
                                                        <!--end radio-->
                                                    {%- endfor -%}
                                                </div>
                                                <!--end radio grid-->
                                            </fieldset>
                                            <!--end fieldset-->
                                        {%- endif -%}
                                        <!--fieldset-->
                                        <fieldset class="bfs-form__fieldset" data-availability-badges
                                                  {% if current_variant.inventory_quantity > 5 -%}hidden{%- endif -%}>
                                            {%- liquid
                                                assign sold_out_badge_label = 'product.labels.sold_out' | t
                                                assign sold_out_badge_attributes = 'data-availability-badge-sold-out hidden'
                                                assign one_left_badge_label = 'product.labels.one_left' | t
                                                assign one_left_badge_attributes = 'data-availability-badge-one hidden'
                                                assign few_left_badge_label = 'product.labels.few_left' | t
                                                assign few_left_badge_attributes = 'data-availability-badge-few hidden'
                                                if current_variant.inventory_quantity == 0
                                                    assign sold_out_badge_attributes = 'data-availability-badge-sold-out'
                                                elsif current_variant.inventory_quantity == 1
                                                    assign one_left_badge_attributes = 'data-availability-badge-one'
                                                elsif current_variant.inventory_quantity <= 5
                                                    assign few_left_badge_attributes = 'data-availability-badge-few'
                                                endif
                                            -%}
                                            {%- render 'bfs-badge',
                                                label: sold_out_badge_label,
                                                style: 'red',
                                                data_attribute: sold_out_badge_attributes
                                            -%}
                                            {%- render 'bfs-badge',
                                                label: one_left_badge_label,
                                                style: 'red',
                                                data_attribute: one_left_badge_attributes
                                            -%}
                                            {%- render 'bfs-badge',
                                                label: few_left_badge_label,
                                                style: 'yellow',
                                                data_attribute: few_left_badge_attributes
                                            -%}
                                        </fieldset>
                                        <!--end fieldset-->
                                        {%- if
                                            is_personalised and
                                            block.settings.personalisation_font_input_label != blank and
                                            block.settings.personalisation_text_input_label != blank and
                                            block.settings.personalisation_validation != blank and
                                            block.settings.personalisation_note != blank
                                        -%}
                                            <!--fieldset-->
                                            <fieldset class="bfs-form__fieldset" data-personalisation
                                                      style="display: none;">
                                                <label class="bfs-form__label bfs-fs-b3"
                                                       for="bfsProductForm-{{- section.id -}}-personalisation-font">
                                                    {{- block.settings.personalisation_font_input_label -}}
                                                </label>
                                                <!--select wrapper-->
                                                <div class="bfs-form__select-wrapper">
                                                    <select class="bfs-form__input bfs-form__input--select"
                                                            name="properties[Font]"
                                                            id="bfsProductForm-{{- section.id -}}-personalisation-font"
                                                            data-font="Venti"
                                                            disabled>
                                                        <option value="Venti" selected="selected">Venti CF</option>
                                                        <option value="Diary">Diary Notes</option>
                                                        <option value="Breeder">Breeder</option>
                                                        <option value="Thunderstorm">Thunderstorm</option>
                                                    </select>
                                                    <span class="bfs-form__select-indicator">
                                                        {%- render 'bfs-icon-arrow-down' -%}
                                                    </span>
                                                </div>
                                                <!--end select wrapper-->
                                            </fieldset>
                                            <!--end fieldset-->
                                            <!--fieldset-->
                                            <fieldset class="bfs-form__fieldset" data-personalisation
                                                      style="display: none;">
                                                <label class="bfs-form__label bfs-fs-b3"
                                                       for="bfsProductForm-{{- section.id -}}-personalisation-text">
                                                    {{- block.settings.personalisation_text_input_label -}}
                                                </label>
                                                <input class="bfs-form__input"
                                                       type="text"
                                                       name="properties[Custom Text]"
                                                       id="bfsProductForm-{{- section.id -}}-personalisation-text"
                                                       maxlength="14"
                                                       pattern="[A-Za-z0-9' ]*"
                                                       title="{{- block.settings.personalisation_validation -}}"
                                                       aria-describedby="bfsProductFormPersonalisationTextError"
                                                       data-font="Venti"
                                                       disabled>
                                                <!--validation message-->
                                                <p class="bfs-form__validation-message bfs-fs-b4"
                                                   id="bfsProductFormPersonalisationTextError"
                                                   data-personalisation-validation
                                                   style="display: none;">
                                                    {{- block.settings.personalisation_validation -}}
                                                </p>
                                                <!--end validation message-->
                                            </fieldset>
                                            <!--end fieldset-->
                                            <!--fieldset-->
                                            <fieldset class="bfs-form__fieldset" data-personalisation
                                                      style="display: none;">
                                                {%- render 'bfs-notification',
                                                    label: block.settings.personalisation_note,
                                                    style: 'red'
                                                -%}
                                            </fieldset>
                                            <!--end fieldset-->
                                        {%- endif -%}
                                        {%- if
                                            is_preorder and
                                            has_preorder_shipping_time and
                                            block.settings.preorder_note != blank and
                                            block.settings.preorder_property_label != blank
                                        -%}
                                            <!--fieldset-->
                                            <fieldset class="bfs-form__fieldset">
                                                {%- liquid
                                                    assign preorder_notification_label = block.settings.preorder_note | replace: '[preorder_shipping_time]', preorder_shipping_time
                                                    assign preorder_property_label = block.settings.preorder_property_label | replace: '[preorder_shipping_time]', preorder_shipping_time
                                                -%}
                                                <input type="hidden"
                                                       id="preorderid"
                                                       name="properties[preorder]"
                                                       value="{{- preorder_property_label -}}">
                                                {%- render 'bfs-notification',
                                                    label: preorder_notification_label,
                                                    style: 'red'
                                                -%}
                                            </fieldset>
                                            <!--end fieldset-->
                                        {%- endif -%}
                                        {%- if product.metafields.prod.custom_text.value != blank -%}
                                            <!--fieldset-->
                                            <fieldset class="bfs-form__fieldset">
                                                {%- render 'bfs-notification',
                                                    label: product.metafields.prod.custom_text.value,
                                                    style: 'medium-gray'
                                                -%}
                                            </fieldset>
                                            <!--end fieldset-->
                                        {%- endif -%}
                                        <!--fieldset-->
                                        <fieldset class="bfs-form__fieldset">
                                            <input type="hidden"
                                                   name="quantity"
                                                   value="1"
                                                   min="1">
                                            <button type="submit"
                                                    name="add"
                                                    class="bfs-button bfs-button--primary bfs-button--full bfs-button--medium bfs-fs-b2"
                                                    data-action="add-to-cart"
                                                    onclick="obApi('track', 'Add To Cart'); pintrk('track', 'addtocart');"
                                                    {% if current_variant.available == false or is_coming_soon -%}disabled{%- endif -%}>
                                                {%- if is_coming_soon -%}
                                                    Coming Soon
                                                {%- elsif current_variant.available -%}
                                                    {%- if is_preorder -%}
                                                        {{- 'product.form.pre_order' | t -}}
                                                    {%- else -%}
                                                        {{- 'product.form.add_to_cart' | t -}}
                                                    {%- endif -%}
                                                {%- else -%}
                                                    {{- 'product.form.sold_out' | t -}}
                                                {%- endif -%}
                                            </button>
                                        </fieldset>
                                        <!--end fieldset-->
                                    {%- endform -%}
                                </div>
                                <!--end form-->
                                {%- if is_coming_soon or current_variant.available == false -%}
                                    <!--oos cta-->
                                    <div class="bfs-product__oos-cta">
                                        <button
                                            class="bfs-button bfs-button--primary bfs-button--full bfs-button--medium bfs-fs-b2 bis-button BIS_trigger"
                                            data-variant-id="{{ current_variant.id }}">
                                            {%- if is_coming_soon -%}
                                                Get notified when available
                                            {%- else -%}
                                                {{- 'product.trigger_labels.restock' | t -}}
                                            {%- endif -%}
                                        </button>
                                    </div>
                                    <!--end oos cta-->
                                {%- endif -%}
                            {%- when 'upsell_progress_bar' -%}
                                {%- if
                                    block.settings.step_1_label != blank and
                                    block.settings.step_1_threshold != nil and
                                    block.settings.step_1_status_message != blank and
                                    block.settings.success_message != blank
                                -%}
                                    {%- assign upsell_progress_bar_success_message = block.settings.success_message -%}
                                    <!--upsell bar-->
                                    <div class="bfs-product__upsell-bar">
                                        <!--BFS UPSELL PROGRESS BAR-->
                                        <div class="bfs-upsell-progress-bar">
                                            <!--thresholds-->
                                            <ul class="bfs-upsell-progress-bar__thresholds" role="list">
                                                {%- if
                                                    block.settings.step_1_label != blank and
                                                    block.settings.step_1_threshold != nil and
                                                    block.settings.step_1_status_message != blank
                                                -%}
                                                    <!--threshold-->
                                                    <li class="bfs-upsell-progress-bar__threshold" role="listitem"
                                                        data-threshold="{{- block.settings.step_1_threshold -}}"
                                                        data-status-message="{{- block.settings.step_1_status_message -}}">
                                                        <!--indicator-->
                                                        <span class="bfs-upsell-progress-bar__threshold-indicator">
                                                            {%- render 'bfs-icon-checkmark' -%}
                                                        </span>
                                                        <!--end indicator-->
                                                        <!--label-->
                                                        <p class="bfs-upsell-progress-bar__threshold-label bfs-fs-b4">
                                                            {{- block.settings.step_1_label -}}
                                                        </p>
                                                        <!--end label-->
                                                    </li>
                                                    <!--end threshold-->
                                                {%- endif -%}
                                                {%- if
                                                    block.settings.step_2_label != blank and
                                                    block.settings.step_2_threshold != nil and
                                                    block.settings.step_2_status_message != blank
                                                -%}
                                                    <!--threshold-->
                                                    <li class="bfs-upsell-progress-bar__threshold" role="listitem"
                                                        data-threshold="{{- block.settings.step_2_threshold -}}"
                                                        data-status-message="{{- block.settings.step_2_status_message -}}">
                                                        <!--indicator-->
                                                        <span class="bfs-upsell-progress-bar__threshold-indicator">
                                                            {%- render 'bfs-icon-checkmark' -%}
                                                        </span>
                                                        <!--end indicator-->
                                                        <!--label-->
                                                        <p class="bfs-upsell-progress-bar__threshold-label bfs-fs-b4">
                                                            {{- block.settings.step_2_label -}}
                                                        </p>
                                                        <!--end label-->
                                                    </li>
                                                    <!--end threshold-->
                                                {%- endif -%}
                                                {%- if
                                                    block.settings.step_3_label != blank and
                                                    block.settings.step_3_threshold != nil and
                                                    block.settings.step_3_status_message != blank
                                                -%}
                                                    <!--threshold-->
                                                    <li class="bfs-upsell-progress-bar__threshold" role="listitem"
                                                        data-threshold="{{- block.settings.step_3_threshold -}}"
                                                        data-status-message="{{- block.settings.step_3_status_message -}}">
                                                        <!--indicator-->
                                                        <span class="bfs-upsell-progress-bar__threshold-indicator">
                                                            {%- render 'bfs-icon-checkmark' -%}
                                                        </span>
                                                        <!--end indicator-->
                                                        <!--label-->
                                                        <p class="bfs-upsell-progress-bar__threshold-label bfs-fs-b4">
                                                            {{- block.settings.step_3_label -}}
                                                        </p>
                                                        <!--end label-->
                                                    </li>
                                                    <!--end threshold-->
                                                {%- endif -%}
                                            </ul>
                                            <!--end thresholds-->
                                            <!--progress bar-->
                                            <div class="bfs-upsell-progress-bar__progress-bar">
                                                <span></span>
                                            </div>
                                            <!--end progress bar-->
                                            <!--status-->
                                            <p class="bfs-upsell-progress-bar__status bfs-fs-b4"></p>
                                            <!--end status-->
                                        </div>
                                        <!--end BFS UPSELL PROGRESS BAR-->
                                    </div>
                                    <!--end upsell bar-->
                                {%- endif -%}
                            {%- when 'selling_points' -%}
                                {%- if
                                    block.settings.selling_point_1_icon != nil and
                                    block.settings.selling_point_1_title != blank
                                -%}
                                    <!--selling points-->
                                    <ul class="bfs-product__selling-points" role="list">
                                        {%- if
                                            block.settings.selling_point_1_icon != nil and
                                            block.settings.selling_point_1_title != blank
                                        -%}
                                            <!--selling point-->
                                            <li class="bfs-product__selling-point" role="listitem">
                                                <!--icon-->
                                                <div class="bfs-product__selling-point-icon">
                                                    {%- liquid
                                                        assign desktop_img_url = block.settings.selling_point_1_icon | image_url: width: 30
                                                        assign desktop_retina_img_url = block.settings.selling_point_1_icon | image_url: width: 40
                                                        assign tablet_img_url = block.settings.selling_point_1_icon | image_url: width: 30
                                                        assign tablet_retina_img_url = block.settings.selling_point_1_icon | image_url: width: 40
                                                        assign mobile_img_url = block.settings.selling_point_1_icon | image_url: width: 30
                                                        assign mobile_retina_img_url = block.settings.selling_point_1_icon | image_url: width: 40
                                                    -%}
                                                    {%- render 'bfs-image',
                                                        desktop_img_url: desktop_img_url,
                                                        desktop_retina_img_url: desktop_retina_img_url,
                                                        tablet_img_url: tablet_img_url,
                                                        tablet_retina_img_url: tablet_retina_img_url,
                                                        mobile_img_url: mobile_img_url,
                                                        mobile_retina_img_url: mobile_retina_img_url,
                                                        alt: block.settings.selling_point_1_icon.alt,
                                                        aspect_ratio: '1-1',
                                                        object_fit: 'contain',
                                                        object_position: 'center',
                                                        is_background: false,
                                                        lazy: true,
                                                        priority: false,
                                                        decoding: 'async'
                                                    -%}
                                                </div>
                                                <!--end icon-->
                                                <!--title-->
                                                <h4 class="bfs-product__selling-point-title bfs-fs-b4">
                                                    {{- block.settings.selling_point_1_title -}}
                                                </h4>
                                                <!--end title-->
                                            </li>
                                            <!--end selling point-->
                                        {%- endif -%}
                                        {%- if
                                            block.settings.selling_point_2_icon != nil and
                                            block.settings.selling_point_2_title != blank
                                        -%}
                                            <!--selling point-->
                                            <li class="bfs-product__selling-point" role="listitem">
                                                <!--icon-->
                                                <div class="bfs-product__selling-point-icon">
                                                    {%- liquid
                                                        assign desktop_img_url = block.settings.selling_point_2_icon | image_url: width: 30
                                                        assign desktop_retina_img_url = block.settings.selling_point_2_icon | image_url: width: 40
                                                        assign tablet_img_url = block.settings.selling_point_2_icon | image_url: width: 30
                                                        assign tablet_retina_img_url = block.settings.selling_point_2_icon | image_url: width: 40
                                                        assign mobile_img_url = block.settings.selling_point_2_icon | image_url: width: 30
                                                        assign mobile_retina_img_url = block.settings.selling_point_2_icon | image_url: width: 40
                                                    -%}
                                                    {%- render 'bfs-image',
                                                        desktop_img_url: desktop_img_url,
                                                        desktop_retina_img_url: desktop_retina_img_url,
                                                        tablet_img_url: tablet_img_url,
                                                        tablet_retina_img_url: tablet_retina_img_url,
                                                        mobile_img_url: mobile_img_url,
                                                        mobile_retina_img_url: mobile_retina_img_url,
                                                        alt: block.settings.selling_point_2_icon.alt,
                                                        aspect_ratio: '1-1',
                                                        object_fit: 'contain',
                                                        object_position: 'center',
                                                        is_background: false,
                                                        lazy: true,
                                                        priority: false,
                                                        decoding: 'async'
                                                    -%}
                                                </div>
                                                <!--end icon-->
                                                <!--title-->
                                                <h4 class="bfs-product__selling-point-title bfs-fs-b4">
                                                    {{- block.settings.selling_point_2_title -}}
                                                </h4>
                                                <!--end title-->
                                            </li>
                                            <!--end selling point-->
                                        {%- endif -%}
                                        {%- if
                                            block.settings.selling_point_3_icon != nil and
                                            block.settings.selling_point_3_title != blank
                                        -%}
                                            <!--selling point-->
                                            <li class="bfs-product__selling-point" role="listitem">
                                                <!--icon-->
                                                <div class="bfs-product__selling-point-icon">
                                                    {%- liquid
                                                        assign desktop_img_url = block.settings.selling_point_3_icon | image_url: width: 30
                                                        assign desktop_retina_img_url = block.settings.selling_point_3_icon | image_url: width: 40
                                                        assign tablet_img_url = block.settings.selling_point_3_icon | image_url: width: 30
                                                        assign tablet_retina_img_url = block.settings.selling_point_3_icon | image_url: width: 40
                                                        assign mobile_img_url = block.settings.selling_point_3_icon | image_url: width: 30
                                                        assign mobile_retina_img_url = block.settings.selling_point_3_icon | image_url: width: 40
                                                    -%}
                                                    {%- render 'bfs-image',
                                                        desktop_img_url: desktop_img_url,
                                                        desktop_retina_img_url: desktop_retina_img_url,
                                                        tablet_img_url: tablet_img_url,
                                                        tablet_retina_img_url: tablet_retina_img_url,
                                                        mobile_img_url: mobile_img_url,
                                                        mobile_retina_img_url: mobile_retina_img_url,
                                                        alt: block.settings.selling_point_3_icon.alt,
                                                        aspect_ratio: '1-1',
                                                        object_fit: 'contain',
                                                        object_position: 'center',
                                                        is_background: false,
                                                        lazy: true,
                                                        priority: false,
                                                        decoding: 'async'
                                                    -%}
                                                </div>
                                                <!--end icon-->
                                                <!--title-->
                                                <h4 class="bfs-product__selling-point-title bfs-fs-b4">
                                                    {{- block.settings.selling_point_3_title -}}
                                                </h4>
                                                <!--end title-->
                                            </li>
                                            <!--end selling point-->
                                        {%- endif -%}
                                    </ul>
                                    <!--end selling points-->
                                {%- endif -%}
                            {%- when 'corporate_cta' -%}
                                {%- if
                                    product.metafields.c_f.custom_button_link != blank and
                                    block.settings.label != blank and
                                    block.settings.min_label != blank
                                -%}
                                    <!--corporate cta-->
                                    <div class="bfs-product__corporate-cta">
                                        <a class="bfs-product__corporate-cta-button bfs-button bfs-button--primary bfs-button--bordered bfs-button--medium bfs-button--full bfs-fs-b2"
                                           href="{{- product.metafields.c_f.custom_button_link -}}"
                                           title="{{- block.settings.label -}}"
                                           aria-label="{{- block.settings.label -}}"
                                           {% if block.settings.new_tab -%}target="_blank"
                                           rel="noopener noreferrer"{%- endif -%}>
                                            <span>{{- block.settings.label -}}</span>
                                            <span style="text-transform: none">
                                                {{- block.settings.min_label -}}{%- render 'bfs-icon-arrow-right' -%}
                                            </span>
                                        </a>
                                    </div>
                                    <!--end corporate cta-->
                                {%- endif -%}
                            {%- when 'rewards' -%}
                                {%- if
                                    block.settings.title_customer != blank and
                                    block.settings.title_guest != blank and
                                    block.settings.cta_label_customer != blank and
                                    block.settings.cta_label_guest != blank and
                                    block.settings.cta_url != blank
                                -%}
                                    {%- if customer -%}
                                        <!--rewards-->
                                        <div class="bfs-product__rewards" style="background-color: {{ block.settings.background_color | default: '#f8f8f8' }};"
                                            <!--content-->
                                            <div class="bfs-product__rewards-content">
                                                <!--title-->
                                                <h5 class="bfs-product__rewards-title bfs-fs-a3" style="color: {{ block.settings.title_color | default: '#000000' }};">
                                                    {{- block.settings.title_customer -}}
                                                </h5>
                                                <!--end title-->
                                                <!--subtitle-->
                                                <div
                                                    class="bfs-product__rewards-subtitle bfs-fs-b3 yotpo-widget-instance"
                                                    style="color: {{ block.settings.subtitle_color | default: '#666666' }};"
                                                    data-yotpo-instance-id="852054"
                                                    data-yotpo-product-id="{{- product.id -}}"
                                                    data-yotpo-variant-id="{{- current_variant.id -}}"
                                                    data-yotpo-product-variants="{{- product.variants | json | url_encode -}}"
                                                    data-yotpo-product-collections="{{- product.collections | json | url_encode -}}"
                                                    data-yotpo-product-tags="{{- product.tags | json | url_encode -}}"
                                                    data-yotpo-product-type="{{- product.type | json | url_encode -}}"></div>
                                                <!--end subtitle-->
                                            </div>
                                            <!--end content-->
                                            <!--cta-->
                                            <div class="bfs-product__rewards-cta">
                                                <div class="bfs-rewards-button-wrapper" style="--button-bg-color: {{ block.settings.button_background_color | default: '#000000' }}; --button-text-color: {{ block.settings.button_text_color | default: '#ffffff' }};">
                                                    {%- render 'bfs-button',
                                                        label: block.settings.cta_label_customer,
                                                        url: block.settings.cta_url,
                                                        style: 'secondary',
                                                        bordered: false,
                                                        size: 'medium',
                                                        full_width: false,
                                                        download: false,
                                                        new_tab: false
                                                    -%}
                                                </div>
                                            </div>
                                            <!--end cta-->
                                        </div>
                                        <!--end rewards-->
                                    {%- else -%}
                                        <!--rewards-->
                                        <div class="bfs-product__rewards" style="background-color: {{ block.settings.background_color | default: '#f8f8f8' }};">
                                            <!--content-->
                                            <div class="bfs-product__rewards-content">
                                                <!--title-->
                                                <h5 class="bfs-product__rewards-title bfs-fs-a3" style="color: {{ block.settings.title_color | default: '#000000' }};">
                                                    {{- block.settings.title_guest -}}
                                                </h5>
                                                <!--end title-->
                                                <!--subtitle-->
                                                <div
                                                    class="bfs-product__rewards-subtitle bfs-fs-b3 yotpo-widget-instance"
                                                    style="color: {{ block.settings.subtitle_color | default: '#666666' }};"
                                                    data-yotpo-instance-id="852054"
                                                    data-yotpo-product-id="{{- product.id -}}"
                                                    data-yotpo-variant-id="{{- current_variant.id -}}"
                                                    data-yotpo-product-variants="{{- product.variants | json | url_encode -}}"
                                                    data-yotpo-product-collections="{{- product.collections | json | url_encode -}}"
                                                    data-yotpo-product-tags="{{- product.tags | json | url_encode -}}"
                                                    data-yotpo-product-type="{{- product.type | json | url_encode -}}"></div>
                                                <!--end subtitle-->
                                            </div>
                                            <!--end content-->
                                            <!--cta-->
                                            <div class="bfs-product__rewards-cta">
                                                <div class="bfs-rewards-button-wrapper" style="--button-bg-color: {{ block.settings.button_background_color | default: '#000000' }}; --button-text-color: {{ block.settings.button_text_color | default: '#ffffff' }};">
                                                    {%- render 'bfs-button',
                                                        label: block.settings.cta_label_guest,
                                                        url: block.settings.cta_url,
                                                        style: 'secondary',
                                                        bordered: false,
                                                        size: 'medium',
                                                        full_width: false,
                                                        download: false,
                                                        new_tab: false
                                                    -%}
                                                </div>
                                            </div>
                                            <!--end cta-->
                                        </div>
                                        <!--end rewards-->
                                    {%- endif -%}
                                {%- endif -%}
                            {%- when 'artist' -%}
                                <!--artist-->
                                <div id="bfs-artist-inject"></div>
                                <!--end artist-->
                            {%- when 'accordions' -%}
                                <!--accordions-->
                                <div class="bfs-product__accordions">
                                    <!--ACCORDION-->
                                    <div id="bfs-product-accordion-{{- section.id -}}" class="bfs-accordion">
                                        {%- if product.description != blank -%}
                                            <!--single-->
                                            <div class="bfs-accordion__single">
                                                <button class="bfs-accordion__header bfs-fs-b3">
                                                    {{- 'product.accordion_labels.description' | t -}}
                                                    <span class="bfs-accordion__header-indicator">
                                                        {%- render 'bfs-icon-arrow-down-round' -%}
                                                    </span>
                                                </button>
                                                <div class="bfs-accordion__panel">
                                                    <div class="bfs-accordion__panel-inner bfs-fs-b1 bfs-richtext">
                                                        {{- product.description -}}
                                                    </div>
                                                </div>
                                            </div>
                                            <!--end single-->
                                        {%- endif -%}
                                        {%- if has_artwork -%}
                                            <!--single-->
                                            <div class="bfs-accordion__single">
                                                <button class="bfs-accordion__header bfs-fs-b3">
                                                    {{- 'product.accordion_labels.artwork' | t -}}
                                                    <span class="bfs-accordion__header-indicator">
                                                        {%- render 'bfs-icon-arrow-down-round' -%}
                                                    </span>
                                                </button>
                                                <div class="bfs-accordion__panel">
                                                    <div class="bfs-accordion__panel-inner bfs-fs-b1 bfs-richtext">
                                                        <p>DEBUG: has_artwork = {{ has_artwork }}</p>
                                                        <p>DEBUG: artist_tag_name = {{ artist_tag_name }}</p>
                                                        <p>DEBUG: artistcollection = {{ artistcollection }}</p>
                                                        <p>DEBUG: artist_profile.id = {{ artist_profile.id }}</p>
                                                        <p>DEBUG: artist_profile.artist_name = {{ artist_profile.artist_name.value }}</p>
                                                        <p>DEBUG: Direct access methods tested:</p>
                                                        {%- if artist_tag_name != "" -%}
                                                            {%- assign artist_handle = artist_tag_name | downcase | replace: ' ', '-' | replace: '(', '' | replace: ')', '' -%}
                                                            {%- assign test_direct = shop.metaobjects.artist_profile[artist_handle] -%}
                                                            <p>DEBUG: -- Handle "{{ artist_handle }}" = {{ test_direct.artist_name.value }}</p>
                                                            {%- assign artist_handle_alt = artist_tag_name | downcase | replace: ' ', '-' | append: '-bayley-mifsud' -%}
                                                            {%- assign test_direct_alt = shop.metaobjects.artist_profile[artist_handle_alt] -%}
                                                            <p>DEBUG: -- Handle "{{ artist_handle_alt }}" = {{ test_direct_alt.artist_name.value }}</p>
                                                            {%- assign artist_handle_alt2 = artist_tag_name | downcase | replace: ' ', '-' | replace: '(', '' | replace: ')', '' | append: '-bayley-mifsud' -%}
                                                            {%- assign test_direct_alt2 = shop.metaobjects.artist_profile[artist_handle_alt2] -%}
                                                            <p>DEBUG: -- Handle "{{ artist_handle_alt2 }}" = {{ test_direct_alt2.artist_name.value }}</p>
                                                            {%- assign test_direct_known = shop.metaobjects.artist_profile['sheri-skele-bigi-nagala'] -%}
                                                            <p>DEBUG: -- Known handle "sheri-skele-bigi-nagala" = {{ test_direct_known.artist_name.value }}</p>
                                                            <p>DEBUG: Name matching test:</p>
                                                            {%- assign tag_name_clean = artist_tag_name | downcase | replace: ' ', '-' -%}
                                                            <p>DEBUG: -- tag_name_clean = "{{ tag_name_clean }}"</p>
                                                            {%- assign test_artist_name_clean = test_direct_known.artist_name.value | downcase | replace: ' ', '-' | replace: '(', '-' | replace: ')', '' -%}
                                                            <p>DEBUG: -- test_artist_name_clean = "{{ test_artist_name_clean }}"</p>
                                                            <p>DEBUG: -- contains check: "{{ test_artist_name_clean }}" contains "{{ tag_name_clean }}" = {{ test_artist_name_clean contains tag_name_clean }}</p>
                                                        {%- endif -%}
                                                        {%- if artist_profile -%}
                                                            {%- assign max_stories = block.settings.max_artwork_stories | default: 5 -%}
                                                            <p>DEBUG: max_stories = {{ max_stories }}</p>
                                                            {%- for i in (1..max_stories) -%}
                                                                {%- capture artwork_field -%}artwork_{{ i }}{%- endcapture -%}
                                                                {%- capture art_info_field -%}art_info_{{ i }}{%- endcapture -%}
                                                                <p>DEBUG: {{ artwork_field }} = {{ artist_profile[artwork_field].value }}</p>
                                                                <p>DEBUG: {{ art_info_field }} = {{ artist_profile[art_info_field].value }}</p>
                                                                {%- if artist_profile[artwork_field].value != blank -%}
                                                                    <div style="margin-bottom: 20px;">
                                                                        <img src="{{ artist_profile[artwork_field].value | image_url: width: 600 }}" style="width: 100%; height: auto; margin-bottom: 10px;" alt="Artwork {{ i }}" width="600" height="auto">
                                                                        {%- if artist_profile[art_info_field].value != blank -%}
                                                                            <div>{{ artist_profile[art_info_field] | metafield_tag }}</div>
                                                                        {%- endif -%}
                                                                    </div>
                                                                {%- endif -%}
                                                            {%- endfor -%}
                                                        {%- else -%}
                                                            <p>DEBUG: No artist_profile found with any method</p>
                                                        {%- endif -%}
                                                    </div>
                                                </div>
                                            </div>
                                            <!--end single-->
                                        {%- endif -%}
                                        {%- if
                                            product.first_available_variant.requires_shipping and
                                            block.settings.shipping_info_general != blank and
                                            block.settings.returns_info_general != blank and
                                            block.settings.shipping_info_gallery != blank
                                        -%}
                                            {%- assign free_shipping_threshold = settings.cart_free_shipping_threshold | prepend: cart.currency.symbol -%}
                                            <!--single-->
                                            <div class="bfs-accordion__single">
                                                <button class="bfs-accordion__header bfs-fs-b3">
                                                    {{- 'product.accordion_labels.shipping' | t -}}
                                                    <span class="bfs-accordion__header-indicator">
                                                        {%- render 'bfs-icon-arrow-down-round' -%}
                                                    </span>
                                                </button>
                                                <div class="bfs-accordion__panel">
                                                    <div class="bfs-accordion__panel-inner bfs-fs-b1 bfs-richtext">
                                                        {%- if is_collection_gallery -%}
                                                            {{- block.settings.shipping_info_gallery | replace: '[free_shipping_threshold]', free_shipping_threshold -}}
                                                        {%- else -%}
                                                            {{- block.settings.shipping_info_general | replace: '[free_shipping_threshold]', free_shipping_threshold -}}
                                                            {%- unless has_no_returns -%}
                                                                {{- block.settings.returns_info_general -}}
                                                            {%- endunless -%}
                                                        {%- endif -%}
                                                    </div>
                                                </div>
                                            </div>
                                            <!--end single-->
                                        {%- endif -%}
                                        <!--single-->
                                        <div class="bfs-accordion__single">
                                            <button class="bfs-accordion__header bfs-fs-b3">
                                                {{- 'product.accordion_labels.share' | t -}}
                                                <span class="bfs-accordion__header-indicator">
                                                    {%- render 'bfs-icon-arrow-down-round' -%}
                                                </span>
                                            </button>
                                            <div class="bfs-accordion__panel">
                                                <div class="bfs-accordion__panel-inner bfs-fs-b0">
                                                    {%- render 'bfs-product-share',
                                                        url: product_share_url
                                                    -%}
                                                </div>
                                            </div>
                                        </div>
                                        <!--end single-->
                                    </div>
                                    <!--end ACCORDION-->
                                </div>
                                <!--end accordions-->
                            {%- when 'divider' -%}
                                <!--divider-->
                                <div class="bfs-product__divider"></div>
                                <!--end divider-->
                            {%- when '@app' -%}
                                <!--app block-->
                                <div class="bfs-product__app-block">
                                    {%- render block -%}
                                </div>
                                <!--end app block-->
                        {%- endcase -%}
                    {%- endfor -%}
                </div>
                <!--end main-->
            {%- endif -%}
        </div>
    </div>
</section>
<!--end BFS PRODUCT SECTION-->

{%- if has_size_chart -%}
    <div id="bfs-product-size-chart-modal" class="modal">
        <h2 class="ProductMeta__Title Heading u-h3">
            {{- size_chart_page.title -}}
        </h2>
        <main class="Rte">
            {{- size_chart_page.content -}}
        </main>
    </div>
{%- endif -%}

<script>
    function bfsFormatMoney(cents, format) {
        if (typeof cents === 'string') {
            cents = cents.replace('.', '');
        }
        let value = '';
        const placeholderRegex = /\{\{\s*(\w+)\s*\}\}/;
        const formatString = format || moneyFormat;

        function formatWithDelimiters(
            number,
            precision = 2,
            thousands = ',',
            decimal = '.'
        ) {
            if (isNaN(number) || number == null) {
                return '0';
            }

            number = (number / 100.0).toFixed(precision);
            const parts = number.split('.');
            const dollarsAmount = parts[0].replace(
                /(\d)(?=(\d\d\d)+(?!\d))/g,
                `$1${thousands}`
            );

            const centsAmount = parts[1] && parts[1] !== '00' ? decimal + parts[1] : '';
            return dollarsAmount + centsAmount;
        }

        switch (formatString.match(placeholderRegex)[1]) {
            case 'amount':
                value = formatWithDelimiters(cents, 2);
                break;
            case 'amount_no_decimals':
                value = formatWithDelimiters(cents, 0);
                break;
            case 'amount_with_comma_separator':
                value = formatWithDelimiters(cents, 2, '.', ',');
                break;
            case 'amount_no_decimals_with_comma_separator':
                value = formatWithDelimiters(cents, 0, '.', ',');
                break;
        }
        return formatString.replace(placeholderRegex, value);
    }

    function bfsProductForm() {
        const moneyFormat = "{{- shop.money_format -}}";
        const variants = {{- product.variants | json -}};
        if (!variants.length) return;
        let currentVariantId = {{- current_variant.id -}};
        let currentVariant = bfsGetCurrentVariant(variants, currentVariantId);
        if (!currentVariant) return;
        const template = document.querySelector("[data-product]");
        if (!template) return;
        const variantSelectors = template.querySelectorAll('input[name="id"]');
        const submit = template.querySelector('button[type="submit"]');
        const price = template.querySelector("[data-price]");
        const regularPrice = template.querySelector("[data-regular-price]");
        const compareAtPrice = template.querySelector("[data-compare-at-price]");
        const discountPercentage = template.querySelector("[data-discount-percentage]");
        const availabilityBadges = template.querySelector("[data-availability-badges]")
        const availabilityBadgeOne = template.querySelector("[data-availability-badge-one]")
        const availabilityBadgeFew = template.querySelector("[data-availability-badge-few]")
        const availabilityBadgeSoldOut = template.querySelector("[data-availability-badge-sold-out]")
        if (!variantSelectors.length || !submit || !price || !regularPrice || !compareAtPrice || !discountPercentage || !availabilityBadges || !availabilityBadgeOne || !availabilityBadgeFew || !availabilityBadgeSoldOut) return;

        variantSelectors.forEach(input => {
            input.addEventListener("change", (event) => {
                currentVariantId = Number(event.target.value);
                currentVariant = bfsGetCurrentVariant(variants, currentVariantId);
                bfsUpdateAvailability(currentVariant, submit);
                bfsUpdateAvailabilityBadges(currentVariant, availabilityBadges, availabilityBadgeOne, availabilityBadgeFew, availabilityBadgeSoldOut);
                bfsUpdatePrice(currentVariant, price, regularPrice, compareAtPrice, discountPercentage, moneyFormat);
                bfsUpdateBISButton(currentVariant);
                bfsUpdateHistoryState(currentVariant);
            });
        });

        function bfsGetCurrentVariant(variants, currentVariantId) {
            return variants.find(variant => variant.id === currentVariantId) || null;
        }

        function bfsUpdateAvailability(currentVariant, submit) {
            {%- if is_coming_soon -%}
                submit.setAttribute("disabled", "disabled");
                submit.textContent = "Coming Soon";
            {%- else -%}
                if (currentVariant.available) {
                    submit.removeAttribute("disabled");
                    submit.textContent = "{%- if is_preorder -%}{{- 'product.form.pre_order' | t -}}{%- else -%}{{- 'product.form.add_to_cart' | t -}}{%- endif -%}";
                } else {
                    submit.setAttribute("disabled", "disabled");
                    submit.textContent = "{{- 'product.form.sold_out' | t -}}";
                }
            {%- endif -%}
        }

        function bfsUpdateAvailabilityBadges(currentVariant, availabilityBadges, availabilityBadgeOne, availabilityBadgeFew, availabilityBadgeSoldOut) {
            if (currentVariant.inventory_quantity === 0) {
                availabilityBadges.removeAttribute("hidden");
                availabilityBadgeOne.setAttribute("hidden", "");
                availabilityBadgeFew.setAttribute("hidden", "");
                availabilityBadgeSoldOut.removeAttribute("hidden");
            } else if (currentVariant.inventory_quantity === 1) {
                availabilityBadges.removeAttribute("hidden");
                availabilityBadgeOne.removeAttribute("hidden");
                availabilityBadgeFew.setAttribute("hidden", "");
                availabilityBadgeSoldOut.setAttribute("hidden", "");
            } else if (currentVariant.inventory_quantity <= 5) {
                availabilityBadges.removeAttribute("hidden");
                availabilityBadgeOne.setAttribute("hidden", "");
                availabilityBadgeFew.removeAttribute("hidden");
                availabilityBadgeSoldOut.setAttribute("hidden", "");
            } else {
                availabilityBadges.setAttribute("hidden", "");
                availabilityBadgeOne.setAttribute("hidden", "");
                availabilityBadgeFew.setAttribute("hidden", "");
                availabilityBadgeSoldOut.setAttribute("hidden", "");
            }
        }

        function bfsUpdatePrice(currentVariant, price, regularPrice, compareAtPrice, discountPercentage, moneyFormat) {
            regularPrice.textContent = bfsFormatMoney(currentVariant.price, moneyFormat);
            if (currentVariant.compare_at_price && currentVariant.compare_at_price > currentVariant.price) {
                price.classList.add("is-on-sale");
                compareAtPrice.textContent = bfsFormatMoney(currentVariant.compare_at_price, moneyFormat);
                discountPercentage.textContent = Math.round(((currentVariant.compare_at_price - currentVariant.price) * 100) / currentVariant.compare_at_price) + "% OFF";
            } else {
                price.classList.remove("is-on-sale");
                compareAtPrice.textContent = "";
                discountPercentage.textContent = "";
            }
        }

        function bfsUpdateBISButton(currentVariant) {
            const bisButton = template.querySelector(".bfs-product__oos-cta");
            const bisButtonTrigger = template.querySelector(".BIS_trigger");

            if (bisButton && bisButtonTrigger) {
                // Update variant ID for Klaviyo
                bisButtonTrigger.setAttribute("data-variant-id", currentVariant.id);

                {%- if is_coming_soon -%}
                    bisButton.style.display = "block";
                {%- else -%}
                    if (currentVariant.available) {
                        bisButton.style.display = "none";
                    } else {
                        bisButton.style.display = "block";
                    }
                {%- endif -%}
            }
        }

        function bfsUpdateHistoryState(currentVariant) {
            window.history.replaceState({}, "", `{{- product.url -}}?variant=${currentVariant.id}`);
        }
    }

    function bfsUpsellProgressBar() {
        const moneyFormat = "{{- shop.money_format -}}";
        const thresholdElements = document.querySelectorAll(".bfs-upsell-progress-bar__threshold");
        const progressBar = document.querySelector(".bfs-upsell-progress-bar__progress-bar span");
        const statusElement = document.querySelector(".bfs-upsell-progress-bar__status");
        if (!thresholdElements.length || !progressBar || !statusElement) return;
        const thresholds = Array.from(thresholdElements).map((el) => ({
            el,
            value: parseInt(el.dataset.threshold, 10),
            statusMessage: el.dataset.statusMessage,
        }));
        if (!thresholds.length) return;
        thresholds.sort((a, b) => a.value - b.value);
        const maxThreshold = thresholds[thresholds.length - 1].value;

        getCartAndUpdateProgress();

        document.addEventListener('rebuy:cart.add', () => {
            getCartAndUpdateProgress();
        });

        document.addEventListener('rebuy:cart.change', () => {
            getCartAndUpdateProgress();
        });

        document.addEventListener('rebuy:cart.enriched', () => {
            getCartAndUpdateProgress();
        });

        function getCartAndUpdateProgress() {
            fetch("/cart.js")
                .then((response) => response.json())
                .then((cart) => {
                    thresholds.forEach(({el, value}) => {
                        if (cart.total_price >= value) {
                            el.classList.add("is-active");
                        } else {
                            el.classList.remove("is-active");
                        }
                    });
                    progressBar.style.width = `${Math.min((cart.total_price / maxThreshold) * 100, 100)}%`;
                    const nextThreshold = thresholds.find((t) => cart.total_price < t.value);
                    if (nextThreshold) {
                        const remainingCents = nextThreshold.value - cart.total_price;
                        statusElement.innerHTML = nextThreshold.statusMessage.replace("[amount]", bfsFormatMoney(remainingCents, moneyFormat));
                    } else {
                        statusElement.innerHTML = "{{- upsell_progress_bar_success_message -}}";
                    }
                })
                .catch((error) => {
                    console.error("Failed to fetch cart data:", error);
                });
        }
    }

    function bfsProductAccordion() {
        const bfsProductAccordionWrapper = document.getElementById("bfs-product-accordion-{{- section.id -}}");
        if (!bfsProductAccordionWrapper) return;
        const bfsProductAccordionHeaders = bfsProductAccordionWrapper.getElementsByClassName("bfs-accordion__header");
        if (bfsProductAccordionHeaders.length < 1) return;
        let currentlyActiveHeader = null;
        for (let i = 0; i < bfsProductAccordionHeaders.length; i++) {
            bfsProductAccordionHeaders[i].addEventListener("click", function () {
                const panel = this.nextElementSibling;
                const isActive = this === currentlyActiveHeader;
                if (currentlyActiveHeader && !isActive) {
                    currentlyActiveHeader.classList.remove("is-active");
                    const activePanel = currentlyActiveHeader.nextElementSibling;
                    if (activePanel) activePanel.style.maxHeight = null;
                }
                if (isActive) {
                    this.classList.remove("is-active");
                    if (panel) panel.style.maxHeight = null;
                    currentlyActiveHeader = null;
                } else {
                    this.classList.add("is-active");
                    if (panel) panel.style.maxHeight = panel.scrollHeight + "px";
                    currentlyActiveHeader = this;
                }
            });
        }
    }

    function bfsProductSizeChartModal() {
        const bfsProductSizeChartModal = document.getElementById("bfs-product-size-chart-modal");
        const bfsProductSizeChartModalTrigger = document.getElementById("bfs-product-size-chart-modal-trigger");
        if (!bfsProductSizeChartModal || !bfsProductSizeChartModalTrigger) return;
        bfsProductSizeChartModalTrigger.addEventListener("click", function () {
            $(bfsProductSizeChartModal).modal();
        });
    }

    bfsProductForm();
    bfsUpsellProgressBar();
    bfsProductAccordion();
    bfsProductSizeChartModal();
</script>

{%- if product.media.size > 0 -%}
    <script>
        function bfsProductDesktopGallery() {
            const bfsDesktopGalleryHidden = document.getElementById("bfs-product-desktop-gallery-hidden");
            const bfsDesktopGalleryTrigger = document.getElementById("bfs-product-desktop-gallery-trigger");
            if (!bfsDesktopGalleryHidden || !bfsDesktopGalleryTrigger) return;
            bfsDesktopGalleryTrigger.addEventListener("click", function () {
                this.classList.toggle("is-active");
                if (bfsDesktopGalleryHidden.style.maxHeight) {
                    bfsDesktopGalleryHidden.style.maxHeight = null;
                } else {
                    bfsDesktopGalleryHidden.style.maxHeight = bfsDesktopGalleryHidden.scrollHeight + "px";
                }
            });
        }

        function bfsProductMobileGallery() {
            new Swiper("#bfs-product-mobile-gallery", {
                slidesPerView: 1,
                spaceBetween: 10,
                grabCursor: true,
                navigation: {
                    nextEl: ".bfs-slider-next-{{- section.id -}}",
                    prevEl: ".bfs-slider-prev-{{- section.id -}}",
                },
            });
        }

        bfsProductDesktopGallery();
        bfsProductMobileGallery();
    </script>
{%- endif -%}

{%- if is_personalised -%}
    <style>
        @font-face {
            font-family: 'Diary Notes';
            src: url('https://cdn.shopify.com/s/files/1/0247/4021/files/Diary-Notes.woff2?v=1668636602') format('woff2'),
            url('https://cdn.shopify.com/s/files/1/0247/4021/files/Diary-Notes.woff?v=1668636603') format('woff');
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }

        @font-face {
            font-family: 'Breeder';
            src: url('https://cdn.shopify.com/s/files/1/0247/4021/files/Breeder-Regular.woff2?v=1668636603') format('woff2'),
            url('https://cdn.shopify.com/s/files/1/0247/4021/files/Breeder-Regular.woff?v=1668636603') format('woff');
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }

        @font-face {
            font-family: 'Thunderstorm';
            src: url('https://cdn.shopify.com/s/files/1/0247/4021/files/Thunderstorm-Regular.woff2?v=1668636602') format('woff2'),
            url('https://cdn.shopify.com/s/files/1/0247/4021/files/Thunderstorm-Regular.woff?v=1668636603') format('woff');
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }

        @font-face {
            font-family: 'Venti CF';
            src: url('https://cdn.shopify.com/s/files/1/0247/4021/files/VentiCF-Bold_972706ed-6296-487f-980e-d91b7b933665.woff2?v=1668636604') format('woff2'),
            url('https://cdn.shopify.com/s/files/1/0247/4021/files/VentiCF-Bold_4de2bd49-31d9-4f13-bb78-3c0f09ff5f62.woff?v=1668636603') format('woff');
            font-weight: bold;
            font-style: normal;
            font-display: swap;
        }

        input[data-font="Venti"],
        select[data-font="Venti"] {
            font-family: 'Venti CF';
        }

        input[data-font="Diary"],
        select[data-font="Diary"] {
            font-family: 'Diary Notes';
        }

        input[data-font="Breeder"],
        select[data-font="Breeder"] {
            font-family: 'Breeder';
        }

        input[data-font="Thunderstorm"],
        select[data-font="Thunderstorm"] {
            font-family: 'Thunderstorm';
        }
    </style>

    <script>
        function bfsProductPersonalisation() {
            const template = document.querySelector("[data-product]");
            if (!template) return;
            const variantSelectors = template.querySelectorAll('input[name="id"]');
            const submit = template.querySelector('button[type="submit"]');
            const fieldsets = template.querySelectorAll("[data-personalisation]");
            const fontInput = template.querySelector('select[name="properties[Font]"]');
            const textInput = template.querySelector('input[name="properties[Custom Text]"]');
            const textInputValidationMessage = template.querySelector("[data-personalisation-validation]");
            if (!variantSelectors.length || !submit || !fieldsets.length || !fontInput || !textInput || !textInputValidationMessage) return;
            const initiallySelectedValue = [...variantSelectors].find(radio => radio.checked).dataset.value ?? null;

            if (initiallySelectedValue === "yes") {
                bfsShowPersonalisationElements();
                bfsUpdatePersonalisationElements();
            } else {
                bfsHidePersonalisationElements();
            }

            variantSelectors.forEach(input => {
                input.addEventListener("change", (event) => {
                    if (event.target.dataset.value === "yes") {
                        bfsShowPersonalisationElements();
                        bfsUpdatePersonalisationElements();
                    } else {
                        bfsHidePersonalisationElements();
                    }
                });
            });

            fontInput.addEventListener("change", () => {
                fontInput.dataset.font = fontInput.options[fontInput.selectedIndex].value;
                textInput.dataset.font = fontInput.options[fontInput.selectedIndex].value;
            });

            textInput.addEventListener("input", () => {
                bfsUpdatePersonalisationElements();
            })

            function bfsHidePersonalisationElements() {
                fieldsets.forEach((fieldset) => {
                    fieldset.style.display = "none";
                });
                fontInput.removeAttribute("name");
                fontInput.setAttribute("disabled", "");
                textInput.removeAttribute("name");
                textInput.setAttribute("disabled", "");
            }

            function bfsShowPersonalisationElements() {
                fieldsets.forEach((fieldset) => {
                    fieldset.style.display = "block";
                });
                fontInput.removeAttribute("disabled");
                fontInput.setAttribute("name", "properties[Font]");
                textInput.removeAttribute("disabled");
                textInput.setAttribute("name", "properties[Custom Text]");
            }

            function bfsUpdatePersonalisationElements() {
                {%- if is_coming_soon -%}
                    submit.setAttribute("disabled", "disabled");
                    submit.textContent = "Coming Soon";
                {%- else -%}
                    if (textInput.value.trim() !== "" && validateTextInput()) {
                        submit.removeAttribute("disabled");
                        submit.textContent = "{%- if is_preorder -%}{{- 'product.form.pre_order' | t -}}{%- else -%}{{- 'product.form.add_to_cart' | t -}}{%- endif -%}";
                        textInput.classList.remove("bfs-form__input--error");
                        textInputValidationMessage.style.display = "none";
                    } else if (textInput.value.trim() !== "" && !validateTextInput()) {
                        submit.setAttribute("disabled", "disabled");
                        submit.textContent = "{{- 'product.personalisation.text_error' | t -}}";
                        textInput.classList.add("bfs-form__input--error");
                        textInputValidationMessage.style.display = "block";
                    } else {
                        submit.setAttribute("disabled", "disabled");
                        submit.textContent = "{{- 'product.personalisation.add_text' | t -}}";
                        textInput.classList.remove("bfs-form__input--error");
                        textInputValidationMessage.style.display = "none";
                    }
                {%- endif -%}
            }

            function validateTextInput() {
                const regex = /^[a-zA-Z0-9 ']*$/;
                return regex.test(textInput.value);
            }
        }

        bfsProductPersonalisation();
    </script>
{%- endif -%}

{%- render 'bfs-product-schema' -%}

<style>
.bfs-rewards-button-wrapper .bfs-button {
    background-color: var(--button-bg-color) !important;
    color: var(--button-text-color) !important;
    border-color: var(--button-bg-color) !important;
}

.bfs-rewards-button-wrapper .bfs-button:hover {
    background-color: var(--button-bg-color) !important;
    color: var(--button-text-color) !important;
    opacity: 0.8;
}
</style>

{%- schema -%}
{
    "name": "BFS Product Template",
    "tag": "section",
    "settings": [
        {
            "type": "header",
            "content": "CONTENT"
        },
        {
            "type": "header",
            "content": "Gallery"
        },
        {
            "type": "checkbox",
            "id": "video_autoplay",
            "label": "Video Autoplay",
            "default": false
        }
    ],
    "blocks": [
        {
            "type": "title",
            "name": "Title",
            "limit": 1,
            "settings": [
            ]
        },
        {
            "type": "reviews",
            "name": "Reviews",
            "limit": 1,
            "settings": [
            ]
        },
        {
            "type": "price",
            "name": "Price",
            "limit": 1,
            "settings": [
            ]
        },
        {
            "type": "form",
            "name": "Form",
            "limit": 1,
            "settings": [
                {
                    "type": "header",
                    "content": "Personalisation"
                },
                {
                    "type": "text",
                    "id": "personalisation_font_input_label",
                    "label": "Personalisation - Font Input Label"
                },
                {
                    "type": "text",
                    "id": "personalisation_text_input_label",
                    "label": "Personalisation - Text Input Label"
                },
                {
                    "type": "text",
                    "id": "personalisation_validation",
                    "label": "Personalisation - Validation"
                },
                {
                    "type": "text",
                    "id": "personalisation_note",
                    "label": "Personalisation - Note"
                },
                {
                    "type": "header",
                    "content": "Preorder"
                },
                {
                    "type": "text",
                    "id": "preorder_note",
                    "label": "Preorder - Note",
                    "info": "Use [preorder_shipping_time] to display the preorder shipping time from tag."
                },
                {
                    "type": "text",
                    "id": "preorder_property_label",
                    "label": "Preorder - Property Label",
                    "info": "Use [preorder_shipping_time] to display the preorder shipping time from tag."
                }
            ]
        },
        {
            "type": "upsell_progress_bar",
            "name": "Upsell Progress Bar",
            "limit": 1,
            "settings": [
                {
                    "type": "text",
                    "id": "step_1_label",
                    "label": "Step 1 - Label"
                },
                {
                    "type": "number",
                    "id": "step_1_threshold",
                    "label": "Step 1 - Threshold",
                    "info": "Non formatted value only => 20000 = $200.00 / 2099 = $20.99."
                },
                {
                    "type": "inline_richtext",
                    "id": "step_1_status_message",
                    "label": "Step 1 - Status Message",
                    "info": "Put [amount] where price breaks the string of text. Make text italic to appear red."
                },
                {
                    "type": "text",
                    "id": "step_2_label",
                    "label": "Step 2 - Label"
                },
                {
                    "type": "number",
                    "id": "step_2_threshold",
                    "label": "Step 2 - Threshold",
                    "info": "Non formatted value only => 20000 = $200.00 / 2099 = $20.99."
                },
                {
                    "type": "inline_richtext",
                    "id": "step_2_status_message",
                    "label": "Step 2 - Status Message",
                    "info": "Put [amount] where price breaks the string of text. Make text italic to appear red."
                },
                {
                    "type": "text",
                    "id": "step_3_label",
                    "label": "Step 3 - Label"
                },
                {
                    "type": "number",
                    "id": "step_3_threshold",
                    "label": "Step 3 - Threshold",
                    "info": "Non formatted value only => 20000 = $200.00 / 2099 = $20.99."
                },
                {
                    "type": "inline_richtext",
                    "id": "step_3_status_message",
                    "label": "Step 3 - Status Message",
                    "info": "Put [amount] where price breaks the string of text. Make text italic to appear red."
                },
                {
                    "type": "inline_richtext",
                    "id": "success_message",
                    "label": "Success Message",
                    "info": "Make text italic to appear red."
                }
            ]
        },
        {
            "type": "selling_points",
            "name": "Selling Points",
            "limit": 1,
            "settings": [
                {
                    "type": "image_picker",
                    "id": "selling_point_1_icon",
                    "label": "Selling Point 1 - Icon"
                },
                {
                    "type": "text",
                    "id": "selling_point_1_title",
                    "label": "Selling Point 1 - Title"
                },
                {
                    "type": "image_picker",
                    "id": "selling_point_2_icon",
                    "label": "Selling Point 2 - Icon"
                },
                {
                    "type": "text",
                    "id": "selling_point_2_title",
                    "label": "Selling Point 2 - Title"
                },
                {
                    "type": "image_picker",
                    "id": "selling_point_3_icon",
                    "label": "Selling Point 3 - Icon"
                },
                {
                    "type": "text",
                    "id": "selling_point_3_title",
                    "label": "Selling Point 3 - Title"
                }
            ]
        },
        {
            "type": "corporate_cta",
            "name": "Corporate CTA",
            "limit": 1,
            "settings": [
                {
                    "type": "text",
                    "id": "label",
                    "label": "Label"
                },
                {
                    "type": "text",
                    "id": "min_label",
                    "label": "Min Label"
                },
                {
                    "type": "checkbox",
                    "id": "new_tab",
                    "label": "New Tab",
                    "default": false
                }
            ]
        },
        {
            "type": "rewards",
            "name": "Rewards",
            "limit": 1,
            "settings": [
                {
                    "type": "text",
                    "id": "title_customer",
                    "label": "Title Customer"
                },
                {
                    "type": "text",
                    "id": "title_guest",
                    "label": "Title Guest"
                },
                {
                    "type": "text",
                    "id": "cta_label_customer",
                    "label": "CTA Label Customer"
                },
                {
                    "type": "text",
                    "id": "cta_label_guest",
                    "label": "CTA Label Guest"
                },
                {
                    "type": "url",
                    "id": "cta_url",
                    "label": "CTA URL"
                }
,
                {
                    "type": "color",
                    "id": "background_color",
                    "label": "Background Color",
                    "default": "#faf6e5"
                },
                {
                    "type": "color",
                    "id": "title_color",
                    "label": "Title Color",
                    "default": "#000000"
                },
                {
                    "type": "color",
                    "id": "subtitle_color",
                    "label": "Subtitle Color",
                    "default": "#000000"
                },
                {
                    "type": "color",
                    "id": "button_background_color",
                    "label": "Button Background Color",
                    "default": "#febf46"
                },
                {
                    "type": "color",
                    "id": "button_text_color",
                    "label": "Button Text Color",
                    "default": "#000000"
                }
            ]
        },
        {
            "type": "artist",
            "name": "Artist",
            "settings": [
            ]
        },
        {
            "type": "accordions",
            "name": "Accordions",
            "limit": 1,
            "settings": [
                {
                    "type": "range",
                    "id": "max_artwork_stories",
                    "label": "Maximum Artwork Stories",
                    "info": "Control how many artwork stories are displayed in the artwork accordion",
                    "min": 1,
                    "max": 15,
                    "step": 1,
                    "default": 1
                },
                {
                    "type": "richtext",
                    "id": "shipping_info_general",
                    "label": "Shipping Info - General",
                    "info": "Use [free_shipping_threshold] to display the free shipping threshold."
                },
                {
                    "type": "richtext",
                    "id": "returns_info_general",
                    "label": "Returns Info - General"
                },
                {
                    "type": "richtext",
                    "id": "shipping_info_gallery",
                    "label": "Shipping Info - Gallery",
                    "info": "Use [free_shipping_threshold] to display the free shipping threshold."
                }
            ]
        },
        {
            "type": "divider",
            "name": "Divider",
            "settings": [
            ]
        },
        {
            "type": "@app"
        }
    ]
}
{%- endschema -%}
