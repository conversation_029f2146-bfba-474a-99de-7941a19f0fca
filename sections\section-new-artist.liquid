{% if page.handle == 'artists' %}
    <div class="artists-filter" id="artists-filter" data-sticky-class="is-sticky">
        <div class="artists-label">SORT BY LETTER</div>
        <ul>
            {% assign letters_array = section.settings.letters | split: ',' %}
            {% if letters_array.size == 0 or section.settings.letters == blank %}
                {% assign letters_array = 'A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z' | split: ',' %}
            {% endif %}
            {% for letter in letters_array %}
                <li><a href="#letter-{{ letter | strip }}" class="scrollme">{{ letter | strip }}</a></li>
            {% endfor %}
        </ul>
    </div>
    <div class="wrapper">
        <div class="artists-grid">
            {% paginate shop.metaobjects[section.settings.metaobject_type].values by 250 %}
                {% assign all_artists = shop.metaobjects[section.settings.metaobject_type].values %}
                {% for letter in letters_array %}
                    {% assign letter_stripped = letter | strip %}
                    {% assign artists_found = 0 %}
                    {% for artist_profile in all_artists %}
                        {% assign first_letter = artist_profile.artist_name.value | slice: 0 | upcase %}
                        {% if first_letter == letter_stripped %}
                            {% assign artists_found = artists_found | plus: 1 %}
                            {% if artists_found == 1 %}
                                <div class="artists-letter" id="letter-{{ letter_stripped }}">{{ letter_stripped }}</div>
                            {% endif %}
                            <div class="artists-profile artist-{{ artist_profile.artist_name.value | downcase | replace: ' ', '-' | replace: '.', '' | replace: '(', '' | replace: ')', '' | replace: '&', '' }}">
                                {% if artist_profile.profile_image.value != blank %}
                                    <div class="profile-image">
                                        <img src="{{ artist_profile.profile_image.value | img_url: '300x300' }}">
                                    </div>
                                {% endif %}
                                <div class="profile-content" style="{% if artist_profile.profile_image.value == blank %}width: 100%{% endif %}">
                                    <div class="artists-label">{{ artist_profile.artist_category.value }}</div>
                                    <p class="artists-name">{{ artist_profile.artist_name.value }}</p>
                                    {% if artist_profile.heritage.value != blank %}
                                        <p class="artists-place">{{ artist_profile.heritage.value }}</p>
                                    {% endif %}
                                    {% if artist_profile.artist_page.value != blank %}
                                        <a href="{{ artist_profile.artist_page.value.url }}" class="artists-link">READ MORE</a>
                                    {% endif %}
                                </div>
                            </div>
                        {% endif %}
                    {% endfor %}
                {% endfor %}
            {% endpaginate %}
        </div>
    </div>
{% endif %}

{% if page.template_suffix == 'artist-single' %}
    {% paginate shop.metaobjects[section.settings.metaobject_type].values by 250 %}
        {% for artist_profile in shop.metaobjects[section.settings.metaobject_type].values %}
            {% capture artistlink %}{{ shop.secure_url }}{{ artist_profile.artist_page.value.url }}{% endcapture %}
            {% if artistlink == canonical_url %}
                <div class="artist-intro">
                    {% if artist_profile.profile_image.value != blank %}
                        <div class="block__img">
                            <p><img class="artist-avi" src="{{ artist_profile.profile_image.value | img_url: "380x380" }}"></p>
                            {% if artist_profile.signature.value %}
                                <p><img src="{{ artist_profile.signature.value | img_url: "120x", scale: 2 }}" style="width: 120px"></p>
                            {% endif %}
                        </div>
                    {% endif %}
                    <div class="block__text" style="{% if artist_profile.profile_image.value == blank %}width: 100%{% endif %}">
                        <h1 class="Heading u-h2" style="margin-bottom: 0px; font-weight: 500">{{ artist_profile.artist_name.value }}</h1>
                        {% if artist_profile.heritage.value != blank %}
                            <h2 class="Heading u-h5">{{ artist_profile.heritage.value }}</h2>
                        {% endif %}
                        {{ artist_profile.artist_long_info | metafield_tag }}
                        {% if artist_profile.instagram.value != '' %}
                            <div class="block__social">
                                <a href="https://instagram.com/{{ artist_profile.instagram.value }}"><img src="{{ 'insta.svg' | asset_url }}"> @{{ artist_profile.instagram.value }}</a>
                            </div>
                        {% endif %}
                    </div>
                </div>
                {% if artist_profile.artwork_1.value != blank %}
                    <div class="artist-carousel">
                        <h2 class="Heading u-h2" style="font-weight: 500">Artworks</h2>
                        <div class="paintings-wrapperr">
                            <div class="carousel paintings-carousel">
                                {% for i in (1..15) %}
                                    {% capture artwork %}artwork_{{ i }}{% endcapture %}
                                    {% capture artdesc %}art_info_{{ i }}{% endcapture %}
                                    {% if artist_profile[artwork].value != blank %}
                                        <div class="carousel-cell">
                                            <div class="cell-wrap">
                                                <img src="{{ artist_profile[artwork].value | img_url: "400x"}}" height="{{ artist_profile[artwork].value.height }}" width="{{ artist_profile[artwork].value.width }}" style="height: auto">
                                                {% if artist_profile[artdesc].value != blank %}
                                                    <div class="art-desc">{{ artist_profile[artdesc] | metafield_tag }}</div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                {% endif %}
                {% if artist_profile.artist_collection.value != blank %}
                    {%- assign collection = artist_profile.artist_collection.value -%}
                    <div class="artist-collection">
                        <h2 class="Heading u-h2" style="font-weight: 500">Products</h2>
                        <div class="paintings-wrapperr">
                            <div class="carousel" data-flickity='{ "wrapAround": true, "cellAlign": "left", "pageDots": false }'>
                                {%- for product in collection.products limit: 12 -%}
                                    {% unless product.tags contains 'hideme' or product.available == false %}
                                        <div class="carousel-cell">
                                            {%- include 'product-item', show_product_info: true, show_color_swatch: section.settings.show_color_swatch, show_labels: true -%}
                                        </div>
                                    {% endunless %}
                                {%- endfor -%}
                                {%- for product in collection.products limit: 12 -%}
                                    {% unless product.tags contains 'hideme' or product.available == true %}
                                        <div class="carousel-cell">
                                            {%- include 'product-item', show_product_info: true, show_color_swatch: section.settings.show_color_swatch, show_labels: true -%}
                                        </div>
                                    {% endunless %}
                                {%- endfor -%}
                            </div>
                        </div>
                    </div>
                {% endif %}
            {% endif %}
        {% endfor %}
    {% endpaginate %}
{% endif %}

{% if template.name == "product" %}
    {% assign story = false %}
    {% for tag in product.tags %}
        {% if tag contains ':story' %}
            {% assign story = tag %}
        {% endif %}
    {% endfor %}
    {% assign artistcollection = "" %}
    {% for collection in product.collections %}
        {% assign artistcollection = artistcollection | append: collection.handle | append: ' ' %}
    {% endfor %}
    {% paginate shop.metaobjects[section.settings.metaobject_type].values by 250 %}
        {%- for artist_profile in shop.metaobjects[section.settings.metaobject_type].values -%}
            {% if artistcollection contains artist_profile.artist_collection.value.handle %}
                {% assign artistname = artist_profile.artist_name.value %}
                {% assign artistimgg = artist_profile.profile_image.value %}
                {% assign artistheritage = artist_profile.heritage.value %}
                {% assign artistquote = artist_profile.quote.value %}
                {% assign collection = artist_profile.artist_collection.value.handle %}
                {% assign artistcolllink = artist_profile.artist_collection.value.url %}
                {% assign artistpagelink = artist_profile.artist_page.value.url %}
                {% break %}
            {% endif %}
        {%- endfor -%}
    {% endpaginate %}
    {%- if artistname != blank -%}
        <div class="bfs-product__artist">
            {%- if artistimgg != nil -%}
                <!--image-->
                <div class="bfs-product__artist-image bfs-media-wrapper bfs-media-wrapper--round">
                    {%- liquid
                        assign desktop_img_url = artistimgg | image_url: width: 600
                        assign desktop_retina_img_url = artistimgg | image_url: width: 800
                        assign tablet_img_url = artistimgg | image_url: width: 600
                        assign tablet_retina_img_url = artistimgg | image_url: width: 800
                        assign mobile_img_url = artistimgg | image_url: width: 500
                        assign mobile_retina_img_url = artistimgg | image_url: width: 700
                    -%}
                    {%- render 'bfs-image',
                        desktop_img_url: desktop_img_url,
                        desktop_retina_img_url: desktop_retina_img_url,
                        tablet_img_url: tablet_img_url,
                        tablet_retina_img_url: tablet_retina_img_url,
                        mobile_img_url: mobile_img_url,
                        mobile_retina_img_url: mobile_retina_img_url,
                        alt: artistimgg.alt,
                        aspect_ratio: '1-1',
                        object_fit: 'cover',
                        object_position: 'center',
                        is_background: false,
                        lazy: true,
                        priority: false
                    -%}
                </div>
                <!--end image-->
            {%- endif -%}
            <!--content-->
            <div class="bfs-product__artist-content">
                {%- if artistname != blank -%}
                    <!--name-->
                    <h4 class="bfs-product__artist-name bfs-fs-b3">
                        {{- artistname -}}
                    </h4>
                    <!--end name-->
                {%- endif -%}
                {%- if artistheritage != blank -%}
                    <!--heritage-->
                    <p class="bfs-product__artist-heritage bfs-fs-b4">
                        {{- artistheritage -}}
                    </p>
                    <!--end heritage-->
                {%- endif -%}
                {%- if artistquote != blank -%}
                    <!--quote-->
                    <p class="bfs-product__artist-quote bfs-fs-b4">
                        {{- artistquote | truncate: 130 -}}
                    </p>
                    <!--end quote-->
                {%- endif -%}
                {%- if artistpagelink != blank -%}
                    <!--cta-->
                    <a class="bfs-product__artist-cta bfs-fs-b4" href="{{- artistpagelink -}}">
                        {{- 'product.artist.page_cta_label' | t -}}
                    </a>
                    <!--end cta-->
                {%- endif -%}
            </div>
            <!--end content-->
        </div>
    {%- endif -%}
    {%- if
        collection or
        section.settings.featured_links_title != blank or
        section.settings.featured_links_cta_1_label != blank or
        section.settings.featured_links_cta_2_label != blank or
        section.settings.featured_links_cta_3_label != blank or
        section.settings.featured_links_cta_4_label != blank or
        section.settings.featured_links_cta_5_label != blank or
        section.settings.featured_links_cta_6_label != blank
    -%}
        <!--BFS FEATURED LINKS-->
        <section class="bfs-section bfs-section--theme-light bfs-section--color-dark">
            <div class="bfs-container">
                <div class="bfs-featured-links">
                    {%- if section.settings.featured_links_title != blank -%}
                        <!--title-->
                        <h2 class="bfs-featured-links__title bfs-fs-a5">
                            {{- section.settings.featured_links_title -}}
                        </h2>
                        <!--end title-->
                    {%- endif -%}
                    <!--links-->
                    <ul class="bfs-featured-links__links bfs-featured-links__links--big-gap" role="list">
                        {%- if collection and collection.products_count -%}
                            {%- assign collection_cta_label = 'product.artist.collection_cta_label' | t -%}
                            <!--single-->
                            <li role="listitem">
                                {%- render 'bfs-button',
                                    label: collection_cta_label,
                                    url: collection.url,
                                    style: 'primary',
                                    bordered: false,
                                    size: 'medium',
                                    full_width: false,
                                    download: false,
                                    new_tab: false
                                -%}
                            </li>
                            <!--end single-->
                        {%- endif -%}
                        {%- if
                            section.settings.featured_links_cta_1_label != blank and
                            section.settings.featured_links_cta_1_url != blank
                        -%}
                            <!--single-->
                            <li role="listitem">
                                {%- render 'bfs-button',
                                    label: section.settings.featured_links_cta_1_label,
                                    url: section.settings.featured_links_cta_1_url,
                                    style: 'primary',
                                    bordered: true,
                                    size: 'medium',
                                    full_width: false,
                                    download: false,
                                    new_tab: false
                                -%}
                            </li>
                            <!--end single-->
                        {%- endif -%}
                        {%- if
                            section.settings.featured_links_cta_2_label != blank and
                            section.settings.featured_links_cta_2_url != blank
                        -%}
                            <!--single-->
                            <li role="listitem">
                                {%- render 'bfs-button',
                                    label: section.settings.featured_links_cta_2_label,
                                    url: section.settings.featured_links_cta_2_url,
                                    style: 'primary',
                                    bordered: true,
                                    size: 'medium',
                                    full_width: false,
                                    download: false,
                                    new_tab: false
                                -%}
                            </li>
                            <!--end single-->
                        {%- endif -%}
                        {%- if
                            section.settings.featured_links_cta_3_label != blank and
                            section.settings.featured_links_cta_3_url != blank
                        -%}
                            <!--single-->
                            <li role="listitem">
                                {%- render 'bfs-button',
                                    label: section.settings.featured_links_cta_3_label,
                                    url: section.settings.featured_links_cta_3_url,
                                    style: 'primary',
                                    bordered: true,
                                    size: 'medium',
                                    full_width: false,
                                    download: false,
                                    new_tab: false
                                -%}
                            </li>
                            <!--end single-->
                        {%- endif -%}
                        {%- if
                            section.settings.featured_links_cta_4_label != blank and
                            section.settings.featured_links_cta_4_url != blank
                        -%}
                            <!--single-->
                            <li role="listitem">
                                {%- render 'bfs-button',
                                    label: section.settings.featured_links_cta_4_label,
                                    url: section.settings.featured_links_cta_4_url,
                                    style: 'primary',
                                    bordered: true,
                                    size: 'medium',
                                    full_width: false,
                                    download: false,
                                    new_tab: false
                                -%}
                            </li>
                            <!--end single-->
                        {%- endif -%}
                        {%- if
                            section.settings.featured_links_cta_5_label != blank and
                            section.settings.featured_links_cta_5_url != blank
                        -%}
                            <!--single-->
                            <li role="listitem">
                                {%- render 'bfs-button',
                                    label: section.settings.featured_links_cta_5_label,
                                    url: section.settings.featured_links_cta_5_url,
                                    style: 'primary',
                                    bordered: true,
                                    size: 'medium',
                                    full_width: false,
                                    download: false,
                                    new_tab: false
                                -%}
                            </li>
                            <!--end single-->
                        {%- endif -%}
                        {%- if
                            section.settings.featured_links_cta_6_label != blank and
                            section.settings.featured_links_cta_6_url != blank
                        -%}
                            <!--single-->
                            <li role="listitem">
                                {%- render 'bfs-button',
                                    label: section.settings.featured_links_cta_6_label,
                                    url: section.settings.featured_links_cta_6_url,
                                    style: 'primary',
                                    bordered: true,
                                    size: 'medium',
                                    full_width: false,
                                    download: false,
                                    new_tab: false
                                -%}
                            </li>
                            <!--end single-->
                        {%- endif -%}
                    </ul>
                    <!--end links-->
                </div>
            </div>
        </section>
        <!--end BFS FEATURED LINKS-->
    {%- endif -%}
{% endif %}

{%- capture flickity_options -%}
    {
    "prevNextButtons": true,
    "pageDots": false,
    "wrapAround": false,
    "contain": true,
    "cellAlign": "center",
    "watchCSS": true,
    "dragThreshold": 8,
    "groupCells": true,
    "arrowShape": {"x0": 20, "x1": 60, "y1": 40, "x2": 60, "y2": 35, "x3": 25}
    }
{%- endcapture -%}

{% if collection and collection.products_count > 6 and template.name != "product" %}
    <section class="Section Section--spacingNormal" data-section-id="{{ section.id }}"
             data-section-type="related-products">
        <div class="SectionFooter" style="margin-top: 0">
            <a href="{{ collection.url }}" class="Button Button--primary">VIEW MORE BY THIS ARTIST</a>
        </div>
    </section>
{% endif %}

<script>
    $('.scrollme').on('click', function (el) {
        el.preventDefault();
        $('html, body').animate({
            scrollTop: $($(this).attr('href')).offset().top - 180
        }, 700, 'linear');
        // $('.naidoc-submenu-list.n-mobile ul').css( "display", "none" );
    });
    (function () {
        var one = document.querySelector(".bfs-product__artist");
        var block = document.querySelector("#bfs-artist-inject");
        block.appendChild(one);
    })();
</script>

{% schema %}
{
    "name": "NEW Artists 👀",
    "settings": [
        {
            "type": "text",
            "id": "letters",
            "label": "Letters",
            "info": "Comma-separated list of letters for sorting (e.g., A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z)"
        },
        {
            "type": "text",
            "id": "metaobject_type",
            "label": "Artist Metaobject Type",
            "default": "artist_profile",
            "info": "The metaobject type handle for artist profiles"
        },
        {
            "type": "header",
            "content": "BFS Featured Links"
        },
        {
            "type": "text",
            "id": "featured_links_title",
            "label": "Featured Links - Title"
        },
        {
            "type": "text",
            "id": "featured_links_cta_1_label",
            "label": "Featured Links - CTA 1 Label"
        },
        {
            "type": "url",
            "id": "featured_links_cta_1_url",
            "label": "Featured Links - CTA 1 URL"
        },
        {
            "type": "text",
            "id": "featured_links_cta_2_label",
            "label": "Featured Links - CTA 2 Label"
        },
        {
            "type": "url",
            "id": "featured_links_cta_2_url",
            "label": "Featured Links - CTA 2 URL"
        },
        {
            "type": "text",
            "id": "featured_links_cta_3_label",
            "label": "Featured Links - CTA 3 Label"
        },
        {
            "type": "url",
            "id": "featured_links_cta_3_url",
            "label": "Featured Links - CTA 3 URL"
        },
        {
            "type": "text",
            "id": "featured_links_cta_4_label",
            "label": "Featured Links - CTA 4 Label"
        },
        {
            "type": "url",
            "id": "featured_links_cta_4_url",
            "label": "Featured Links - CTA 4 URL"
        },
        {
            "type": "text",
            "id": "featured_links_cta_5_label",
            "label": "Featured Links - CTA 5 Label"
        },
        {
            "type": "url",
            "id": "featured_links_cta_5_url",
            "label": "Featured Links - CTA 5 URL"
        },
        {
            "type": "text",
            "id": "featured_links_cta_6_label",
            "label": "Featured Links - CTA 6 Label"
        },
        {
            "type": "url",
            "id": "featured_links_cta_6_url",
            "label": "Featured Links - CTA 6 URL"
        },
        {
            "type": "paragraph",
            "content": "⚠️ IMPORTANT: Make sure your artist metaobjects have 'Storefront access' enabled in Settings > Custom data > Metaobjects > [your metaobject type] > Access settings. Without this, metaobjects won't show on the frontend."
        }
    ],

}
{% endschema %}
