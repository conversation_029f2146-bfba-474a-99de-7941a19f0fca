<style>
  .Slideshow__ImageContainer iframe{
    position: absolute;
    left: 50%; /* % of surrounding element */
    top: 50%;
    transform: translate(-50%, -50%); /* % of current element */
    width: 100%;
    height: 100%;
  }
  

  
  .modal {
    transition: opacity 0.3s ease-in-out;
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1000;
  }

  .modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    padding: 50px;
    width: 80%;
    max-width: 600px;
  }
  a.But<PERSON>.Button--primary.rcl-popup {
    width: 100%;
    margin: 10px 0;
  }
  .rcl-content h3 {
    font-size: 30px;
    color: #106572;
    line-height: 1em;
    margin-bottom: 15px;
    text-align: center;
  }
  a.close-modal {
    float: right;
    width: 30px;
    text-align: center;
  }
  a.close-modal:focus, a.close-modal:hover {
    background-color: rgba(0,0,0,.3);
  }
  {% if section.settings.enable_80vh_height %}
    .Slideshow__ImageContainer {
      height: 80vh;
    }
  {% endif %}

</style>

{%- capture flickity_options -%}
{
  "prevNextButtons": false,
  "watchCSS": false,
  "resize": false,
  "setGallerySize": {% if section.settings.show_fullscreen %}false{% else %}true{% endif %},
  "adaptiveHeight": {% if section.settings.show_fullscreen %}false{% else %}true{% endif %},
  "wrapAround": true,
  "dragThreshold": 15,
  "pauseAutoPlayOnHover": false,
  "autoPlay": false,
  "pageDots": {% if section.blocks.size > 1 %}true{% else %}false{% endif %}
}
{%- endcapture -%}
{%- assign svg_close = '<svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 384 512"><!--! Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. --><path d="M376.6 84.5c11.3-13.6 9.5-33.8-4.1-45.1s-33.8-9.5-45.1 4.1L192 206 56.6 43.5C45.3 29.9 25.1 28.1 11.5 39.4S-3.9 70.9 7.4 84.5L150.3 256 7.4 427.5c-11.3 13.6-9.5 33.8 4.1 45.1s33.8 9.5 45.1-4.1L192 306 327.4 468.5c11.3 13.6 31.5 15.4 45.1 4.1s15.4-31.5 4.1-45.1L233.7 256 376.6 84.5z"/></svg>' -%}

<section id="section-{{ section.id }}" data-section-id="{{ section.id }}" data-section-type="slideshow">
  <div class="Slideshow {% if section.settings.show_fullscreen %}Slideshow--fullscreen{% endif %}">
    <div class="Slideshow__Carousel {% if section.settings.show_arrow %}Slideshow__Carousel--withScrollButton{% endif %} Carousel Carousel--fadeIn {% if section.settings.show_fullscreen %}Carousel--fixed{% endif %} Carousel--insideDots"
         data-flickity-config='{{ flickity_options }}'>
      {%- for block in section.blocks -%}
        <div id="Slide{{ block.id }}" class="Slideshow__Slide Carousel__Cell {% if forloop.first %}is-selected{% endif %}" {% if forloop.first %}style="visibility: visible"{% endif %} data-slide-index="{{ forloop.index0 }}" {{ block.shopify_attributes }}>
          {%- comment -%}
            Implementation note: this is an art driven image selection so it would have been better to be able to use the "picture" tag instead. However,
            as the mobile and desktop image could have different aspect ratio, the image allocation space would not have work properly without explicitly
            distinguishing the two images, hence the two containers.
          {%- endcomment -%}
          
          <style>
            {% if block.settings.heading_colour %}
            {% unless block.settings.heading_colour == "rgba(0,0,0,0)" %}
              #section-{{ section.id }} #Slide{{ block.id }} .SectionHeader .Heading{
				color: {{ block.settings.heading_colour }};
              }
            {% endunless %}
            {% endif %}
            
            {% if block.settings.button_1_colour %}
            {% unless block.settings.button_1_colour == "rgba(0,0,0,0)" %}
              #section-{{ section.id }} #Slide{{ block.id }} .Button {
                 border-color: {{block.settings.button_1_colour}};
              }
              #section-{{ section.id }} #Slide{{ block.id }} .Button:before {
                 background-color: {{block.settings.button_1_colour}};
              }
            
              #section-{{ section.id }} #Slide{{ block.id }} .Button:hover {
                 color: {{block.settings.button_1_colour}}!important;
              }
            
            {% endunless %}
            {% endif %}
            
            {% if block.settings.button_1_text_colour %}
            {% unless block.settings.button_1_text_colour == "rgba(0,0,0,0)" %}
               #section-{{ section.id }} #Slide{{ block.id }} .Button {
                 color: {{block.settings.button_1_text_colour}};
              }
            {% endunless %}
            {% endif %}

          </style>

          {%- if block.type == 'video_slide' -%}
            <style>
              .Slideshow__VideoContainer {
                position: relative;
                width: 100%;
                height: 100%;
                overflow: hidden;
              }

              .Slideshow__Video {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
              }

              .Slideshow__VideoContainer.AspectRatio {
                position: relative;
                height: 0;
                padding-bottom: calc(100% / var(--aspect-ratio));
              }

              .Slideshow__VideoContainer.Image--contrast::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.3);
                z-index: 1;
              }

              .Slideshow__VideoContainer .Slideshow__Content {
                position: absolute;
                z-index: 2;
              }
            </style>
            {%- assign mobile_video = block.settings.mobile_video | default: block.settings.desktop_video -%}
            {%- assign desktop_video = block.settings.desktop_video | default: block.settings.mobile_video -%}
            {%- assign video_aspect_ratio = 1.78 -%}
            {%- if mobile_video.aspect_ratio -%}
              {%- assign video_aspect_ratio = mobile_video.aspect_ratio -%}
            {%- elsif desktop_video.aspect_ratio -%}
              {%- assign video_aspect_ratio = desktop_video.aspect_ratio -%}
            {%- endif -%}

            <div class="Slideshow__ImageContainer {% if block.settings.apply_overlay %}Image--contrast{% endif %} {% unless section.settings.show_fullscreen %}AspectRatio{% endunless %} hidden-tablet-and-up"
                 style="{% unless section.settings.show_fullscreen %}--aspect-ratio: {{ video_aspect_ratio }};{% endunless %}"
                 {% if block.settings.open_modal %}data-slide-id="{{ block.id }}-mobile"{% endif %}
                 data-slide-duration="{{ block.settings.slide_duration | default: 5 }}">

              {%- if mobile_video -%}
                <video class="Slideshow__Image"
                       autoplay muted loop playsinline
                       style="width: 100%; height: 100%; object-fit: cover;">
                  {%- for source in mobile_video.sources -%}
                    {%- if source.mime_type == 'video/mp4' -%}
                      <source src="{{ source.url }}" type="video/mp4">
                    {%- endif -%}
                  {%- endfor -%}
                  {%- if mobile_video.sources.size > 0 -%}
                    <source src="{{ mobile_video.sources[0].url }}" type="{{ mobile_video.sources[0].mime_type }}">
                  {%- endif -%}
                </video>
              {%- endif -%}
            </div>

            <div class="Slideshow__ImageContainer {% if block.settings.apply_overlay %}Image--contrast{% endif %} {% unless section.settings.show_fullscreen %}AspectRatio{% endunless %} hidden-phone"
                 style="{% unless section.settings.show_fullscreen %}--aspect-ratio: {{ video_aspect_ratio }};{% endunless %}"
                 {% if block.settings.open_modal %}data-slide-id="{{ block.id }}-desktop"{% endif %}
                 data-slide-duration="{{ block.settings.slide_duration | default: 5 }}">

              {%- if desktop_video -%}
                <video class="Slideshow__Image"
                       autoplay muted loop playsinline
                       style="width: 100%; height: 100%; object-fit: cover;">
                  {%- for source in desktop_video.sources -%}
                    {%- if source.mime_type == 'video/mp4' -%}
                      <source src="{{ source.url }}" type="video/mp4">
                    {%- endif -%}
                  {%- endfor -%}
                  {%- if desktop_video.sources.size > 0 -%}
                    <source src="{{ desktop_video.sources[0].url }}" type="{{ desktop_video.sources[0].mime_type }}">
                  {%- endif -%}
                </video>
              {%- endif -%}
            </div>



              <div class="Slideshow__Content Slideshow__Content--{{ block.settings.content_position }}">
                <div class="Slideshow__ContentWrapper">
                  <div class="Container">
                    <header class="SectionHeader">
                      {%- if block.settings.subheading != blank -%}
                        <h3 class="SectionHeader__SubHeading Heading u-h6" style="{% if block.settings.heading_colour %}color: {{ block.settings.heading_colour }};{% endif %}">{{ block.settings.subheading | escape }}</h3>
                      {%- endif -%}

                      {%- if block.settings.title != blank -%}
                        <h2 class="SectionHeader__Heading Heading u-h1" style="{% if block.settings.heading_colour %}color: {{ block.settings.heading_colour }};{% endif %}">{{ block.settings.title | escape }}</h2>
                      {%- endif -%}

                      <div class="SectionHeader__ButtonWrapper">
                        {%- if block.settings.button_1_text != blank -%}
                          <a href="{{ block.settings.button_1_link }}" class="Button Button--primary" style="{% if block.settings.button_1_colour %}background-color: {{ block.settings.button_1_colour }};{% endif %} {% if block.settings.button_1_text_colour %}color: {{ block.settings.button_1_text_colour }};{% endif %}">{{ block.settings.button_1_text | escape }}</a>
                        {%- endif -%}

                        {%- if block.settings.button_2_text != blank -%}
                          <a href="{{ block.settings.button_2_link }}" class="Button Button--secondary">{{ block.settings.button_2_text | escape }}</a>
                        {%- endif -%}
                      </div>
                    </header>
                  </div>
                </div>
              </div>

              {% if block.settings.open_modal %}
                <div class="modal" id="modal-{{ block.id }}" data-modal-container>
                  <div class="modal-content">
                    <a href="#" class="close-modal" aria-label="button" role="button">
                      <span data-close-modal="true">
                        <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 384 512" style="pointer-events: none;">
                          <path d="M376.6 84.5c11.3-13.6 9.5-33.8-4.1-45.1s-33.8-9.5-45.1 4.1L192 206 56.6 43.5C45.3 29.9 25.1 28.1 11.5 39.4S-3.9 70.9 7.4 84.5L150.3 256 7.4 427.5c-11.3 13.6-9.5 33.8 4.1 45.1s33.8 9.5 45.1-4.1L192 306 327.4 468.5c11.3 13.6 31.5 15.4 45.1 4.1s15.4-31.5 4.1-45.1L233.7 256 376.6 84.5z"></path>
                        </svg>
                      </span>
                    </a>
                    {%- assign cta_heading = block.settings.cta_heading -%}
                    {%- if cta_heading != blank -%}
                      <div class="Container rcl-content">
                        <h3 class="sSectionHeader__SubHeading hHeading uu-h6 qualy">{{ cta_heading }}</h3>
                      </div>
                    {%- endif -%}
                    {%- for i in (1..5) -%}
                      {%- assign cta_key_name = "cta_" | append: i | append: "_name" -%}
                      {%- assign cta_key_link = "cta_" | append: i | append: "_link" -%}
                      {%- assign cta_name = block.settings[cta_key_name] -%}
                      {%- assign cta_link = block.settings[cta_key_link] -%}
                      {%- if cta_name and cta_link -%}
                        <a href="{{ cta_link }}" data-cta-link="true" class="Button Button--primary rcl-popup">{{ cta_name }}</a>
                      {%- endif -%}
                    {%- endfor -%}
                  </div>
                </div>
              {% endif %}
            </div>

          {%- else -%}
            {%- assign mobile_image = block.settings.mobile_image | default: block.settings.image -%}

            {%- if mobile_image -%}
            <div
              class="Slideshow__ImageContainer modal-trigger {% if block.settings.apply_overlay %}Image--contrast{% endif %} {% unless section.settings.show_fullscreen %}AspectRatio{% endunless %} hidden-tablet-and-up"
              style="{% unless section.settings.show_fullscreen %}--aspect-ratio: {{ mobile_image.aspect_ratio }};{% endunless %} background-image: url({{ mobile_image | img_url: '1x1', format: 'jpg' }})"
              {% if block.settings.open_modal %}data-slide-id="{{ block.id }}-mobile"class="open-modal"{% endif %}>
              <img class="Slideshow__Image Image--lazyLoad"
                     src="{{ mobile_image | img_url: '1x1' }}"
                     data-src="{{ mobile_image | img_url: '800x'}}"
                     alt="{{ mobile_image.alt | escape }}">

                <noscript>
                  <img class="Slideshow__Image" src="{{ mobile_image | img_url: '800x'}}" alt="{{ mobile_image.alt | escape }}">
                </noscript>

                {% if block.settings.open_modal %}
                    <div class="modal" id="modal-{{ block.id }}-mobile" data-modal-container>
                        <div class="modal-content">
                            <a href="#" class="close-modal" aria-label="button" role="button">
                                <span data-close-modal="true">
                                    <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 384 512" style="pointer-events: none;">
                                        <!--! Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2023 Fonticons, Inc. -->
                                        <path d="M376.6 84.5c11.3-13.6 9.5-33.8-4.1-45.1s-33.8-9.5-45.1 4.1L192 206 56.6 43.5C45.3 29.9 25.1 28.1 11.5 39.4S-3.9 70.9 7.4 84.5L150.3 256 7.4 427.5c-11.3 13.6-9.5 33.8 4.1 45.1s33.8 9.5 45.1-4.1L192 306 327.4 468.5c11.3 13.6 31.5 15.4 45.1 4.1s15.4-31.5 4.1-45.1L233.7 256 376.6 84.5z"></path>
                                    </svg>
                                </span>
                            </a>

                            {%- assign cta_heading = block.settings.cta_heading -%}
                            {%- if cta_heading != blank -%}
                                <div class="Container rcl-content">
                                    <h3 class="sSectionHeader__SubHeading hHeading uu-h6 qualy">{{ cta_heading }}</h3>
                                </div>
                            {%- endif -%}

                            {%- for i in (1..5) -%}
                                {%- assign cta_key_name = "cta_" | append: i | append: "_name" -%}
                                {%- assign cta_key_link = "cta_" | append: i | append: "_link" -%}
                                {%- assign cta_name = block.settings[cta_key_name] -%}
                                {%- assign cta_link = block.settings[cta_key_link] -%}
                                {%- if cta_name and cta_link -%}
                                    <a href="{{ cta_link }}" data-cta-link="true" class="Button Button--primary rcl-popup">{{ cta_name }}</a>
                                {%- endif -%}
                            {%- endfor -%}
                        </div>
                    </div>
                {% endif %}
            </div>
          {%- endif -%}
          
          
          
          {%- if block.settings.image -%}
             <div
              class="Slideshow__ImageContainer modal-trigger {% if block.settings.apply_overlay %}Image--contrast{% endif %} {% unless section.settings.show_fullscreen %}AspectRatio{% endunless %} hidden-phone"
              style="{% unless section.settings.show_fullscreen %}--aspect-ratio: {{ block.settings.image.aspect_ratio }};{% endunless %} background-image: url({{ block.settings.image | img_url: '1x1', format: 'jpg' }})"
              {% if block.settings.open_modal %}data-slide-id="{{ block.id }}-desktop"class="open-modal"{% endif %}>
              {% assign image_url = block.settings.image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' %}

              <img class="Slideshow__Image Image--lazyLoad hide-no-js"
                   data-src="{{ image_url }}"
                   data-optimumx="1.2"
                   data-widths="[400, 600, 700, 800, 1000, 1100, 1300, 1500]"
                   data-sizes="auto"
                   alt="{{ block.settings.image.alt | escape }}">

              <noscript>
                <img class="Slideshow__Image" src="{{ block.settings.image | img_url: '1000x' }}" alt="{{ block.settings.image.alt | escape }}">
              </noscript>

              {% if block.settings.open_modal %}
                <div
                  class="modal"
                  id="modal-{{ block.id }}-desktop"
                  data-modal-container>
                  <div class="modal-content">
                    <a href="#" class="close-modal" aria-label="button" role="button">
                        <span data-close-modal="true">
                            {{ svg_close }}
                        </span>
                    </a>

                    {%- assign cta_heading = block.settings.cta_heading -%}
                    {%- if cta_heading != blank -%}
                      <div class="Container rcl-content">
                        <h3 class="sSectionHeader__SubHeading hHeading uu-h6 qualy">{{ cta_heading }}</h3>
                      </div>
                    {%- endif -%}

                    {%- for i in (1..5) -%}
                      {%- assign cta_key_name = "cta_" | append: i | append: "_name" -%}
                      {%- assign cta_key_link = "cta_" | append: i | append: "_link" -%}
                      {%- assign cta_name = block.settings[cta_key_name] -%}
                      {%- assign cta_link = block.settings[cta_key_link] -%}
                      {%- if cta_name and cta_link -%}
                        <a href="{{ cta_link }}" data-cta-link="true" class="Button Button--primary rcl-popup">{{ cta_name }}</a>
                      {%- endif -%}
                    {%- endfor -%}
                  </div>
                </div>
              {% endif %} 
            </div>
          {%- else -%}
           
            <div class="Slideshow__ImageContainer {% if block.settings.apply_overlay %}Image--contrast{% endif %} {% if section.settings.show_fullscreen %}PlaceholderBackground{% endif %} PlaceholderSvg--dark">
              {%- capture placeholder -%}{% cycle 'lifestyle-1', 'lifestyle-2' %}{%- endcapture -%}
              {{ placeholder | placeholder_svg_tag: 'Slideshow__Image PlaceholderBackground__Svg' }}
            </div>
           
        


          {%- endif -%}
          {%- endif -%}

          <div class="Slideshow__Content Slideshow__Content--{{ block.settings.content_position }}">
            {%- if block.settings.subheading != blank or block.settings.title != blank or block.settings.button_1_text != blank -%}
              <header class="SectionHeaderr">
                {%- if block.settings.subheading != blank -%}
                  <h3 class="SectionHeader__SubHeading Heading u-h6">{{ block.settings.subheading | escape }}</h3>
                {%- endif -%}

                {%- if block.settings.title != blank -%}
                  <h2 class="SectionHeader__Heading SectionHeader__Heading--emphasize Heading u-h1">{{ block.settings.title | escape }}</h2>
                {%- endif -%}

                {%- if block.settings.button_1_text != blank and block.settings.button_2_text != blank -%}
                  {%- assign has_two_buttons = true -%}
                {%- else -%}
                  {%- assign has_two_buttons = false -%}
                {%- endif -%}

                {%- if block.settings.button_1_text != blank or block.settings.button_2_text != blank -%}
                  <div class="SSectionHeader__ButtonWrapper">
                    <div class="ButtonGroup ButtonGroup--spacingSmall {% if has_two_buttons %}ButtonGroup--sameSize{% endif %}">
                      {%- if block.settings.button_1_text != blank -%}
                        <a href="{{ block.settings.button_1_link }}" class="ButtonGroup__Item Button">{{ block.settings.button_1_text | escape }}</a>
                      {%- endif -%}

                      {%- if block.settings.button_2_text != blank -%}
                        <a href="{{ block.settings.button_2_link }}" class="Slideshow__Button ButtonGroup__Item Button">{{ block.settings.button_2_text | escape }}</a>
                      {%- endif -%}

                      {%- if block.settings.button_3_text != blank -%}
                      <style>
                        @media screen and (min-width: 641px) {
                          #Slide{{ block.id }} .ButtonGroup--sameSize {
                            display: inline-grid;
                            grid-template-columns: 1fr 1fr 1fr;
                           }
                        }
                      </style>
                        <a href="{{ block.settings.button_3_link }}" class="Slideshow__Button ButtonGroup__Item Button">{{ block.settings.button_3_text | escape }}</a>
                      {%- endif -%}
                    </div>
                  </div>
                {%- endif -%}
                
                
              </header>
            {%- endif -%}
          </div>
        </div>
      {%- endfor -%}
    </div>


    {% comment %}
    <div style="background-image: linear-gradient(#D4D4D4, #ffffff); height: 9px; width: 100%; margin-top: -25px;">
      <center>
      </center>
    </div>
    {% endcomment %}
    
    {%- if section.settings.show_arrow -%}
      <button data-href="#section-{{ section.id }}-end" class="Slideshow__ScrollButton RoundButton RoundButton--medium" data-animate-bottom>
        {%- include 'icon' with 'arrow-bottom' -%}
      </button>
    {%- endif -%}
  </div>

  <span id="section-{{ section.id }}-end" class="Anchor"></span>
</section>

<style>
  #section-{{ section.id }} .Heading,
   #section-{{ section.id }} .flickity-page-dots {
    color: {{ section.settings.text_color }};
  }

  #section-{{ section.id }} .Button {
    color: {{ section.settings.button_color }};
    border-color: {{ section.settings.text_color }};
  }

  #section-{{ section.id }} .Button::before {
    background-color: {{ section.settings.text_color }};
  }

  @media (-moz-touch-enabled: 0), (hover: hover) {
    #section-{{ section.id }} .Button:hover {
      color: {{ section.settings.text_color }};
    }
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  function showModal(modal) {
      modal.style.display = 'block';
  }

  function hideModal(modal) {
      modal.style.display = 'none';
  }

  function initCustomSlideshow() {
    console.log('🎬 Initializing custom slideshow');
    const carousel = document.querySelector('#section-{{ section.id }} .Slideshow__Carousel');
    if (!carousel) {
      console.log('❌ No carousel found');
      return;
    }

    console.log('✅ Carousel found:', carousel);
    console.log('🔧 Autoplay setting:', {{ section.settings.autoplay | json }});

    if (!{{ section.settings.autoplay | json }}) {
      console.log('⏸️ Autoplay disabled, skipping custom timing');
      return;
    }

    console.log('🎯 Autoplay enabled, setting up custom timing for all slides');

    let autoplayTimer;
    let flickityInstance;

    function getSlideTimeout(slideIndex) {
      const slides = carousel.querySelectorAll('.Slideshow__Slide');
      const slide = slides[slideIndex];
      console.log(`⏱️ Getting timeout for slide ${slideIndex}:`, slide);

      if (!slide) {
        console.log('❌ No slide found, using default timeout');
        return {{ section.settings.cycle_speed | times: 1000 }};
      }

      const videoContainers = slide.querySelectorAll('.Slideshow__VideoContainer[data-slide-duration]');
      if (videoContainers.length > 0) {
        const duration = parseInt(videoContainers[0].getAttribute('data-slide-duration')) || 5;
        console.log(`🎥 Video slide found, duration: ${duration}s`);
        return duration * 1000;
      }

      console.log(`🖼️ Image slide, using section speed: {{ section.settings.cycle_speed }}s`);
      return {{ section.settings.cycle_speed | times: 1000 }};
    }

    function scheduleNextSlide() {
      console.log('📅 Scheduling next slide');
      clearTimeout(autoplayTimer);

      if (!flickityInstance) {
        flickityInstance = Flickity.data(carousel);
        console.log('🎠 Getting Flickity instance:', flickityInstance);
      }

      if (flickityInstance) {
        const currentIndex = flickityInstance.selectedIndex;
        const timeout = getSlideTimeout(currentIndex);
        console.log(`⏰ Setting timeout for ${timeout}ms`);

        autoplayTimer = setTimeout(() => {
          console.log('▶️ Moving to next slide');
          if (flickityInstance) {
            flickityInstance.next();
          }
        }, timeout);
      }
    }

    function initFlickityEvents() {
      console.log('🎪 Initializing Flickity events');
      flickityInstance = Flickity.data(carousel);

      if (!flickityInstance) {
        console.log('⏳ Flickity not ready, retrying...');
        setTimeout(initFlickityEvents, 100);
        return;
      }

      console.log('✅ Flickity ready:', flickityInstance);

      carousel.addEventListener('select.flickity', function() {
        console.log('🎯 Slide selected, scheduling next');
        scheduleNextSlide();
      });

      carousel.addEventListener('dragStart.flickity', function() {
        console.log('👆 Drag started, clearing timer');
        clearTimeout(autoplayTimer);
      });

      carousel.addEventListener('dragEnd.flickity', function() {
        console.log('👆 Drag ended, resuming autoplay');
        scheduleNextSlide();
      });

      scheduleNextSlide();
    }

    // Wait for Flickity to be ready
    setTimeout(() => {
      if (carousel.classList.contains('flickity-enabled')) {
        console.log('🎠 Flickity already enabled');
        initFlickityEvents();
      } else {
        console.log('⏳ Waiting for Flickity ready event');
        carousel.addEventListener('ready.flickity', initFlickityEvents);
      }
    }, 500);
  }

  initCustomSlideshow();

  setTimeout(() => {
    const videos = document.querySelectorAll('#section-{{ section.id }} .Slideshow__Video');
    videos.forEach(video => {
      video.addEventListener('loadeddata', () => {
        video.play().catch(e => console.log('Video autoplay failed:', e));
      });
      if (video.readyState >= 3) {
        video.play().catch(e => console.log('Video autoplay failed:', e));
      }
    });
  }, 500);

  document.body.addEventListener('click', function(e) {
      // Check if the clicked element is a CTA link
      if (e.target.hasAttribute('data-cta-link')) {
          return;
      }

      // Handling modal opening
      let triggerElement = e.target.closest('.modal-trigger');
      if (triggerElement) {
          e.preventDefault();
          let slideId = triggerElement.getAttribute('data-slide-id');
          let modalId = 'modal-' + slideId;
          let modal = document.getElementById(modalId);
          if (modal) {
              showModal(modal);
          }
      }

      // Handling modal closing
      let isCloseButton = e.target.classList.contains('close-modal') || 
                          e.target.closest('.close-modal');
      let isModalBackground = e.target.hasAttribute('data-modal-container') && 
                              !e.target.closest('.modal-content');

      if (isCloseButton || isModalBackground) {
          e.preventDefault();

          // Close all modals
          let modals = document.querySelectorAll('.modal');
          modals.forEach(function(modal) {
              hideModal(modal);
          });
      }
  });

  // Close modal on Escape key press
  document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
          let modals = document.querySelectorAll('.modal');
          modals.forEach(function(modal) {
              hideModal(modal);
          });
      }
  });
});

</script>
  
{% schema %}
{
  "name": "Slideshow",
  "class": "shopify-section--slideshow",
  "max_blocks": 15,
  "settings": [
    {
      "type": "checkbox",
      "id": "show_fullscreen",
      "label": "Show full-screen images",
      "info": "If enabled, images will be resized to fit the entire screen.",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_arrow",
      "label": "Show bottom arrow",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Auto rotate between slides",
      "default": true
    },
    {
      "type": "range",
      "id": "cycle_speed",
      "min": 3,
      "max": 8,
      "step": 1,
      "unit": "sec",
      "label": "Change slides every",
      "default": 5
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "button_color",
      "label": "Button text",
      "default": "#363636"
    }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "Slide",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "1560 x 1050px jpg recommended"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "Mobile image",
          "info": "750 x 1100px jpg recommended. If none is set, desktop image will be cropped."
        },
        {
          "type": "checkbox",
          "id": "enable_80vh_height",
          "label": "Enable fullscreen height for the main image",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "apply_overlay",
          "label": "Apply overlay on image",
          "info": "This can improve text visibility.",
          "default": true
        },
        {
          "type": "select",
          "id": "content_position",
          "label": "Desktop content position",
          "options": [
            {
              "value": "middleLeft",
              "label": "Middle left"
            },
            {
              "value": "middleCenter",
              "label": "Middle center"
            },
            {
              "value": "bottomLeft",
              "label": "Bottom left"
            },
            {
              "value": "bottomCenter",
              "label": "Bottom center"
            }
          ],
          "default": "bottomLeft"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Sub-heading",
          "default": "Slide title"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Tell your story"
        },
        {
          "type": "color",
          "id": "heading_colour",
          "label": "Heading Colour"
        },
        {
          "type": "header",
          "content": "Button 1"
        },
        {
          "type": "text",
          "id": "button_1_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "button_1_link",
          "label": "Link"
        },
        {
          "type": "color",
          "id": "button_1_colour",
          "label": "Button Colour"
        },
        {
          "type": "color",
          "id": "button_1_text_colour",
          "label": "Button Text Colour"
        },
        {
          "type": "header",
          "content": "Button 2"
        },
        {
          "type": "text",
          "id": "button_2_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "button_2_link",
          "label": "Link"
        },
        {
          "type": "header",
          "content": "Button 3"
        },
        {
          "type": "text",
          "id": "button_3_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "button_3_link",
          "label": "Link"
        },

        {
            "type": "checkbox",
            "id": "open_modal",
            "label": "Open in popup?",
            "default": false
          }, {
            "type": "header",
            "content": "Modal CTAs (Only if popup checked)"
          }, {
            "type": "text",
            "id": "cta_heading",
            "label": "Heading for CTA section"
          }, {
            "type": "text",
            "id": "cta_1_name",
            "label": "CTA Name 1"
          }, {
            "type": "url",
            "id": "cta_1_link",
            "label": "CTA Link 1"
          }, {
            "type": "text",
            "id": "cta_2_name",
            "label": "CTA Name 2"
          }, {
            "type": "url",
            "id": "cta_2_link",
            "label": "CTA Link 2"
          }, {
            "type": "text",
            "id": "cta_3_name",
            "label": "CTA Name 3"
          }, {
            "type": "url",
            "id": "cta_3_link",
            "label": "CTA Link 3"
          }, {
            "type": "text",
            "id": "cta_4_name",
            "label": "CTA Name 4"
          }, {
            "type": "url",
            "id": "cta_4_link",
            "label": "CTA Link 4"
          }, {
            "type": "text",
            "id": "cta_5_name",
            "label": "CTA Name 5"
          }, {
            "type": "url",
            "id": "cta_5_link",
            "label": "CTA Link 5"
        }
      ]
    },
    {
      "type": "video_slide",
      "name": "Video Slide",
      "settings": [
        {
          "type": "video",
          "id": "desktop_video",
          "label": "Desktop Video"
        },
        {
          "type": "video",
          "id": "mobile_video",
          "label": "Mobile Video"
        },
        {
          "type": "range",
          "id": "slide_duration",
          "label": "Slide Duration (seconds)",
          "default": 5,
          "min": 1,
          "max": 60,
          "step": 1,
          "unit": "sec"
        },
        {
          "type": "checkbox",
          "id": "apply_overlay",
          "label": "Apply overlay on video",
          "info": "This can improve text visibility.",
          "default": true
        },
        {
          "type": "select",
          "id": "content_position",
          "label": "Desktop content position",
          "options": [
            {
              "value": "middleLeft",
              "label": "Middle left"
            },
            {
              "value": "middleCenter",
              "label": "Middle center"
            },
            {
              "value": "bottomLeft",
              "label": "Bottom left"
            },
            {
              "value": "bottomCenter",
              "label": "Bottom center"
            }
          ],
          "default": "bottomLeft"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Sub-heading"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading"
        },
        {
          "type": "color",
          "id": "heading_colour",
          "label": "Heading Colour"
        },
        {
          "type": "header",
          "content": "Button 1"
        },
        {
          "type": "text",
          "id": "button_1_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "button_1_link",
          "label": "Link"
        },
        {
          "type": "color",
          "id": "button_1_colour",
          "label": "Button Colour"
        },
        {
          "type": "color",
          "id": "button_1_text_colour",
          "label": "Button Text Colour"
        },
        {
          "type": "header",
          "content": "Button 2"
        },
        {
          "type": "text",
          "id": "button_2_text",
          "label": "Text"
        },
        {
          "type": "url",
          "id": "button_2_link",
          "label": "Link"
        },
        {
          "type": "checkbox",
          "id": "open_modal",
          "label": "Open in popup?",
          "default": false
        },
        {
          "type": "header",
          "content": "Modal CTAs (Only if popup checked)"
        },
        {
          "type": "text",
          "id": "cta_heading",
          "label": "Heading for CTA section"
        },
        {
          "type": "text",
          "id": "cta_1_name",
          "label": "CTA Name 1"
        },
        {
          "type": "url",
          "id": "cta_1_link",
          "label": "CTA Link 1"
        },
        {
          "type": "text",
          "id": "cta_2_name",
          "label": "CTA Name 2"
        },
        {
          "type": "url",
          "id": "cta_2_link",
          "label": "CTA Link 2"
        },
        {
          "type": "text",
          "id": "cta_3_name",
          "label": "CTA Name 3"
        },
        {
          "type": "url",
          "id": "cta_3_link",
          "label": "CTA Link 3"
        },
        {
          "type": "text",
          "id": "cta_4_name",
          "label": "CTA Name 4"
        },
        {
          "type": "url",
          "id": "cta_4_link",
          "label": "CTA Link 4"
        },
        {
          "type": "text",
          "id": "cta_5_name",
          "label": "CTA Name 5"
        },
        {
          "type": "url",
          "id": "cta_5_link",
          "label": "CTA Link 5"
        }
      ]
    }
  ],
  "presets": [
    {
      "category": "Image",
      "name": "Slideshow",
      "settings": {},
      "blocks": [
        {
          "type": "image"
        },
        {
          "type": "image"
        }
      ]
    }
  ]
}
{% endschema %}