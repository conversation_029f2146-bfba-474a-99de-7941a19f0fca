<style>
  .VideoSection {
    position: relative;
    width: 100%;
    overflow: hidden;
  }

  .VideoSection__Container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%;
  }

  .VideoSection__Video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }

  .VideoSection__Content {
    position: absolute;
    z-index: 2;
    width: 90%;
    max-width: 600px;
  }

  .VideoSection__Content--middleLeft {
    top: 50%;
    left: 5%;
    transform: translateY(-50%);
    text-align: left;
  }

  .VideoSection__Content--middleCenter {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
  }

  .VideoSection__Content--bottomLeft {
    bottom: 10%;
    left: 5%;
    text-align: left;
  }

  .VideoSection__Content--bottomCenter {
    bottom: 10%;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
  }

  .VideoSection--overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
  }

  .VideoSection--small .VideoSection__Container {
    padding-bottom: 30vh;
  }

  .VideoSection--normal .VideoSection__Container {
    padding-bottom: 50vh;
  }

  .VideoSection--large .VideoSection__Container {
    padding-bottom: 70vh;
  }

  .VideoSection--fullscreen .VideoSection__Container {
    padding-bottom: 100vh;
  }

  @media screen and (max-width: 640px) {
    .VideoSection__Video--desktop {
      display: none;
    }
  }

  @media screen and (min-width: 641px) {
    .VideoSection__Video--mobile {
      display: none;
    }
  }

  .VideoSection__PlayButton {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 20px auto;
  }

  .VideoSection__PlayButton:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
  }

  .VideoSection__PlayButton svg {
    width: 30px;
    height: 30px;
    fill: #333;
    margin-left: 3px;
  }
</style>

<section class="Section VideoSection {% if section.settings.apply_overlay %}VideoSection--overlay{% endif %} {% if section.settings.section_size != 'normal' %}VideoSection--{{ section.settings.section_size }}{% endif %}" id="section-{{ section.id }}">
  <div class="VideoSection__Container">
    {%- assign mobile_video = section.settings.mobile_video | default: section.settings.desktop_video -%}
    {%- assign desktop_video = section.settings.desktop_video | default: section.settings.mobile_video -%}
    {%- assign mobile_gif = section.settings.mobile_gif | default: section.settings.desktop_gif -%}
    {%- assign desktop_gif = section.settings.desktop_gif | default: section.settings.mobile_gif -%}

    {%- if mobile_video -%}
      <video class="VideoSection__Video VideoSection__Video--mobile"
             autoplay muted loop playsinline
             {% if section.settings.enable_controls %}controls{% endif %}>
        {%- for source in mobile_video.sources -%}
          <source src="{{ source.url }}" type="{{ source.mime_type }}" />
        {%- endfor -%}
      </video>
    {%- endif -%}

    {%- if desktop_video -%}
      <video class="VideoSection__Video VideoSection__Video--desktop"
             autoplay muted loop playsinline
             {% if section.settings.enable_controls %}controls{% endif %}>
        {%- for source in desktop_video.sources -%}
          <source src="{{ source.url }}" type="{{ source.mime_type }}" />
        {%- endfor -%}
      </video>
    {%- endif -%}

    {%- if mobile_gif -%}
      <img class="VideoSection__Video VideoSection__Video--mobile"
           src="{{ mobile_gif | img_url: '1000x' }}"
           alt="{{ mobile_gif.alt | escape }}">
    {%- endif -%}

    {%- if desktop_gif -%}
      <img class="VideoSection__Video VideoSection__Video--desktop"
           src="{{ desktop_gif | img_url: '2000x' }}"
           alt="{{ desktop_gif.alt | escape }}">
    {%- endif -%}

    {%- if section.settings.fallback_image -%}
      <img class="VideoSection__Video" 
           src="{{ section.settings.fallback_image | img_url: '1920x' }}" 
           alt="{{ section.settings.fallback_image.alt | escape }}"
           style="display: none;">
    {%- endif -%}

    {%- if section.settings.title != blank or section.settings.subtitle != blank or section.settings.button_1_text != blank or section.settings.button_2_text != blank -%}
      <div class="VideoSection__Content VideoSection__Content--{{ section.settings.content_position | default: 'middleCenter' }}" {% if section.settings.open_modal %}data-slide-id="{{ section.id }}" class="open-modal"{% endif %}>
        {%- if section.settings.subtitle != blank -%}
          <h3 class="SectionHeader__SubHeading Heading u-h6" style="color: {{ section.settings.text_color }};">{{ section.settings.subtitle | escape }}</h3>
        {%- endif -%}

        {%- if section.settings.title != blank -%}
          <h2 class="SectionHeader__Heading Heading u-h1" style="color: {{ section.settings.text_color }};">{{ section.settings.title | escape }}</h2>
        {%- endif -%}

        {%- if section.settings.button_1_text != blank -%}
          <a href="{{ section.settings.button_1_link }}" class="Button Button--primary" style="margin-top: 20px; {% if section.settings.button_1_colour %}background-color: {{ section.settings.button_1_colour }};{% endif %} {% if section.settings.button_1_text_colour %}color: {{ section.settings.button_1_text_colour }};{% endif %}">{{ section.settings.button_1_text | escape }}</a>
        {%- endif -%}

        {%- if section.settings.button_2_text != blank -%}
          <a href="{{ section.settings.button_2_link }}" class="Button Button--secondary" style="margin-top: 20px; margin-left: 10px;">{{ section.settings.button_2_text | escape }}</a>
        {%- endif -%}

        {%- if section.settings.show_play_button -%}
          <button class="VideoSection__PlayButton" onclick="toggleVideoPlayback('{{ section.id }}')">
            <svg viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </button>
        {%- endif -%}
      </div>
    {%- endif -%}

    {% if section.settings.open_modal %}
      <div class="modal" id="modal-{{ section.id }}" data-modal-container>
          <div class="modal-content">
              <a href="#" class="close-modal" aria-label="button" role="button">
                  <span data-close-modal="true">
                      <svg xmlns="http://www.w3.org/2000/svg" height="1em" viewBox="0 0 384 512" style="pointer-events: none;">
                          <path d="M376.6 84.5c11.3-13.6 9.5-33.8-4.1-45.1s-33.8-9.5-45.1 4.1L192 206 56.6 43.5C45.3 29.9 25.1 28.1 11.5 39.4S-3.9 70.9 7.4 84.5L150.3 256 7.4 427.5c-11.3 13.6-9.5 33.8 4.1 45.1s33.8 9.5 45.1-4.1L192 306 327.4 468.5c11.3 13.6 31.5 15.4 45.1 4.1s15.4-31.5 4.1-45.1L233.7 256 376.6 84.5z"></path>
                      </svg>
                  </span>
              </a>
              <div class="Container rcl-content">
                  {%- if section.settings.cta_heading != blank -%}
                    <h3 class="sSectionHeader__SubHeading hHeading uu-h6 qualy">{{ section.settings.cta_heading }}</h3>
                  {%- endif -%}

                  {%- if section.settings.cta_1_name != blank -%}
                    <a href="{{ section.settings.cta_1_link }}" class="Button Button--primary" style="margin: 10px;">{{ section.settings.cta_1_name | escape }}</a>
                  {%- endif -%}

                  {%- if section.settings.cta_2_name != blank -%}
                    <a href="{{ section.settings.cta_2_link }}" class="Button Button--primary" style="margin: 10px;">{{ section.settings.cta_2_name | escape }}</a>
                  {%- endif -%}

                  {%- if section.settings.cta_3_name != blank -%}
                    <a href="{{ section.settings.cta_3_link }}" class="Button Button--primary" style="margin: 10px;">{{ section.settings.cta_3_name | escape }}</a>
                  {%- endif -%}

                  {%- if section.settings.cta_4_name != blank -%}
                    <a href="{{ section.settings.cta_4_link }}" class="Button Button--primary" style="margin: 10px;">{{ section.settings.cta_4_name | escape }}</a>
                  {%- endif -%}

                  {%- if section.settings.cta_5_name != blank -%}
                    <a href="{{ section.settings.cta_5_link }}" class="Button Button--primary" style="margin: 10px;">{{ section.settings.cta_5_name | escape }}</a>
                  {%- endif -%}
              </div>
          </div>
      </div>
    {% endif %}
  </div>
</section>

<script>
function toggleVideoPlayback(sectionId) {
  const section = document.getElementById('section-' + sectionId);
  const videos = section.querySelectorAll('video');
  
  videos.forEach(video => {
    if (video.paused) {
      video.play();
    } else {
      video.pause();
    }
  });
}

document.addEventListener('DOMContentLoaded', function() {
  const videos = document.querySelectorAll('#section-{{ section.id }} video');
  
  videos.forEach(video => {
    video.addEventListener('error', function() {
      const fallbackImage = video.parentElement.querySelector('img');
      if (fallbackImage) {
        video.style.display = 'none';
        fallbackImage.style.display = 'block';
      }
    });
  });
});
</script>

{% schema %}
{
  "name": "Video Section",
  "settings": [
    {
      "type": "select",
      "id": "section_size",
      "label": "Section size",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "normal",
          "label": "Normal"
        },
        {
          "value": "large",
          "label": "Large"
        },
        {
          "value": "fullscreen",
          "label": "Fullscreen"
        }
      ],
      "default": "normal"
    },
    {
      "type": "video",
      "id": "desktop_video",
      "label": "Desktop Video",
      "info": "Upload MP4 or GIF file for desktop devices"
    },
    {
      "type": "video",
      "id": "mobile_video",
      "label": "Mobile Video",
      "info": "Upload MP4 or GIF file for mobile devices. If not provided, desktop video will be used"
    },
    {
      "type": "image_picker",
      "id": "desktop_gif",
      "label": "Desktop GIF"
    },
    {
      "type": "image_picker",
      "id": "mobile_gif",
      "label": "Mobile GIF"
    },
    {
      "type": "image_picker",
      "id": "fallback_image",
      "label": "Fallback Image",
      "info": "Shown if video fails to load"
    },
    {
      "type": "checkbox",
      "id": "apply_overlay",
      "label": "Apply dark overlay",
      "info": "Improves text readability over video",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_controls",
      "label": "Show video controls",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_play_button",
      "label": "Show play/pause button",
      "default": false
    },
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "select",
      "id": "content_position",
      "label": "Content position",
      "options": [
        {
          "value": "middleLeft",
          "label": "Middle left"
        },
        {
          "value": "middleCenter",
          "label": "Middle center"
        },
        {
          "value": "bottomLeft",
          "label": "Bottom left"
        },
        {
          "value": "bottomCenter",
          "label": "Bottom center"
        }
      ],
      "default": "middleCenter"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#ffffff"
    },
    {
      "type": "header",
      "content": "Button 1"
    },
    {
      "type": "text",
      "id": "button_1_text",
      "label": "Text"
    },
    {
      "type": "url",
      "id": "button_1_link",
      "label": "Link"
    },
    {
      "type": "color",
      "id": "button_1_colour",
      "label": "Button Colour"
    },
    {
      "type": "color",
      "id": "button_1_text_colour",
      "label": "Button Text Colour"
    },
    {
      "type": "header",
      "content": "Button 2"
    },
    {
      "type": "text",
      "id": "button_2_text",
      "label": "Text"
    },
    {
      "type": "url",
      "id": "button_2_link",
      "label": "Link"
    },
    {
      "type": "checkbox",
      "id": "open_modal",
      "label": "Open in popup?",
      "default": false
    },
    {
      "type": "header",
      "content": "Modal CTAs (Only if popup checked)"
    },
    {
      "type": "text",
      "id": "cta_heading",
      "label": "Heading for CTA section"
    },
    {
      "type": "text",
      "id": "cta_1_name",
      "label": "CTA Name 1"
    },
    {
      "type": "url",
      "id": "cta_1_link",
      "label": "CTA Link 1"
    },
    {
      "type": "text",
      "id": "cta_2_name",
      "label": "CTA Name 2"
    },
    {
      "type": "url",
      "id": "cta_2_link",
      "label": "CTA Link 2"
    },
    {
      "type": "text",
      "id": "cta_3_name",
      "label": "CTA Name 3"
    },
    {
      "type": "url",
      "id": "cta_3_link",
      "label": "CTA Link 3"
    },
    {
      "type": "text",
      "id": "cta_4_name",
      "label": "CTA Name 4"
    },
    {
      "type": "url",
      "id": "cta_4_link",
      "label": "CTA Link 4"
    },
    {
      "type": "text",
      "id": "cta_5_name",
      "label": "CTA Name 5"
    },
    {
      "type": "url",
      "id": "cta_5_link",
      "label": "CTA Link 5"
    }
  ],
  "presets": [
    {
      "name": "Video Section",
      "category": "Video"
    }
  ]
}
{% endschema %}
