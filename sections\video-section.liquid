<style>
  .VideoSection {
    position: relative;
    width: 100%;
    overflow: hidden;
  }

  .VideoSection__Container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%;
  }

  .VideoSection__Video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }

  .VideoSection__Content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 2;
    width: 90%;
    max-width: 600px;
  }

  .VideoSection--overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
  }

  .VideoSection--small .VideoSection__Container {
    padding-bottom: 30vh;
  }

  .VideoSection--normal .VideoSection__Container {
    padding-bottom: 50vh;
  }

  .VideoSection--large .VideoSection__Container {
    padding-bottom: 70vh;
  }

  .VideoSection--fullscreen .VideoSection__Container {
    padding-bottom: 100vh;
  }

  @media screen and (max-width: 640px) {
    .VideoSection__Video--desktop {
      display: none;
    }
  }

  @media screen and (min-width: 641px) {
    .VideoSection__Video--mobile {
      display: none;
    }
  }

  .VideoSection__PlayButton {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 20px auto;
  }

  .VideoSection__PlayButton:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
  }

  .VideoSection__PlayButton svg {
    width: 30px;
    height: 30px;
    fill: #333;
    margin-left: 3px;
  }
</style>

<section class="Section VideoSection {% if section.settings.apply_overlay %}VideoSection--overlay{% endif %} {% if section.settings.section_size != 'normal' %}VideoSection--{{ section.settings.section_size }}{% endif %}" id="section-{{ section.id }}">
  <div class="VideoSection__Container">
    {%- if section.settings.mobile_video -%}
      <video class="VideoSection__Video VideoSection__Video--mobile" 
             autoplay muted loop playsinline
             {% if section.settings.enable_controls %}controls{% endif %}>
        <source src="{{ section.settings.mobile_video }}" type="video/mp4">
      </video>
    {%- endif -%}

    {%- if section.settings.desktop_video -%}
      <video class="VideoSection__Video VideoSection__Video--desktop" 
             autoplay muted loop playsinline
             {% if section.settings.enable_controls %}controls{% endif %}>
        <source src="{{ section.settings.desktop_video }}" type="video/mp4">
      </video>
    {%- endif -%}

    {%- if section.settings.fallback_image -%}
      <img class="VideoSection__Video" 
           src="{{ section.settings.fallback_image | img_url: '1920x' }}" 
           alt="{{ section.settings.fallback_image.alt | escape }}"
           style="display: none;">
    {%- endif -%}

    {%- if section.settings.title != blank or section.settings.subtitle != blank or section.settings.button_text != blank -%}
      <div class="VideoSection__Content">
        {%- if section.settings.subtitle != blank -%}
          <h3 class="SectionHeader__SubHeading Heading u-h6" style="color: {{ section.settings.text_color }};">{{ section.settings.subtitle | escape }}</h3>
        {%- endif -%}

        {%- if section.settings.title != blank -%}
          <h2 class="SectionHeader__Heading Heading u-h1" style="color: {{ section.settings.text_color }};">{{ section.settings.title | escape }}</h2>
        {%- endif -%}

        {%- if section.settings.button_text != blank -%}
          <a href="{{ section.settings.button_link }}" class="Button Button--primary" style="margin-top: 20px;">{{ section.settings.button_text | escape }}</a>
        {%- endif -%}

        {%- if section.settings.show_play_button -%}
          <button class="VideoSection__PlayButton" onclick="toggleVideoPlayback('{{ section.id }}')">
            <svg viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </button>
        {%- endif -%}
      </div>
    {%- endif -%}
  </div>
</section>

<script>
function toggleVideoPlayback(sectionId) {
  const section = document.getElementById('section-' + sectionId);
  const videos = section.querySelectorAll('video');
  
  videos.forEach(video => {
    if (video.paused) {
      video.play();
    } else {
      video.pause();
    }
  });
}

document.addEventListener('DOMContentLoaded', function() {
  const videos = document.querySelectorAll('#section-{{ section.id }} video');
  
  videos.forEach(video => {
    video.addEventListener('error', function() {
      const fallbackImage = video.parentElement.querySelector('img');
      if (fallbackImage) {
        video.style.display = 'none';
        fallbackImage.style.display = 'block';
      }
    });
  });
});
</script>

{% schema %}
{
  "name": "Video Section",
  "settings": [
    {
      "type": "select",
      "id": "section_size",
      "label": "Section size",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "normal",
          "label": "Normal"
        },
        {
          "value": "large",
          "label": "Large"
        },
        {
          "value": "fullscreen",
          "label": "Fullscreen"
        }
      ],
      "default": "normal"
    },
    {
      "type": "url",
      "id": "desktop_video",
      "label": "Desktop Video URL",
      "info": "MP4 format recommended for best compatibility"
    },
    {
      "type": "url",
      "id": "mobile_video",
      "label": "Mobile Video URL",
      "info": "MP4 format recommended. If not provided, desktop video will be used"
    },
    {
      "type": "image_picker",
      "id": "fallback_image",
      "label": "Fallback Image",
      "info": "Shown if video fails to load"
    },
    {
      "type": "checkbox",
      "id": "apply_overlay",
      "label": "Apply dark overlay",
      "info": "Improves text readability over video",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_controls",
      "label": "Show video controls",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_play_button",
      "label": "Show play/pause button",
      "default": false
    },
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Subtitle"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#ffffff"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    }
  ],
  "presets": [
    {
      "name": "Video Section",
      "category": "Video"
    }
  ]
}
{% endschema %}
