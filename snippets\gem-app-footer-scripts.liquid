{% capture gemFooterScripts %}    <script data-instant-track type="text/javascript">
      var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());
      if(typeof pageLibs === 'undefined' || pageLibs === null){
        var pageLibs = [];
      }
      GEMVENDOR.init(pageLibs);
      </script>
    <script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
{% assign gemHandleCheck = 'index.gem-1745265618-template, product.gem-1745265619-template, collection.gem-1745265620-template' | split: ", " %}{% for h in gemHandleCheck %}{% if template == h %}{{ gemFooterScripts }}{% endif %}{% endfor %}
          {% if GEM_FOOTER_SCRIPT %}
          {{ GEM_FOOTER_SCRIPT }}
          {% else %}
                <script data-instant-track type="text/javascript">
      var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());
      if(typeof pageLibs === 'undefined' || pageLibs === null){
        var pageLibs = [];
      }
      GEMVENDOR.init(pageLibs);
      </script>
    <script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>

          {% endif%}    {% if shop.metafields.gpGlobalConfigs.enableProductSchema == "true" or shop.metafields.gpGlobalConfigs.enableProductSchema == blank %}
      {{ GEM_SCHEMA_SCRIPT }}
    {% endif %}
    {{ shop.metafields.gempages.gempagesBodyScriptInject }}
