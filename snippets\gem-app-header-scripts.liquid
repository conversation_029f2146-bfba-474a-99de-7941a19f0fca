{% capture gemHeaderScripts %}
<link rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/libs/css/fontawesome-*******.min.css" class="gf-style">
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />{% endcapture %}{% assign gemHandleCheck = 'index.gem-1745265618-template, product.gem-1745265619-template, collection.gem-1745265620-template' | split: ", " %}{% for h in gemHandleCheck %}{% if template == h %}{{ gemHeaderScripts }}{% endif %}{% endfor %}    
{% if request.page_type == 'page'  and page.metafields.gempages.featuredImage != blank %}
      <meta property="og:image" content="{{ page.metafields.gempages.featuredImage }}" />
      <meta property="og:image:secure_url" content="{{ page.metafields.gempages.featuredImage }}" />
      <meta property="twitter:image" content="{{ page.metafields.gempages.featuredImage }}" />
    {% endif %}
