{% comment %}
Auto-generated by Fast Simon.
DO NOT EDIT this as this file can be re-written at any time.
{% endcomment %}

{% if collection.handle != 'all' and collection.handle != 'vendors' %}
	<div id="isp_search_result_page" style="width:100%;min-height:100vh;"></div>
	<script src="https://shopify.instantsearchplus.com/js/search_result_loading_page.js?smart_navigation=1&isp_platform=shopify&UUID=9fba8b94-6705-4641-8c48-49f945444fdc&store_id=2474021"></script>
{% endif %}






	<link id="isp_search_results_css" rel="stylesheet" href="https://cdn1-gae-ssl-default.akamaized.net/css/search_result_page_spotless.min.css">
	<noscript><link rel="stylesheet" href="https://cdn1-gae-ssl-default.akamaized.net/css/search_result_page_spotless.min.css"></noscript>	



<div id="isp_search_result_page_container_prerender">	
  <div id="isp_left_container" class="isp_left_container"></div>	
  <div id="isp_center_container" class="isp_center_container isp_center_container_position">
    <ul id="isp_search_results_container">		
      {%- paginate collection.products by 24 -%}
		{%- for product in collection.products -%}
			<li class="isp_grid_product" product_id="{{ product.id }}">
				<div class="isp_product_image_wrapper">
					<a class="isp_product_image_href" href="{{ product.url }}">
					<img alt="{{ product.title }}" src="{{ product | img_url: '800x' }}" class="isp_product_image"></a>
				</div>
				<div class="isp_product_info">
					<a class="isp_product_image_href" href="{{ product.url }}"><div class="isp_product_title">{{ product.title }}</div></a>
					<div class="isp_product_vendor"><span class="isp_product_vendor_title"></span>{{ product.vendor }}</div>
					<div class="isp_product_sku"><span class="isp_product_sku_title">SKU: </span>{{ product.sku }}</div>
					<div class="isp_product_price_wrapper"><span class="isp_product_price money">{{ product.price }}</span></div>		
					</div>
			</li>
			{%- endfor -%}
			<div class="prerender-pagination">{{- paginate | default_pagination -}}</div>
		{%- endpaginate -%}	
    </ul>		
  </div>
</div>
