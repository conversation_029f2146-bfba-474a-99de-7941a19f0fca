{% if product.metafields.custom.label_bundles %}
  <style>
    .ProductForm {
      display: none;
    }

    .iconslist {
      display: none;
    }

    .label-bundle-info {
      margin: 20px 0;
    }
    .label-bundle-info p {
      margin: 0;
    }

    .Bundle__AddToCart_disabled,
    .Bundle__AddToCart {
      display: none;
    }

    .Bundle__AddToCart_disabled.show,
    .Bundle__AddToCart.show {
      display: block;
    }

    .Product__Tabs.product .Collapsible:last-child {}

    .Product__Tabs.product .Collapsible:not(:first-child) {
      display: none;
    }

    .yarnradio input {
      -webkit-appearance: auto;
    }

    .yarnradio label {
      margin-right: 10px;
    }
  </style>

  {% assign custom_polos_tag_found = false %}
  {% assign design_your_own_title_found = false %}
  
  {% for tag in product.tags %}
    {% if tag == 'custom-polos' %}
      {% assign custom_polos_tag_found = true %}
      {% break %}
    {% endif %}
  {% endfor %}
  
  {% if product.title contains "Design Your Own" %}
    {% assign design_your_own_title_found = true %}
  {% endif %}
  
  {% unless custom_polos_tag_found %}
    <div style="margin-top: 20px">
      <ul class="iconslist" style="display: block">
        <li><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/ProductIcons-October_Combo_A-02.svg"> Royalties given back to artists on every item sold</li>
        <li><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/ProductIcons-October_Combo_A-01.svg"> 100% Authentic Indigenous artwork</li>
        <li><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/ProductIcons-October_Combo_A-03.svg"> Supports Indigenous employment &amp; training</li>
      </ul>
    </div>
  {% else %}
    <ol class="custom-polos instructions">
      <li>Upload logo/s (min 1 is required)</li>
      {% if design_your_own_title_found %}
        <li>Upload your background graphic</li>
      {% endif %}
      <li>Select quantity by size</li>
      <li>Place order</li>
      <li>Receive mockup for your approval</li>
    </ol>
    <span class="custom-polos instructions">Note: No production will begin until mockup is approved</span>
  {% endunless %}



  <div id="label-bundle-wrap" style="margin-top: 25px">



    {% assign hasupload = true %}
    <div class="upload-artwork" id="upload-artwork"></div>
    <label class="rcl-upload--label">Upload Logo/s</label>
    <div class="accordion">
      <div class="accordion-header">
        <span class="label">Add Logo</span>
        <span class="arrow">&#9660;</span>
      </div>
      <div class="accordion-content">
        <div class="upload-left-chest" id="upload-left-chest"></div>
        <div class="upload-right-chest" id="upload-right-chest"></div>
        <div class="upload-left-hand-sleeve" id="upload-left-hand-sleeve"></div>
        <div class="upload-right-hand-sleeve" id="upload-right-hand-sleeve"></div>
        <div class="upload-back-yoke" id="upload-back-yoke"></div>
        <div class="upload-centre-back" id="upload-centre-back"></div>
      </div>
    </div>


    <p>
      <a
        style="text-decoration: underline"
        target="_blank"
        href="https://cdn.shopify.com/s/files/1/0247/4021/files/CORPORATE_Artwork_Guidelines_Sheet.pdf?v=1679296729">Artwork Guidelines</a>
    </p>

    <div class="label-bundle-info">
      <label style="display: block">Additional details for customisation</label>
      <p><input
          type="text"
          id="labeldetails"
          style="border: 1px solid #d8d8d8; padding: 10px; width: 100%"></p>
    </div>

    <img style="margin: 10px 0" src="https://cdn.shopify.com/s/files/1/0247/4021/files/flow-yarn1.jpg?v=1679297098">

    <div style="margin: 10px 0 20px" class="saso-volumes"></div>

    {% for product in product.metafields.custom.label_bundles.value %}

      <h3 style="font-weight: 600; margin-bottom: -10px">{{ product.title }}</h3>
      <div class="bulk-flex__wrap" style="">

        <div class="bulk-flex bulk-header" style="">
          <div>Size</div>
          <div>Qty</div>
        </div>

        {%- for variant in product.variants -%}

          {% if variant.inventory_management != nil %}
            {% assign maxItem = variant.inventory_quantity %}
          {% else %}
            {% assign maxItem = 9999 %}
          {% endif %}

          <div class="bulk-flex bulk-flex-row" style="">

            <div data-value="{{ variant.id }}" class="">{{ variant.title }}</div>


            <div class="ProductForm__QuantitySelector">
              <div class="QuantitySelector QuantitySelector--large">
                {%- assign quantity_minus_one = line_item.quantity | minus: 1 -%}
                {% if variant.available %}
                  <span class="QuantitySelector__Button Link Link--secondary" data-action="decrease-quantity">{% include 'icon' with 'minus' %}</span>
                  <input
                    type="number"
                    class="QuantitySelector__CurrentQuantity"
                    pattern="[0-9]*"
                    min="0"
                    max="{{ maxItem }}"
                    data-inventory="{{ maxItem }}"
                    name="quantity"
                    value="0">
                  <span class="QuantitySelector__Button Link Link--secondary" data-action="increase-quantity">{% include 'icon' with 'plus' %}</span>
                {% else %}
                  <div style="text-align: center">Out of stock</div>
                {% endif %}
              </div>
            </div>


          </div>

        {%- endfor -%}

      </div>
    {% endfor %}
    
    {% assign moq_100_tag_found = false %}
    {% assign moq_25_tag_found = false %}
    
    {% for tag in product.tags %}
      {% if tag == 'MOQ-100' %}
        {% assign moq_100_tag_found = true %}
      {% elsif tag == 'MOQ-25' %}
        {% assign moq_25_tag_found = true %}
      {% endif %}
    {% endfor %}
    
    {% if moq_100_tag_found %}
      <button class="Bundle__AddToCart_disabled Button Button--secondary Button--full saso-add-to-cart show" disabled>
        <span class="button-atc-disabled">Add To Cart - (MIN 100)</span>
      </button>
    {% elsif moq_25_tag_found %}
      <button class="Bundle__AddToCart_disabled Button Button--secondary Button--full saso-add-to-cart show" disabled>
        <span class="button-atc-disabled">Add To Cart - (MIN 25)</span>
      </button>
    {% else %}
      <button class="Bundle__AddToCart_disabled Button Button--secondary Button--full saso-add-to-cart show" disabled>
        <span class="button-atc-disabled">Add To Cart - (MIN 15)</span>
      </button>
    {% endif %}
    
    <button class="Bundle__AddToCart Button Button--primary Button--full saso-add-to-cart">
      <span class="button-atc">Add To Cart</span>
    </button>


  </div>
  {% if product.tags contains 'upload_artwork' %}
    <script>
      var requiresArtwork = true;
    </script>
  {% else %}
    <script>
      var requiresArtwork = false;
    </script>
  {% endif %}

  <script>
    document.addEventListener("DOMContentLoaded", () => {
      var moq100 = {% if moq_100_tag_found %} true {% else %} false {% endif %};
      var moq25 = {% if moq_25_tag_found %} true {% else %} false {% endif %};
      let qtyInputArray = document.querySelectorAll('#label-bundle-wrap .QuantitySelector input');
      let qtyBtnArray = document.querySelectorAll('#label-bundle-wrap .QuantitySelector__Button');
      let disableBtn = document.querySelector('#label-bundle-wrap .Bundle__AddToCart_disabled');
      let buyBtn = document.querySelector('#label-bundle-wrap .Bundle__AddToCart');
  
      function updateButtonState() {
          let areChecked = 0;
          qtyInputArray.forEach(function(item) {
              let itemValue = Number(item.value);
              areChecked += itemValue;
          });
  
          console.log(areChecked);
  
          let minimumQuantity = moq100 ? 100 : (moq25 ? 25 : 15);
          if (areChecked >= minimumQuantity) {
              disableBtn.classList.remove('show');
              buyBtn.classList.add('show');
          } else {
              disableBtn.classList.add('show');
              buyBtn.classList.remove('show');
          }
      }
  
      qtyBtnArray.forEach(button => button.addEventListener('click', () => {
          setTimeout(updateButtonState, 500);
      }));
  
      qtyInputArray.forEach(input => input.addEventListener('input', updateButtonState));



      buyBtn.addEventListener("click", (event) => {

        let uploadLeftChest = $('#upload-left-chest input[name="properties[upload-left-chest]"]').val();
        let uploadRightChest = $('#upload-right-chest input[name="properties[upload-right-chest]"]').val();
        let uploadLeftHandSleeve = $('#upload-left-hand-sleeve input[name="properties[upload-left-hand-sleeve]"]').val();
        let uploadRightHandSleeve = $('#upload-right-hand-sleeve input[name="properties[upload-right-hand-sleeve]"]').val();
        let uploadBackYoke = $('#upload-back-yoke input[name="properties[upload-back-yoke]"]').val();
        let uploadCentreBack = $('#upload-centre-back input[name="properties[upload-centre-back]"]').val();

        if (! uploadLeftChest && ! uploadRightChest && ! uploadLeftHandSleeve && ! uploadRightHandSleeve && ! uploadBackYoke && ! uploadCentreBack) {
          alert('Please upload at least one logo/image');
          return;
        }

        let prodArtwork = $('#upload-artwork input[name="properties[artwork]"]').val();
        let prodPos = $('#labeldetails').val();

        if (requiresArtwork && (prodArtwork == null || prodArtwork == "")) {
          alert('Please upload your background design');
          return;
        }

        let BulkProdList = [];

        $('.bulk-flex-row').each(function() {

          let prodId = $(this).find("[data-value]").attr("data-value");
          let prodQty = $(this).find(".QuantitySelector__CurrentQuantity").val();
          prodQty = parseInt(prodQty);

          if (prodQty > 0) {
            let prod = {
              quantity: prodQty,
              id: prodId,
              properties: {
                details: prodPos
              }
            };

            if (uploadLeftChest) 
              prod.properties.uploadLeftChest = uploadLeftChest;
            


            if (uploadRightChest) 
              prod.properties.uploadRightChest = uploadRightChest;
            


            if (uploadLeftHandSleeve) 
              prod.properties.uploadLeftHandSleeve = uploadLeftHandSleeve;
            


            if (uploadRightHandSleeve) 
              prod.properties.uploadRightHandSleeve = uploadRightHandSleeve;
            


            if (uploadBackYoke) 
              prod.properties.uploadBackYoke = uploadBackYoke;
            


            if (uploadCentreBack) 
              prod.properties.uploadCentreBack = uploadCentreBack;
            


            if (requiresArtwork) {
              prod.properties.artwork = prodArtwork;
            }

            BulkProdList.push(prod);
          }

        });

        console.log(BulkProdList);

        jQuery
          .post('/cart/add.js', {
            items: BulkProdList
          }, function() {})
          .done(function() {
            // window.location.href = '/cart';
          })
          .fail(function() {})
          .always(function() {});
      });

      document.querySelector('.Product__Tabs .Collapsible:first-child button').click();
    });

    document.querySelector(".accordion-header").addEventListener("click", function() {
      let accordion = document.querySelector(".accordion");
      accordion.classList.toggle("active");
    });
  </script>

{% endif %}