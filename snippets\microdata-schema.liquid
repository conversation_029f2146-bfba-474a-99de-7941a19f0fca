{%- comment -%}This snippet structures the micro-data using JSON-LD specification{%- endcomment -%}

{%- if template.name == 'index' -%}
  {%- capture main_entity_microdata -%}
  "@type": "Organization",
  "name": "{{ shop.name }}",
  "url": "{{ shop.url }}",
  "logo": {
     "@type": "ImageObject",
        "url": "https:{{ 'logo.png' | asset_url }}"
  },
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "{{ shop.phone }}",
    "contactType": "customer service",
    "areaServed": "AU",
    "availableLanguage": ["English"]
  },
  "sameAs": [
    "https://www.facebook.com/yarn",
    "https://www.instagram.com/yarn_marketplace"
    ]
  {%- endcapture -%}
{%- elsif template.name == 'index' -%}
  {%- capture main_entity_microdata -%}
  "@type": "WebSite",
  "name": {{ shop.name | json }},
  "url": "{{ shop.url }}"
{%- endcapture -%}
{%- elsif template.name == 'collection' -%}
  {%- capture main_entity_microdata -%}
    "@type": "CollectionPage",
    "name": "{{ collection.title | escape }}",
    "url": "{{ collection.url }}",
    "description": "{{ collection.description | escape }}",
    "hasPart": [
      {% for product in collection.products limit:5 %}
        {
          "@type": "WebPage",
          "name": "{{ product.title | escape }}",
          "url": "{{ product.url }}",
          "mainEntity": {
            "@type": "Product",
            "name": "{{ product.title | escape }}",
            "image": "{{ product.featured_image | img_url: 'large' }}",
            "offers": {
              "@type": "Offer",
              "price": "{{ product.price | money_without_currency }}",
              "priceCurrency": "{{ cart.currency.iso_code }}",
              "availability": "{% if product.available %}https://schema.org/InStock{% else %}https://schema.org/OutOfStock{% endif %}"
            }
          }
        }{% unless forloop.last %},{% endunless %}
      {% endfor %}
    ]
  {%- endcapture -%}
{%- elsif template.name == 'product' -%}
  {%- capture main_entity_microdata -%}
      "@type": "Product",
      "offers": {
        "@type": "Offer",
        "availability": {%- if product.available -%}"//schema.org/InStock"{%- else -%}"//schema.org/OutOfStock"{%- endif -%},
        "price": {{ product.price_min | money_without_currency | json }},
        "priceCurrency": {{ shop.currency | json }}
      },
      "brand": {{ product.vendor | json }},
      "name": {{ product.title | json }},
      "description": {{ product.description | strip_html | json }},
      "category": {{ product.type | json }},
      "url": "{{ shop.url }}{{ product.url }}",
      "image": {
        "@type": "ImageObject",
        "url": "https:{{ product.featured_image | img_url: '1024x1024' }}",
        "image": "https:{{ product.featured_image | img_url: '1024x1024' }}",
        "name": {{ product.featured_image.alt | json }},
        "width": 1024,
        "height": 1024
      },
      "aggregateRating": {
        "@type": "AggregateRating",
     "ratingValue": "{{ product.metafields.stamped.reviews_average | round: 1 }}",
    "reviewCount": "{{ product.metafields.stamped.reviews_count }}"
      }
  {%- endcapture -%}
{%- elsif template.name == 'article' -%}
  {%- capture main_entity_microdata -%}
    "@type": "BlogPosting",
    "mainEntityOfPage": "{{ article.url }}",
    "articleSection": {{ blog.title | json }},
    "keywords": "{{ article.tags | join: ', ' }}",
    "headline": {{ article.title | json }},
    "description": {{ article.excerpt_or_content | strip_html | truncatewords: 25 | json }},
    "dateCreated": "{{ article.created_at | date: '%Y-%m-%dT%T' }}",
    "datePublished": "{{ article.published_at | date: '%Y-%m-%dT%T' }}",
    "dateModified": "{{ article.published_at | date: '%Y-%m-%dT%T' }}",
    "image": {
      "@type": "ImageObject",
      "url": "https:{{ article.image | img_url: '1024x1024' }}",
      "image": "https:{{ article.image | img_url: '1024x1024' }}",
      "name": {{ article.image.alt | json }},
      "width": 1024,
      "height": 1024
    },
    "author": {
      "@type": "Person",
      "name": "{{ article.user.first_name | escape }} {{ article.user.last_name | escape }}",
      "givenName": {{ article.user.first_name | json }},
      "familyName": {{ article.user.last_name | json }}
    },
    "publisher": {
      "@type": "Organization",
      "name": {{ shop.name | json }},
      "logo": {
        "@type": "ImageObject",
        "url": "https:{{ 'logo.png' | asset_url }}",
        "image": "https:{{ 'logo.png' | asset_url }}",
        "name": {{ shop.name | json }},
        "width": 100,
        "height": 100
      }
    },
    "commentCount": {{ article.comments_count }},
    "comment": [
      {%- for comment in article.comments limit: 5 -%}
        {
          "@type": "Comment",
          "author": {{ comment.author | json }},
          "datePublished": "{{ comment.created_at | date: '%Y-%m-%dT%T' }}",
          "text": {{ comment.content | json }}
        }{%- unless forloop.last -%},{%- endunless -%}
      {%- endfor %}
    ]
  {%- endcapture -%}
{%- elsif request.path == '/pages/about-us' -%}
  {%- capture main_entity_microdata -%}
    "@type": "AboutPage",
    "name": "About Us",
    "url": "{{ shop.url }}/pages/about-us"
  {%- endcapture -%}
{%- endif -%}

{%- capture breadcrumb_microdata -%}
  "@type": "BreadcrumbList",
  "itemListElement": [
    { "@type": "ListItem", "position": 1, "name": "Home", "item": "{{ shop.url }}" }
    {%- if template.name == 'collection' -%}
      ,{ "@type": "ListItem", "position": 2, "name": "{{ collection.title | escape }}", "item": "{{ collection.url }}" }
    {%- elsif template.name == 'product' -%}
      ,{ "@type": "ListItem", "position": 2, "name": "{{ collection.title | escape }}", "item": "{{ collection.url }}" }
      ,{ "@type": "ListItem", "position": 3, "name": "{{ product.title | escape }}", "item": "{{ product.url }}" }
    {%- elsif template.name == 'article' -%}
      ,{ "@type": "ListItem", "position": 2, "name": "{{ blog.title | escape }}", "item": "{{ blog.url }}" }
      ,{ "@type": "ListItem", "position": 3, "name": "{{ article.title | escape }}", "item": "{{ article.url }}" }
    {%- elsif request.path == '/pages/contact-us' -%}
      ,{ "@type": "ListItem", "position": 2, "name": "Contact Us", "item": "{{ shop.url }}/pages/contact-us" }
    {%- endif -%}
  ]
{%- endcapture -%}

{% if main_entity_microdata != blank %}
  <script type="application/ld+json">
  {
    "@context": "http://schema.org",
    {{ main_entity_microdata }}
  }
  </script>
{% endif %}

{% if breadcrumb_microdata != blank %}
  <script type="application/ld+json">
  {
    "@context": "http://schema.org",
    {{ breadcrumb_microdata }}
  }
  </script>
{% endif %}