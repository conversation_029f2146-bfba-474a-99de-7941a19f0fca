{% if content_for_header contains 'shopifyorderlimits.s3.amazonaws.com' %}
{% if shop.permanent_domain == 'bundarra.myshopify.com' %}
<script type="text/javascript">
minMaxifyContext={
  cartItemExtras: [
  {%- for item in cart.items -%}
  { "id": {{ item.id }}, "product_id": {{ item.product_id }}, "collection": {{ item.product.collections | map: 'id' | json }}, "tag": {{ item.product.tags | json }} }{% unless forloop.last == true %},{% endunless %}
  {%- endfor -%}
]
};
{% if template contains 'product' %}
minMaxifyContext.product = { "product_id": {{ product.id }}, "collection": {{ product.collections | map: 'id' | json }}, "tag": {{ product.tags | json }} };
{% endif%}</script><script src="https://shopifyorderlimits.s3.amazonaws.com/limits/bundarra.myshopify.com?v=89c&r=20220428023920&shop=bundarra.myshopify.com"></script>
{% endif %}
{% else %}
<script type="text/javascript">console.error("MinMaxify is deactivated for this website. Please don't forget to remove minmaxify-head.liquid still being referenced by theme.liquid")</script>
{% endif %}
