{%- capture view_cart_text_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.view_cart_text' }}{%- endcapture -%}
{%- capture checkout_text_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.checkout_text' }}{%- endcapture -%}
{%- capture continue_shopping_text_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.continue_shopping_text' }}{%- endcapture -%}
{%- capture subtotal_text_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.subtotal_text' }}{%- endcapture -%}

{%- comment -%} Added for modal message for Multiple AddtoCart App {%- endcomment -%}
<div class="multiaddtocartlist" style="display:none;">
   <div class="modal-content">
      <div class="modal-header">
         <button type="button" class="cart_close" title="{{ 'cws_bulk_add_to_cart.close_text' | t | replace: close_text_error, 'Close' | strip_newlines }}">&times;</button>
         <h2 id="total_cnt"></h2>
      </div>
      <div class="modal-body">
         <table class="cart_table">
            <thead>
               <tr>
                  {%- if show_prd_image == "yes" -%}<th class="cws_prd_img">{{ 'cws_bulk_add_to_cart.product_image' | t | replace: product_image_error, 'Product Image' | strip_newlines }}</th>{%- endif -%}
                  <th class="cws_prd_name">{{ 'cws_bulk_add_to_cart.product_name' | t | replace: product_name_error, 'Product Name' | strip_newlines }}</th>
                  <th class="cws_prd_qty">{{ 'cws_bulk_add_to_cart.quantity' | t | replace: quantity_error, 'Quantity' | strip_newlines }}</th>
                  <th class="cws_price">{{ 'cws_bulk_add_to_cart.price' | t | replace: price_error, 'Price' | strip_newlines }}</th>     
               </tr>
            </thead>
			{%- comment -%} DO NOT EDIT BELOW LINES [ CART ITEMS ARE WRITTEN IN THIS PART ] {%- endcomment -%}
            <tbody id="multicart-minilist">
            </tbody>
			{%- comment -%} ENDS HERE {%- endcomment -%}
         </table>
      </div>
      <div class="modal-footer">
         <div class="sub_total">{{ 'cws_bulk_add_to_cart.subtotal_text' | t | replace: subtotal_text_error, 'Subtotal' | strip_newlines }}: <span class="sub_total_amount"></span></div>
         <div class="cart-button">
            <a class="btn view_cart" href="/cart">{{ 'cws_bulk_add_to_cart.view_cart_text' | t | replace: view_cart_text_error, 'View Cart' | strip_newlines }}</a>
            {%- if show_checkout_btn == "yes" -%}
            <a class="btn bulk_cart_checkout" href="/checkout">{{ 'cws_bulk_add_to_cart.checkout_text' | t | replace: checkout_text_error, 'Checkout' | strip_newlines }}</a>
            {%- endif -%} 
            <a class="btn continue_btn">{{ 'cws_bulk_add_to_cart.continue_shopping_text' | t | replace: continue_shopping_text_error, 'Continue shopping' | strip_newlines }}</a>
         </div>
      </div>
   </div>
</div>