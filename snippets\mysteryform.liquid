<script>
  
  let Kart = [];
  
  function getMyCart() {
    
    Kart = [];
    
    fetch('/cart.js')
    .then((response) => {
      return response.json();
    })
    .then((myJson) => {
      
      myJson.items.forEach(num => {
        Kart.push(num.variant_id);
      });
      
    }).then(() => {
      console.log(Kart);      
    })
  }
          
  getMyCart();
  
</script>

<script>
  
  let mysteryProducts = [];
  let mysteryProduct;
  let mysteryVar;
  
</script>

{% assign collection_handle = 'mystery' %}
{% for product in collections[collection_handle].products %}

<script>
  
  mysteryProduct = {
    title: '{{ product.title }}',
    variant: []
  };
  
  {% for variant in product.variants %}
  
  mysteryVar = {
    id: {{ variant.id }},
    value: '{{ variant.title }}',
    inv: '{{ variant.inventory_quantity }}'
  }
  
  mysteryProduct.variant.push(mysteryVar);

  
  {% endfor %}
  
  
  mysteryProducts.push(mysteryProduct);
  
</script>


{% endfor %}



<style>
  .ProductForm__AddToCart {
    display: none;
  }
</style>


<div id="mysteryform">

<div class="variant-btn ProductForm__Option" id="ProductSelect-option-0" name="size" style="width: 100%">
  
  <legend style="
                  text-transform: uppercase;
                  margin-bottom: 5px;
                  font-size: 12px;
                  letter-spacing: 1px;
                 ">
                  Choose Size
  </legend>
  
  <div class="variant-input">
    <input name="mysteryid" type="radio" checked="checked" value="S" id="ProductSelect-option-size-S">
    <label class="" for="ProductSelect-option-size-S">
    S
    </label>
  </div>
  <div class="variant-input">
    <input name="mysteryid"  type="radio" value="M" id="ProductSelect-option-size-M">
    <label class="" for="ProductSelect-option-size-M">
    M
    </label>
  </div>
  <div class="variant-input">
    <input name="mysteryid"  type="radio" value="L" id="ProductSelect-option-size-L">
    <label class="" for="ProductSelect-option-size-L">
    L
    </label>
  </div>
  <div class="variant-input">
    <input name="mysteryid"  type="radio" value="XL" id="ProductSelect-option-size-XL">
    <label class="" for="ProductSelect-option-size-XL">
    XL
    </label>
  </div>
  <div class="variant-input">
    <input name="mysteryid"  type="radio" value="2XL" id="ProductSelect-option-size-2XL">
    <label class="" for="ProductSelect-option-size-2XL">
    2XL
    </label>
  </div>
  <div class="variant-input">
    <input name="mysteryid"  type="radio" value="3XL" id="ProductSelect-option-size-3XL">
    <label class="" for="ProductSelect-option-size-3XL">
    3XL
    </label>
  </div>
  <div class="variant-input">
    <input name="mysteryid"  type="radio" value="4XL" id="ProductSelect-option-size-4XL">
    <label class="" for="ProductSelect-option-size-4XL">
    4XL
    </label>
  </div>
  <div class="variant-input 1">
    <input name="mysteryid"  type="radio" value="5XL" id="ProductSelect-option-size-5XL">
    <label class="" for="ProductSelect-option-size-5XL">
    5XL
    </label>
  </div>
</div>
  
<button class="Button Button--primary Button--full" id="mysterybtn">
<span class="">Add to cart</span>
<span class="Button__SeparatorDot"></span>
<span data-money-convertible="">$49</span>
</button>
  
  
</div>



<script>
  
function chooseRandomBox(size, mysteryProducts) {

  let sizeIds = [];
  let notInKartArray = [];
  let useKartArray = false;
  
  mysteryProducts.forEach(num => {

    let sizeId = num.variant.find(x => x.value === size).id;
    let sizeInv = num.variant.find(x => x.value === size).inv;
    Number(sizeId);
    
    if(sizeInv > 0) {
      
      sizeIds.push(sizeId);

      if(Kart.includes(sizeId) == false) {
        notInKartArray.push(sizeId);
        
      }
      
    }

    
  });
  
  ///console.log('sizeids');
  //console.log(sizeIds);
  
  //console.log('notinkartarr');
  //console.log(notInKartArray);
  
  if ((notInKartArray.length != sizeIds.length) && notInKartArray.length > 0) {
    useKartArray = true;
  }
  
  
  
  
  let selectMystery;

  if(useKartArray == true) {
    
    let prodTotal = notInKartArray.length;
    let randomNum = Math.floor(Math.random() * prodTotal);
    selectMystery = notInKartArray[randomNum];    
    
  } else {
    
    let prodTotal = sizeIds.length;
    let randomNum = Math.floor(Math.random() * prodTotal);
    selectMystery = sizeIds[randomNum]; 

  }

  
  
  console.log('selectmystery:' + selectMystery);
  //let selectMystery = mysteryProducts[randomNum].variant.find(x => x.value === size).id;
  return selectMystery;
    
}

function addMystery() {
  
  let size = document.querySelector("#mysteryform .variant-input input:checked").value;
  let mysteryValue = chooseRandomBox(size, mysteryProducts);
  
  console.log('addmystery:' + mysteryValue);
  
  document.querySelector(".ProductForm .ProductForm__Variants input[name='id']").value = mysteryValue;
  
  document.getElementsByClassName("ProductForm__AddToCart")[0].click();
  
  setTimeout(getMyCart, 2000);
  
}
  


// Add event listener
const elz = document.getElementById("mysterybtn");
elz.addEventListener("click", addMystery, false);
  
</script>