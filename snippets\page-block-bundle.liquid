<section class="Section Section--spacingNormal Section--bundles">
  <header class="SectionHeader SectionHeader--center">
    <div class="Container"><h2 style="padding-top: 40px" class="SectionHeader__Heading Heading u-h1">{{ block.settings.bundle_title}}</h2></div>
  </header>
  <div class="ProductListWrapper">
  		
        <div class="ProductList ProductList--grid ProductList--removeMargin Grid" data-mobile-count="2" data-desktop-count="4">
              
                
                {% if block.settings.bundleproduct1 != '' %}
                  <div class="Grid__Cell 1/2--phone 1/2--tablet 1/4--lap-and-up">
                    {%- include 'product-item-bundle', bundleproduct: block.settings.bundleproduct1, show_labels: true -%}
                  </div>
                {% endif %}
          
                {% if block.settings.bundleproduct2 != '' %}
                  <div class="Grid__Cell 1/2--phone 1/2--tablet 1/4--lap-and-up">
                    {%- include 'product-item-bundle', bundleproduct: block.settings.bundleproduct2, show_labels: true -%}
                  </div>
                {% endif %}
          
                {% if block.settings.bundleproduct3 != '' %}
                  <div class="Grid__Cell 1/2--phone 1/2--tablet 1/4--lap-and-up">
                    {%- include 'product-item-bundle', bundleproduct: block.settings.bundleproduct3, show_labels: true -%}
                  </div>
                {% endif %}
                
                
                  <div class="Grid__Cell 1/2--phone 1/2--tablet 1/4--lap-and-up">
                    <button onclick="obApi('track', 'Add To Cart');" class="ProductForm__AddToCart Button Button--primary Button--full"><span>Add to cart</span><span class="Button__SeparatorDot"></span><span>{{ block.settings.total_price }}</span></button>
                    <div class="bundles-savings">{{ block.settings.total_savings }}</div>
                  </div>
                
              
        </div>
  
  </div>
</section>

