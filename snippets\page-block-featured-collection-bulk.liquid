{% assign inventoryMin = block.settings.inventory | abs %}

{% if block.settings.total_products != "" %}
{% assign productMax = block.settings.total_products | abs %}
{% else %}
{% assign productMax = 999 %}
{% endif %}

{% assign my_counter = 0 %}

<section class="section-collection-block">
{%- assign collection = collections[block.settings.collection] -%}
<div class="Container">
<header class="SectionHeader SectionHeader--center">
    <div class="Container"><h2 style="padding-top: 40px" class="SectionHeader__Heading Heading u-h1">{{ block.settings.title}}</h2></div>
</header>
<div class="CollectionInner__Products naidoc-coll">
	<div class="ProductListWrapper">
        <div class="ProductList ProductList--grid  Grid" data-mobile-count="1" data-desktop-count="4">
    		
            {%- for product in collection.products -%}
              {% if product.url contains "sca_clone_freegift" %} {% continue %} {% endif %}
            	
                {% assign inventoryTotal = 0 %}
                {% assign variantTotal = 0 %}
          
                {% comment %} <!-- Get Product Variant Total --> {% endcomment %}
                {% for variant in product.variants %}

                  {% assign variantTotal = variantTotal | plus: variant.inventory_quantity %}

                {% endfor %}
          
                {% comment %} <!-- Check Product Variant Total is More than Inventory Minimum --> {% endcomment %}
                {% if inventoryMin < variantTotal %}
                   
                  {% comment %} <!-- Only show Total Products items maximum --> {% endcomment %}
                  {% assign my_counter =  my_counter | plus:1 %}
                  {% if my_counter > productMax %}
                     {% break %}
                  {% endif %}

                  <div class="Grid__Cell 1/2--phone 1/3--tablet-and-up 1/4--desk" data-product-max="{{productMax}}" data-inventory-min="{{inventoryMin}}" data-inventory="{{variantTotal}}" data-counter="{{my_counter}}">
                       {%- include 'product-item', show_product_info: true, show_color_swatch: section.settings.show_color_swatch, show_labels: true -%}
                  </div>
          
                {% endif %}

          
           {%- endfor -%}  
          
          
    </div>
</div>
</div>
</div>
</section>