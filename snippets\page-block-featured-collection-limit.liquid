<style>
  @media (min-width: 600px) {
    .seemorebtn {
      margin-top: -30px;
    }
  }
</style>

<section class="section-collection-block" class="{{block.id}}">
{%- assign collection = collections[block.settings.collection] -%}
<div class="Container">
<header class="SectionHeader SectionHeader--center">
    <div class="Container"><h2 style="padding-top: 40px" class="SectionHeader__Heading Heading u-h1">{{ block.settings.title}}</h2></div>
</header>
<div class="CollectionInner__Products naidoc-coll">
	<div class="ProductListWrapper">
      {% paginate collection.products by 1000 %}
        <div class="ProductList ProductList--grid  Grid" data-mobile-count="1" data-desktop-count="4">
    		
            {%- for product in collection.products limit:10 -%}
              {% if product.url contains "sca_clone_freegift" %} {% continue %} {% endif %}

                  <div class="Grid__Cell 1/2--phone 1/3--tablet-and-up 1/4--desk" data-product-max="{{productMax}}" data-inventory-min="{{inventoryMin}}" data-inventory="{{variantTotal}}" data-counter="{{my_counter}}">
                       {%- include 'product-item', show_product_info: true, show_color_swatch: section.settings.show_color_swatch, show_labels: true -%}
                  </div>

          
           {%- endfor -%}  
          
          
    </div>
    {%- endpaginate -%}
</div>
</div>
{% if collection.all_products_count > 10 %}
  
<p class="seemorebtn" style="text-align: center; padding-bottom: 40px">
<a href="{{ collection.url }}" class="Button Button--primary">See More</a>
</p>
  
{% endif %}
</div>
</section>

