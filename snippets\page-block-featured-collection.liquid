<section class="section-collection-block" class="{{block.id}}">
  


{% if page.handle == "black-friday" %}
{% assign theDay  = "now" | date: "%a" %}
  
  {% if theDay == "Mon" %}
  
    {%- assign collection = collections["black-friday-monday-sale"] -%}
   
  {% elsif theDay == "Tue" %}
  
    {%- assign collection = collections["black-friday-tuesday-sale"] -%}
    
  {% elsif theDay == "Wed" %}
  
    {%- assign collection = collections["black-friday-wednesday-sale"] -%}
  
  {% elsif theDay == "Thu " %}
  
    {%- assign collection = collections["black-friday-thursday-sale"] -%}
  
  {% else %}
    
    {%- assign collection = collections[block.settings.collection] -%}
  
  {% endif %}
  
{% else %}
{%- assign collection = collections[block.settings.collection] -%}
{% endif %}
  
  
  
<div class="Container">
<header class="SectionHeader SectionHeader--center">
    <div class="Container"><h2 style="padding-top: 40px" class="SectionHeader__Heading Heading u-h1">{{ block.settings.title}}</h2></div>
</header>
<div class="CollectionInner__Products naidoc-coll {{ collection.handle }}-coll">
	<div class="ProductListWrapper">
      {% paginate collection.products by 1000 %}
        <div class="ProductList ProductList--grid  Grid" data-mobile-count="1" data-desktop-count="4">
    		
            {%- for product in collection.products -%}
              {% if product.url contains "sca_clone_freegift" %} {% continue %} {% endif %}

                  <div class="Grid__Cell 1/2--phone 1/3--tablet-and-up 1/4--desk" data-product-max="{{productMax}}" data-inventory-min="{{inventoryMin}}" data-inventory="{{variantTotal}}" data-counter="{{my_counter}}">
                       {%- include 'product-item', show_product_info: true, show_color_swatch: section.settings.show_color_swatch, show_labels: true -%}
                  </div>

          
           {%- endfor -%}  
          
          
    </div>
    {%- endpaginate -%}
</div>
</div>
</div>
</section>

{% unless page.handle == "black-friday" %}
<a href="#set-intro" class="scrollme set-scroll">View your Mask Set</a>
{% endunless %}