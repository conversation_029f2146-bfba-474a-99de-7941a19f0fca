<style>
  .ProductItem--set .QuantitySelector {
    display: none;
    width: 100%;
  }
  
  .ProductItem--set .set-actions.is-active .QuantitySelector {  
    display: flex;
  }
  
  .ProductItem--set .set-actions.is-active .QuantitySelector input{
    flex-grow: 2;
  }
  
  .ProductItem--set .ProductForm__QuantitySelector {
    margin-top: 10px;
  }
</style> 

<section class="section-collection-block" class="{{block.id}}">
{%- assign collection = collections[block.settings.collection] -%}
<div class="Container">
<header class="SectionHeader SectionHeader--center">
    <div class="Container"><h2 style="padding-top: 40px" class="SectionHeader__Heading Heading u-h1">{{ block.settings.title}}</h2></div>
</header>
<div class="CollectionInner__Products naidoc-coll">
	<div class="ProductListWrapper">
      {% paginate collection.products by 1000 %}
        <div class="ProductList ProductList--grid  Grid" data-mobile-count="1" data-desktop-count="4">
          
        <script>

            var maskSet = [];
            let totalMasks = 0;

        </script>
    		
            {% paginate collection.products by 250 %}
            {%- for product in collection.products -%}
              {% if product.url contains "sca_clone_freegift" %} {% continue %} {% endif %}
			{% if product.available == true %}
          
                  <div class="Grid__Cell 1/1--phone 1/3--tablet-and-up 1/4--desk" data-product-max="{{productMax}}" data-inventory-min="{{inventoryMin}}" data-inventory="{{variantTotal}}" data-counter="{{my_counter}}">
                       {%- include 'product-item-set', show_product_info: true, show_color_swatch: section.settings.show_color_swatch, show_labels: true -%}
                  </div>

              <script>
               
               maskSet.push({
                  quantity: 0,
                  id: {{ product.first_available_variant.id }},
                  image: "{{ product.featured_image | img_url: "200x", crop: 'center'  }}",
                  price: {{ product.price }}
                });

              </script>
          
            {% endif %}
            {%- endfor -%}  
            {% endpaginate %}
          
    </div>
    {%- endpaginate -%}
</div>
</div>
</div>
</section>

<div id="setmodal" style="display: none">
  
</div>



<script>
  
  function searchSet(nameKey, myArray){
    for (var i=0; i < myArray.length; i++) {
        if (myArray[i].id == nameKey) {
            return myArray[i];
        }
    }
  }
  
  
    
  

  // var resultObject = search("string 1", array);
  
  // Update Object - Product Item Qty
  
  function updateSet(varid, qty) {
    let myobj = searchSet(varid, maskSet);
    if (myobj != undefined) {
      myobj["quantity"] = qty;
    }
    
  }
  
  function updateNeedMoreBtn() {
    let thisManyLeft = 5 - totalMasks;
    $('.SetBtn-disabled .btn-add-more').text(thisManyLeft);
    if(thisManyLeft == 1) {
      $('.SetBtn-disabled .btn-add-more-plural').text("");
    } else {
      $('.SetBtn-disabled .btn-add-more-plural').text("s");
    }
    
  }
  
  function createSetThumbs() {
    
    if(totalMasks > 0) {
      
      $('#set-items').empty();
      
      for (var i=0; i < maskSet.length; i++) {
          if (maskSet[i].quantity > 0) {
              $('#set-items').append('<div class="set-items--block"><img src="' + maskSet[i].image + '"><span class="setitemsqty">' + maskSet[i].quantity + '</span><a class="setitemsremove" data-varid="' + maskSet[i].id + '">x</a></div>');
          }
      }
      
    } else {
      $('#set-items').empty();
      $('#set-items').html('No products have been added to the bundle yet');
    }
  }
  
  function setSetPrices() {
    
      let setDiscount;
      let theTotalPrice = 0;

      if(totalMasks >= 3) {
        setDiscount = .75;
      } else {
        setDiscount = 1;
      }


      let price = totalMasks * 24.95;

      for (i = 0; i < this.maskSet.length; i++) {
        theTotalPrice += this.maskSet[i].quantity * this.maskSet[i].price;
      }
    
      
    
      //let totalprice = price * setDiscount;
      let totalprice = theTotalPrice * setDiscount;
      totalprice = totalprice / 100;
      totalprice = (Math.ceil(totalprice*20)/20).toFixed(2);
    
    
      let totalsavings = theTotalPrice/100 - totalprice;
      totalsavings = (Math.ceil(totalsavings*20)/20).toFixed(2);

      $('.ProductForm__AddSetToCart i').text('$' + totalprice);
      $('#set-actions .span-items').text(totalMasks);     
      $('#set-actions .span-savings').text('$' + totalsavings);
      
    }
  
  
  
  function setSet($setParent, arg = "plus") {

    let currentQty = $setParent.data('set');
    let currentId = $setParent.data('varid');
    
    if(arg == "plus") {
      currentQty += 1;
      totalMasks += 1;
      if(currentQty == 1) {
        $setParent.addClass("is-active");
      }


      
    } else {
      currentQty -= 1; 
      totalMasks -= 1;
        if(currentQty < 0) {
          currentQty = 0;
          totalMasks = 0;
        }

      if(currentQty == 0) {
        $setParent.removeClass("is-active");
      }

      
    }
    
    updateSet(currentId, currentQty);
    
    $setParent.data('set', currentQty);

    $setParent.find(".QuantitySelector__CurrentQuantity").val(currentQty);
    
    createSetThumbs();
    
    if(totalMasks >= 3) {
      $('#set-actions').addClass('is-active');
    } else {
       $('#set-actions').removeClass('is-active');
    }

    
    setSetPrices();
    updateNeedMoreBtn();
   

  }
  
  $(document).ready( function() {
    $(".ProductItem--set .ProductForm__AddToCart").on("click", function() {
      
      console.log(1);
      $setParent = $(this).parent(".set-actions");
      setSet($setParent, "plus");

    });
    
    $("#set-intro").on("click",'.setitemsremove', function(e) {
      e.preventDefault();
      let removeId = $(this).data("varid");
	  
      // refactor this
      let myobj = searchSet(removeId, maskSet);
      
      if (myobj != undefined) {
        totalMasks = totalMasks - myobj.quantity;
        myobj["quantity"] = 0;
      }
      
      createSetThumbs();
      $('.set-actions[data-varid="' + removeId + '"]').data("set", 0).removeClass("is-active");
      setSetPrices(); 
      
      if(totalMasks >= 3) {
          $('#set-actions').addClass('is-active');
        } else {
           $('#set-actions').removeClass('is-active');
        }
      
      updateNeedMoreBtn();
      

    });
    
    $(".ProductItem--set").on("click",'.ProductForm__Remove', function(e) {
      e.preventDefault();
      let removeId = $(this).parent('.set-actions').data("varid");
	  
      // refactor this
      let myobj = searchSet(removeId, maskSet);
      
      if (myobj != undefined) {
        totalMasks = totalMasks - myobj.quantity;
        myobj["quantity"] = 0;
      }
      
      createSetThumbs();
      $('.set-actions[data-varid="' + removeId + '"]').data("set", 0).removeClass("is-active");
      setSetPrices();  
      
      if(totalMasks >= 3) {
          $('#set-actions').addClass('is-active');
        } else {
           $('#set-actions').removeClass('is-active');
        }
      
      updateNeedMoreBtn();
      
    });
    
    
    
    $(".ProductItem--set .QuantitySelector__Button").on("click", function() {
      $setParent = $(this).closest(".set-actions");
      if($(this).data("action") == "decrease-quantity") {
        setSet($setParent, "minus");
      } else {
        setSet($setParent, "plus");
      }
      
    });
    
    $("#set-intro .ProductForm__AddSetToCart").on("click", function() {
      jQuery.post('/cart/add.js', {
        items: maskSet
      }, function( data ) {
		window.location.href = '/cart';
      }, "json");
    });
    
    

    $('.ProductItem--set .readset').on('click', function(e) {
       e.preventDefault();
       $('#setmodal').empty();
       $(this).closest(".ProductItem--set").find(".ProductItem--set--Intro").clone().appendTo("#setmodal");
     
       //ProductItem--set--Intro
       $('#setmodal').modal();
    });
    
    
    
    //caches a jQuery object containing the header element
    var setheader = $(".set-scroll");
    $(window).scroll(function() {
        var scroll = $(window).scrollTop();

        if (scroll >= 200) {
            setheader.addClass("set-scroll-show");
        } else {
            setheader.removeClass("set-scroll-show");
        }
    });
    
    $(".set-scroll").click(function(e) {
    e.preventDefault();
        $('html, body').animate({
            scrollTop: $("#set-anchor").offset().top
        }, 1000);
    });

     
    
  });
  
</script>