<div data-label="Row" data-key="row" data-atomgroup="row" id="r-1680740654355" class="gf_row" data-icon="gpicon-row" data-id="1680740654355"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1680740607581" data-id="1680740607581"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1680740654365" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1680740654365"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>Custom Mouse Pad</p><p></p><p></p></div></div><div data-label="Product List" data-key="product-list" data-atomgroup="product-list" id="m-1680740654455" class="module-wrap" data-icon="gpicon-product-list" data-ver="1.0" data-id="1680740654455" style="min-height: auto;"><div class="module " data-cid="269277986950" data-chandle="custom-naidoc-mouse-pads" data-limit="6" data-collg="3" data-colmd="3" data-colsm="3" data-colxs="2"><div class="item-content item-content-no-placeholder"><div class="gf_row gf_row-no-width gf_row_no_tools gf_row-no-padding">{% assign colXsClass = ' gf_col-xs-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colXsClass = colXsClass | append:cols %}{% assign colSmClass = ' gf_col-sm-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colSmClass = colSmClass | append:cols %}{% assign colMdClass = ' gf_col-md-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colMdClass = colMdClass | append:cols %}{% assign colLgClass = ' gf_col-lg-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colLgClass = colLgClass | append:cols %}{% assign colClass = 'gf_column gf_col_no_tools' %}{% assign colClass = colClass | append: colXsClass %}{% assign colClass = colClass | append: colSmClass %}{% assign colClass = colClass | append: colMdClass %}{% assign colClass = colClass | append: colLgClass %}{% assign gPLCount = 1 %}{% paginate collections["custom-naidoc-mouse-pads"].products by 6 %}{% for product in collections["custom-naidoc-mouse-pads"].products %}<div class="{{colClass}}" style="padding: 20px !important"><div data-label="Product" data-key="product" id="m-1680740654455-child{{forloop.index}}" data-ver="3.1" class="module-wrap" data-id="1680740654455-child{{forloop.index}}" data-index="1" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="40128661454982">{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1680740654455-child{{forloop.index}}-0" data-id="1680740654455-child{{forloop.index}}-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="80%" data-height="auto" style="width: 80%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="80%" data-height="auto" style="width: 80%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="80%" data-height="auto" style="width: 80%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="80%" data-height="auto" style="width: 80%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="80%" data-height="auto" style="width: 80%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1680740654455-child{{forloop.index}}-1" data-id="1680740654455-child{{forloop.index}}-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1680740654455-child{{forloop.index}}-12" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1680740654455-child{{forloop.index}}-12"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p><strong>From $6.00</strong></p></div></div><div data-label="(P) View More" data-key="p-view-more" data-atomgroup="child-product" id="m-1680740654455-child{{forloop.index}}-11" class="module-wrap" data-icon="gpicon-product-cartbutton" data-ver="1.0" data-id="1680740654455-child{{forloop.index}}-11"><div data-phandle="{{product.handle}}" class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '0' == '1' %}{% assign tg = '_blank' %}{% else %}{% assign tg = '' %}{% endif %}{% assign current_variant = product.selected_or_first_available_variant %}{% if '' != '' %}<a href="/products/{{ product.handle }}?variant={{current_variant.id}}" target="{{tg}}" class="gf_view-more  gf_gs-button-view-more gf_gs-button---large"><img src="" alt=""></a>{% else %}<a href="/products/{{ product.handle }}?variant={{current_variant.id}}" target="{{tg}}" class="gf_view-more button btn  gf_gs-button-view-more gf_gs-button---large"><span>Add Your Logo</span></a>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div>{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-xs"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-sm"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-md"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-lg"></div>{% endif %}{% assign gPLCount = gPLCount | plus: 1 %}{% endfor %}{% endpaginate %}</div></div></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1680740657978" class="gf_row" data-icon="gpicon-row" data-id="1680740657978"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1680740607581" data-id="1680740607581"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1680740658005" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1680740658005"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-sm gf-elm-center-md gf-elm-center-xs" data-gemlang="en" data-exc=""><p>Custom Tote Bags</p><p></p><p></p></div></div><div data-label="Product List" data-key="product-list" data-atomgroup="product-list" id="m-1680740657949" class="module-wrap" data-icon="gpicon-product-list" data-ver="1.0" data-id="1680740657949" style="min-height: auto;"><div class="module " data-cid="269278019718" data-chandle="custom-naidoc-tote-bags" data-limit="6" data-collg="3" data-colmd="3" data-colsm="3" data-colxs="2"><div class="item-content item-content-no-placeholder"><div class="gf_row gf_row-no-width gf_row_no_tools gf_row-no-padding">{% assign colXsClass = ' gf_col-xs-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colXsClass = colXsClass | append:cols %}{% assign colSmClass = ' gf_col-sm-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colSmClass = colSmClass | append:cols %}{% assign colMdClass = ' gf_col-md-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colMdClass = colMdClass | append:cols %}{% assign colLgClass = ' gf_col-lg-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colLgClass = colLgClass | append:cols %}{% assign colClass = 'gf_column gf_col_no_tools' %}{% assign colClass = colClass | append: colXsClass %}{% assign colClass = colClass | append: colSmClass %}{% assign colClass = colClass | append: colMdClass %}{% assign colClass = colClass | append: colLgClass %}{% assign gPLCount = 1 %}{% paginate collections["custom-naidoc-tote-bags"].products by 6 %}{% for product in collections["custom-naidoc-tote-bags"].products %}<div class="{{colClass}}" style="padding: 20px !important"><div data-label="Product" data-key="product" id="m-1680740657949-child{{forloop.index}}" data-ver="3.1" class="module-wrap" data-id="1680740657949-child{{forloop.index}}" data-index="1" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="40128661684358">{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1680740657949-child{{forloop.index}}-0" data-id="1680740657949-child{{forloop.index}}-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="80%" data-height="auto" style="width: 80%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="80%" data-height="auto" style="width: 80%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="80%" data-height="auto" style="width: 80%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="80%" data-height="auto" style="width: 80%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="80%" data-height="auto" style="width: 80%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1680740657949-child{{forloop.index}}-1" data-id="1680740657949-child{{forloop.index}}-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1680740657949-child{{forloop.index}}-15" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1680740657949-child{{forloop.index}}-15"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p><strong>From $5.50</strong></p></div></div><div data-label="(P) View More" data-key="p-view-more" data-atomgroup="child-product" id="m-1680740657949-child{{forloop.index}}-14" class="module-wrap" data-icon="gpicon-product-cartbutton" data-ver="1.0" data-id="1680740657949-child{{forloop.index}}-14"><div data-phandle="{{product.handle}}" class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '0' == '1' %}{% assign tg = '_blank' %}{% else %}{% assign tg = '' %}{% endif %}{% assign current_variant = product.selected_or_first_available_variant %}{% if '' != '' %}<a href="/products/{{ product.handle }}?variant={{current_variant.id}}" target="{{tg}}" class="gf_view-more  gf_gs-button-view-more gf_gs-button---large"><img src="" alt=""></a>{% else %}<a href="/products/{{ product.handle }}?variant={{current_variant.id}}" target="{{tg}}" class="gf_view-more button btn  gf_gs-button-view-more gf_gs-button---large"><span>Add Your Logo</span></a>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div>{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-xs"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-sm"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-md"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-lg"></div>{% endif %}{% assign gPLCount = gPLCount | plus: 1 %}{% endfor %}{% endpaginate %}</div></div></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1694570710737" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1694570710737" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1694570710720" data-id="1694570710720"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1694575812008" class="gf_row" data-icon="gpicon-row" data-id="1694575812008" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="12+12"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1694575812063" data-id="1694575812063"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1694575799801" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1694575799801"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">CONTACT US</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1694575849187" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1694575849187"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>Our sales team can provide you with expert advice and bring your concept to life! We have an extensive catalogue of products and artworks, so if you don’t see the perfect fit offered above contact <strong><EMAIL></strong> for more options&nbsp;</p><p></p></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1694577307475" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1694577307475"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-heading-2" data-gemlang="en" data-exc=""></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1694583829817" data-id="1694583829817"></div></div></div></div><script>window.__gemStoreData = {};window.__gemStoreData.gemKeyValid = {};window.__gemStoreData.gemKeyValid.bestWayAddToCart = 1;</script>