{%- if paginate.pages > 1 -%}
  <div class="Pagination Text--subdued">
    <div class="Pagination__Nav">
      {%- if paginate.previous -%}
        <a class="Pagination__NavItem Link Link--primary" rel="prev" title="{{ 'general.pagination.previous_page' | t }}" href="{{ paginate.previous.url | remove: 'view=ajax' }}{{ hash }}">
          {%- include 'icon' with 'select-arrow-left' -%}
        </a>
      {%- endif -%}

      {%- for part in paginate.parts -%}
        {%- if part.is_link -%}
          <a href="{{ part.url | remove: 'view=ajax' }}{{ hash }}" class="Pagination__NavItem Link Link--primary" title="{{ 'general.pagination.go_to_page' | t: page: part.title }}">{{ part.title }}</a>
        {%- else -%}
          <span class="Pagination__NavItem {% if part.title == paginate.current_page %}is-active{% endif %}">{{ part.title }}</span>
        {%- endif -%}
      {%- endfor -%}

      {%- if paginate.next -%}
        <a class="Pagination__NavItem Link Link--primary" rel="next" title="{{ 'general.pagination.next_page' | t }}" href="{{ paginate.next.url | remove: 'view=ajax' }}{{ hash }}">
          {%- include 'icon' with 'select-arrow-right' -%}
        </a>
      {%- endif -%}
    </div>
  </div>
{%- endif -%}