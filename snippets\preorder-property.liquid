{% assign shipnumber = 0 %}   
          
{% assign prodtags = product.tags %}
{% if prodtags contains 'preorder' %}
        
{% for tag in product.tags %}
          
    {% if tag contains 'SHIP_plus' %}

      {% assign plusdays = tag | remove: 'SHIP_plus' %}

      {% assign seconds = plusdays | times: 24 | times: 60 | times: 60 %}
      {% capture 'mymonth' %}{{ 'now' | date: "%s" | plus: seconds | date: "%m" | plus: 0 }}{% endcapture %}
      {% capture 'myday' %}{{ 'now' | date: "%s" | plus: seconds | date: "%d" | plus: 0 }}{% endcapture %}

      {% assign temp_day = myday %}
      {% assign temp_month = mymonth %}

      {%- assign monthsEnglish = 'January,February,March,April,May,June,July,August,September,October,November,December' | split:',' -%}
      {% assign temp_month = temp_month | minus: 1 %}
      
	  <input type="hidden" id="preorderid" name="properties[_preorder]" value="shipment on {{temp_day}} {{monthsEnglish[temp_month]}}">

      <div class="product__ship-date" style="padding: 0; text-align: center">
        {%- assign monthsEnglish = 'January,February,March,April,May,June,July,August,September,October,November,December' | split:',' -%}
        {% assign temp_month = temp_month | minus: 1 %}
        <p style="color: black; font-style: italic"><strong>Preorder shipment on {{temp_day}} {{monthsEnglish[temp_month]}}.</strong></p>
      </div>
          

    {% elsif tag contains 'SHIP_TEXT' %}

      {% assign shipdays = tag | split: 'SHIP_TEXT_' %}

      <input type="hidden" id="preorderid" name="properties[preorder]" value="shipment {{shipdays}} or earlier">


      <div class="product__ship-date" style="padding: 0; text-align: center">
        <p style="color: black; font-style: italic"><strong>Preorder shipment {{shipdays}} or earlier.</strong></p>
    </div>

    {% elsif tag contains 'SHIP_' %}

    {% assign shipnumber = shipnumber | plus:1 %}

      {% assign temp_ship = tag | remove: 'SHIP_' %}
      {% assign temp_date = temp_ship | split: '/' %}
      {% assign temp_day = temp_date[0] | plus: 0 %}
      {% assign temp_month = temp_date[1] | plus: 0 %}

    {% unless shipnumber > 1 %}

        {%- assign monthsEnglish = 'January,February,March,April,May,June,July,August,September,October,November,December' | split:',' -%}
        {% assign temp_month = temp_month | minus: 1 %}

        <input type="hidden" id="preorderid" name="properties[_preorder]" value="shipment on {{temp_day}} {{monthsEnglish[temp_month]}}">

        <div class="product__ship-date" style="padding: 0; text-align: center">
        {%- assign monthsEnglish = 'January,February,March,April,May,June,July,August,September,October,November,December' | split:',' -%}
        {% assign temp_month = temp_month | minus: 1 %}
        <p style="color: black; font-style: italic"><strong>Preorder shipment on {{temp_day}} {{monthsEnglish[temp_month]}}.</strong></p>
        </div>

    {% endunless %}

    {% endif %}
          
 
        
{% endfor %}
 
{% endif %}  

{% if product.metafields.prod.custom_text.value != blank %}

   <div class="product__ship-date" style="padding: 0; text-align: center">
      <p style="color: black; font-style: italic"><strong>{{ product.metafields.prod.custom_text.value }}</strong></p>
   </div>

{% endif %}
          