{% if product.metafields.custom.product_bundles2 != blank %}

  <style>
  .bundle-block {
    width: 33%;
    padding-left: 20px;
  }
  .bundle-block .Select.Select--primary {
    display: none;
  }
</style>
  
  {% assign bundles = product.metafields.custom.product_bundles2.value | split: "," %}
  {% assign inStock = true %}

  {% for bundle in bundles %}
    {% if all_products[bundle].available == false %}
        {% assign inStock = false %}
        {% break %}
    {% endif %}            
  {% endfor %}

 
  <section class="Section Section--bundles" style="margin-top: 10px">
  {% if inStock == true %}
  <div class="ProductListWrapper">
  		Bundle Components:
        <div style="flex-wrap: wrap; margin-left: 0" class="ProductList ProductList--gridd ProductList--removeMarginn Grid" data-mobile-count="2" data-desktop-count="4">
        <div class="" style="display: flex; margin-left: -20px; width: calc(100% + 20px)">
                {% for bundle in bundles %}

                  {% if all_products[bundle].available %}
                    
                      <div class="bundle-block">
                        {%- include 'product-item-bundle', bundleproduct: bundle, show_labels: true -%}
                      </div>
  
                  {% endif %}
                  
                {% endfor %}
         </div>
        </div>

</div>
    
{% else %}
<h2 style="padding: 20px; border: 1px solid black; text-align: center; margin: 30px 0">OUT OF STOCK</h2>
{% endif %}
</section>
{% endif %}

