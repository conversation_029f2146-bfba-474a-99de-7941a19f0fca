{% if product.metafields.custom.product_bundles != blank %}

  <style>
  .bundle-block {
    width: 33%;
    padding-left: 20px;
  }

  .Product__InfoWrapper form, .bulk-links {
    display: none!important;
  }
</style>
  
  {% assign bundles = product.metafields.custom.product_bundles.value | split: "," %}
  {% assign inStock = true %}

  {% for bundle in bundles %}
    {% if all_products[bundle].available == false %}
        {% assign inStock = false %}
        {% break %}
    {% endif %}            
  {% endfor %}

 
  <section class="Section Section--bundles" style="margin-top: 10px">
  {% if inStock == true %}
  <div class="ProductListWrapper">
  		
        <div style="flex-wrap: wrap; margin-left: 0" class="ProductList ProductList--gridd ProductList--removeMarginn Grid" data-mobile-count="2" data-desktop-count="4">
        <div class="" style="display: flex; margin-left: -20px; width: calc(100% + 20px)">
                {% for bundle in bundles %}

                  {% if all_products[bundle].available %}
                    
                      <div class="bundle-block">
                        {%- include 'product-item-bundle', bundleproduct: bundle, show_labels: true -%}
                      </div>
  
                  {% endif %}
                  
                {% endfor %}
         </div>
           <div class="p" style="width: 100%">
             <button onclick="obApi('track', 'Add To Cart');" class="ProductForm__AddToCart Button Button--primary Button--full"><span>Add Bundle to cart</span><span class="Button__SeparatorDot"></span><span>{{ product.price | money }}</span></button>
           </div>
        </div>

</div>

<script>
  
  $(document).ready( function() {

    $('.Section--bundles .ProductForm__AddToCart').click( function(e) {
        e.preventDefault();
        // .btn animation
      
        console.log(1);

        let bundleItems = [];

        $(this).closest('.ProductList').find('.product-form__variants option:selected').each( function(){
            let variantId = $(this).val();

            bundleItems.push({
                quantity: 1,
                id: variantId
            });
          
            console.log(2);
        });
      
        $(this).closest('.ProductList').find('.bundle-input').each( function(){
            let variantId = $(this).val();

            bundleItems.push({
                quantity: 1,
                id: variantId
            });
          
          console.log(bundleItems);
        });



        jQuery.post('/cart/add.js', {
            items: bundleItems
        }, function( data ) {
            console.log(4);
            window.location.href = '/cart';
        }, "json");


    });

  });

</script>
    
{% else %}
<h2 style="padding: 20px; border: 1px solid black; text-align: center; margin: 30px 0">OUT OF STOCK</h2>
{% endif %}
</section>
{% endif %}

