{%- comment -%} Multiple Addtocart App - Product Card Snippet {%- endcomment -%}

{%- comment -%} Error Handling {%- endcomment -%}
{%- capture soldout_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.soldout_text' }}{%- endcapture -%}
{%- capture description_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.description' }}{%- endcapture -%}
{%- capture from_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.from' }}{%- endcapture -%}
{%- capture save_text_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.save_text' }}{%- endcapture -%}
{%- capture in_stock_text_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.in_stock_text' }}{%- endcapture -%}

{%- comment -%} On Sale - Badge {%- endcomment -%}
{%- assign on_sale = false -%}
{%- if product.compare_at_price_min > product.price_min -%}
{%- assign on_sale = true -%}
{%- endif -%}

{%- comment -%} Soldout - Badge {%- endcomment -%}
{%- assign sold_out = true -%}
{%- if product.available  -%}
{%- assign sold_out = false -%}
{%- endif -%}

{%- comment -%} New - Badge for 1 Day old Products {%- endcomment -%}
{%- assign date_created = product.created_at | date:'%s' -%}
{%- assign date_now = 'now' | date:'%s' -%}
{%- assign difference = date_now | minus: date_created -%}

{%- comment -%} Get Varaint Details {%- endcomment -%}
{%- assign variant = product.variants.first -%}
{%- assign variant_qty = product.variants.first.inventory_quantity -%}

{%- if variant.inventory_management == 'shopify' and variant.inventory_policy != "continue" -%}
	{%- if variant_qty < 1 -%}
		{%- assign max = 0 -%}
        {%- assign stock_html = "" -%}
	{%- else -%}
		{%- assign max = variant_qty -%}
    	{%- assign stock_html = 'cws_bulk_add_to_cart.in_stock_text' | t | replace: in_stock_text_error, 'In Stock' | strip_newlines | append:": " | append: max -%}
	{%- endif -%}    
{%- else -%}
	{%- comment -%} When policy is "continue" then it wll allow to buy after out of stock {%- endcomment -%}
  	{%- assign max = "" -%}
{%- endif -%}






{% assign currentProductHandle = product.handle %}
{% assign currentProductCollection = "" %}
{% assign break_loop = false %}
{% assign bulkno = "" %}
{% assign bulkyes = "" %}

{% assign collectionHandles = "naidoc-week-2022-customizable-polos, naidoc-week-2022-customizable-contrast-polos, naidoc-week-2022-customizable-t-shirts, naidoc-week-2022-customizable-hoodies, naidoc-week-2022-customizable-stationery, naidoc-week-2022-customizable-water-bottles, naidoc-week-2022-customizable-coffee-mugs, naidoc-week-2022-customizable-beach-towels, naidoc-week-2022-customizable-headwear, naidoc-week-2022-customizable-bags, naidoc-week-2022-customizable-cotton-bags" %}

{% assign collectionArray = collectionHandles | split: ", " %}

{% for collectionHandle in collectionArray %}
  {% for product in collections[collectionHandle].products %}

    {% if product.handle == currentProductHandle %}

      {% assign currentProductCollection = collectionHandle %}

    {% break %}
    {% assign break_loop = true %}
    {% endif %}
  
  {% endfor %}

  {% if break_loop %}
    {% break %}
  {% endif %}

{% endfor %}


{% case currentProductCollection %}

  {% when 'naidoc-week-2022-customizable-polos' %}
     {% assign bulkno = "2760" %}
     {% assign bulkyes = "2870" %}

  {% when 'naidoc-week-2022-customizable-contrast-polos' %}
     {% assign bulkno = "3570" %}
     {% assign bulkyes = "3680" %}

  {% when 'naidoc-week-2022-customizable-t-shirts' %}
     {% assign bulkno = "1050" %}
     {% assign bulkyes = "1160" %}

  {% when 'naidoc-week-2022-customizable-hoodies' %}
     {% assign bulkno = "3395" %}
     {% assign bulkyes = "3590" %}

  {% when 'naidoc-week-2022-customizable-stationery' %}
     {% assign bulkno = "820" %}
     {% assign bulkyes = "875" %}

  {% when 'naidoc-week-2022-customizable-water-bottles' %}
     {% assign bulkno = "1780" %}
     {% assign bulkyes = "1890" %}

  {% when 'naidoc-week-2022-customizable-coffee-mugs' %}
     {% assign bulkno = "495" %}
     {% assign bulkyes = "550" %}

  {% when 'naidoc-week-2022-customizable-beach-towels' %}
     {% assign bulkno = "1970" %}
     {% assign bulkyes = "2025" %}

  {% when 'naidoc-week-2022-customizable-headwear' %}
     {% assign bulkno = "1310" %}
     {% assign bulkyes = "1370" %}

  {% when 'naidoc-week-2022-customizable-bags' %}
     {% assign bulkno = "420" %}
     {% assign bulkyes = "475" %}

  {% when 'naidoc-week-2022-customizable-cotton-bags' %}
     {% assign bulkno = "590" %}
     {% assign bulkyes = "650" %}

{% endcase %}






<tr class="resp-table-row multicart product-{{ variant.id }}" data-product-id="{{ variant.id }}" id="product-{{ variant.id }}">
	{%- comment -%} 1. Product Image Column {%- endcomment -%}
    {%- if show_prd_image == "yes" -%}
	<td class="table-body-cell pro-imgcell cws_prd_img">
    <div class="list-view-item__image-column product-img"> 
    <div class="product-label"> 
		{%- if on_sale and show_lbl_val == "yes" -%}
			<span class="badge-sale">{{ sale_label }}</span> 
        {%- else -%}
       		{%- if difference < new_label_days and sold_out == false and show_lbl_val == "yes" -%}
  	 		<span class="badge-new">{{ new_label }}</span>
			{%- endif -%}
		{%- endif -%}
		{%- if sold_out and show_lbl_val == "yes" -%} 
      		<span class="badge-soldout">{{ soldout_label }}</span> 
      	{%- endif -%} 
    </div>
    <a class="list-view-item quick-view" data-handle="{{ product.handle }}" style="cursor: pointer">
      
	{%- if product.images.size > 1 and disableflipper == "no" -%}
	<div class="reveal"> 		
    	<img class="list-view-item__image" src="{{ product.featured_image.src | img_url: '90X90' }}" alt="{{ product.featured_image.alt | escape }}">
		<div class="hidden">
		<div class="caption">
		<div class="centered"><img class="hidden" src="{{ product.images[1] | img_url: '90X90' }}" alt="{{ product.images[1].alt | escape }}" /></div>
		</div>
		</div>
	</div>
	{%- else -%} 
      <img class="list-view-item__image" src="{{ product.featured_image.src | img_url: '90X90' }}" alt="{{ product.featured_image.alt | escape }}" data-handle="{{ product.handle }}"> 
    {%- endif -%}  
	</a> 
    </div>
	</td>
    {%- endif -%}
  
    {%- comment -%} 2. Product Name Column {%- endcomment -%} 
    <td class="table-body-cell product-name cws_prd_name">
  		<div class="pr_hndl" style="display:none;">{{ product.handle }}</div>
		<div class="list-view-item__title">
          <a class="var_title_{{ variant.id }} quick-view" data-handle="{{ product.handle }}" style="cursor: pointer">{{ product.title }}</a>
          
          {% comment %}
          <a class="descBtn quick-view" data-toggle="tooltip" data-placement="left" data-handle="{{ product.handle }}" style="cursor: pointer"><i class="fa fa-angle-right" aria-hidden="true"></i> <span class="tooltip">{{ 'cws_bulk_add_to_cart.description' | t | replace: description_error, 'Description' | strip_newlines }}</span></a>
          {% endcomment %}
      </div>
    </td>
  
    {%- comment -%} 3. Brand / SKU Column {%- endcomment -%}
    {%- if show_brand_sku == "yes" -%}
    <td class="table-body-cell product-vendor cws_brand_sku">
     	{%- comment -%} Brand {%- endcomment -%}
     	{%- if product.vendor != blank -%}
     	<p><b>{{ 'cws_bulk_add_to_cart.brand_text' | t | replace: brand_text_error, 'Brand' | strip_newlines }}:</b> {{ product.vendor }}</p>
     	{%- endif -%}
     
     	{%- comment -%} SKU {%- endcomment -%}
    	{%- if product.selected_or_first_available_variant.sku != blank -%}
     	<p><b>{{ 'cws_bulk_add_to_cart.sku_text' | t | replace: sku_text_error, 'SKU' | strip_newlines }}:</b> {{ product.selected_or_first_available_variant.sku }}</p>
        {%- endif -%}
    </td>
    {%- endif -%}
  
    {%- comment -%} 4. Price Column {%- endcomment -%}
    <td class="table-body-cell product-price cws_price">
  	{%- assign compare_at_price = product.compare_at_price -%}
  	{%- assign price = product.price -%}
  	{%- assign price_varies = product.price_varies -%}
	{%- assign money_price = price | money -%}
    {%- assign min_price = product.price_min -%}
    {%- assign money_price_min = min_price | money -%}
	  
    {% if bulkno != blank %} 
      
      <p>
        <span class="product-price__price single-price money"><b>from {{ bulkno | money }}* <a style="cursor: pointer" data-fancybox data-src="#{{- currentProductCollection -}}"> (?)</a></b></span>
      </p>
      
    {%- else -%}
    
        {%- if price_varies -%}
            <p>
            <span class="product-price__price">{{ 'cws_bulk_add_to_cart.from' | t | replace: from_error, 'From' | strip_newlines }} </span>
            <span class="money min_price">{{ money_price_min }}</span>
            </p>
        {%- else -%}
            <p>
            <span class="product-price__price single-price money"><b>{{ money_price }}</b></span>
            </p>
            {%- if product.has_only_default_variant -%}
            {%- if product.compare_at_price and product.compare_at_price > product.price -%}
            <p>
            <span class="money compare_price" style="text-decoration:line-through;"><b>{{ compare_at_price | money }}</b></span>
            </p>
            <p>
            <span class="label savings">{{ 'cws_bulk_add_to_cart.save_text' | t | replace: save_text_error, 'Save' | strip_newlines }} {{ product.compare_at_price | minus: product.price | times: 100.0 | divided_by: product.compare_at_price | round }}%</span>
            </p>
            {%- endif -%}
            {%- endif -%}
        {%- endif -%}
      
    {%- endif -%}  
      
  </td>
  
  {%- if sold_out -%}
  {%- comment -%} 5. Qty Column {%- endcomment -%}
  <td class="table-body-cell product-qty cws_prd_qty"></td>
  
  {%- comment -%} 6. Action Column {%- endcomment -%}
  <td class="table-body-cell product-soldout">
  <span class="label soldout">{{ 'cws_bulk_add_to_cart.soldout_text' | t | replace: soldout_error, 'Soldout' | strip_newlines }}</span>
  </td>
  {%- else -%}
  {%- if product.variants.size > 1 -%} 
    
  	{%- comment -%} 5. Qty Column {%- endcomment -%}
  	<td class="table-body-cell product-qty cws_prd_qty"></td>
  
  	{%- comment -%} 6. Action Column {%- endcomment -%}
  	<td class="table-body-cell product-btn">
    <div class="qty-form">
    	<input data-variantproid="variantof-{{ product.id }}" type="button" value="{{ 'cws_bulk_add_to_cart.show_options' | t | replace: show_options_error, 'Show Options' | strip_newlines }}" name="selectVariant" class="showhidevariant btn" style="cursor:pointer;"/>
    </div>
    </td>
    </tr>  



	{%- comment -%} Product Variants Listing Inner Table {%- endcomment -%}
  	<tr class="variantof-{{ product.id }}" style="display:none;">
    <td class="show-product" colspan="6">
   	<table id="resp-table" class="prd_variants">
    	{%- capture option_titles -%}
    	{%- for product_option in product.options_with_values -%}
     	{{ product_option.name }} 
      	{%- if forloop.last == false -%},{%- endif-%}
  		{%- endfor -%}{%- endcapture -%}
    	{%- assign option_titles = option_titles | split: ','-%}	
        {%- assign total_options = option_titles | size -%}
     	<thead id="resp-table-header">
       	<tr>
            {% comment %}{%- if show_prd_image == "yes" -%}<th class="table-header-cell cws_prd_img">{{ 'cws_bulk_add_to_cart.product_image' | t | replace: product_image_error, 'Product Image' | strip_newlines }}</th>{%- endif -%}{% endcomment %}
        	{%- if total_options > 0 -%}<th class="table-header-cell cws_prd_options">{{ 'cws_bulk_add_to_cart.product_options' | t | replace: product_options_error, 'Product Options' | strip_newlines }}</th>{%- endif -%}
        	{%- if show_brand_sku == "yes" -%}<th class="table-header-cell cws_brand_sku">{{ 'cws_bulk_add_to_cart.brand_text' | t | replace: brand_text_error, 'Brand' | strip_newlines }}/{{ 'cws_bulk_add_to_cart.sku_text' | t | replace: sku_text_error, 'SKU' | strip_newlines }}</th>{%- endif -%}
        	<th class="table-header-cell cws_price">{{ 'cws_bulk_add_to_cart.price' | t | replace: price_error, 'Price' | strip_newlines }}</th>
        	<th class="table-header-cell cws_prd_qty">{{ 'cws_bulk_add_to_cart.quantity' | t | replace: quantity_error, 'Quantity' | strip_newlines }}</th>
         	<th></th>
       	</tr>
      	</thead>
      	<tbody>
    		{%- for variant in product.variants -%}
        	{%- assign variant_stock_html = "" -%}
     		{%- if variant.inventory_management == "shopify" -%}
       	 	{%- if variant.inventory_policy != "continue" -%}
   			{%- assign variant_max = variant.inventory_quantity -%}
            {%- assign variant_stock_html = 'cws_bulk_add_to_cart.in_stock_text' | t | replace: in_stock_text_error, 'In Stock' | strip_newlines | append:": " | append: variant_max -%}
        	{%- else-%}
      		{%- assign variant_max = "" -%}
			{%- endif -%}
       		{%- else -%}
            {%- assign variant_max = "" -%}
	   		{%- endif -%}
       
 			{%- comment -%} Product Variant Row {%- endcomment -%}
          	<tr class="resp-table-row prd_variant" data-variant-id="{{ variant.id }}" id="variant_{{ variant.id }}">
              {%- comment -%} 1. Image Column {%- endcomment -%}
              
              {% comment %}
              {%- if show_prd_image == "yes" -%}
          	  <td class="table-body-cell product-img cws_prd_img">
                <a href="{{ variant.url }}">
                {%- if variant.image.src == blank -%}
       			<img src="{{ product.featured_image | img_url: '90X90' }}">
                {%- else -%}
                <img src="{{ variant.image.src | img_url: '90X90' }}">  
                {%- endif -%}
                </a>
       		  </td>
              {%- endif -%}
              {% endcomment %}
              
              
              {%- comment -%} 2. Variant Option-1,2,3 contains Column {%- endcomment -%}
              {%- if total_options > 0 -%}
              <td class="table-body-cell product-options cws_prd_options">
              {%- if variant.option1 != blank -%}
          	  <p class="product-option1">
                <span class="option_title"><b>{{ option_titles[0] | strip | capitalize }}: </b></span> 
                <span class="option_value">{{ variant.option1 | strip }}</span> 
              </p>
              {%- endif -%}
          	
              {%- if variant.option2 != blank -%}
          	  <p class="product-option2">
                <span class="option_title"><b>{{option_titles[1] | strip | capitalize }}: </b></span> 
                <span class="option_value">{{ variant.option2 | strip  }}</span> 
              </p>
       		  {%- endif -%}
             
      		  {%- if variant.option3 != blank -%} 
              <p class="product-option3">
                <span class="option_title"><b>{{ option_titles[2] | strip | capitalize }}: </b></span> 
                <span class="option_value">{{ variant.option3 | strip }}</span> 
              </p>
       		  {%- endif -%}
              </td>
 			  {%- endif -%}
          	 
              {%- comment -%} 5. Brand/SKU Column {%- endcomment -%}
              {%- if show_brand_sku == "yes" -%}
              <td class="table-body-cell product-vendor cws_brand_sku">
     			{%- if product.vendor != blank -%}
     			<p><b>{{ 'cws_bulk_add_to_cart.brand_text' | t | replace: brand_text_error, 'Brand' | strip_newlines }}:</b> {{ product.vendor }}</p>
     			{%- endif -%}
     
    			{%- if variant.sku != blank -%}
     			<p><b>{{ 'cws_bulk_add_to_cart.sku_text' | t | replace: sku_text_error, 'SKU' | strip_newlines }}:</b> {{ variant.sku }}</p>
        		{%- endif -%}
               </td>
              {%- endif -%}
              
               {%- comment -%} 6.Price Column {%- endcomment -%}
               <td class="table-body-cell product-price cws_price">
                 
                {%- comment -%} BRAD UPDATE {%- endcomment -%}
                 
                {% if bulkno != blank %}

                  {%- if variant.title contains "No" -%}
                  <p><b><span class="money">from {{ bulkno | money }}</span></b></p>
                  {%- elsif variant.title contains "Yes" -%}
                  <p><b><span class="money">from {{ bulkyes | money }}</span></b></p>
                  {%- endif -%}
                 
                {% else %}
                 
                  {%- if variant.compare_at_price > variant.price -%}
                  <p>
                    <span class="money price"><b>{{ variant.price | money }}</b></span>
                    <span style="text-decoration:line-through;" class="money compare_price"><b>{{ variant.compare_at_price | money }}</b></span>
                  </p>
                  <p><span class="label savings">{{ 'cws_bulk_add_to_cart.save_text' | t | replace: save_text_error, 'Save' | strip_newlines }} {{ variant.compare_at_price | minus: variant.price | times: 100.0 | divided_by: variant.compare_at_price | round }}%</span></p>
                  {%- else -%}
                  <p><b><span class="money">{{ variant.price | money }}</span></b></p>
                  {%- endif -%}
                 
                 
               {% endif %}
                 
                 
                 
               </td>
              
               {%- comment -%} 7.Qty Column {%- endcomment -%}
               {%- if variant.available -%}
               <td class="table-body-cell product-qty cws_prd_qty">  
               <div class="qty-box clearfix">
                	<input style="cursor:pointer;" id="qtyminus-{{ variant.id }}" class="qtyminus" type="button" value="-">
                 	<input id="updates_{{ variant.id }}" class="list-product-qty currqty_{{ variant.id  }}" type="text" value="{{ preset_qty_val }}" name="updates[]" size="4" max="{{ variant_max }}" onkeyup="this.value = this.value.replace(/[^0-9]/g,&quot;&quot;)">
                 	<input style="cursor:pointer;" id="qtyplus-{{ variant.id }}" class="qtyplus" type="button" value="+">
            	</div>
                <div>{%- if show_in_stock == "yes" -%}{{ variant_stock_html }}{%- endif -%}</div>   
              	</td>
               {%- else -%}
               <td class="table-body-cell product-qty cws_prd_qty"></td>
               {%- endif -%}
              
               {%- comment -%} 8.Action Column {%- endcomment -%}
               {%- if variant.available -%}
               <td class="table-body-cell product-btn">
                 	<div class="qty-form">
                 	<form method="post" action="/cart/add" class="AddToCart">			
             			<input type="hidden" name="id" value="{{ variant.id }}" />
            			<button type="button" value="Buy now" id="{{ variant.id }}" class="btn singleCart">
              			<span>{{ 'cws_bulk_add_to_cart.single_add_to_cart_button_text' | t | replace: single_add_to_cart_button_text_error, 'Add to Cart' | strip_newlines }}</span> 
             			</button>
                   	</form>
                   </div>
            	</td>
               {%- else -%}
               <td class="table-body-cell product-soldout">
   			 	<span class="label soldout">{{ 'cws_bulk_add_to_cart.soldout_text' | t | replace: soldout_error, 'Soldout' | strip_newlines }}</span>
  	 		  </td>
              {%- endif -%}
          	</tr>
     		{%- endfor -%}
      </tbody>
     </table>
     </td>
	 </tr>
     {%- else -%}
  	 {%- comment -%} 5. Qty Column {%- endcomment -%}
  	 <td class="table-body-cell product-qty cws_prd_qty">
      	<div class="qty-box clearfix">
		<input style="cursor:pointer;" id="qtyminus-{{ product.id }}" class="qtyminus" type="button" value="-">
        <input id="updates_{{ product.variants.first.id  }}" class="list-product-qty currqty_{{ product.variants.first.id  }}" type="text" value="{{ preset_qty_val }}" name="updates[]" size="4" max="{{ max }}" onkeyup="this.value = this.value.replace(/[^0-9]/g,&quot;&quot;)">
        <input style="cursor:pointer;" id="qtyplus-{{ product.id }}" class="qtyplus" type="button" value="+">
		</div>
        <div class="qty-stock">{%- if show_in_stock == "yes" -%}{{ stock_html }}{%- endif -%}</div>
     </td>
     {%- comment -%} 6. Action Column {%- endcomment -%}
     <td class="table-body-cell product-btn">
        <div class="qty-form">
        <form method="post" action="/cart/add" class="AddToCart">			
        	<input type="hidden" name="id" value="{{ product.variants.first.id }}" />
        	<button type="button" value="Buy now" id="{{ variant.id }}" class="btn singleCart">
        	<span>{{ 'cws_bulk_add_to_cart.single_add_to_cart_button_text' | t | replace: single_add_to_cart_button_text_error, 'Add to Cart' | strip_newlines }}</span> 
        	</button>
      	</form>
       </div>
	 </td>
  {%- endif -%}
  {%- endif -%}
</tr>