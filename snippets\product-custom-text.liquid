{% for tag in product.tags %}
{% if tag contains 'PRODUCT_CustomText' %}
<style>
  #cb-sticky {
    display: none!important;
  }
  .cpf-text {
    padding: 15px;
    width: 100%;
    border: 1px solid #d2d2d2;
  }
  
  @font-face {
    font-family: 'Hello';
    src: url('https://cdn.shopify.com/s/files/1/0247/4021/files/HelloImperfectRegular.woff2?v=1675389775') format('woff2'),
        url('https://cdn.shopify.com/s/files/1/0247/4021/files/HelloImperfectRegular.woff?v=1675389775') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }
  
  input.cpf-input {
    font-family: 'Hello';
    font-size: 32px;
  }

  .ProductForm__AddToCart {
    display: none;
  }
  
</style>


{% assign textNum = 5 %}
  
  {% for tag in product.tags %}
  {% if tag contains 'PRODUCT_CustomText_' %}
    {% assign textNum = tag | split: "_" | last %}
  {% endif %}
  {% endfor %}

<div class="yarnCT" style="margin-bottom: 15px">
      <p style="margin-bottom: 4px"><strong>Your custom text ({{ textNum }} character max)</strong></p>
      <input type="text" class="cpf-input cpf-text" name="properties[Custom Text]" placeholder="" maxlength="{{ textNum }}">
 </div>
{% break %}
{% endif %}
{% endfor %}


{% for tag in product.tags %}
{% if tag contains 'PRODUCT_2CustomText' %}
<style>
  .cpf-text {
    padding: 15px;
    width: 100%;
    border: 1px solid #d2d2d2;
  }
</style>


{% assign textNum = 5 %}
  
  {% for tag in product.tags %}
  {% if tag contains 'PRODUCT_2CustomText_' %}
    {% assign textNum = tag | split: "_" | last %}
  {% endif %}
  {% endfor %}

<div class="yarnCT" style="margin: 10px 0 15px">
      <p style="margin-bottom: 4px"><strong>Your second custom text ({{ textNum }} character max)</strong></p>
      <input type="text" class="cpf-input cpf-text" name="properties[Custom Text 2]" placeholder="" maxlength="{{ textNum }}">
 </div>
{% break %}
{% endif %}
{% endfor %}

{% for tag in product.tags %}
{% if tag contains 'PRODUCT_CustomText' %}

<div class="Button Button--secondary cpf-atc cpf-atc--show" style="width: 100%; margin-bottom: 25px;"><span>ADD TO CART - PLEASE ADD YOUR TEXT</span></div>



<script>
  window.addEventListener('DOMContentLoaded', (event) => {
    const fakeBtn = document.querySelector(".cpf-atc--show");
    const realBtn = document.querySelector("form .ProductForm__AddToCart");
    const highlightedItems = document.querySelectorAll(".cpf-input");
    const yarnCTLength = highlightedItems.length;
    

    highlightedItems.forEach((userItem) => {
      
      userItem.addEventListener('input', () => {
          let yarnCTActive = 0;
          highlightedItems.forEach((userItem1) => {
        	if (userItem1.value.length != 0 ) {
              yarnCTActive += 1;
            }
          })
          if (yarnCTActive == yarnCTLength ) {
            fakeBtn.style.display = "none";
            realBtn.style.display = "block";
          }                         
      })
    
    });
    
    //console.log(highlightedItems.length);
  });
</script>

{% break %}
{% endif %}
{% endfor %}