{% if product.tags contains 'PRODUCT_PersonalisedName' %}
  <style>
    @font-face {
      font-family: 'Diary Notes';
      src: url('https://cdn.shopify.com/s/files/1/0247/4021/files/Diary-Notes.woff2?v=1668636602') format('woff2'), 
           url('https://cdn.shopify.com/s/files/1/0247/4021/files/Diary-Notes.woff?v=1668636603') format('woff');
      font-weight: normal;
      font-style: normal;
      font-display: swap;
    }
  
    @font-face {
      font-family: 'Breeder';
      src: url('https://cdn.shopify.com/s/files/1/0247/4021/files/Breeder-Regular.woff2?v=1668636603') format('woff2'), 
           url('https://cdn.shopify.com/s/files/1/0247/4021/files/Breeder-Regular.woff?v=1668636603') format('woff');
      font-weight: normal;
      font-style: normal;
      font-display: swap;
    }
  
    @font-face {
      font-family: 'Thunderstorm';
      src: url('https://cdn.shopify.com/s/files/1/0247/4021/files/Thunderstorm-Regular.woff2?v=1668636602') format('woff2'), 
           url('https://cdn.shopify.com/s/files/1/0247/4021/files/Thunderstorm-Regular.woff?v=1668636603') format('woff');
      font-weight: normal;
      font-style: normal;
      font-display: swap;
    }
  
    @font-face {
      font-family: 'Venti CF';
      src: url('https://cdn.shopify.com/s/files/1/0247/4021/files/VentiCF-Bold_972706ed-6296-487f-980e-d91b7b933665.woff2?v=1668636604') format('woff2'), 
           url('https://cdn.shopify.com/s/files/1/0247/4021/files/VentiCF-Bold_4de2bd49-31d9-4f13-bb78-3c0f09ff5f62.woff?v=1668636603') format('woff');
      font-weight: bold;
      font-style: normal;
      font-display: swap;
    }
  
    .cpf-input {
      border: 1px solid #e5dbdb;
      width: 100%;
      padding: 10px;
      margin-bottom: 12px;
    }
  
    .custom-product-font p {
      margin-bottom: 5px;
    }
  
    .custom-product-font {
      margin: 15px 0;
    }
  
    .custom-product-font.hide {
      display: none;
    }
  
    input.cpf-text {
      font-size: 20px;
    }
  
    input.cpf-input[data-font="Venti"],
    select[data-font="Venti"] {
      font-family: 'Venti CF';
    }
  
    input.cpf-input[data-font="Diary"],
    select[data-font="Diary"] {
      font-family: 'Diary Notes';
    }
  
    input.cpf-input[data-font="Breeder"],
    select[data-font="Breeder"] {
      font-family: 'Breeder';
      font-size: 32px;
    }
  
    input.cpf-input[data-font="Thunderstorm"],
    select[data-font="Thunderstorm"] {
      font-family: 'Thunderstorm';
      font-size: 28px;
    }
  
    .cpf-atc {
      display: none;
    }
  
    .cpf-atc.cpf-atc--show {
      display: block;
      width: 100%;
    }
  
    #cb-sticky {
      display: none !important;
    }
  
    .input-error {
      border-color: #f44336;
    }
  
    .tooltip-error {
      display: none;
      position: absolute;
      top: 100%;
      left: 0;
      background-color: #f44336;
      color: #fff;
      padding: 6px 8px;
      border-radius: 4px;
      font-size: 12px;
      margin-top: 4px;
      white-space: nowrap;
      z-index: 100;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
  
    .tooltip-error::after {
      content: '';
      position: absolute;
      top: -5px;
      left: 10px;
      border-width: 5px;
      border-style: solid;
      border-color: transparent transparent #f44336 transparent;
    }
  
    .input-wrapper {
      position: relative;
    }
  </style>
  
  <div class="custom-product-font hide">
    <div class="">
      <p>Choose Font Style</p>
      <select class="cpf-input cpf-select" id="icon" name="properties[Font]" data-font="Venti">
        <option value="Venti">Venti CF</option>
        <option value="Diary">Diary Notes</option>
        <option value="Breeder">Breeder</option>
        <option value="Thunderstorm">Thunderstorm</option>
      </select>
    </div>
  
    <div class="input-wrapper">
      <p>Your text (14 character max)</p>
      <input type="text" class="cpf-input cpf-text" data-font="Venti" name="properties[Custom Text]" placeholder="" maxlength="14" aria-label="Your text (14 character max)" aria-describedby="error-tooltip">
      <div id="error-tooltip" class="tooltip-error" role="alert" aria-live="assertive">
        Only letters, numbers, spaces, and apostrophes are allowed.
      </div>
    </div>
  
    <p style="color: #ea4450">
      <strong>NOTE: Personalised products will require an extra 10 days before dispatch</strong>.<br>Exchange or returns are not available on personalised products.
    </p>
  
    <div class="Button Button--secondary cpf-atc cpf-atc--show">
      <span>ADD TO CART - PLEASE ADD YOUR TEXT</span>
    </div>
  </div>
  
  
  <script>
    window.addEventListener('DOMContentLoaded', (event) => {
        const selectElement = document.querySelector('.cpf-select');
        const inputElement = document.querySelector('.cpf-text');
        const cpfATC = document.querySelector('.cpf-atc');
        const radioButtons = document.querySelectorAll('.ProductForm__Variants .variant-input input[name="id"]');
        const customProduct = document.querySelector('.custom-product-font');
        const tooltip = document.getElementById('error-tooltip');
        const addToCartButton = document.querySelector('button[data-action="add-to-cart"]');
  
        function validateInput() {
            const regex = /^[a-zA-Z0-9 ']*$/;
            return regex.test(inputElement.value);
        }
  
        function updateButtonState() {
            if (inputElement.value.trim() === '' || !validateInput()) {
                cpfATC.classList.add("cpf-atc--show");
                if (addToCartButton) {
                    addToCartButton.style.display = "none";
                }
            } else {
                cpfATC.classList.remove("cpf-atc--show");
                if (addToCartButton) {
                    addToCartButton.style.display = "block";
                }
            }
        }
  
        function CpfBtn() {
            if (!validateInput()) {
                tooltip.style.display = 'block';
                inputElement.classList.add('input-error');
            } else {
                tooltip.style.display = 'none';
                inputElement.classList.remove('input-error');
            }
            updateButtonState();
        }
  
        selectElement.addEventListener('change', (event) => {
            var value = selectElement.options[selectElement.selectedIndex].value;
            selectElement.dataset.font = value;
            inputElement.dataset.font = value;
        });
  
        inputElement.addEventListener('input', (event) => {
            CpfBtn();
        });
  
        function CpfElem(elem) {
            if (elem.dataset.vvalue == "yes") {
                customProduct.classList.remove("hide");
                selectElement.setAttribute('name', 'properties[Font]');
                inputElement.setAttribute('name', 'properties[Custom Text]');
                CpfBtn();
            } else {
                customProduct.classList.add("hide");
                selectElement.setAttribute('name', '');
                inputElement.setAttribute('name', '');
                cpfATC.classList.remove("cpf-atc--show");
                if (addToCartButton) {
                    addToCartButton.style.display = "block";
                }
            }
        }
  
        radioButtons.forEach(function(elem) {
            if (elem.checked) {
                CpfElem(elem);
            }
            elem.addEventListener("click", () => {
                if (elem.checked) {
                    CpfElem(elem);
                }
            });
        });
    });
  </script>
{% endif %}
  