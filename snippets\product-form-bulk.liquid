{%- comment -%}
Those are the option names for which we automatically detect swatch. For the color, we use them to display a swatch, while
for size, we use it to display a size chart (if applicable) 
{%- endcomment -%}

{%- assign color_label = 'color,colour,couleur,colore,farbe,색,色,färg,farve' | split: ',' -%}
{%- assign size_label = 'size,taille,bröße,tamanho,tamaño,koko,サイズ' | split: ',' -%}

{%- assign size_chart_page = '' -%}
{%- assign product_popovers = '' -%}

{%- assign selected_variant = product.selected_or_first_available_variant -%}


{% if template.suffix == "bulkimage" or template.suffix == "hidebutshow" %}

<form>
<div class="upload-lift" id="upload-lift"></div>
</form>
{% endif %}


{%- unless product.has_only_default_variant -%}
<div class="bulk-links">
<span class="size-guide">Size Chart</span>
</div>
{%- endunless -%}

<div class="saso-volumes"></div>
{% render 'special-offers' %}
{% render 'special-offers-ajax-lib' %}

{%- unless product.has_only_default_variant -%}
<div class="bulk-flex__wrap" style="">
           
            <div class="bulk-flex bulk-header" style="">
              <div>Size</div>
              <div>Qty (min 10)</div>
  			</div>
        
            {%- for variant in product.variants -%}

             <div class="bulk-flex bulk-flex-row" style="">
  
              <div data-value="{{ variant.id }}" class="{% unless variant.available %}is-soldout{% endunless %}">{% unless variant.available %}(Sold out) {% endunless %}{{ variant.title }}</div>
            
  			  {% if variant.available %}
              <div class="ProductForm__QuantitySelector">
                <div class="QuantitySelector QuantitySelector--large">
                  {%- assign quantity_minus_one = line_item.quantity | minus: 1 -%}

                  <span class="QuantitySelector__Button Link Link--secondary" data-action="decrease-quantity">{% include 'icon' with 'minus' %}</span>
                  <input type="number" class="QuantitySelector__CurrentQuantity" pattern="[0-9]*" min="0" max="{{ variant.inventory_quantity }}" data-inventory="{{variant.inventory_quantity}}" name="quantity" value="0">
                  <span class="QuantitySelector__Button Link Link--secondary" data-action="increase-quantity">{% include 'icon' with 'plus' %}</span>
                </div>
              </div>
              {% endif %}
               
  			</div>
       
              {%- endfor -%}
          
        
      </div>
    {%- else -%}
<div class="bulk-flex-row">
      <input type="hidden" name="id" data-productid="{{ product.id }}" data-sku="{{ selected_variant.sku }}" value="{{ selected_variant.id }}" data-value="{{ selected_variant.id }}">
      <div class="ProductForm__QuantitySelector">
        <div class="QuantitySelector QuantitySelector--large">
          {%- assign quantity_minus_one = line_item.quantity | minus: 1 -%}

          <span class="QuantitySelector__Button Link Link--secondary" data-action="decrease-quantity">{% include 'icon' with 'minus' %}</span>
          <input type="number" id="{%- if product.has_only_default_variant -%}input-onlydefault{%- endif -%}" class="QuantitySelector__CurrentQuantity" pattern="[0-9]*" min="10" name="quantity" value="10" data-inventory="{{selected_variant.inventory_quantity}}" >
          <span class="QuantitySelector__Button Link Link--secondary" data-action="increase-quantity">{% include 'icon' with 'plus' %}</span>
        </div>
      </div>
</div>
    {%- endunless -%}

    
   

      



{%- unless product.has_only_default_variant -%}     
<button class="ProductForm__AddToCart Button Button--secondary Button--full" data-action="add-to-cart-bulk" disabled="disabled">
    
      <span class="">{%if product.tags contains 'preorder'%}Pre-Order{% else %}{{ 'product.form.add_to_cart' | t }}{% endif %}</span>
      <span class="Button__SeparatorDot"></span>
      <span data-btnqty style="text-transform: lowercase"></span>
      <span data-money-convertible>MIN QTY 10</span>
   
</button>

{%- else -%}
<button class="ProductForm__AddToCart Button Button--primary Button--full" data-action="add-to-cart-bulk" >
    
      <span class="">{%if product.tags contains 'preorder'%}Pre-Order{% else %}{{ 'product.form.add_to_cart' | t }}{% endif %}</span>
      <span class="Button__SeparatorDot"></span>
      <span data-btnqty style="text-transform: lowercase">10 x&nbsp;</span>
      <span data-money-convertible>{{ product.selected_or_first_available_variant.price | times: 0.90 | money_without_trailing_zeros }}</span>
   
</button>
{%- endunless -%}



{% comment %}
{%- form 'product', product, data-productid: product.id, class: 'ProductForm' -%}
  <div class="ProductForm__Variants">
    {%- unless product.has_only_default_variant -%}
      {%- for option in product.options_with_values -%}
        {%- assign downcase_option = option.name | downcase -%}
        {%- capture popover_id -%}popover-{{ product.id }}-{{ section.id }}-{{ option.name | handle }}{%- endcapture -%}

        {%- if section.settings.show_color_swatch and color_label contains downcase_option -%}
          {%- assign is_option_with_color_swatch = true -%}
        {%- else -%}
          {%- assign is_option_with_color_swatch = false -%}
        {%- endif -%}

        <div class="ProductForm__Option">
          <button type="button" class="ProductForm__Item" aria-expanded="false" aria-controls="{{ popover_id }}">
            {%- if is_option_with_color_swatch -%}
              {%- assign downcase_value = option.selected_value | downcase -%}

              <span class="ProductForm__ColorSwatch {% if downcase_value == 'white' %}ProductForm__ColorSwatch--white{% endif %}" style="background-color: {{ option.selected_value | replace: ' ', '' }}; background-image: url({{ option.selected_value | handle | append: '.png' | asset_url }})"></span>
              <span class="ProductForm__SelectedValue">{{ option.selected_value }}</span>
              <span class="ProductForm__OptionCount Text--subdued">{{ 'product.form.colors_count' | t: count: option.values.size }}</span>
            {%- else -%}
              <span class="ProductForm__OptionName">{{ option.name }}: <span class="ProductForm__SelectedValue">{{ option.selected_value }}</span></span>
            {%- endif -%}

            {%- include 'icon' with 'select-arrow' -%}
          </button>

          {%- capture popover_html -%}
            {%- if color_label contains downcase_option and section.settings.show_color_carousel -%}
              {%- for value in option.values -%}
                {%- if value == option.selected_value -%}
                  {%- assign initial_image_index = forloop.index0 -%}
                  {%- break -%}
                {%- endif -%}
              {%- endfor -%}

              {%- capture flickity_options -%}
              {
                "prevNextButtons": true,
                "pageDots": true,
                "initialIndex": {{ initial_image_index }},
                "arrowShape": {"x0": 20, "x1": 60, "y1": 40, "x2": 60, "y2": 35, "x3": 25}
              }
              {%- endcapture -%}

              <div id="{{ popover_id }}" class="VariantSelector" aria-hidden="true">
                {%- capture option_index -%}option{{ option.position }}{%- endcapture -%}

                <div class="VariantSelector__Carousel Carousel" data-flickity-config='{{ flickity_options }}'>
                  {%- for value in option.values -%}
                    {%- for variant in product.variants -%}
                      {%- if variant[option_index] == value -%}
                        {%- assign variant_image = variant.image | default: product.featured_image -%}
                        {%- break -%}
                      {%- endif -%}
                    {%- endfor -%}

                    <div class="VariantSelector__Item Carousel__Cell {% if value == option.selected_value %}is-selected{% endif %}"
                         {% if is_option_with_color_swatch %}
                           data-background-color="{{ value | split: ' ' | last | handle }}"
                           data-background-image="{{ value | handle | append: '.png' | asset_url }}"
                         {% endif %}
                         data-option-position="{{ option.position }}"
                         data-option-value="{{ value | escape }}">
                      <div class="VariantSelector__ImageWrapper AspectRatio AspectRatio--withFallback" style="max-width: {{ variant_image.width }}px; padding-bottom: {{ 100.0 | divided_by: variant_image.aspect_ratio }}%; --aspect-ratio: {{ variant_image.aspect_ratio }}">
                        {%- include 'image-size', sizes: '200,400,600,800', image: variant_image -%}
                        {%- assign image_url = variant_image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}

                        <img class="VariantSelector__Image Image--lazyLoad Image--fadeIn" data-src="{{ image_url }}" data-widths="[{{ supported_sizes }}]" data-sizes="auto" alt="{{ variant_image.alt | escape }}">
                        <span class="Image__Loader"></span>
                      </div>
                    </div>
                  {%- endfor -%}
                </div>

                <div class="VariantSelector__Info">
                  <div class="VariantSelector__ChoiceList">
                    {%- for value in option.values -%}
                      {%- assign available_prices_for_option_value = '' -%}

                      {%- for variant in product.variants -%}
                        {%- if variant[option_index] == value -%}
                          {%- assign available_prices_for_option_value = available_prices_for_option_value | append: variant.price | append: ',' -%}
                        {%- endif -%}
                      {%- endfor -%}

                      {%- assign available_prices_for_option_value = available_prices_for_option_value | split: ',' | compact | uniq | sort -%}

                      <div class="VariantSelector__Choice {% if value == option.selected_value %}is-selected{% endif %}">
                        <div class="VariantSelector__ChoiceColor">
                          {%- if is_option_with_color_swatch -%}
                            {%- assign downcase_value = value | downcase -%}
                            <span class="VariantSelector__ColorSwatch {% if downcase_value == 'white' %}VariantSelector__ColorSwatch--white{% endif %}" style="background-color: {{ value | replace: ' ', '' }}; background-image: url({{ value | handle | append: '.png' | asset_url }})"></span>
                          {%- endif -%}

                          <span class="VariantSelector__ChoiceValue">{{ value }}</span>
                        </div>

                        <div class="VariantSelector__ChoicePrice">
                          {%- if available_prices_for_option_value.size > 1 -%}
                            {%- capture formatted_min_price -%}<span data-money-convertible>{{ available_prices_for_option_value.first | money_without_trailing_zeros }}</span>{%- endcapture -%}
                            {%- capture formatted_max_price -%}<span data-money-convertible>{{ available_prices_for_option_value.last | money_without_trailing_zeros }}</span>{%- endcapture -%}
                            <span class="Heading Text--subdued">{{ 'product.form.from_price_html' | t: min_price: formatted_min_price, max_price: formatted_max_price }}</span>
                          {%- else -%}
                            <span class="Heading Text--subdued" data-money-convertible>{{ available_prices_for_option_value.first | money_without_trailing_zeros }}</span>
                          {%- endif -%}
                        </div>
                      </div>
                    {%- endfor -%}
                  </div>

                  <button type="button" class="VariantSelector__Button Button Button--primary Button--full" data-action="select-variant">{{- 'product.form.select_model' | t -}}</button>
                </div>
              </div>
            {%- else -%}
              <div id="{{ popover_id }}" class="OptionSelector Popover Popover--withMinWidth" aria-hidden="true">
                <header class="Popover__Header">
                  <button type="button" class="Popover__Close Icon-Wrapper--clickable" data-action="close-popover">{% include 'icon' with 'close' %}</button>
                  <span class="Popover__Title Heading u-h4">{{ option.name | escape }}</span>
                </header>

                <div class="Popover__Content">
                  <div class="Popover__ValueList" data-scrollable>
                    {%- for value in option.values -%}
                      <button type="button" class="Popover__Value {% if value == option.selected_value %}is-selected{% endif %} Heading Link Link--primary u-h6"
                              data-value="{{ value | escape }}"
                              data-option-position="{{ option.position }}"
                              {% if is_option_with_color_swatch %}
                                data-background-color="{{ value | replace: ' ', '' }}"
                                data-background-image="{{ value | handle | append: '.png' | asset_url }}"
                              {% endif %}
                              data-action="select-value">
                        {{- value | escape -}}
                      </button>
                    {%- endfor -%}
                  </div>

                  {%- assign size_chart_page_handle = settings.size_chart_page | default: 'size-chart' -%}
                  {%- assign size_chart_page = pages[size_chart_page_handle] -%}

                  {%- if size_label contains downcase_option and size_chart_page != empty -%}
                    <button type="button" class="Popover__FooterHelp Heading Link Link--primary Text--subdued u-h6" data-action="open-modal" aria-controls="modal-{{ size_chart_page.handle }}">
                      {{- 'product.form.size_chart' | t -}}
                    </button>
                  {%- endif -%}
                </div>
              </div>
            {%- endif -%}
          {%- endcapture -%}

          {%- assign product_popovers = product_popovers | append: popover_html -%}
        </div>
      {%- endfor -%}

      <div class="no-js ProductForm__Option">
        <div class="Select Select--primary">
          {%- include 'icon' with 'select-arrow' -%}

          <select id="product-select-{{ product.id }}" name="id" data-productid="{{ product.id }}" title="Variant">
            {%- for variant in product.variants -%}
              <option {% if variant == selected_variant %}selected="selected"{% endif %} {% unless variant.available %}disabled="disabled"{% endunless %} value="{{ variant.id }}" data-sku="{{ variant.sku }}">{{ variant.title }} - {{ variant.price | money }}</option>
            {%- endfor -%}
          </select>
        </div>
      </div>
    {%- else -%}
      <input type="hidden" name="id" data-productid="{{ product.id }}" data-sku="{{ selected_variant.sku }}" value="{{ selected_variant.id }}">
    {%- endunless -%}

    
   

      <div class="ProductForm__QuantitySelector">
        <div class="QuantitySelector QuantitySelector--large">
          {%- assign quantity_minus_one = line_item.quantity | minus: 1 -%}

          <span class="QuantitySelector__Button Link Link--secondary" data-action="decrease-quantity">{% include 'icon' with 'minus' %}</span>
          <input type="number" class="QuantitySelector__CurrentQuantity" pattern="[0-9]*" min="10" name="quantity" value="10">
          <span class="QuantitySelector__Button Link Link--secondary" data-action="increase-quantity">{% include 'icon' with 'plus' %}</span>
        </div>
      </div>

   

    {%- if section.settings.show_inventory_quantity -%}
      {%- assign hide_inventory_quantity_by_default = false -%}
      
      {%- if selected_variant.inventory_management == blank or selected_variant.inventory_quantity <= 0 -%}
        {%- assign hide_inventory_quantity_by_default = true -%}
      {%- endif -%}

      {%- if section.settings.inventory_quantity_threshold != 0 and selected_variant.inventory_quantity > section.settings.inventory_quantity_threshold -%}
        {%- assign hide_inventory_quantity_by_default = true -%}
      {%- endif -%}

      <p class="ProductForm__Inventory Text--subdued" {% if hide_inventory_quantity_by_default %}style="display: none"{% endif %}>
        {%- if section.settings.inventory_quantity_threshold == 0 -%}
          {{- 'product.form.inventory_quantity_count' | t: count: selected_variant.inventory_quantity -}}
        {%- else -%}
          {{- 'product.form.low_inventory_quantity_count' | t: count: selected_variant.inventory_quantity -}}
        {%- endif -%}
      </p>
    {%- endif -%}
  </div>

  {%- comment -%}
  --------------------------------------------------------------------------------------------------------------------
  ADD TO CART BUTTON
  --------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}

  <button onclick="obApi('track', 'Add To Cart');" type="submit" class="hiya ProductForm__AddToCart Button {% if selected_variant.available and section.settings.show_payment_button == false %}Button--primary{% else %}Button--secondary{% endif %} Button--full" {% if selected_variant.available %}data-action="add-to-cart"{% else %}disabled="disabled"{% endif %}>
    {%- if selected_variant.available -%}
      <span class="">{% if product.template_suffix == 'PREORDER' or product.tags contains 'preorder' %}{{ 'product.form.pre_order' | t }}{% else %}{{ 'product.form.add_to_cart' | t }}{% endif %}</span>
      <span class="Button__SeparatorDot hiya"></span>
      <span data-money-convertible>{{ selected_variant.price | money_without_trailing_zeros }}</span>
    {%- else -%}
      {{- 'product.form.sold_out' | t -}}
    {%- endif -%}
  </button>
<div class="single_variant" style="display:none;"></div>
<div class="variants_all">
{% for variant in product.variants %}
<div data-id="{{variant.inventory_quantity}}" data-item="{{variant.id}}" data-val="{{ variant.price | money_without_trailing_zeros}}"></div>


{% endfor %}
</div>
  {%- if section.settings.show_payment_button -%}
    {{ form | payment_button }}
  {%- endif -%}
{%- endform -%}
{% endcomment %}


{%- if size_chart_page != empty -%}
  {%- comment -%}If we have a size chart we capture the modal content (it must be displayed outside the form for proper positioning){%- endcomment -%}

  {%- capture product_modals -%}
    <div id="modal-{{ size_chart_page.handle }}" class="Modal Modal--dark Modal--fullScreen Modal--pageContent" aria-hidden="true" role="dialog" data-scrollable>
      <header class="Modal__Header">
        <h2 class="Modal__Title Heading u-h1">{{ size_chart_page.title }}</h2>
      </header>

      <div class="Modal__Content Rte">
        <div class="Container Container--extraNarrow">
          {{- size_chart_page.content -}}
        </div>
      </div>

      <button class="Modal__Close RoundButton RoundButton--large" data-animate-bottom data-action="close-modal">{% include 'icon' with 'close' %}</button>
    </div>
  {%- endcapture -%}
{%- endif -%}

{%- if section.settings.show_payment_button and selected_variant.available == false -%}
  <style>
    .shopify-payment-button {
      display: none;
    }
  </style>
{%- endif -%}

{% comment %}
------------------------------------------------------------------------------
Product Data. This must be outputted for all products (including home page).

IMPORTANT: THIS CODE IS VITAL. DO NOT EDIT IT NOT REMOVE IT. MAKE SURE TO KEEP
THE EXACT SAME ATTRIBUTES.
------------------------------------------------------------------------------
{% endcomment %}

<script type="application/json" data-product-json>
  {
    "product": {{ product | json }},
    "selected_variant_id": {{ selected_variant.id }}
    {%- if section.settings.show_inventory_quantity -%}
      ,"inventories": {
        {%- for variant in product.variants -%}

          {%- assign inventory_message = '' -%}

          {%- if section.settings.inventory_quantity_threshold == 0 -%}
            {%- capture inventory_message -%}{{- 'product.form.inventory_quantity_count' | t: count: variant.inventory_quantity -}}{%- endcapture -%}
          {%- else -%}
            {%- capture inventory_message -%}{{- 'product.form.low_inventory_quantity_count' | t: count: variant.inventory_quantity -}}{%- endcapture -%}
          {%- endif -%}

          "{{ variant.id }}": {
            "inventory_management": {{ variant.inventory_management | json }},
            "inventory_policy": {{ variant.inventory_policy | json }},
            "inventory_quantity": {{ variant.inventory_quantity | json }},
            "inventory_message": {{ inventory_message | json }}
          }{% unless forloop.last %},{% endunless %}
         
        {%- endfor -%}
      }
    {%- endif -%}
  }
</script>