<style>
  .variant-input label{
    position: relative;
  }
  
  .variant-input .discounted:before {
    content: '%';
    position: absolute;
    width: 18px; height: 18px; background: #F94C43; color: white;
    top: -6px; right: -6px;
    border-radius: 50%;
    text-align: center;
    line-height: 18px;
    font-size: 10px;
  }
</style>


{% assign prodtags = product.tags %}

{%- form 'product', product, data-productid: product.id, class: 'ProductForm' -%}
 

  <div class="ProductForm__Variants" style="flex-wrap: wrap">
    
  <input type="hidden" name="id" data-productid="{{ product.id }}" data-sku="{{ selected_variant.sku }}" value="39574869999750">
  <input type="hidden" name="quantity" value="1">

  </div>


  <button type="submit" class="ProductForm__AddToCart Button Button--primary Button--full" data-action="add-to-cart">

      <span class="">{{ 'product.form.add_to_cart' | t }}</span>
      <span class="Button__SeparatorDot"></span>
      <span data-money-convertible>{{ selected_variant.price | money_without_trailing_zeros }}</span>
    
  </button>

<div class="single_variant" style="display:none;"></div>
<div class="variants_all">
{% for variant in product.variants %}

<div data-id="{{variant.inventory_quantity}}" data-item="{{variant.id}}" data-val="{{ variant.price | money_without_trailing_zeros}}"></div>

{% endfor %}
</div>


{%- endform -%}


{% comment %}
------------------------------------------------------------------------------
Product Data. This must be outputted for all products (including home page).

IMPORTANT: THIS CODE IS VITAL. DO NOT EDIT IT NOT REMOVE IT. MAKE SURE TO KEEP
THE EXACT SAME ATTRIBUTES.
------------------------------------------------------------------------------
{% endcomment %}

<script type="application/json" data-product-json>
  {
    "product": {{ product | json }},
    "selected_variant_id": {{ selected_variant.id }}
    {%- if section.settings.show_inventory_quantity -%}
      ,"inventories": {
        {%- for variant in product.variants -%}

          {%- assign inventory_message = '' -%}

          {%- if section.settings.inventory_quantity_threshold == 0 -%}
            {%- capture inventory_message -%}{{- 'product.form.inventory_quantity_count' | t: count: variant.inventory_quantity -}}{%- endcapture -%}
          {%- else -%}
            {%- capture inventory_message -%}{{- 'product.form.low_inventory_quantity_count' | t: count: variant.inventory_quantity -}}{%- endcapture -%}
          {%- endif -%}

          "{{ variant.id }}": {
            "inventory_management": {{ variant.inventory_management | json }},
            "inventory_policy": {{ variant.inventory_policy | json }},
            "inventory_quantity": {{ variant.inventory_quantity | json }},
            "inventory_message": {{ inventory_message | json }}
          }{% unless forloop.last %},{% endunless %}
          
        {%- endfor -%}
      }
    {%- endif -%}
  }
</script>