<style>
  .variant-input label{
    position: relative;
  }
  
  .variant-input .discounted:before {
    content: '%';
    position: absolute;
    width: 18px; height: 18px; background: #F94C43; color: white;
    top: -6px; right: -6px;
    border-radius: 50%;
    text-align: center;
    line-height: 18px;
    font-size: 10px;
  }

  .cpf-text {
    padding: 15px;
    width: 100%;
    border: 1px solid #d2d2d2;
  }
  
  .variant-input .alldiscounted:before {
    ddisplay: none!important;
  }
</style>

<style>
.shopify-customise-button {
    display: block;
    width: 100%;
    line-height: 1.4;
    padding-left: 5px;
    padding-right: 5px;
    white-space: normal;
    margin-top: 0;
    margin-bottom: 10px;
    min-height: 44px;
    padding: 10px 18px;
    text-align: center;
    border: 1px solid black;
    color: black;
    text-transform: uppercase;
    font-weight: 500;
}

.clock-title,
p.clock-sub-title,
.clock-table-div-label,
.clock-table-div-number {
    font-family: Visby CF,sans-serif;
}

#BIS_trigger {
    display: none;
}
</style>
<script>
function clickCustom(prodId) {
  let currentLocation = window.location.href ;
  console.log(`https://designer.naturalart.com.au/external/load_resource?mode=designer&product=${prodId}&callback_cancel_url=${currentLocation}`);
 
  window.location.href = `https://designer.naturalart.com.au/external/load_resource?mode=designer&product=${prodId}&callback_cancel_url=${currentLocation}`
}
</script>

{%- comment -%}
Those are the option names for which we automatically detect swatch. For the color, we use them to display a swatch, while
for size, we use it to display a size chart (if applicable) 
{%- endcomment -%}

{%- assign color_label = 'color,colour,couleur,colore,farbe,색,色,färg,farve' | split: ',' -%}
{%- assign size_label = 'size,taille,bröße,tamanho,tamaño,koko,サイズ' | split: ',' -%}

{%- assign size_chart_page = '' -%}
{%- assign product_popovers = '' -%}

{%- assign selected_variant = product.selected_or_first_available_variant -%}

{% assign prodtags = product.tags %}
{% capture prodtagz %}{{ product.tags }}{% endcapture %}


{% unless prodtags contains 'COLLECTION_YarnGallery' %}




 
<!--<div class="bulk-links">
{%- unless product.has_only_default_variant or product.handle contains 'lanyard' -%}
{%- assign floatright = true -%}
{%- endunless -%}

{% assign isbulkcollection = false %}
{% for c in product.collections %}
{% if c.handle == "bulk" %}
{% assign isbulkcollection = true %}
{% endif %}
{% endfor %}

{% comment %}
{%- unless product.compare_at_price > product.price -%}
    {% if isbulkcollection == true %}
    <h4 style="font-size: 13px; {% if floatright %}float: right;{% endif %}margin-bottom: 0">Buy more, save more! <a style="color: #106572" href="{{ product.url }}?view=bulk">Order in bulk here</a></h4>
    {% endif %}
{% endunless %}
{% endcomment %}

{% unless product.price < product.compare_at_price %}
{% if isbulkcollection == true %}
  <h4 style="font-size: 15px; font-weight: 800;{% if floatright %}float: right;{% endif %}margin-bottom: 0">Buy more, save more! <a style="color: #106572; font-weight: 800;" href="{{ product.url }}?view=bulk">Order in bulk here</a></h4>
{% endif %}
{% endunless %}
  
  
  </div>-->

{% endunless %}


{%- form 'product', product, data-productid: product.id, class: 'ProductForm' -%}
{% if template.suffix == "bulkimage" or template.suffix == "hidebutshow" or product.tags contains "upload" %}
  
{% unless hasupload == true %}
<div class="upload-lift" id="upload-lift"></div>
<div class="yarnCT" style="margin-bottom: 15px">
      <p style="margin-bottom: 4px">Additional details for customisation</p>
      <input type="text" class="cpf-input cpf-text" name="properties[Details]" placeholder="" maxlength="">
 </div>
{% endunless %}


{% endif %}

  <div class="ProductForm__Variants" style="flex-wrap: wrap" {% if product.variants.size == 1 %} style="display: none;" {% endif %} >
    
    
    {%- unless product.has_only_default_variant -%}
      {%- for option in product.options_with_values -%}
    
    
        {%- assign downcase_option = option.name | downcase -%}
        {%- capture popover_id -%}popover-{{ product.id }}-{{ section.id }}-{{ option.name | handle }}{%- endcapture -%}


        {%- if section.settings.show_color_swatch and color_label contains downcase_option -%}
          {%- assign is_option_with_color_swatch = true -%}
        {%- else -%}
          {%- assign is_option_with_color_swatch = false -%}
        {%- endif -%}
    
    
        {% unless product.template_suffix == "hidebutshow"%}
    
        {%- unless product.has_only_default_variant -%}
          {%- for option in product.options_with_values -%}



              <div class="variant-btn ProductForm__Option" id="ProductSelect-option-{{ forloop.index0 }}" name="{{ option.name | handleize }}" style="width: 100%">
                
                
                <legend style="
                  text-transform: uppercase;
                  margin-bottom: 5px;
                  font-size: 12px;
                  letter-spacing: 1px;
                 ">
                  {{ option.name | escape }}
                </legend>
                
                
                
                {% comment %}
                BELOW: FIND OUT IF ALL VARIANTS HAVE A DISCOUNT OR NOT, TO DECIDE IF WE SHOW SALES BADGE ON VARIANT
                {% endcomment %}
                
                
                {% assign alldiscount = false %}
                {% assign discountnum = 0 %}
                
                {%- for value in option.values -%}
                {% unless value contains '% off)' %}
                
                  {% if product.variants[forloop.index0].compare_at_price > product.variants[forloop.index0].price or product.variants[forloop.index0].available == false %}
                    {% assign discountnum = discountnum | plus: 1 %}
                
                    {% comment %}<div class="d1-{{ product.variants[forloop.index0].compare_at_price }}  d2-{{ product.variants[forloop.index0].price }}"></div>{% endcomment %}
                  {% endif %}
                  
                  {% if forloop.length == discountnum %}
                    {% assign alldiscount = true %}
                    <script>
                      console.log('all variants discounted');
                    </script>
                  {% endif %}
                {%endunless%}
                {%- endfor -%}
                
                
                
                
                {%- for value in option.values -%}
                {% unless value contains '% off)' %}
                  {%- assign variant_label_state = true -%}

                  {%- if product.options.size == 1 -%}
                    {%- unless product.variants[forloop.index0].available -%}
                      {%- assign variant_label_state = false -%}
                    {%- endunless -%}
                  {%- endif -%}
                <div class="variant-input {{product.options.size}}">
                  <input type="radio"
                    {% if option.selected_value == value %} checked="checked"{% endif %}
                    {% unless variant_label_state %} disabled="disabled"{% endunless %}
                    value="{{product.variants[forloop.index0].id}}"
                    data-img="{{product.variants[forloop.index0].image}}"
                    data-vvalue="{{ value | downcase }}"
                    data-index="option{{ forloop.index }}"
                    name="id" data-productid="{{ product.id }}"
                    data-inventory="{% if product.variants[forloop.index0].inventory_quantity < 0 %}0{% else %}{{product.variants[forloop.index0].inventory_quantity}}{% endif %}"
                    data-price="{{ product.variants[forloop.index0].price | money_without_trailing_zeros }}"
                    data-compare-price="{{ product.variants[forloop.index0].compare_at_price | money_without_trailing_zeros }}"
                    id="ProductSelect-option-{{ option.name | handleize }}-{{ value | escape }}"  
                    >
                  
                  {% if variant_label_state %}
                  
                      {% if alldiscount == true and product.price_varies %}

                          {% capture discountvar %}
                            {{ product.variants[forloop.index0].compare_at_price | minus:product.variants[forloop.index0].price | plus:10 | times:100 | divided_by:product.variants[forloop.index0].compare_at_price }}
                          {% endcapture %}

                          <label id="discount-{{product.variants[forloop.index0].id}}" class="{% if product.price_varies and product.variants[forloop.index0].compare_at_price > product.variants[forloop.index0].price %}discounted {% endif %}{% if alldiscount == false %}{%- if product.variants[forloop.index0].compare_at_price > product.variants[forloop.index0].price -%}discounted{%- endif -%}{% endif %}"  for="ProductSelect-option-{{ option.name | handleize }}-{{ value | escape }}">
                              {{ value | escape }}
                          </label>
                  
                          <style>
                            #discount-{{product.variants[forloop.index0].id}}:before {
                              content: '{{discountvar | strip }}%';
                              font-size: 8px;
                              width: 21px;
                              height: 21px;
                              line-height: 21px;
                              font-weight: bold;
                              top: -8px;
                            }
                          </style>

                      {% else %}

                        <label class="  {% if product.price_varies and product.variants[forloop.index0].compare_at_price > product.variants[forloop.index0].price %}discounted {% endif %}{% if alldiscount == false %}{%- if product.variants[forloop.index0].compare_at_price > product.variants[forloop.index0].price -%}discounted{%- endif -%}{% endif %}"  for="ProductSelect-option-{{ option.name | handleize }}-{{ value | escape }}">
                          {{ value | escape }}
                        </label>

                      
                      {% endif %}
                  
               
                  
                  {% else %}
                    <a href="#" class="klaviyo-bis-trigger" data-variant-id={{product.variants[forloop.index0].id}}>
                      <label for="ProductSelect-option-{{ option.name | handleize }}-{{ value | escape }}">
                        {{ value | escape }}
                      </label>
                    </a>
                  {% endif %}
                </div>
                {%endunless%}
                {%- endfor -%}
                
               {% if prodtagz contains 'size-chart' %}
                 <a class="size-guide">Size Chart</a>
               {%- endif-%}
                
              </div>
    
    

        {%- endfor -%}
      {%- endunless -%}
    
      {%- endunless -%}
    
    

        {% if product.template_suffix == "hidebutshow" %}
		
        <div class="ProductForm__Option hiya-{{product.template_suffix}}" style="">
          <button type="button" class="ProductForm__Item" aria-expanded="false" aria-controls="{{ popover_id }}">
            {%- if is_option_with_color_swatch -%}
              {%- assign downcase_value = option.selected_value | downcase -%}

              <span class="ProductForm__ColorSwatch {% if downcase_value == 'white' %}ProductForm__ColorSwatch--white{% endif %}" style="background-color: {{ option.selected_value | replace: ' ', '' }}; background-image: url({{ option.selected_value | handle | append: '.png' | asset_url }})"></span>
              <span class="ProductForm__SelectedValue">{{ option.selected_value }}</span>
              <span class="ProductForm__OptionCount Text--subdued">{{ 'product.form.colors_count' | t: count: option.values.size }}</span>
            {%- else -%}
              <span class="ProductForm__OptionName">{{ option.name }}: <span class="ProductForm__SelectedValue">{{ option.selected_value }}</span></span>
            {%- endif -%}

            {%- include 'icon' with 'select-arrow' -%}
          </button>

          {%- capture popover_html -%}
            {%- if color_label contains downcase_option and section.settings.show_color_carousel -%}
              {%- for value in option.values -%}
              {% unless value contains '% off)' %}
                {%- if value == option.selected_value -%}
                  {%- assign initial_image_index = forloop.index0 -%}
                  {%- break -%}
                {%- endif -%}
              {%endunless%}  
              {%- endfor -%}

              {%- capture flickity_options -%}
              {
                "prevNextButtons": true,
                "pageDots": true,
                "initialIndex": {{ initial_image_index }},
                "arrowShape": {"x0": 20, "x1": 60, "y1": 40, "x2": 60, "y2": 35, "x3": 25}
              }
              {%- endcapture -%}

              <div id="{{ popover_id }}" class="VariantSelector" aria-hidden="true">
                {%- capture option_index -%}option{{ option.position }}{%- endcapture -%}

                <div class="VariantSelector__Carousel Carousel" data-flickity-config='{{ flickity_options }}'>
                  {%- for value in option.values -%}
                    {%- for variant in product.variants -%}

                      {%- if variant[option_index] == value -%}
                        {%- assign variant_image = variant.image | default: product.featured_image -%}
                        {%- break -%}
                      {%- endif -%}
                      
                    {%- endfor -%}

                    <div class="VariantSelector__Item Carousel__Cell {% if value == option.selected_value %}is-selected{% endif %}"
                         {% if is_option_with_color_swatch %}
                           data-background-color="{{ value | split: ' ' | last | handle }}"
                           data-background-image="{{ value | handle | append: '.png' | asset_url }}"
                         {% endif %}
                         data-option-position="{{ option.position }}"
                         data-option-value="{{ value | escape }}">
                      <div class="VariantSelector__ImageWrapper AspectRatio AspectRatio--withFallback" style="max-width: {{ variant_image.width }}px; padding-bottom: {{ 100.0 | divided_by: variant_image.aspect_ratio }}%; --aspect-ratio: {{ variant_image.aspect_ratio }}">
                        {%- include 'image-size', sizes: '200,400,600,800', image: variant_image -%}
                        {%- assign image_url = variant_image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}

                        <img class="VariantSelector__Image Image--lazyLoad Image--fadeIn" data-src="{{ image_url }}" data-widths="[{{ supported_sizes }}]" data-sizes="auto" alt="{{ variant_image.alt | escape }}">
                        <span class="Image__Loader"></span>
                      </div>
                    </div>  
                  {%- endfor -%}
                </div>

                <div class="VariantSelector__Info">
                  <div class="VariantSelector__ChoiceList">
                    {%- for value in option.values -%}
                    {% unless value contains '% off)' %}
                      {%- assign available_prices_for_option_value = '' -%}

                      {%- for variant in product.variants -%}
                        {%- if variant[option_index] == value -%}
                          {%- assign available_prices_for_option_value = available_prices_for_option_value | append: variant.price | append: ',' -%}
                        {%- endif -%}
                      {%- endfor -%}

                      {%- assign available_prices_for_option_value = available_prices_for_option_value | split: ',' | compact | uniq | sort -%}

                      <div class="VariantSelector__Choice {% if value == option.selected_value %}is-selected{% endif %}">
                        <div class="VariantSelector__ChoiceColor">
                          {%- if is_option_with_color_swatch -%}
                            {%- assign downcase_value = value | downcase -%}
                            <span class="VariantSelector__ColorSwatch {% if downcase_value == 'white' %}VariantSelector__ColorSwatch--white{% endif %}" style="background-color: {{ value | replace: ' ', '' }}; background-image: url({{ value | handle | append: '.png' | asset_url }})"></span>
                          {%- endif -%}

                          <span class="VariantSelector__ChoiceValue">{{ value }}</span>
                        </div>

                        <div class="VariantSelector__ChoicePrice">
                          {%- if available_prices_for_option_value.size > 1 -%}
                            {%- capture formatted_min_price -%}<span data-money-convertible>{{ available_prices_for_option_value.first | money_without_trailing_zeros }}</span>{%- endcapture -%}
                            {%- capture formatted_max_price -%}<span data-money-convertible>{{ available_prices_for_option_value.last | money_without_trailing_zeros }}</span>{%- endcapture -%}
                            <span class="Heading Text--subdued">{{ 'product.form.from_price_html' | t: min_price: formatted_min_price, max_price: formatted_max_price }}</span>
                          {%- else -%}
                            <span class="Heading Text--subdued" data-money-convertible>{{ available_prices_for_option_value.first | money_without_trailing_zeros }}</span>
                          {%- endif -%}
                        </div>
                      </div>
                    {%endunless%}  
                    {%- endfor -%}
                  </div>

                  <button type="button" class="VariantSelector__Button Button Button--primary Button--full" data-action="select-variant">{{- 'product.form.select_model' | t -}}</button>
                </div>
              </div>
            {%- else -%}
              <div id="{{ popover_id }}" class="OptionSelector Popover Popover--withMinWidth" aria-hidden="true">
                <header class="Popover__Header">
                  <button type="button" class="Popover__Close Icon-Wrapper--clickable" data-action="close-popover">{% include 'icon' with 'close' %}</button>
                  <span class="Popover__Title Heading u-h4">{{ option.name | escape }}</span>
                </header>

                <div class="Popover__Content">
                  <div class="Popover__ValueList hiya" data-scrollable>
                    {%- for value in option.values -%}
                    {% unless value contains '% off)' %}
                    
                      {%- for variant in product.variants -%}
                        {%- if variant.title == value -%}
                          {%- assign option_inventory = variant.inventory_quantity -%}
                    
                          {% if variant.available == true %}
                    		{%- assign option_inventory = 99 -%}
                          {%- endif -%}
                           
                        {%- endif -%}
                      {%- endfor -%}
                    
                      <button type="button" data-inventory="{{option_inventory}}" class="Popover__Value {% if value == option.selected_value %}is-selected{% endif %} Heading Link Link--primary u-h6"
                              data-value="{{ value | escape }}"
                              data-option-position="{{ option.position }}"
                              {% if is_option_with_color_swatch %}
                                data-background-color="{{ value | replace: ' ', '' }}"
                                data-background-image="{{ value | handle | append: '.png' | asset_url }}"
                              {% endif %}
                              data-action="select-value" {% if option_inventory == 0 %}disabled{% endif %}>
                        {% if option_inventory == 0 %}(Sold out) {% endif %}{{- value | escape -}}
                      </button>
                    {%endunless%}
                    {%- endfor -%}
                  </div>

                  {%- assign size_chart_page_handle = settings.size_chart_page | default: 'size-chart' -%}
                  {%- assign size_chart_page = pages[size_chart_page_handle] -%}

                  {%- if size_label contains downcase_option and size_chart_page != empty -%}
                    <button type="button" class="Popover__FooterHelp Heading Link Link--primary Text--subdued u-h6" data-action="open-modal" aria-controls="modal-{{ size_chart_page.handle }}">
                      {{- 'product.form.size_chart' | t -}}
                    </button>
                  {%- endif -%}
                </div>
              </div>
            {%- endif -%}
          {%- endcapture -%}

          {%- assign product_popovers = product_popovers | append: popover_html -%}
          
          
          
        </div>
    
    
      {%- endif -%} {% comment %} If Dropdown Template {% endcomment %}
    
    
      {%- endfor -%}

      <div class="no-js ProductForm__Option">
        <div class="Select Select--primary">
          {%- include 'icon' with 'select-arrow' -%}

          <select id="product-select-{{ product.id }}" name="id" data-productid="{{ product.id }}" title="Variant">
            {%- for variant in product.variants -%}
              <option {% if variant == selected_variant %}selected="selected"{% endif %} {% unless variant.available %}disabled="disabled"{% endunless %} value="{{ variant.id }}" data-sku="{{ variant.sku }}">{{ variant.title }} - {{ variant.price | money }}</option>
            {%- endfor -%}
          </select>
        </div>
      </div>
    {%- else -%}
      <input type="hidden" name="id" data-productid="{{ product.id }}" data-sku="{{ selected_variant.sku }}" value="{{ selected_variant.id }}">
    {%- endunless -%}
    
    
    <style>
      
      .inv-labels span {
        color: white;
        display: inline-block;
        border-radius: 12px;
        padding: 3px 8px;
        font-size: 10px;
        font-weight: 600;
        
        display: none;
      }
      
      .label-one {
        background: red;
      }
      
      .label-few {
        background: orange;
      }
    </style>
    
    {% if product.available %}
    
    <div class="inv-labels">
      <span class="label-one">Only 1 left</span>
      <span class="label-few">Only a few left</span>
    </div>
    
    {% endif %}

    
    {% if template.suffix == 'bulk' %}

      <div class="ProductForm__QuantitySelector">
        <div class="QuantitySelector QuantitySelector--large">
          {%- assign quantity_minus_one = line_item.quantity | minus: 1 -%}

          <span class="QuantitySelector__Button Link Link--secondary" data-action="decrease-quantity">{% include 'icon' with 'minus' %}</span>
          <input type="text" id="{%- if product.has_only_default_variant -%}input-onlydefault{%- endif -%}" class="QuantitySelector__CurrentQuantity" pattern="[0-9]*" min="10" name="quantity" data-inventory="{{selected_variant.inventory_quantity}}" value="10">
          <span class="QuantitySelector__Button Link Link--secondary" data-action="increase-quantity">{% include 'icon' with 'plus' %}</span>
        </div>
      </div>

      {% elsif product.tags contains 'MOQ-25' %}
      
      <div class="ProductForm__QuantitySelector">
        <div class="QuantitySelector QuantitySelector--large">
          
          <span class="QuantitySelector__Button Link Link--secondary qty-minus" data-action="decrease-quantity">{% include 'icon' with 'minus' %}</span>
          <input type="text" id="{%- if product.has_only_default_variant -%}input-onlydefault{%- endif -%}" class="QuantitySelector__CurrentQuantity" pattern="[0-9]*" min="100" name="quantity" data-inventory="{{selected_variant.inventory_quantity}}" value="100">
          <span class="QuantitySelector__Button Link Link--secondary" data-action="increase-quantity">{% include 'icon' with 'plus' %}</span>
        </div>
      </div>

      <script>
        function validateQuantity() {
          var quantityInput = document.getElementById('QuantityInput').value;
          if (quantityInput < 100) {
            alert("Minimum order quantity for this product is 100.");
            return false;
          }
          return true;
        }
      </script>
      
      {% elsif product.tags contains 'MOQ-100' %}

      <div class="ProductForm__QuantitySelector">
        <div class="QuantitySelector QuantitySelector--large">
          
          <span class="QuantitySelector__Button Link Link--secondary qty-minus" data-action="decrease-quantity">{% include 'icon' with 'minus' %}</span>
          <input type="text" id="{%- if product.has_only_default_variant -%}input-onlydefault{%- endif -%}" class="QuantitySelector__CurrentQuantity" pattern="[0-9]*" min="100" name="quantity" data-inventory="{{selected_variant.inventory_quantity}}" value="100">
          <span class="QuantitySelector__Button Link Link--secondary" data-action="increase-quantity">{% include 'icon' with 'plus' %}</span>
        </div>
      </div>

      <script>
        function validateQuantity() {
          var quantityInput = document.getElementById('QuantityInput').value;
          if (quantityInput < 100) {
            alert("Minimum order quantity for this product is 100.");
            return false;
          }
          return true;
        }
      </script>

     {% elsif product.tags contains "showqty" %}

      <div class="ProductForm__QuantitySelector">
        <div class="QuantitySelector QuantitySelector--large">
          
          <span class="QuantitySelector__Button Link Link--secondary qty-minus" data-action="decrease-quantity">{% include 'icon' with 'minus' %}</span>
          <input type="text" id="{%- if product.has_only_default_variant -%}input-onlydefault{%- endif -%}" class="QuantitySelector__CurrentQuantity" pattern="[0-9]*" name="quantity" data-inventory="99999" value="10">
          <span class="QuantitySelector__Button Link Link--secondary" data-action="increase-quantity">{% include 'icon' with 'plus' %}</span>
        </div>
      </div>

    {% else %}

    <input type="hidden" name="quantity" value="1">

    {% endif %}
    

  </div>

  {% comment %} 
    For products with Font Addon 
    use "PRODUCT_PersonalisedName" tag 
  {% endcomment %}
  {% render 'product-font' %}

  {% comment %} 
    For products with Custom text
    use "PRODUCT_CustomText" tag (defaults to 5 letters)
    or "PRODUCT_CustomText_[number here]" to add max letters 
    {% endcomment %}
  {% render 'product-custom-text' %}

  {%- include 'preorder-property' -%}

  {%- comment -%}
  --------------------------------------------------------------------------------------------------------------------
  ADD TO CART BUTTON
  --------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}
{% unless product.tags contains 'freegift' %}
  <button onclick="obApi('track', 'Add To Cart'); pintrk('track', 'addtocart'){% if product.tags contains 'MOQ-100' %}; return validateQuantity(){% endif %}" type="submit" class="ProductForm__AddToCart Button {% if selected_variant.available and section.settings.show_payment_button == false %}Button--primary{% else %}Button--primary{% endif %} Button--full" {% if selected_variant.available %}data-action="add-to-cart"{% else %}disabled="disabled"{% endif %}>
    {%- if selected_variant.available -%}
      <span class="button-atc">{% if product.template_suffix == 'PREORDER' or product.tags contains 'preorder' %}{{ 'product.form.pre_order' | t }}{% else %}{{ 'product.form.add_to_cart' | t }}{% endif %}</span>
      {% if product.metafields.custom.min_qty %}
      <span class="Button__SeparatorDot"></span>
      <span data-money-convertible>MIN 10</span>
      {% else %}
        {% if product.tags contains 'hidepriceatc' %}
          <!-- Add additional logic here if needed for 'hidepriceatc' tag -->
        {% else %}
          {% comment %}
          //Hidden as per 09-08 task
          <span class="Button__SeparatorDot"></span>
          <span data-money-convertible>{{ selected_variant.price | money_without_trailing_zeros }}</span>
          {% endcomment %}
        {% endif %}
      {% endif %}
    {%- else -%}
      {% if product.tags contains 'comingsoon' %}Coming Soon{% else %}{{- 'product.form.sold_out' | t -}}{% endif %}
    {%- endif -%}
  </button>
  
    {% render 'custom-button-url', section: section, product: product %}
  

  {% if product.variants.size == 1 and selected_variant.available == false %}
    <a href="#" class="Button Button--full Button-primary klaviyo-bis-trigger">Get notified when we restock</a>
  {% endif %}

  
  {% render 'yarn-rewards-btn' %}
  
  {% if section.settings.timer_custom_html != empty %}
    {% style %}
      .ia-countdown-timer-title,
      .ia-countdown-timer-subheading,
      .ia-countdown-wrapper {
        font-family: 'Visby CF';
      }
    {% endstyle %}
    <div class="ia-countdown-timer-block" id="ia-countdown-timer-block-{{ section.settings.timer_custom_html }}"></div>
  {% endif %}
  

  {% for tag in product.tags %}
    {% if tag contains "customisabl" %}

      {% assign cp_id = tag | split: "_" | last%}
        <!--    https://cottoncreations.secure-decoration.com/external/load_resource?mode=designer&amp;product=219136908&amp;callback_cancel_url=https://dev.durationsports.com/product/toddler-t-shirt-long-sleeve-by-rabbit-skins/ -->
        <button  class="shopify-customise-button" type="button" onclick="clickCustom({{cp_id}})">Start Designing</button>
    {% endif %}
  {% endfor %}
{% endunless %}
  <div class="single_variant" style="display:none;"></div>
  <div class="variants_all">
    {% for variant in product.variants %}
      <div data-id="{% if variant.inventory_quantity < 0 %}0{% else %}{{ variant.inventory_quantity }}{% endif %}" data-item="{{variant.id}}" data-val="{{ variant.price | money_without_trailing_zeros}}"></div>
    {% endfor %}
  </div>

  {% comment %}
  {% if product.tags contains 'TeePromo' %}
    <img style="margin: 20px 0" src="https://cdn.shopify.com/s/files/1/0247/4021/files/2Tees_3rdFree.png?v=1646974802">
  {% endif %}   
  {% endcomment %}

  {% unless product.tags contains 'PRODUCT_PersonalisedName' %}
    {%- if section.settings.show_payment_button -%}
      {{ form | payment_button }}
    {%- endif -%}
  {% endunless %}
{%- endform -%}

{% comment %}
  <div class="saso-volumes findme" {% unless template.suffix != 'bulk' or product.tags contains '#yarncorpcustom' %}style="display: none"{% endunless %}></div>
{% endcomment %}
{% unless product.tags contains '#yarncorpcustom' %}
  <div class="saso-volumes findme"></div>
{% endunless %}
  
{%- if size_chart_page != empty -%}
  {%- comment -%}If we have a size chart we capture the modal content (it must be displayed outside the form for proper positioning){%- endcomment -%}
  {%- capture product_modals -%}
    <div id="modal-{{ size_chart_page.handle }}" class="Modal Modal--dark Modal--fullScreen Modal--pageContent" aria-hidden="true" role="dialog" data-scrollable>
      <header class="Modal__Header">
        <h2 class="Modal__Title Heading u-h1">{{ size_chart_page.title }}</h2>
      </header>

      <div class="Modal__Content Rte">
        <div class="Container Container--extraNarrow">
          {{- size_chart_page.content -}}
        </div>
      </div>

      <button class="Modal__Close RoundButton RoundButton--large" data-animate-bottom data-action="close-modal">{% include 'icon' with 'close' %}</button>
    </div>
  {%- endcapture -%}
{%- endif -%}

{%- if section.settings.show_payment_button and selected_variant.available == false -%}
  <style>
    .shopify-payment-button {
      display: none;
    }
  </style>
{%- endif -%}

{% if product.tags contains 'freegift' %}
  <style>
    .shopify-payment-button,
    .cbb-frequently-bought-container {
      display: none;
    }
  </style>
{% endif %}


{% comment %}
------------------------------------------------------------------------------
Product Data. This must be outputted for all products (including home page).

IMPORTANT: THIS CODE IS VITAL. DO NOT EDIT IT NOT REMOVE IT. MAKE SURE TO KEEP
THE EXACT SAME ATTRIBUTES.
------------------------------------------------------------------------------
{% endcomment %}

<script type="application/json" data-product-json>
  {
    "product": {{ product | json }},
    "selected_variant_id": {{ selected_variant.id }}
    {%- if section.settings.show_inventory_quantity -%}
      ,"inventories": {
        {%- for variant in product.variants -%}

          {%- assign inventory_message = '' -%}

          {%- if section.settings.inventory_quantity_threshold == 0 -%}
            {%- capture inventory_message -%}{{- 'product.form.inventory_quantity_count' | t: count: variant.inventory_quantity -}}{%- endcapture -%}
          {%- else -%}
            {%- capture inventory_message -%}{{- 'product.form.low_inventory_quantity_count' | t: count: variant.inventory_quantity -}}{%- endcapture -%}
          {%- endif -%}

          "{{ variant.id }}": {
            "inventory_management": {{ variant.inventory_management | json }},
            "inventory_policy": {{ variant.inventory_policy | json }},
            "inventory_quantity": {{ variant.inventory_quantity | json }},
            "inventory_message": {{ inventory_message | json }}
          }{% unless forloop.last %},{% endunless %}
          
        {%- endfor -%}
      }
    {%- endif -%}
  }
</script>

{% if product.metafields.custom.min_qty %}

  <style>
    .ProductForm__AddToCart span:not(:first-child) {
      display: none;
    }

    .ProductForm__AddToCart:disabled span {
      display: inline;
    }
  </style>
        
  <script>
  document.addEventListener("DOMContentLoaded", () => {

    document.querySelector('.Product__Tabs .Collapsible:first-child button').click();

    let qtyBtnArray = document.querySelectorAll('.ProductForm__QuantitySelector .QuantitySelector__Button');
    let buyBtn = document.querySelector('.ProductForm__AddToCart');

    qtyBtnArray.forEach(radio => radio.addEventListener('click', () => {

      setTimeout(function(){

        let inputBtn = document.querySelector('.ProductForm__QuantitySelector .QuantitySelector__CurrentQuantity');
  
      	if(inputBtn.value < 10) {
            buyBtn.disabled = true;
      	} else {
            buyBtn.disabled = false;
        }
  
      }, 500); 
  
    }));

  });

    
  </script>
{% endif %}