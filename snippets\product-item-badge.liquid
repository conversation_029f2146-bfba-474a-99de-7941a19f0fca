<div class="ProductItem {% if use_horizontal %}ProductItem--horizontal{% endif %}">
  <div class="ProductItem__Wrapper">
    {%- comment -%}
    We are using LazySizes to efficiently choose the most appropriate image in the set. However, because internally LazySizes uses srcset, we need to make
    sure that the image sizes we offer is not larger than the max size uploaded by the merchants.
    {%- endcomment -%}

    {%- if settings.product_image_size == 'natural' or use_horizontal -%}
      {%- assign use_natural_size = true -%}
    {%- else -%}
      {%- assign use_natural_size = false -%}
    {%- endif -%}

    {%- if settings.product_show_secondary_image and product.images[1] != blank and use_horizontal != true -%}
      {%- assign has_alternate_image = true -%}
    {%- else -%}
      {%- assign has_alternate_image = false -%}
    {%- endif -%}

    <a href="{{ product.url }}" class="ProductItem__ImageWrapper {% if has_alternate_image %}ProductItem__ImageWrapper--withAlternateImage{% endif %}">
      {%- if use_horizontal -%}
        {%- assign max_width = 125 -%}
      {%- else -%}
        {%- assign max_width = product.featured_image.width -%}
      {%- endif -%}

      <div class="AspectRatio AspectRatio--{% if use_natural_size %}withFallback{% else %}{{ settings.product_image_size }}{% endif %}" style="max-width: {{ max_width }}px; {% if use_natural_size %}padding-bottom: {{ 100.0 | divided_by: product.featured_image.aspect_ratio }}%;{% endif %} --aspect-ratio: {{ product.featured_image.aspect_ratio }}">
        {%- comment -%}
        IMPLEMENTATION NOTE: The alternate image (not visible by default) should be the first in the DOM, as the spinner (Image__Loader element) is
        displayed based on the immediately previously shown image.
        {%- endcomment -%}

        {%- if has_alternate_image -%}
          {%- include 'image-size', sizes: '200,300,400,600,800,900,1000,1200', image: product.images[1] -%}
          {%- assign image_url = product.images[1] | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}

          <img
            class="ProductItem__Image ProductItem__Image--alternate Image--lazyLoad Image--fadeIn"
            data-src="{{ image_url }}"
            data-widths="[{{ supported_sizes }}]"
            data-sizes="auto"
            alt="{{ product.images[1].alt | escape }}"
            data-image-id="{{ product.images[1].id }}">
        {%- endif -%}

        {%- include 'image-size', sizes: '200,400,600,700,800,900,1000,1200', image: product.featured_image -%}
        {%- assign image_url = product.featured_image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}

        <img
          class="ProductItem__Image Image--lazyLoad Image--fadeIn"
          data-src="{{ image_url }}"
          data-widths="[{{ supported_sizes }}]"
          data-sizes="auto"
          alt="{{ product.featured_image.alt | escape }}"
          data-image-id="{{ product.featured_image.id }}">
        <span class="Image__Loader"></span>

        <noscript>
          <img
            class="ProductItem__Image ProductItem__Image--alternate"
            src="{{ product.images[1] | img_url: '400x' }}"
            alt="{{ product.images[1].alt | escape }}">
          <img
            class="ProductItem__Image"
            src="{{ product.featured_image | img_url: '400x' }}"
            alt="{{ product.featured_image.alt | escape }}">
        </noscript>
        {% if collection.metafields.custom.badge != blank %}
          <div class="test">{{ collection.metafields.custom.badge }}</div>
        {% endif %}
      </div>
    </a>

    {%- if show_labels -%}
      {%- capture product_labels -%}
        {%- for tag in product.tags -%}
          {%- if tag contains '__label' -%}
            <span class="ProductItem__Label Heading Text--subdued">{{ tag | split: '__label:' | last }}</span>
            {%- break -%}
          {%- endif -%}
        {%- endfor -%}



        {%- if product.available -%}
          {%- if product.compare_at_price > product.price -%}

            {% capture discount %}
              {{ product.compare_at_price | minus: product.price | plus: 10 | times: 100 | divided_by: product.compare_at_price }}%
            {% endcapture %}



            <span class="ProductItem__Label Heading Text--subdued">Save {{ discount }}</span>
          {%- endif -%}
        {%- else -%}
          <span class="ProductItem__Label Heading Text--subdued label-so">{{ 'product.labels.sold_out' | t }}</span>
        {%- endif -%}
      {%- endcapture -%}

      {%- if product_labels != blank -%}
        <div class="ProductItem__LabelList">
          {{ product_labels }}
        </div>


      {%- endif -%}
    {%- endif -%}

    <div class="ProductItem__LabelList cust_right">
      {% if product.tags contains 'NEW' %}
        <span class="ProductItem__Label Heading Text--subdued cus_right_title label-new">New</span>
      {% endif %}
      {% if product.tags contains 'preorder' %}
        <span class="ProductItem__Label Heading Text--subdued cus_right_title label-po">Pre Order</span>
      {% endif %}
    </div>

    <div class="ProductItem__Info {% unless use_horizontal %}ProductItem__Info--center{% endunless %}">
      <h2 class="ProductItem__Title Heading" style="">
        <a href="{{ product.url | within: collection }}{% if collection.template_suffix == "bulk" %}?view=bulk{% endif %}">{{ product.title }}</a>
      </h2>


      {%- for tag in product.tags -%}
        {% if tag contains 'BRAND_' %}
          {% assign brand = tag | split: "BRAND_" %}
          <p style="margin-bottom: 5px; text-transform: uppercase">
            <small>{{ brand }}</small>
          </p>
        {% endif %}
      {% endfor %}



      {%- assign color_swatch_list = '' -%}



      {%- capture color_swatch -%}
        {%- capture color_name -%}
          {{ section.id }}-{{ product.id }}-{% increment color_name %}{%- endcapture -%}

        {%- for option in product.options_with_values -%}
          {%- assign downcased_option = option.name | downcase -%}

          {%- if downcased_option == 'color' or downcased_option == 'colour' or downcased_option == 'couleur' or downcased_option == 'size' or downcased_option == 'sizes' -%}
            {%- assign variant_option = 'option' | append: forloop.index -%}

            {%- for value in option.values -%}
              {%- assign downcased_value = value | downcase -%}
              {%- assign is_avail = false -%}
              {%- capture color_id -%}
                {{ section.id }}-{{ product.id }}-{% increment color_index %}{%- endcapture -%}

              {%- for variant in product.variants -%}

                {%- if variant[variant_option] == value -%}
                  {%- assign variant_for_value = variant -%}
                  {%- break -%}
                {%- endif -%}
                {% if variant.available %}
                  {%- assign is_avail = true -%}
                {% endif %}

              {%- endfor -%}

              {%- assign variant_label_state = true -%}
              {%- if product.options.size == 1 -%}
                {%- unless product.variants[forloop.index0].available -%}
                  {%- assign variant_label_state = false -%}
                {%- endunless -%}
              {%- endif -%}

              <div class="ProductItem__ColorSwatchItem">
                {%- if variant_for_value.image -%}
                  {%- include 'image-size', sizes: '200,400,600,700,800,900,1000,1200', image: variant_for_value.image -%}
                  {%- assign variant_image_url = variant_for_value.image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}
                {%- endif -%}

                {% comment %}
                <input class="ColorSwatch__Radio" type="radio" name="{{ color_name }}" id="{{ color_id }}" value="{{ value | escape }}" {% if option.selected_value == value %}checked="checked"{% endif %} data-variant-url="{{ variant_for_value.url }}" {% if variant_for_value.image %}data-image-id="{{ variant_for_value.image.id }}" data-image-url="{{ variant_image_url }}" data-image-widths="[{{ supported_sizes }}]" data-image-aspect-ratio="{{ variant_for_value.image.aspect_ratio }}"{% endif %} aria-hidden="true">
                <label class="ColorSwatch ColorSwatch--small" for="{{ color_id }}" style="background-color: {{ value | replace: ' ', '' | downcase }}; background-image: url({{ value | handle | append: '.png' | asset_url }})" title="{{ value | escape }}" data-tooltip="{{ value | escape }}"></label>
                {% endcomment %}

                <label class="{% unless variant_label_state %}notavail{% endunless %} ColorSwatch ColorSwatch--small">{{ value | replace: ' ', '' | downcase }}</label>

              </div>
            {%- endfor -%}
          {%- endif -%}
        {%- endfor -%}


      {%- endcapture -%}


      {%- if color_swatch != blank -%}
        {%- capture color_swatch_list -%}
          <div class="ProductItem__ColorSwatchList">
            {{- color_swatch -}}
          </div>
        {%- endcapture -%}
      {%- endif -%}



      {%- if show_price_on_hover == nil -%}
        {%- assign show_price_on_hover = settings.product_show_price_on_hover -%}
      {%- endif -%}
      
      {%- if show_price_on_hover and color_swatch_list != blank -%}
        {{- color_swatch_list -}}
      {%- endif -%}
      
      {% assign lowest_price = product.first_available_variant.price %}
      
      {% for variant in product.variants %}
        {% if variant.available %}
          {% if variant.price < lowest_price %}
            {% assign lowest_price = variant.price %}
          {% endif %}
        {% endif %}
      {% endfor %}       
      
      <div class="ProductItem__PriceList {% if show_price_on_hover %}ProductItem__PriceList--showOnHover{% endif %} Heading">
        {%- if product.compare_at_price > lowest_price -%}
          <span class="ProductItem__Price Price Price--highlight Text--subdued" data-money-convertible>{{ lowest_price | money_without_trailing_zeros }}</span>
          <span class="ProductItem__Price Price Price--compareAt Text--subdued" data-money-convertible>{{ product.compare_at_price | money_without_trailing_zeros }}</span>
        {%- elsif product.price_varies -%}
          {%- capture formatted_min_price -%}
            <span data-money-convertible>{{ lowest_price | money_without_trailing_zeros }}</span>
          {%- endcapture -%}
          {%- capture formatted_max_price -%}
            <span data-money-convertible>{{ product.price_max | money_without_trailing_zeros }}</span>
          {%- endcapture -%}
          <span class="ProductItem__Price Price Text--subdued">{{ 'collection.product.from_price_html' | t: min_price: formatted_min_price, max_price: formatted_max_price }}{% if product.metafields.custom.from_price != blank %}
              {{ product.metafields.custom.from_price }}{% endif %}
          </span>
        {%- else -%}
          <span class="ProductItem__Price Price Text--subdued" data-money-convertible>{{ product.price | money_without_trailing_zeros }}</span>
        {%- endif -%}
        {% if product.price > 11999 %}
          <span class="ProductItem__Label Heading Text--subdued cus_right_title label-fs">Free Shipping</span>
        {% endif %}
      
        {% if product.tags contains 'cotton'or product.tags contains "__badge:Cotton" %}
          <span class="ProductItem__Label Heading Text--subdued cus_right_title label-cot" style="float: none; display: inline-block; margin-left: 10px">Cotton</span>
        {% endif %}
        {% if product.tags contains 'recycled' %}
          <span class="ProductItem__Label Heading Text--subdued cus_right_title label-rec" style="float: none; display: inline-block; margin-left: 10px">100% Recycled</span>
        {% endif %}
        {% if product.tags contains 'ART_CODE' %}
          <span class="artcode"></span>
        {% endif %}
      </div>      

      {% if product.tags contains '__badge:2 Tees 3rd Free' %}
        <span class="ProductItem__Label Heading Text--subdued label-2 Tees 3rd Free" style="float: none; display: inline-block; margin-left: 0px">2 Tees 3rd Free</span>
      {% endif %}



      {%- if show_price_on_hover == false and color_swatch_list != blank -%}
        {{- color_swatch_list -}}
      {%- endif -%}
    </div>

  </div>

  {%- if use_horizontal -%}
    <a href="{{ associated_product.url }}" class="ProductItem__ViewButton Button Button--secondary hidden-pocket">{{ 'collection.product.view_product' | t }}</a>
  {%- endif -%}

  <!-- Swym Wishlist Plus EPI Button-->
  <button
    data-with-epi="true"
    class="swym-button swym-add-to-wishlist-view-product product_{{product.id}}"
    data-swaction="addToWishlist"
    data-product-id="{{product.id | json}}"
    data-variant-id="{{product.variants[0].id}}"
    data-product-url="{{ shop.url }}{{ product.url }}"></button>
  <!-- Swym Wishlist Plus EPI Button-->

</div>