<div class="ProductItemm">
  <div class="ProductItem__Wrapper">

      {%- assign use_natural_size = false -%}
      {%- assign has_alternate_image = true -%}

        {% if gift %}
        {%- assign product = gift -%}
        {% else %}
        {%- assign productt = all_products[bundleproduct] -%}
        {% endif %}


        {%- assign max_width = productt.featured_image.width -%}
    
   
      
      <div class="AspectRatio AspectRatio--tall" style="max-width: {{ max_width }}px;  --aspect-ratio: 0.66666">

          <img class="ProductItem__Image ProductItem__Image--alternate" src="{{ productt.images[1] | img_url: '600x' }}" alt="{{ productt.images[1].alt | escape }}">
          <img class="ProductItem__Image" src="{{ productt.featured_image | img_url: '600x' }}" alt="{{ productt.featured_image.alt | escape }}">
        
      </div>
     
  
   
      <div class="ProductItem__Info {% unless use_horizontal %}ProductItem__Info--{{ settings.product_info_alignment }}{% endunless %}">
        <h4 class="ProductItem__Title Heading" style="font-size: 14px;line-height: 19px;margin-bottom: 10px;margin-top: -10px;">
          {{ productt.title }}
        </h4>

          {%- unless productt.has_only_default_variant -%}
           <div class="ProductForm__Option">
              <div class="Select Select--primary">
                {%- include 'icon' with 'select-arrow' -%}

                <select id="product-select-{{ productt.id }}" name="id" data-productid="{{ productt.id }}" title="Variant" class="product-form__variants">
                  {%- for variant in productt.variants -%}
                    <option {% if variant == selected_variant %}selected="selected"{% endif %} {% unless variant.available %}disabled="disabled"{% endunless %} value="{{ variant.id }}" data-sku="{{ variant.sku }}">{{ variant.title }}</option>
                  {%- endfor -%}
                </select>
              </div>
            </div>
          {%- else -%}
          <input class="bundle-input" type="hidden" name="id" data-productid="{{ productt.id }}"  value="{{ productt.selected_or_first_available_variant.id }}">
          
          {%- endunless -%}
        
        

      
      </div>
    
  </div>

  
</div>