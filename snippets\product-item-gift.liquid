<div class="ProductItemm">
  <div class="ProductItem__Wrapper">

      {%- assign use_natural_size = false -%}
      {%- assign has_alternate_image = true -%}
      
      {% if gift %}
      {%- assign product = gift -%}
      {% else %}
      {%- assign product = all_products[bundleproduct] -%}
      {% endif %}
      
    <span class="ProductItem__ImageWrapper">

        {%- assign max_width = product.featured_image.width -%}

      <div class="aAspectRatio aAspectRatio--tall" style="max-width: {{ max_width }}px;  --aaspect-ratio: 1">

          <img class="ProductItem__Image" src="{{ product.featured_image | img_url: '480x480' }}" alt="{{ product.featured_image.alt | escape }}" style="filter: grayscale(0);">
        
      </div>
      
    </span>

    {% comment %}
    <div class="ProductItem__LabelList cust_right">
          <span class="ProductItem__Label Heading Text--subdued cus_right_title label-po">FREE</span>
    </div>
    {% endcomment %}
   
      <div class="ProductItem__Info {% unless use_horizontal %}ProductItem__Info--{{ settings.product_info_alignment }}{% endunless %}">
        
        <h2 class="ProductItem__Title Heading">
          {{ product.title }}
        </h2>

        
        <div class="ProductItem__PriceList Heading">
          
            <span class="ProductItem__Price Price Text--subdued" data-money-convertible>$0.00</span>
            <span class="ProductItem__Price Price Price--compareAt Text--subdued" data-money-convertible>{{ product.price | money_without_trailing_zeros }}</span>
         
          
        </div>
        
        
        
        
        {% comment %}
         {%- unless product.has_only_default_variant -%}
           <div class="ProductForm__Option">
              <div class="Select Select--primary">
                {%- include 'icon' with 'select-arrow' -%}

                <select id="product-select-{{ product.id }}" name="id" data-productid="{{ product.id }}" title="Variant" class="product-form__variants">
                  {%- for variant in product.variants -%}
                    <option {% if variant == selected_variant %}selected="selected"{% endif %} {% unless variant.available %}disabled="disabled"{% endunless %} value="{{ variant.id }}" data-sku="{{ variant.sku }}">{{ variant.title }}</option>
                  {%- endfor -%}
                </select>
              </div>
            </div>
          {%- else -%}
          <input class="bundle-input" type="hidden" name="id" data-productid="{{ product.id }}"  value="{{ product.selected_or_first_available_variant.id }}">
          <div style="height: 53px"></div>
          {%- endunless -%}
        {% endcomment %}
          
        
          <button class="hiya ProductForm__AddToCart Button Button--secondary Button--full" disabled="disabled" title="Spend $150 for FREE Gift">
    
          <span class="">ADD TO CART</span>
          {% comment %}
          <span class="Button__SeparatorDot hiya"></span>
          <span data-money-convertible>{{ selected_variant.price | money_without_trailing_zeros }}</span>
          {% endcomment %}
    
  		</button>
      
      </div>
    
  </div>

  
</div>