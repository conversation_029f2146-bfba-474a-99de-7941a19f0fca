<style>
  .ProductForm__AddToCart {
    font-weight: bold;
  }
</style>

<div class="ProductItemm ProductItem--set">
  <div class="ProductItem__Wrapper">
    
    <div class="ProductItem--set--Intro">
      <div class="AspectRatio AspectRatio--tall" style="max-width: {{ max_width }}px;  --aspect-ratio: 0.66666">
        <img class="ProductItem__Image ProductItem__Image--alternate" src="{{ product.images[1] | img_url: '600x' }}" alt="{{ product.images[1].alt | escape }}">
        <img class="ProductItem__Image" src="{{ product.featured_image | img_url: '600x' }}" alt="{{ product.featured_image.alt | escape }}">
      </div>

      <div class="ProductItem__LabelList cust_right">
        {%if product.tags contains 'NEW'%} <span class="ProductItem__Label Heading Text--subdued cus_right_title">New</span>{%endif%}      
        {%if product.tags contains 'preorder'%} <span class="ProductItem__Label Heading Text--subdued cus_right_title label-po">Pre Order</span>{%endif%}
      </div>


      <div class="ProductItem__Info {% unless use_horizontal %}ProductItem__Info--{{ settings.product_info_alignment }}{% endunless %}">
        <h2 class="ProductItem__Title Heading u-h5">{{ product.title }}</h2>
        <div class="prod-d">{{ product.description }}</div>
      </div>			
    </div>
    
    <div class="set-actions" data-set="0" data-varid="{{ product.first_available_variant.id }}" data-inv="{{ product.first_available_variant.inventory_quantity }}">
    
      
      
        
        <button class="ProductForm__Remove Button Button--secondary Button--full">
          <span class="">x</span>
        </button>
  
      
        <button class="ProductForm__AddToCart Button Button--primary Button--full">
          <span class="">+</span>
        </button>
      
      
      <div class="ProductForm__QuantitySelector">
        <div class="QuantitySelector QuantitySelector--large">
          <span class="QuantitySelector__Button Link Link--secondary" data-action="decrease-quantity">{% include 'icon' with 'minus' %}</span>
          <input type="text" class="QuantitySelector__CurrentQuantity" pattern="[0-9]*" name="quantity" value="1" max="{{ product.first_available_variant.inventory_quantity }}" data-inventory="{{ product.first_available_variant.inventory_quantity }}">
          <span class="QuantitySelector__Button Link Link--secondary" data-action="increase-quantity">{% include 'icon' with 'plus' %}</span>
        </div>
      </div>
      
    </div>
    
    <a href="" class="readset">Read more</a>
    
    
    
    
  </div>
</div>