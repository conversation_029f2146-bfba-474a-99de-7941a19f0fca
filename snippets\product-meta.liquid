<div class="ProductMeta">
  {% comment %}
  {%- if section.settings.show_vendor and product.vendor != blank -%}
    <h2 class="ProductMeta__Vendor Heading u-h6">
      {%- assign vendor_handle = product.vendor | handle -%}
      {%- assign collection_for_vendor = collections[vendor_handle] -%}

      {%- unless collection_for_vendor.empty? -%}
        <a href="{{ collection_for_vendor.url }}">{{ product.vendor }}</a>
      {%- else -%}
        {{- product.vendor -}}
      {%- endunless -%}
    </h2>
  {%- endif -%}
  {% endcomment %}
  
  
  
  <div class="ProductItem__LabelList cust_right">
    
     {% comment %}
       {% if product.tags contains 'TeePromo' %}
       <img style="height: 48px;vertical-align: middle;display: inline-block; margin-top: -2px;" src="https://cdn.shopify.com/s/files/1/0247/4021/files/CollectionBadge1.jpg?v=1635814111">
       {% endif %}
     {% endcomment %}
    
    
    {% if product.tags contains 'ART_CODE' %} <span class="artcode"></span>{%endif%} 
    {% if product.tags contains 'UPF50' %} <img style="width: 24px; 
    height: 24px;
    vertical-align: middle;
    display: inline-block; margin-top: -2px;
" src="https://cdn.shopify.com/s/files/1/0247/4021/files/UPF50.svg?v=1620621593">{% endif %} 
    {%if product.tags contains '__badge:UPF 50+'%} <span class="ProductItem__Label Heading Text--subdued cus_right_title label-UPF">UPF 50+</span>{%endif%} 
    {%if product.tags contains 'NEW'%} <span class="ProductItem__Label Heading Text--subdued cus_right_title label-new">New</span>{%endif%}   
    {%if product.tags contains 'preorder'%} <span class="ProductItem__Label Heading Text--subdued cus_right_title label-po">Pre Order</span>{%endif%}
    {%if product.tags contains 'cotton'%} <span class="ProductItem__Label Heading Text--subdued cus_right_title label-cot">Cotton</span>{%endif%}
    {%if product.tags contains 'recycled'%} <span class="ProductItem__Label Heading Text--subdued cus_right_title label-rec">100% Recycled</span>{%endif%}
    {%if product.tags contains 'Handmade'%} <span class="ProductItem__Label Heading Text--subdued cus_right_title label-hand">Hand-crafted</span>{%endif%}
    {%if product.tags contains 'organic' or product.tags contains '__badge:Organic' %} <span class="ProductItem__Label Heading Text--subdued cus_right_title label-org">Organic</span>{%endif%}
    
    {%if product.tags contains 'australianmade'%}<span class="ProductItem__Label Heading Text--subdued cus_right_title" style="background: #008365!important">Australian Made</span>{%endif%}
    {%if product.tags contains 'PRODUCT_PersonalisedName'%}<span class="ProductItem__Label Heading Text--subdued cus_right_title label-po" style="">Personalise me</span>{%endif%}
    
    
    {% if template.suffix == 'bulk' %} <span class="ProductItem__Label Heading Text--subdued cus_right_title label-po" style="background-color: #637381!important">Available for Bulk Order</span>{% endif %}
    
    
    {% assign discountvar = 0 %}
    {% assign discountsame = false %}

    {% assign varcount = 0 %}
    {% assign discountcount = 1 %}
    
    {%- if product.compare_at_price > product.price -%}
    
      {% if product.price_varies %}
        
        {% for variant in product.variants %}
  
    
          {% if variant.available %}
          {% assign varcount = varcount | plus: 1 %}
            {% if variant.compare_at_price > variant.price %}

                {% capture discountvarz %}
                  {{ variant.compare_at_price | minus:variant.price | plus:10 | times:100 | divided_by:variant.compare_at_price }}
                {% endcapture %}
                {% assign discountvarz = discountvarz | strip | plus:0 %}

                {% if discountvarz == discountvar %}
                    {% assign discountcount = discountcount | plus: 1 %}
                {% endif %}

                {% if discountvarz > discountvar %}
                   {% assign discountvar = discountvarz %}
                {% endif %}

            {% endif %}
          {% endif %}
    

        {% endfor %}
    
      {% else %}
    
      	{% capture discountvar %}
          {{ product.compare_at_price | minus:product.price | plus:10 | times:100 | divided_by:product.compare_at_price }}
      	{% endcapture %}

      {% endif %}
    {%- endif -%}

    {% if varcount == discountcount %}
      {% assign discountsame = true %}
    {% endif %}
    
	{% assign discountvar = discountvar | strip | plus:0 %}
    
    {%- if discountvar > 0 -%}
      
            {% comment %}
            {% if product.price_varies %}
              <span class="ProductItem__Label Heading Text--subdued">Save {% if discountsame == false %}up to{% endif %}{{ discountvar }}%</span>
            {% else %}
              <span class="ProductItem__Label Heading Text--subdued">Save {{ discountvar }}%</span>
            {% endif %}
            {% endcomment %}
    
    {%- endif -%}
    
  </div>
  
  {% for tag in product.tags %}
    {% if tag contains 'BRAND_' %}  
    
    {% assign brand = tag | remove_first: "BRAND_" %}
    {% assign brandlink = brand | replace: " ", "-" | downcase %}
 
      <h2 class="ProductMeta__Vendorr Heading u-h6" style="text-transform: uppercase">
        <a href="/collections/{{ brandlink }}">{{ brand }}
        </a>
        {% if brand contains 'Aneura' or brand contains 'Traka' or brand contains 'Custodian' or brand contains 'Lore' or brand contains 'Ochre' %}
        <a href="/pages/our-brands#yarnbrandsanchor"> (by Yarn)</a>
        {% endif %}
      </h2>
  
    {% endif %}
  {% endfor %}
  
  <h1 class="ProductMeta__Title Heading u-h2">
    {%- if template.name == 'product' -%}
      {{- product.title -}}
    {%- else -%}
      <a href="{{ product.url }}">{{ product.title }}</a>
    {%- endif -%}
  </h1>
 
<span class="stamped-product-reviews-badge stamped-main-badge" data-id="{{ product.id }}" data-product-title="{{ product.title | escape }}" data-product-type="{{ product.type }}" style="display: block;" onclick="$('.stamped-tab-review').click();var scrollContainer = jQuery('html, body'); var curElem = jQuery('.Product__Tabs'); scrollContainer.animate({scrollTop : curElem.offset().top + 200}, 800);">{{ product.metafields.stamped.badge }}</span>
 
  {% if template.suffix == 'bulk' %}
    <div class="ProductMeta__PriceList Heading"><span class="ProductMeta__Price Price Price--highlight Text--subdued u-h4 bulkprice" data-money-convertible="" style="color: #00b9f2">{{ product.selected_or_first_available_variant.price | times: 0.90 | money_without_trailing_zeros }}</span>
      <span class="gst-txt">incl. GST</span>
      <span class="ProductMeta__Price Price Price--compareAt Text--subdued u-h4 " data-money-convertible="">{{ product.selected_or_first_available_variant.price | money_without_trailing_zeros }}</span>
     
    </div>
  
 {% else %}
  
<style>
  .Price--compareAt:before {
    display: none!important;
  }
</style>
  

  {% if product.metafields.custom.from_price %}
    <div class="ProductMeta__PriceList Heading from-price-container">
    <span class="u-h4 from-price">from {{ product.metafields.custom.from_price }}</span>
  </div>
  {% else %}
  {% unless product.tags contains 'freegift' %}

    <div class="ProductMeta__PriceList Heading" data-price="{{ product.selected_or_first_available_variant.price | money_without_trailing_zeros }}" data-compare-price="{{ product.selected_or_first_available_variant.compare_at_price | money_without_trailing_zeros }}">
      
    {%- if product.selected_or_first_available_variant.compare_at_price > product.selected_or_first_available_variant.price -%}
      <span class="ProductMeta__Price Price Price--highlight Text--subdued u-h4" data-money-convertible data-lion-price-for-product-id="{{ product.id }}">{{ product.selected_or_first_available_variant.price | money_without_trailing_zeros }}</span>
      <span class="ProductMeta__Price Price Price--compareAt Text--subdued u-h4" data-money-convertible><s>{{ product.selected_or_first_available_variant.compare_at_price | money_without_trailing_zeros }}</s></span>
    {%- else -%}
      <span class="ProductMeta__Price Price Text--subdued u-h4" data-lion-price-for-product-id="{{ product.id }}" data-money-convertible>{% if product.tags contains '#yarncorpcustom' %}from {% endif %}{{ product.selected_or_first_available_variant.price | money_without_trailing_zeros }}</span>
      <span class="ProductMeta__Price Price Price--compareAt Text--subdued u-h4" data-money-convertible><s></s></span>
    {%- endif -%}

    {% if product.price > 11999 %} 
      <span class="ProductItem__Label Heading Text--subdued cus_right_title label-fs">Free Shipping</span>
    {% endif %}

    <!-- Placeholder for discount percentage -->
    <div class="NewSaleHeading_container">
      <span class="product-discount-percentage"></span>
    </div>

    </div>



  </div>
    {% endunless %}
  {% endif %}


  
  
  {% endif %}
  
    <div id="product-price-selectorr"></div>
  
  {% comment %}
    <script src="https://js.afterpay.com/afterpay-1.x.js" 
          async></script>
    <style>
      afterpay-placement { 
        --logo-badge-width: 70px;
        margin: 0;
      }
    </style>
    <afterpay-placement data-locale="en_US" data-badge-theme="white-on-black" data-amount="25" data-size="sm"></afterpay-placement>
 {% endcomment %}
  
 {% comment %}
 This section has been copied and replicted under the the product form ATC button for CRO experiments - Kaleb from EKOH
 {% endcomment %}
 {% comment %}
  <p class="afterpay-paragraph" data-product-id="{{ product.id }}" style="margin-top: 0px; margin-bottom: 0px;"><span class="afterpay-text1">or 4 payments of </span><strong class="afterpay-instalments">{{ product.price | divided_by: 4 | money }} AUD</strong><span class="afterpay-text2"> </span><img class="afterpay-logo" src="https://static.afterpay.com/integration/product-page/logo-afterpay-colour.png" srcset="https://static.afterpay.com/integration/product-page/logo-afterpay-colour.png 1x, https://static.afterpay.com/integration/product-page/<EMAIL> 2x, https://static.afterpay.com/integration/product-page/<EMAIL> 3x" width="100" height="21" alt="Afterpay" style="vertical-align: middle; width: 100px; border: 0px;"><span class="afterpay-text3">&nbsp;</span><a class="afterpay-link" href="/pages/shop-now-pay-later" target="_blank"><u class="afterpay-link-inner" style="font-size: 12px; text-decoration: underline;">More info</u></a></p>

  <div class="youpay-container youpay-callout-container" data-theme="light">
    <a class="youpay-callout-link" href="/blogs/yarn-in-the-community/introducing-youpay" target="_blank">
      <span class="youpay-callout-content">
          <span class="youpay-callout-message-before">Have someone else pay with</span>
          <span aria-label="YouPay information">
              <svg viewBox="0 0 121 22" fill="none" xmlns="http://www.w3.org/2000/svg" class="youpay-light-logo"><path class="youpay-logo-text" d="M44.71 1.993a1.882 1.882 0 0 1-.093.507l-7.06 18.903c-.142.4-.718.601-1.724.601h-.525c-1.232 0-1.848-.2-1.848-.601.001-.177.043-.352.123-.51l1.94-4.766-5.086-13.631c-.059-.164-.1-.334-.123-.507 0-.415.627-.622 1.88-.622h.99c1.007 0 1.582.197 1.726.591l1.818 5.212c.186.528.474 1.664.864 3.408h.122c.412-1.805.7-2.941.864-3.408l1.818-5.212c.145-.4.72-.59 1.727-.59h.74c1.233.002 1.849.21 1.847.625zM46.348 14.447c-1.278-1.313-1.917-3.198-1.915-5.657 0-2.459.64-4.34 1.915-5.64 1.274-1.3 3.133-1.95 5.578-1.95 2.444 0 4.298.65 5.563 1.95 1.265 1.295 1.898 3.176 1.898 5.64 0 2.464-.633 4.35-1.898 5.657-1.263 1.31-3.118 1.964-5.563 1.962-2.446-.001-4.305-.655-5.578-1.962zm7.725-2.426c.462-.723.693-1.8.693-3.231 0-1.431-.231-2.5-.693-3.207-.461-.715-1.176-1.073-2.144-1.073-.967 0-1.686.358-2.157 1.073-.472.716-.709 1.785-.709 3.207 0 2.878.956 4.318 2.866 4.318.962 0 1.676-.362 2.144-1.087zM62.218 15.295c-.74-.743-1.11-1.895-1.11-3.456V2.273c0-.601.617-.902 1.85-.902h.862c1.235 0 1.851.3 1.85.902v8.944c0 .583.144 1.02.432 1.311.287.293.76.438 1.418.438a2.313 2.313 0 0 0 1.464-.488 3.89 3.89 0 0 0 1.002-1.142V2.273c0-.601.617-.902 1.85-.902h.856c1.234 0 1.85.3 1.85.902v8.944c-.01.66.016 1.319.077 1.975.051.452.117.95.198 1.495.055.203.087.412.093.622 0 .3-.416.527-1.248.675a12.43 12.43 0 0 1-2.205.227.785.785 0 0 1-.77-.401 3.613 3.613 0 0 1-.325-.888c-.071-.32-.117-.537-.138-.65a6.004 6.004 0 0 1-1.957 1.524 5.73 5.73 0 0 1-2.635.621c-1.537-.005-2.675-.38-3.414-1.122zM77.722 21.43V5.716c0-1.24-.062-2.129-.184-2.666-.125-.535-.187-.832-.187-.888 0-.227.198-.391.595-.493a4.197 4.197 0 0 1 1.08-.157.658.658 0 0 1 .631.401c.134.275.237.564.307.862.071.3.119.497.139.592 1.475-1.409 3.106-2.113 4.894-2.113a5.33 5.33 0 0 1 3.22.972 6.143 6.143 0 0 1 2.05 2.68c.492 1.242.733 2.571.71 3.909 0 1.604-.264 2.967-.793 4.09a5.794 5.794 0 0 1-2.198 2.581 6.084 6.084 0 0 1-3.266.874 7.114 7.114 0 0 1-2.39-.367c-.719-.26-1.399-.62-2.02-1.07v6.517c0 .413-.41.62-1.231.62h-.125c-.822-.006-1.232-.215-1.232-.628zm9.493-8.52c.7-.903 1.05-2.2 1.048-3.894 0-1.824-.33-3.227-.99-4.21a3.197 3.197 0 0 0-1.208-1.117 3.152 3.152 0 0 0-1.597-.363 5.054 5.054 0 0 0-2.141.507 7 7 0 0 0-2.016 1.429v7.734a6.135 6.135 0 0 0 3.822 1.268c1.351 0 2.378-.451 3.082-1.355zM94.307 15.285c-.967-.716-1.45-1.77-1.45-3.161 0-1.487.488-2.601 1.463-3.344a7.769 7.769 0 0 1 3.592-1.437 37.222 37.222 0 0 1 5.179-.353V5.663a2.129 2.129 0 0 0-.201-.95 2.093 2.093 0 0 0-.601-.756c-.528-.442-1.391-.663-2.589-.663a8.473 8.473 0 0 0-2.377.296c-.576.161-1.143.359-1.695.592-.24.116-.487.215-.741.296-.198 0-.436-.188-.693-.563-.258-.375-.38-.658-.38-.858 0-.243.27-.507.816-.79a9.066 9.066 0 0 1 2.178-.72c.92-.197 1.858-.296 2.799-.294 1.912 0 3.402.39 4.47 1.17 1.069.781 1.603 1.867 1.603 3.258v6.326c0 1.242.062 2.13.186 2.666.123.536.184.832.184.888 0 .227-.198.401-.594.495a4.284 4.284 0 0 1-1.078.155.66.66 0 0 1-.631-.437 4.435 4.435 0 0 1-.309-.918 35.203 35.203 0 0 1-.139-.62c-1.39 1.412-3.114 2.116-5.172 2.115-1.581 0-2.855-.355-3.82-1.066zm6.55-1.496c.8-.385 1.55-.868 2.234-1.439V9.017c-1.541.02-2.799.076-3.775.17a6.219 6.219 0 0 0-2.575.79 2.141 2.141 0 0 0-.84.84c-.197.355-.29.758-.27 1.164 0 .751.221 1.325.662 1.72.441.396 1.094.596 1.96.601a6.145 6.145 0 0 0 2.604-.517v.004zM121 1.854c-.009.138-.04.274-.093.401l-7.923 19.214c-.164.4-.645.601-1.448.601h-.461c-.679 0-1.018-.15-1.018-.45.027-.167.079-.329.154-.48l2.589-5.775-5.393-13.148a1.23 1.23 0 0 1-.093-.363c0-.32.441-.479 1.325-.479h.247c.719 0 1.129.15 1.232.451l3.146 7.646c.327.828.657 1.778.99 2.85h.061c.04-.132.082-.258.123-.38.042-.123.081-.241.123-.354l.766-2.116 3.082-7.65c.103-.3.525-.45 1.266-.45h.091c.823 0 1.234.16 1.234.482z" fill="currentColor"></path> <path d="M11.167 11.29V.05H0s7.487 6.537 8.767 17.57c0 0 2.4-1.52 2.4-6.33z" fill="#F796B9"></path> <path d="M17.511 8.746V.05h-6.338v11.24c0 4.81-2.4 6.33-2.4 6.33a8.73 8.73 0 0 0 6.176-2.614 8.943 8.943 0 0 0 2.562-6.26z" fill="#2F3A93"></path> <path d="M17.96.05h-.454v8.696a8.94 8.94 0 0 1-2.561 6.261 8.726 8.726 0 0 1-6.178 2.613l9.192-.012a8.584 8.584 0 0 0 3.36-.63 8.667 8.667 0 0 0 2.86-1.894 8.786 8.786 0 0 0 1.914-2.864 8.871 8.871 0 0 0 0-6.781 8.786 8.786 0 0 0-1.915-2.865A8.666 8.666 0 0 0 21.32.68a8.584 8.584 0 0 0-3.36-.63z" fill="#0CD9DC"></path></svg>
          </span>
          <span class="youpay-callout-message-after">Learn more</span>
      </span>
    </a>
  </div>

 
  
  <div id="refundid-option-container" style="position: relative; display: flex; flex-direction: column; justify-content: normal; margin-bottom: 10px;">
    <div style="width: 110%; padding: 0;">
      Get an instant refund if you decide to return with
      <span onclick=launchRefundidPopup() style="cursor: pointer; width: 100%;"><img style="width: 100px; vertical-align: middle;" src="https://storage.googleapis.com/concrete-fusion-289612.appspot.com/refundid_logo_header.png"/> ⓘ</span>
    </div>
  </div>
{% endcomment %}
  {% comment %} <!-- commented out prior to CRO experiment change -->
  <script src="//ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"></script>
  {% endcomment %}

  {% comment %} <!-- commented out because of cro experiment -->
  <script src="https://storage.googleapis.com/concrete-fusion-289612.appspot.com/refundid-popup-script.js"></script>
    {% endcomment %}
</div>

<script>
  var variantsData = {{ product.variants | json }};
  var selectedVariantId = {{ product.selected_or_first_available_variant.id }};

  document.addEventListener("DOMContentLoaded", function() {
    console.log("DOMContentLoaded event fired.");
    waitForElementsAndApplyStyles();
  });

  document.addEventListener('change', function(event) {
    if (event.target.matches('.variant-input input[type="radio"]')) {
      handleVariantChange(event);
    }
  });

  document.addEventListener('click', function(event) {
    if (event.target.matches('.variant-input input[type="radio"]')) {
      handleVariantChange(event);
    }
  });

  function handleVariantChange(event) {
    if (event.target.matches('.variant-input input[type="radio"]')) {
      selectedVariantId = parseInt(event.target.value);
      console.log("Variant changed, applying styles for variant ID:", selectedVariantId);
      setTimeout(applyStyles, 0);
    }
  }

  function waitForElementsAndApplyStyles() {
    const priceList = document.querySelector('.ProductMeta__PriceList');
    if (priceList) {
      console.log("ProductMeta__PriceList found, applying initial styles.");
      applyStyles();
    } else {
      console.log("Waiting for ProductMeta__PriceList to load...");
      setTimeout(waitForElementsAndApplyStyles, 100);
    }
  }

  function applyStyles() {
    var selectedVariant = variantsData.find(function(variant) {
      return variant.id === selectedVariantId;
    });

    if (!selectedVariant) {
      console.log('Selected variant not found.');
      return;
    }

    var salePrice = parseFloat(selectedVariant.price) / 100;
    var compareAtPrice = selectedVariant.compare_at_price ? parseFloat(selectedVariant.compare_at_price) / 100 : null;

    console.log('Applying styles with Sale Price:', salePrice, 'Compare At Price:', compareAtPrice);

    var priceList = document.querySelector('.ProductMeta__PriceList');
    if (priceList) {
      priceList.setAttribute('data-price', salePrice.toFixed(2));
      priceList.setAttribute('data-compare-price', compareAtPrice ? compareAtPrice.toFixed(2) : '');
      updatePriceDisplays(salePrice, compareAtPrice);
    } else {
      console.log("PriceList element not found.");
    }
  }

  function updatePriceDisplays(salePrice, compareAtPrice) {
    var priceList = document.querySelector('.ProductMeta__PriceList');
    if (!priceList) {
      console.log("Price list element not found during update.");
      return;
    }

    var salePriceDiv = priceList.querySelector('.ProductMeta__Price:not(.Price--compareAt)');
    var compareAtPriceDiv = priceList.querySelector('.ProductMeta__Price.Price--compareAt');
    var discountContainer = document.querySelector('.NewSaleHeading_container');
    var discountSpan = document.querySelector('.product-discount-percentage');

    if (!salePriceDiv) {
      console.log("Sale price div not found.");
      return;
    }

    if (!compareAtPriceDiv) {
      compareAtPriceDiv = document.createElement('span');
      compareAtPriceDiv.classList.add('ProductMeta__Price', 'Price', 'Price--compareAt', 'Text--subdued', 'u-h4');
      compareAtPriceDiv.setAttribute('data-money-convertible', '');
      salePriceDiv.parentNode.insertBefore(compareAtPriceDiv, salePriceDiv.nextSibling);
      console.log("Created compare at price div.");
    }

    salePriceDiv.innerHTML = formatPrice(salePrice);
    if (compareAtPrice && compareAtPrice > salePrice) {
      salePriceDiv.classList.add('Price--highlight');
      compareAtPriceDiv.style.display = 'flex';
      compareAtPriceDiv.innerHTML = formatPrice(compareAtPrice);
      addStrikeThrough(compareAtPriceDiv);

      var discountPercentage = Math.round(((compareAtPrice - salePrice) / compareAtPrice) * 100);
      if (discountSpan) {
        discountSpan.textContent = discountPercentage + '% OFF';
      }
      if (discountContainer) {
        discountContainer.style.display = 'flex';
      }
      console.log("Discount applied:", discountPercentage + '%');
    } else {
      salePriceDiv.classList.remove('Price--highlight');
      compareAtPriceDiv.style.display = 'none';
      compareAtPriceDiv.innerHTML = '';

      if (discountSpan) {
        discountSpan.textContent = '';
      }
      if (discountContainer) {
        discountContainer.style.display = 'none';
      }
      console.log("No discount applied.");
    }
  }

  function formatPrice(price) {
    var mainPrice = Math.floor(price);
    var cents = (price % 1).toFixed(2).substring(2);

    return `
      <span class="dollar-sign">$</span>
      <span class="main-price">${mainPrice}</span>
      <span class="cents">${cents}</span>
    `;
  }

  function addStrikeThrough(priceDiv) {
    var existingStrikethrough = priceDiv.querySelector('.custom-strikethrough');
    if (existingStrikethrough) {
      existingStrikethrough.remove();
    }

    var strikeThrough = document.createElement('div');
    strikeThrough.className = 'custom-strikethrough';
    var width = priceDiv.offsetWidth + 'px';

    strikeThrough.style.position = 'absolute';
    strikeThrough.style.left = '0';
    strikeThrough.style.width = width;
    strikeThrough.style.top = '50%';
    strikeThrough.style.height = '1px';
    strikeThrough.style.backgroundColor = 'currentColor';
    strikeThrough.style.transform = 'translateY(-50%)';
    strikeThrough.style.zIndex = '1';

    priceDiv.style.position = 'relative';
    priceDiv.appendChild(strikeThrough);
  }
</script>



