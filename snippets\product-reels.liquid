{% assign video_tags = '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>,VI<PERSON><PERSON>_<PERSON><PERSON>,VIDEO_AFathersTeachings,VIDEO_GroundedCountry' | split: ',' %}
{% assign video_urls = 'https://www.youtube.com/watch?v=JmqW-__ivlQ,https://www.youtube.com/watch?v=uXoEAsK0O3E,https://www.youtube.com/watch?v=BAm6y3n_08Q,https://www.youtube.com/watch?v=jVn5yLrSAW4,https://www.youtube.com/watch?v=hcAKTjbN2WY,https://www.youtube.com/watch?v=Kxpa3NBZ_Go,https://www.youtube.com/watch?v=IUJsH133vXA,https://www.youtube.com/watch?v=CdpUin03RPI,https://www.youtube.com/watch?v=Qb4ZeAIOmSY,https://www.youtube.com/watch?v=Orc57fsqeOk,https://www.youtube.com/watch?v=We1AI8K4uv4' | split: ',' %}
{% assign bg_classes = 'boobie-bg,bayley-bg,nathaniel-bg,alkina-bg,caitlyn-bg,gunawirra-bg,luke-bg,charlie-bg,alnf-bg,bg-afathersteaching,bg-groundedcountry' | split: ',' %}

{% for tag in video_tags %}
  {% assign index = forloop.index0 %}
  {% assign current_tag = video_tags[index] %}
  {% assign current_url = video_urls[index] %}
  {% assign current_bg_class = bg_classes[index] %}
  
  {% if product.tags contains current_tag %}
    <link href="{{ 'lity.min.css' | asset_url }}" rel="stylesheet">
    <script src="{{ 'lity.min.js' | asset_url }}"></script>
    <style>
    .video-play-button {
        box-sizing: content-box;
        display: block;
        position: relative;
        width: 32px;
        height: 44px;
        border-radius: 50%;
        padding: 18px 20px 18px 28px;
    }


    .video-play-button:before {
        content: "";
        position: absolute;
        z-index: 0;
        left: 50%;
        top: 50%;
        transform: translateX(-50%) translateY(-50%);
        display: block;
        width: 80px;
        height: 80px;
        background: #106572;
        border-radius: 50%;
        animation: pulse-border 1500ms ease-out infinite;
    }

    .video-play-button:after {
        content: "";
        position: absolute;
        z-index: 1;
        left: 50%;
        top: 50%;
        transform: translateX(-50%) translateY(-50%);
        display: block;
        width: 80px;
        height: 80px;
        background: #0e4f58;
        border-radius: 50%;
        transition: all 200ms;
    }

    .video-play-button:hover:after {
        background-color: darken(#0e4f58, 10%);
    }

    .video-play-button img {
        position: relative;
        z-index: 3;
        max-width: 100%;
        width: auto;
        height: auto;
    }

    .video-play-button span {
        display: block;
        position: relative;
        z-index: 3;
        width: 0;
        height: 0;
        border-left: 32px solid #fff;
        border-top: 22px solid transparent;
        border-bottom: 22px solid transparent;
        top: 50%;
        left: 50%;
        transform: translateX(-50%) translateY(-50%);
    }

    @keyframes pulse-border {
        0% {
            transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
            opacity: 1;
        }

        100% {
            transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
            opacity: 0;
        }
    }

    .rcl-vid {
        height: 50px;
    }

    @media only screen and (max-width: 767px) {
        img.rcl-vid {
            height: 65px;
        }
    }

    .rcl-block.video-reel {
        width: 100%;
        padding-bottom: 56.25%; 
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
    }

    .rcl-content {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    .playbtn-container {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
    }

    h2.video-reel.Heading.u-h2 {
        color: #106572;
        font-weight: 700;
        text-transform: uppercase;
        text-align: center;
        margin-bottom: 10px;
    }

    .rcl-block {
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
    }

    .boobie-bg {
        background-image: url('{{ 'boobie-bg.jpg' | asset_url }}');
    }

    .bayley-bg {
        background-image: url('{{ 'bayley-bg.jpg' | asset_url }}');
    }

    .nathaniel-bg {
        background-image: url('{{ 'nathaniel-bg.jpg' | asset_url }}');
    }

    .alkina-bg {
        background-image: url('{{ 'alkina-bg.jpg' | asset_url }}');
    }

    .caitlyn-bg {
        background-image: url('{{ 'caitlyn-bg.jpg' | asset_url }}');
    }

    .gunawirra-bg {
        background-image: url('{{ 'gunawirra-bg.jpg' | asset_url }}');
    }

    .luke-bg {
        background-image: url('{{ 'luke-bg.jpg' | asset_url }}');
    }

    .charlie-bg {
        background-image: url('{{ 'charlie-bg.jpg' | asset_url }}');
    }

    .alnf-bg {
        background-image: url('{{ 'alnf-bg.jpg' | asset_url }}');
    }
    .bg-afathersteaching {
        background-image: url('{{ 'bg-afathersteaching.png' | asset_url }}')
    }
    .bg-groundedcountry {
        background-image: url('{{ 'bg-groundedcountry.png' | asset_url }}')
    }
    </style>
    <h2 class="video-reel Heading u-h2">Watch Campaign Video</h2>
    <div class="rcl-block video-reel {{ current_bg_class }}">
      <div class="rcl-content">
        <a href="{{ current_url }}" data-lity>
          <div class="playbtn-container">
            <div class="video-play-button">
              <span></span>
            </div>
          </div>
        </a>
      </div>
    </div>
  {% endif %}
{% endfor %}
