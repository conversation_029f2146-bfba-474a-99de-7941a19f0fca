<!-- SWATCHES -->


{%- assign isswatch = false -%}

{% for collection in product.collections %}

{% if collection.handle contains 'swatch_' %}
    {%- assign isswatch = true -%}
    {% break %}
{% endif %}

{% endfor %}


{% if isswatch == true %}

<style>

    .swatch-wrap img {
    border-radius: 50%;
    margin: 0 10px 10px 0;
    width: 52px;
    }

    .swatch-wrap .cell {
    width: 100%;
    padding: 4px;
    }

    .swatch-wrap img:hover,
    .swatch-wrap .current-swatch img {
    box-shadow: 0 0 0 2px #106572;

    }
</style>

{% assign currenthandle = product.handle %}

{% for collection in product.collections %}

    {% if collection.handle contains 'swatch_' %}
    {%- assign collection = collections[collection.handle] -%}
    {% break %}
    {% endif %}


{% endfor %}

<div style="margin: 20px 0">
    <div class="swatch-wrap" {% if collection.products_count > 36 %}data-flickity='{ "cellAlign": "left", "contain": true, "prevNextButtons": false, "imagesLoaded": true }'{% endif %}>

    {% paginate collection.products by 250 %}

        {% assign products_by_sort = collection.products | sort: "manual" | reverse %}

        {% for product in products_by_sort %}


        {% if forloop.index0 == 0 %}
            <div class="cell">
            {% endif %}

            {% unless product.tags contains "hideme" %}

            {% assign hasswatch = false %}

            {% for image in product.images %}
                {% if image.alt == "swatch" %}
                <a href="{{ product.url }}?view=bag-swatches" class="{% if currenthandle == product.handle %}current-swatch{% endif %}"><img src="{{ image | img_url: "100x100", crop: 'center' }}"></a>
                {% assign hasswatch = true %}
                {% endif %}
            {% endfor %}

            {% if hasswatch == false %}
                <a href="{{ product.url }}?view=bag-swatches" class="{% if currenthandle == product.handle %}current-swatch{% endif %}"><img src="{{ product.featured_image | img_url: "100x100", crop: 'center' }}"></a>
            {% endif %}
            {% endunless %}

            {% if forloop.index0 == 35 or forloop.index0 == 71 or forloop.index0 == 107 or forloop.index0 == 143 or forloop.index0 == 179 or forloop.index0 == 215 or forloop.index0 == 251 %}
            </div>
            <div class="cell">
            {% endif %}

            {% if forloop.last == true %}
            </div>
        {% endif %}

        {% endfor %}


        {% assign dm_paginate_by = paginate.page_size %}{% endpaginate %}

    </div>
</div>

{% endif %}