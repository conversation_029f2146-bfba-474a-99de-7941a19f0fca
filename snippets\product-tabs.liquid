<style>
  #content--cart-gifts #shopify-section-page-block-cart-gifts header, #content--cart-gifts #shopify-section-page-block-cart-gifts  .ProductItem__PriceList, #content--cart-gifts #shopify-section-page-block-cart-gifts .ProductForm__AddToCart{
    display: none;
  }
  #content--cart-gifts #shopify-section-page-block-cart-gifts .ProductList{
    padding: 0;
  }
  
  #content--cart-gifts #shopify-section-page-block-cart-gifts .Section--spacingNormal {
    margin: 0;
  }
  
  #content--cart-gifts #shopify-section-page-block-cart-gifts .Grid__Cell {
    width: 25%!important;
    padding-left: 5px;
    margin-bottom: 20px;
  }
  #content--cart-gifts #shopify-section-page-block-cart-gifts .ProductItem__Title {
    height: auto;
    font-size: 11px;
    line-height: 1em;
  }
  
  .Product__Tabs .Collapsible__Content.coll-cart-gifts {
    padding-right: 20px;
  } 
  
  #prod-reviews #stamped-main-widget .stamped-review{
    width: 100%!important;
  }
  
  #prod-reviews .stamped-summary-actions, #prod-reviews  .stamped-reviews-filter, #prod-reviews .stamped-tab-container {
    display: none!important;
  }
  #prod-reviews div.stamped-container {
    margin: 0!important;
  }
</style>



{%- assign first_page = pages[section.settings.tab_page_1_handle] -%}
{%- assign second_page = pages[section.settings.tab_page_2_handle] -%}
{%- assign third_page = pages[section.settings.tab_page_3_handle] -%}
{%- assign first_custom_page = '' -%}
{%- assign second_custom_page = '' -%}
{%- assign third_custom_page = '' -%}
{%- assign artwork_page = '' -%}


{%- comment -%}
  We allow to add two tabs that are extracted using the tag system. To add such a tag, simply create a page and
  note the handle somewhere.
  Next, add a tag to your product in this form: __tab1:page-handle or __tab2:page-handle
  The theme will automatically link the tab to the page
{%- endcomment -%}

{%- assign has_unique_tab_1 = false -%}
{%- assign has_unique_tab_2 = false -%}
{%- assign has_unique_tab_3 = false -%}
{%- assign has_artwork = false -%}

{%- comment -%}First, check the metafields{%- endcomment -%}

{%- if product.metafields.sf_product_tabs.tab_1_title != blank and product.metafields.sf_product_tabs.tab_1_content != blank -%}
  {%- assign unique_tab_1_title = product.metafields.sf_product_tabs.tab_1_title -%}
  {%- assign unique_tab_1_content = product.metafields.sf_product_tabs.tab_1_content -%}
  {%- assign has_unique_tab_1 = true -%}
{%- endif -%}

{%- if product.metafields.sf_product_tabs.tab_2_title != blank and product.metafields.sf_product_tabs.tab_2_content != blank -%}
  {%- assign unique_tab_2_title = product.metafields.sf_product_tabs.tab_2_title -%}
  {%- assign unique_tab_2_content = product.metafields.sf_product_tabs.tab_2_content -%}
  {%- assign has_unique_tab_2 = true -%}
{%- endif -%}

{%- if product.metafields.sf_product_tabs.tab_3_title != blank and product.metafields.sf_product_tabs.tab_3_content != blank -%}
  {%- assign unique_tab_3_title = product.metafields.sf_product_tabs.tab_3_title -%}
  {%- assign unique_tab_3_content = product.metafields.sf_product_tabs.tab_3_content -%}
  {%- assign has_unique_tab_3 = true -%}
{%- endif -%}

{%- comment -%}Then, let's try the tags{%- endcomment -%}

{%- for tag in product.tags -%}
  {%- if tag contains '__tab1' -%}
    {%- assign first_custom_page = tag | split: ':' | last -%}
    {%- assign first_custom_page = pages[first_custom_page] -%}

    {%- assign unique_tab_1_title = first_custom_page.title -%}
    {%- assign unique_tab_1_content = first_custom_page.content -%}
    {%- assign has_unique_tab_1 = true -%}
  {%- endif -%}

  {%- if tag contains '__tab2' -%}
    {%- assign second_custom_page = tag | split: ':' | last -%}
    {%- assign second_custom_page = pages[second_custom_page] -%}

    {%- assign unique_tab_2_title = second_custom_page.title -%}
    {%- assign unique_tab_2_content = second_custom_page.content -%}
    {%- assign has_unique_tab_2 = true -%}
  {%- endif -%}

{%- if tag contains '__tab3' -%}
    {%- assign third_custom_page = tag | split: ':' | last -%}
    {%- assign third_custom_page = pages[third_custom_page] -%}

    {%- assign unique_tab_3_title = third_custom_page.title -%}
    {%- assign unique_tab_3_content = third_custom_page.content -%}
    {%- assign has_unique_tab_3 = true -%}
  {%- endif -%}

{%- comment -%} Artwork story now handled by metaobjects in section-new-artist {%- endcomment -%}



{%- endfor -%}

{%- if first_page.handle != blank or second_page.handle != blank or third_page.handle != blank or has_unique_tab_1 or has_unique_tab_2 or has_unique_tab_3 or section.settings.reviews_enabled or product.tags contains 'preorder' -%}
{% endif %}

<div class="Product__Tabs {{ template }}">
    
      {% if product.description != blank %}
      <div class="Collapsible Collapsible--large">
        <button class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
          description <span class="Collapsible__Plus"></span>
        </button>

        <div class="Collapsible__Inner">
          <div class="Collapsible__Content">
            <div class="Rte">
              {% if product.tags contains 'BRAND_Better World Arts' %}
              <p>Better World Arts is endorsed by/member of:</p>
              <img style="width: 70%;" src="https://cdn.shopify.com/s/files/1/0247/4021/files/BetterWorldEndorsements.jpg">
              {% endif %}
              
              {{- product.description -}}
              {% if product.vendor contains "Natural Art" %}
                 <img src="https://cdn.shopify.com/s/files/1/0247/4021/files/printed-australia.svg?v=1593648025" style="width: 200px">
              {% endif %}
            </div>
          </div>
        </div>
      </div>
      {% endif %}
    
  {%- for tag in product.tags -%}

    {%- if tag contains ':artist' -%}
      {%- assign artist_page = tag | split: ':' | last -%}
      {%- assign artist_page = pages[artist_page] -%}
      {%- assign artist_title = artist_page.title -%}
      {%- assign artist_content = artist_page.content -%}
      {%- assign has_artist = true -%}
    {%- endif -%}
    
    {% unless has_artist %}

    {%- if tag contains 'ARTIST_' -%}
      {%- assign artist_page = tag | remove_first: "ARTIST_" -%}
      {%- assign artist_page = artist_page | split: ' ' -%}
      {%- capture artist_handle -%}artist-{{ artist_page | join: "-" }}{%- endcapture -%}
      
      {%- assign artist_page = pages[artist_handle] -%}
      {%- assign artist_title = artist_page.title -%}
      {%- assign artist_content = artist_page.content -%}
      {% if artist_page != blank%}
        {%- assign has_artist = true -%}
      {% endif %}
    {%- endif -%}

  	{% endunless %}

  {% endfor %}
  
  {%- comment -%} Check if artwork stories exist from section-new-artist {%- endcomment -%}
  {%- assign has_artwork_stories = false -%}
  {%- for section in sections -%}
    {%- if section.type == 'section-new-artist' and section.settings.metaobject_type == 'artist_profile' -%}
      {%- assign artistcollection = "" -%}
      {%- for collection in product.collections -%}
        {%- assign artistcollection = artistcollection | append: collection.handle | append: ' ' -%}
      {%- endfor -%}
      {%- paginate shop.metaobjects[section.settings.metaobject_type].values by 50 -%}
        {%- for artist_profile in shop.metaobjects[section.settings.metaobject_type].values -%}
          {%- if artistcollection contains artist_profile.artist_collection.value.handle -%}
            {%- assign max_stories = section.settings.max_artwork_stories | default: 5 -%}
            {%- for i in (1..max_stories) -%}
              {%- capture artwork_field -%}artwork_{{ i }}{%- endcapture -%}
              {%- capture art_info_field -%}art_info_{{ i }}{%- endcapture -%}
              {%- if artist_profile[artwork_field].value != blank -%}
                {%- assign has_artwork_stories = true -%}
                {%- break -%}
              {%- endif -%}
            {%- endfor -%}
            {%- break -%}
          {%- endif -%}
        {%- endfor -%}
      {%- endpaginate -%}
      {%- break -%}
    {%- endif -%}
  {%- endfor -%}

  {% if has_artwork_stories == true %}

  <div class="Collapsible Collapsible--large">
      <button class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
         artwork story <span class="Collapsible__Plus"></span>
      </button>

        <div class="Collapsible__Inner">
          <div class="Collapsible__Content">
            <div class="Rte">
              {%- for section in sections -%}
                {%- if section.type == 'section-new-artist' and section.settings.metaobject_type == 'artist_profile' -%}
                  {%- assign artistcollection = "" -%}
                  {%- for collection in product.collections -%}
                    {%- assign artistcollection = artistcollection | append: collection.handle | append: ' ' -%}
                  {%- endfor -%}
                  {%- paginate shop.metaobjects[section.settings.metaobject_type].values by 50 -%}
                    {%- for artist_profile in shop.metaobjects[section.settings.metaobject_type].values -%}
                      {%- if artistcollection contains artist_profile.artist_collection.value.handle -%}
                        {%- assign max_stories = section.settings.max_artwork_stories | default: 5 -%}
                        {%- for i in (1..max_stories) -%}
                          {%- capture artwork_field -%}artwork_{{ i }}{%- endcapture -%}
                          {%- capture art_info_field -%}art_info_{{ i }}{%- endcapture -%}
                          {%- if artist_profile[artwork_field].value != blank -%}
                            <div style="margin-bottom: 20px;">
                              <img src="{{ artist_profile[artwork_field].value | img_url: '600x' }}" style="width: 100%; height: auto; margin-bottom: 10px;" alt="Artwork {{ i }}">
                              {%- if artist_profile[art_info_field].value != blank -%}
                                <div>{{ artist_profile[art_info_field] | metafield_tag }}</div>
                              {%- endif -%}
                            </div>
                          {%- endif -%}
                        {%- endfor -%}
                        {%- break -%}
                      {%- endif -%}
                    {%- endfor -%}
                  {%- endpaginate -%}
                  {%- break -%}
                {%- endif -%}
              {%- endfor -%}
            </div>
          </div>
        </div>
    </div>

  {% endif %}
    
  {% comment %}
  {%- elsif has_artist -%}
    
    <div class="Collapsible Collapsible--large">
      <button class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
         artwork story <span class="Collapsible__Plus"></span>
      </button>

        <div class="Collapsible__Inner">
          <div class="Collapsible__Content">
            <div class="Rte">
              {{ artist_content }}
            </div>
          </div>
        </div>
    </div>             
  {%- endif -%}
  {% endcomment %}
  
  
  {% if product.first_available_variant.requires_shipping %}
    
  <div class="Collapsible Collapsible--large">
      <button class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
         shipping* and returns <span class="Collapsible__Plus"></span>
      </button>

        <div class="Collapsible__Inner">
          <div class="Collapsible__Content">
            <div class="Rte">
              
              
   {% if product.tags contains 'COLLECTION_YarnGallery' %}
              
              <p>Due to the delicate nature of these original paintings, each will be carefully packaged and shipped within 1 week.              
              </p>

              <p><b>Standard Shipping:</b> $9.95<br>
                <b>Express Shipping:</b> $14.95<br>
                <b>*Free Standard Shipping:</b> Spend over {% if settings.cart_show_free_shipping_threshold %}${{ settings.cart_free_shipping_threshold }}{% endif %}<br>

              <p><b>Yarn Gallery artwork must be returned within 7 days of receipt of your shipment.</b></p>
              
              <p>To organise a return for artwork, <b>you must</b> contact a Yarn customer representative to arrange associated postage/courier costs.</p>

              <p> 
               <i>
                 COVID-19 UPDATE: Australia Post has been experiencing some delays in these uncertain times, but are doing their best to ship your orders to you in time. We thank you for your patience!
                </i>
			  </p>
              
              <p>
                You can find more information about Shipping and Returns <a href="/pages/returns" target="_blank">here</a>.
              </p>     
              
   {% else %}

              {% assign shipdays = "within 2-3 business days" %}
              
              {% for tag in product.tags %}
              
                {% if tag contains 'SHIP_TEXT' %}
              
                  {% assign shipdays = tag | split: 'SHIP_TEXT_' %}
              
                {% endif %}
              
                {% if tag contains 'COLLECTION_YarnGallery' %}
              
                  {% assign shipdays = "within 2 weeks" %}
              
                {% endif %}
              

              {% endfor %}

              
              <p>
Due to the impact of Cyclone Alfred affecting Brisbane and surrounding areas, some orders may experience shipping delays. We are working closely with our carriers to minimise disruptions and will keep you updated on any changes to your delivery timeline.

Thank you for your patience and understanding during this time. If you have any questions, please contact our customer support team.
                
    			Will leave the warehouse next business day* subject to seasonal fluctuation{% comment %}{{ shipdays }}{% endcomment %}.<br>                
              </p>

              <p>
                <b>Standard Shipping:</b> $9.95<br>
                <b>Express Shipping:</b> $14.95<br>
                <b>*Free Standard Shipping:</b> Spend over {% if settings.cart_show_free_shipping_threshold %}${{ settings.cart_free_shipping_threshold }}{% endif %}<br>

                <p>
                You can find more information about shipping <a href="/pages/returns" target="_blank">here</a>.
                </p>

              {% unless product.tags contains 'NoReturn' %}
              <p><b> 30 Day Returns.</b><br>
			    Returns are self paid. Please visit our returns portal for futher information <a href="/pages/returns" target="_blank">here</a>.
              </p>
              {% endunless %}

   {% endif %}

      
              
              
            </div>
          </div>
        </div>
   </div>
  
  {% endif %}
   
<!--<div class="Collapsible Collapsible--large">
      <button class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
         naidoc pre-order info<span class="Collapsible__Plus"></span>
      </button>

        <div class="Collapsible__Inner">
          <div class="Collapsible__Content">
            <div class="Rte">
The first NAIDOC pre-orders will start dispatching from our warehouse by the end of March or earlier. Should there be any delays all customers will be notified promptly by email. If you have any questions please feel free to contact our customer service team.
<ul>
<li><strong>NAIDOC Unisex, Womens & Kids Polos - dispatching by the end of March or earlier</strong></li>
<li><strong>NAIDOC Contrast Bamboo Polos - dispatching early April or earlier</strong></li>

<li>Orders containing "In-stock" items will have them shipped out immediately. Pre-order items will ship out <span>separately.<br /></span></li>
<li><span>Pre-order items will ship out via standard shipping free of charge.</span></li>
</ul>
            </div>
          </div>
        </div>
    </div> 
    
  </div>-->



<div id="ex1" class="modal">
  <h2 class="ProductMeta__Title Heading u-h3" id="">
     {{- unique_tab_2_title -}}
  </h2>
  <main class="Rte" id="">
      {{- unique_tab_2_content -}}
  </main>
  
</div>
