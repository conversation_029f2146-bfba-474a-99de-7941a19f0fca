<style>
  #section-announcement {
    display: none;
  }
  .promo-bar {
    background: #106572;
    height: 35px;
    max-width: 100%;
    position: relative;
    z-index: 1;
    width: 100vw;
    -webkit-transition: .2s all ease-in-out 0s;
    -moz-transition: .2s all ease-in-out 0s;
    -ms-transition: .2s all ease-in-out 0s;
    -o-transition: .2s all ease-in-out 0s;
    transition: .2s all ease-in-out 0s;
}
  
.promo-bar-inner.promo-bar-inner--scrolling {
    animation: marquee 20s linear infinite;
    max-width: none;
    padding: 0;
    position: absolute;
    top: 0;
    white-space: nowrap;
    width: auto;
    will-change: transform;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    -webkit-justify-content: space-between;
    -moz-justify-content: space-between;
    justify-content: space-between;
}
  
.promo-bar-inner.promo-bar-inner--scrolling .promo-bar__text {
    display: inline-block;
    flex: none;
    padding: 0 150px;
    line-height: 35px;
    font-size: 15px;
    text-decoration: underline;
}
  
.promo-bar * {
    color: #fff;
}
@keyframes marquee{from{transform:translateX(0)}to{transform:translateX(-50%)}}
  
</style>

<div class="promo-bar ">
<div class="promo-bar-inner container flex f-vertical-center f-space-between promo-bar-inner--scrolling">

{%- for block in section.blocks -%}
<div class="promo-bar__text">
  <p><a href="{{ block.settings.link }}" title="Clearance">{{ block.settings.text }}</a></p>
</div>
{% endfor %}
{%- for block in section.blocks -%}
<div class="promo-bar__text">
  <p><a href="{{ block.settings.link }}" title="Clearance">{{ block.settings.text }}</a></p>
</div>
{% endfor %}

 
</div>
</div>