{% assign shappify_bdl_options = '' %}
{% assign shappify_bdl_options_count = '0' %}
{% for variant in product.variants %}
    {% if variant.metafields.shappify_bundle.is_bundle == "true" %}{% else %}
        {% assign shappify_bdl_options_count = shappify_bdl_options_count|plus:1 %}
    {% endif %}
{% endfor %}

{% if shappify_bdl_options_count > 0 %}
<script>
 var domLoaded = function (callback) {
    /* Internet Explorer */
    /*@cc_on
    @if (@_win32 || @_win64)
    document.write('<script id="ieScriptLoad" defer src="//:"><\/script>');
        document.getElementById('ieScriptLoad').onreadystatechange = function() {
            if (this.readyState == 'complete') {
                callback();
            }
        };
        @end @*/
            /* Mozilla, Chrome, Opera */
        if (document.addEventListener) {
            document.addEventListener('DOMContentLoaded', callback, false);
        }
        /* Safari, iCab, Konqueror */
        else if (/KHTML|WebKit|iCab/i.test(navigator.userAgent)) {
            var DOMLoadTimer = setInterval(function () {
                if (/loaded|complete/i.test(document.readyState)) {
                    callback();
                    clearInterval(DOMLoadTimer);
                }
            }, 10);
        }else{
            /* Other web browsers */
            window.onload = callback;
        }
    };


domLoaded(function() {
  
  setTimeout(fix_variants, 1000);
  function fix_variants(){
{% for variant in product.variants %}
    {% if variant.metafields.shappify_bundle.is_bundle == "true" %}

          jQuery('.single-option-selector option').filter(function() {
            return jQuery(this).text() == {{ variant.option1 | json }}
          }).remove();

          jQuery('.single-option-selector option').filter(function() {
            return jQuery(this).text() == {{ variant.option2 | json }}
          }).remove();

          jQuery('.single-option-selector option').filter(function() {
            return jQuery(this).text() == {{ variant.option3 | json }}
          }).remove();

      {% endif %}
    {% endfor %}
  }
    jQuery('.single-option-selector').trigger('change');
});
</script>

{% endif %}
