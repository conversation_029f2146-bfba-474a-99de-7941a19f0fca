{% comment %}
 
{% if collection.title == "NAIDOC WEEK 2018 LEGGINGS" %}
              {%- for group in groups -%}

                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                 
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}}<span class="Collapsible__Plus"></span>
                  </button>
              
               
                  
     
            <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                              <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                                
                              </button>
                            </li>
                          {%- else -%}
                   
                         {% unless tag_parts.last == "Leggings" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                               {{- tag_parts.last -}}
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                        
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}




{% elsif collection.title contains "WOMEN'S POLOZ" %}
    
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                 <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                     
                       {% unless tag_parts.last == "Polo Shirts" or tag_parts.last == "Womens" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                               {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}


{% elsif collection.title == "CROP TOPS" %}
    
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                 <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                     
                       {% unless tag_parts.last == "Crop Tops" or tag contains "tab"  %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                               {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}



{% elsif collection.title == "WOMEN'S T-SHIRTS" %}
    
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                     
                       {% unless tag_parts.last == "T-Shirts" or tag_parts.last == "Womens" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                               {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}


{% elsif collection.title == "LEGGINGS" %}
    
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                  <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                     
                       {% unless tag_parts.last == "Leggings" or tag_parts.last == "Womens" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                               {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}





    
    
    {% elsif collection.title == "NAIDOC WEEK 2018 POLO SHIRTS" %}
    
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                  <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                     
                       {% unless tag_parts.last == "Polo Shirts" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                               {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

    {% elsif collection.title == "NAIDOC WEEK 2018 T-SHIRTS" %}
    
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
              <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                     
                       {% unless tag_parts.last == "T-Shirts" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

 {% elsif collection.title == "NAIDOC WEEK 2018 SINGLETS" %}
    
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                     
                       {% unless tag_parts.last == "Singlets" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}


{% elsif collection.title == "NAIDOC WEEK 2018 HOODIES" %}
    
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
               <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                     
                       {% unless tag_parts.last == "Hoodies" or tag contains "tab"  %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}


{% elsif collection.title == "NAIDOC WEEK 2018 SHORTS" %}
    
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
               <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                     
                       {% unless tag_parts.last == "Shorts" or  tag_parts.last == "Mens" or tag contains "tab"   %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "MENS" %}
    
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
               <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                   
                       {% unless tag_parts.last == "Mens" or tag_parts.last == "Adults" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "MEN'S POLO SHIRTS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Adults" or tag_parts.last == "Polo Shirts" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                 {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}
{% elsif collection.title == "NAIDOC WEEK 2018 RANGE" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless  tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                 {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "MEN'S T-SHIRTS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "T-Shirts" or tag_parts.last == "Mens" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "MENS TOPS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                  <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Adults" or tag_parts.last == "Mens" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "NAIDOC WEEK 2018 BAGS RANGE" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Bags" or tag contains "tab"  %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                               {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}
{% elsif collection.title == "NAIDOC WEEK 2018 SOCKS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
              <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Socks" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                               {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "NAIDOC WEEK 2018 STATIONERY" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                 <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Stationery" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "NAIDOC WEEK 2018 HEADWEAR" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                  <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Caps" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "NAIDOC WEEK 2018 HEADWEAR" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                 <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Caps" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                 {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}
{% elsif collection.title == "MEN'S SINGLETS / TANKS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Singlets" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                 {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "PERSONALISED JERSEY" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                  <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Jerseys" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "MENS HOODIES" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                  <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Hoodies" or tag_parts.last == "Mens" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                               {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "MENS BOTTOMS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                  <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Mens" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "MENS SHORTS/SWIMMERS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                 <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Mens" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "MENS SHOES" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
               <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Shoes" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                               {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "SHOES" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Shoes" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "SOCKS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                  <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Socks" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "WOMENS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
               <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "3/4" or tag_parts.last == "High Waist" or tag_parts.last == "Full" or tag_parts.last == "Womens" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}
{% elsif collection.title == "WOMENS BOTTOMS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                 <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "3/4" or tag_parts.last == "High Waist" or tag_parts.last == "Full" or tag_parts.last == "Womens"  %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}
{% elsif collection.title == "WOMENS TOPS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
               <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Womens" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "WOMEN'S POLO SHIRTS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
               <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Womens" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}
{% elsif collection.title == "WOMEN'S T-SHIRTS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                 <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Womens" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "WOMENS HOODIES" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                 <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Womens" or tag_parts.last == "Hoodies" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                 {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "LEGGINGS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                 <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Womens" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                               {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}
{% elsif collection.title == "WOMEN'S POLO SHIRTS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Polo Shirts" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                 {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "CROP TOPS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "T-Shirts" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}
{% elsif collection.title == "BLOUSES" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Blouses" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                 {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "WOMENS SHORTS/SWIMMERS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
               <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Boardshorts" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                               {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "SKIRTS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                 <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Skirts" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "BAGS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                 <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Bags" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "BAGS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                 <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Bags" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "DRESSES" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
               <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Dresses" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                               {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "KIDS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
<div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Kids" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}

{% elsif collection.title == "KID'S POLO SHIRTS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                  <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Kids" or tag_parts.last == "Polo Shirts"%}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}



{% elsif collection.title == "KIDS LEGGINGS" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                 <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Leggings" or tag_parts.last == "Kids" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                               {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}



{% elsif collection.title == "WOMENS SHOES" %}
   
     {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--autoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                 <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                    
                       {% unless tag_parts.last == "Shoes" or tag contains "tab" %}
                            <li class="Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                               {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                        {% endunless %}
                      
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}



    {% else %}

 {%- for group in groups -%}
                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                     
                       {% unless tag contains "tab" %}
                            <li class="tseting Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                 {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                      {% endunless %}
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
              {%- endfor -%}
      {%- endif -%}


{% endcomment %}



 {%- for group in groups -%}

                {%- assign group_downcase = group | downcase -%}

                {%- if color_label contains group_downcase and section.settings.show_filter_color_swatch -%}
                  {%- assign show_color_swatch = true -%}
                {%- else -%}
                  {%- assign show_color_swatch = false -%}
                {%- endif -%}

                <div class="Collapsible Collapsible--padded Collapsible--aautoExpand">
                  <button type="button" class="Collapsible__Button Heading u-h6" data-action="toggle-collapsible" aria-expanded="false">
                    {{- group | escape -}} <span class="Collapsible__Plus"></span>
                  </button>
                  
     
                <div class="Collapsible__Inner customcheck">
                      <div class="Collapsible__Content">
                       
					 
                      <ul class="{% if show_color_swatch %}ColorSwatchList HorizontalList HorizontalList--spacingTight{% else %}Linklist{% endif %}">
                       
                        {%- for tag in collection.all_tags -%}
                          {%- assign tag_parts = tag | split: '_' -%}

                          {%- if tag_parts.first != group -%}
                            {%- continue -%}
                          {%- endif -%}

                          {%- if show_color_swatch -%}
                            <li class="HorizontalList__Item">
                              <button type="button" class="ColorSwatch {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag" data-tooltip="{{ tag_parts.last | escape }}" style="background-color: {{ tag_parts.last | split: ' ' | last | handle }}; background-image: url({{ tag_parts.last | handle | append: '.png' | asset_url }})">
                                <span class="u-visually-hidden">{{ tag_parts.last }}</span>
                              </button>
                            </li>
                          {%- else -%}
                     
                       {% unless tag contains "tab" %}
                            <li class="tseting Linklist__Item {% if current_tags contains tag %}is-selected{% endif %}">
                              <button type="button" class="Text--subdued Link Link--primary {% if current_tags contains tag %}is-active{% endif %}" data-tag="{{ tag | handle }}" data-action="toggle-tag">
                                 {{- tag_parts.last -}}
                                
                              
                              </button>
                            </li>
                      {% endunless %}
						
                          {%- endif -%}
                          
                    
                          
                        {%- endfor -%}
                      </ul>
                         
                    </div>
                  </div>
                </div>
           
          
                
{%- endfor -%}