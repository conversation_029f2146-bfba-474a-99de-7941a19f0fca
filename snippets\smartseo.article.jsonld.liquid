{%- assign smartseoSettingsMetafieldNamespace = 'smartseo-settings' -%}
{%- assign smartseoSettingsMetafieldKey = 'json-ld' -%}
{%- assign smartseoSettings = shop.metafields[smartseoSettingsMetafieldNamespace][smartseoSettingsMetafieldKey] -%}
{%- assign logo = smartseoSettings.LogoUrl %}

<!--JSON-LD data generated by Smart SEO-->
<script type="application/ld+json">
    {
        "@context": "http://schema.org",
        "@type": "Article",
        "about": "{{ smartseo_description }}",
    {%- if smartseo_keywords != blank %}
        "keywords": "{{ smartseo_keywords }}",
    {%- endif %}
        "headline": "{{ smartseo_title | truncate: 110 }}",
        "mainEntityOfPage": "{{ shop.url | append: article.url }}",
        "image": {
            "@type": "ImageObject",
            "url": "{{ article.image.src | img_url: "1024x1024" | prepend: "https:" }}",
            "width": 1024,
            "height": 1024
        },
        "url": "{{ shop.url | append: article.url }}",
        "dateModified": "{{ article.published_at }}",
        "datePublished": "{{ article.published_at }}",
        "dateCreated": "{{ article.created_at }}",
    {%- if article.excerpt != blank %}
        "description": "{{ article.excerpt | strip_html | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}",
    {%- endif %}
        "articleBody": "{{ article.content | strip_html | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}",
        "author": {
            "@type": "Person",
            "name": "{{ article.author | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}"
        },
        "publisher": {
            "@type": "Organization",
        {%- if logo != blank %}
            "logo": {
                "@type": "ImageObject",
                "url": "{{ logo }}"
            },
        {%- endif %}
            "name": "{{ shop.name | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}"
        }
    }
</script>