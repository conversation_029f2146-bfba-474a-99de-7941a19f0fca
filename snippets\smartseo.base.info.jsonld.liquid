{%- assign smartseoSettingsMetafieldNamespace = 'smartseo-settings' -%}
{%- assign smartseoSettingsMetafieldKey = 'json-ld' -%}
{%- assign smartseoSettings = shop.metafields[smartseoSettingsMetafieldNamespace][smartseoSettingsMetafieldKey] -%}

{%- if template.name == 'index' %}
<!--JSON-LD data generated by Smart SEO-->
<script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Website",
        "url": "{{ shop.url }}",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ shop.url }}/search?q={query}",
            "query-input": "required name=query"
        }
    }
</script>
{%- endif -%}

{%- if smartseoSettings.EnableStructuredDataForRealStore == true %}
<!--JSON-LD data generated by Smart SEO-->
<script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Store",
        "name": "{{ shop.name | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}",
        "url": "{{ shop.url }}",
        "description": "{{ shop.description | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}",
    {%- if smartseoSettings.LogoUrl != blank %}
        "logo": "{{ smartseoSettings.LogoUrl }}",
    {%- endif -%}
    {%- if smartseoSettings.LogoUrl != blank %}
        "image": "{{ smartseoSettings.LogoUrl }}",
    {%- endif -%}
    {%- if smartseoSettings.SocialMediaLinks != blank %}
        "sameAs": [{{ smartseoSettings.SocialMediaLinks }}],
    {%- endif -%}
    {%- if smartseoSettings.EnableStoreAddress == true and smartseoSettings.LocationUrl != blank %}
        "hasMap": "{{ smartseoSettings.LocationUrl }}",
    {%- endif -%}
    {%- if smartseoSettings.EnableStoreAddress == true %}
        "address": {
            "@type": "PostalAddress",
        {%- if shop.address.street != blank %}
            "streetAddress": "{{ shop.address.street | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}",
        {%- endif -%}
        {%- if shop.address.city != blank %}
            "addressLocality": "{{ shop.address.city | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}",
        {%- endif -%}
        {%- if shop.address.province != blank %}
            "addressRegion": "{{ shop.address.province | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}",
        {%- endif -%}
        {%- if shop.address.zip != blank %}
            "postalCode": "{{ shop.address.zip | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}",
        {%- endif -%}
        {%- if shop.address.country != blank %}
            "addressCountry": "{{ shop.address.country | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}"
        {%- endif %}
        },
    {%- endif %}
        "priceRange": "{{ smartseoSettings.PriceRange }}"{%- if smartseoSettings.EnablePhoneNumber == true and shop.address.phone != blank %},
        "telephone": "{{ shop.address.phone | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}"
    {%- endif -%}
    {%- if smartseoSettings.EnableStoreAddress == true and smartseoSettings.Latitude != blank and smartseoSettings.Longitude != blank %},
        "geo": {
            "@type": "GeoCoordinates",
            "latitude": {{ smartseoSettings.Latitude }},
            "longitude": {{ smartseoSettings.Longitude }}
        }
    {%- endif %}
    }
</script>
{%- else %}
<!--JSON-LD data generated by Smart SEO-->
<script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "{{ shop.name | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}",
        "url": "{{ shop.url }}",
        "description": "{{ shop.description | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}"
    {%- if smartseoSettings.LogoUrl != blank %},
        "logo": "{{ smartseoSettings.LogoUrl }}"
    {%- endif -%}
    {%- if smartseoSettings.LogoUrl != blank %},
        "image": "{{ smartseoSettings.LogoUrl }}"
    {%- endif -%}
    {%- if smartseoSettings.SocialMediaLinks != blank %},
        "sameAs": [{{ smartseoSettings.SocialMediaLinks }}]
    {%- endif -%}
    {%- if smartseoSettings.EnableStoreAddress == true %},
        "address": {
            "@type": "PostalAddress",
        {%- if shop.address.street != blank %}
            "streetAddress": "{{ shop.address.street | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}",
        {%- endif -%}
        {%- if shop.address.city != blank %}
            "addressLocality": "{{ shop.address.city | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}",
        {%- endif -%}
        {%- if shop.address.province != blank %}
            "addressRegion": "{{ shop.address.province | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}",
        {%- endif -%}
        {%- if shop.address.zip != blank %}
            "postalCode": "{{ shop.address.zip | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}",
        {%- endif -%}
        {%- if shop.address.country != blank %}
            "addressCountry": "{{ shop.address.country | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}"
        {%- endif %}
        }
    {%- endif -%}
    {%- if smartseoSettings.EnablePhoneNumber == true and shop.address.phone != blank %},
        "telephone": "{{ shop.address.phone | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}"
    {%- endif %}
    }
</script>
{%- endif %}