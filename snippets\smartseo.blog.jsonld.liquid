{%- assign smartseoSettingsMetafieldNamespace = 'smartseo-settings' -%}
{%- assign smartseoSettingsMetafieldKey = 'json-ld' -%}
{%- assign smartseoSettings = shop.metafields[smartseoSettingsMetafieldNamespace][smartseoSettingsMetafieldKey] -%}
{%- assign logo = smartseoSettings.LogoUrl -%}

<!--JSON-LD data generated by Smart SEO-->
<script type="application/ld+json">
    {
        "@context": "http://schema.org",
        "@type": "Blog",
    {%- if smartseo_description != blank %}
        "about": "{{ smartseo_description }}",
    {%- endif -%}
    {%- if smartseo_keywords != blank %}
        "keywords": "{{ smartseo_keywords }}",
    {%- endif -%}
    {%- if smartseo_title != blank %}
        "name": "{{ smartseo_title }}",
    {%- endif %}
        "url": "{{ blog.url }}"
    {%- if blog.articles != blank %},
        "blogPosts": [
        {%- for article in blog.articles %}
            {
                "@context": "http://schema.org",
                "@type": "BlogPosting",
                "headline": "{{ article.title | strip_newlines | strip_html | escape_once | replace: '\', '\\\\'  | truncate: 110}}",
                "mainEntityOfPage": "{{ shop.url | append: article.url }}",
                "image": {
                    "@type": "ImageObject",
                    "url": "{{ article.image.src | img_url: "1024x1024" | prepend: "https:" }}",
                    "width": 1024,
                    "height": 1024
                },
                "url": "{{ shop.url | append: article.url }}",
                "datePublished": "{{ article.published_at }}",
                "dateModified": "{{ article.published_at }}",
                "dateCreated": "{{ article.created_at }}",
            {%- if article.excerpt != blank %}
                "description": "{{ article.excerpt | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}",
            {%- endif %}
                "author": {
                    "@type": "Person",
                    "name": "{{ article.author | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}"
                },
                "publisher": {
                    "@type": "Organization",
                {%- if logo != blank %}
                    "logo": {
                        "@type": "ImageObject",
                        "url": "{{ logo }}"
                    },
                {%- endif %}
                    "name": "{{ shop.name | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}"
                }
            }{% unless forloop.last %},{% endunless %}
        {%- endfor %}
        ]
    {%- endif %}
    }
</script>
