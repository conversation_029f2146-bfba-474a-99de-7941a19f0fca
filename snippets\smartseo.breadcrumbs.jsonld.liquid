{%- assign position = 1 -%}
{%- assign breadcrumb_tags_item_name = current_tags | join: ' + ' -%}
{%- assign current_tags_url_part = current_tags | join: '+' | downcase -%}
{%- assign smartseoSettingsMetafieldNamespace = 'smartseo-settings' -%}
{%- assign smartseoSettingsMetafieldKey = 'json-ld' -%}
{%- assign smartseoSettings = shop.metafields[smartseoSettingsMetafieldNamespace][smartseoSettingsMetafieldKey] -%}
{%- assign logo = smartseoSettings.LogoUrl -%}

<!--JSON-LD data generated by Smart SEO-->
<script type="application/ld+json">
    {
        "@context": "http://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": {{ position }},
                "item": {
                    "@type": "Website",
                    "@id": "{{ shop.url }}",
                    "name": "{{ 'general.breadcrumbs.home' | t }}"
                }
            }

    {%- if article -%}

            {%- assign position = position | plus: 1 -%},
            {
                "@type": "ListItem",
                "position": {{ position }},
                "item": {
                    "@type": "Blog",
                    "@id": "{{ shop.url | append: blog.url }}",
                    "name": "{{ blog.title | escape_once }}"
                }
            }

            {%- assign position = position | plus: 1 -%},
            {
                "@type": "ListItem",
                "position": {{ position }},
                "item": {
                    "@type": "Article",
                    "@id": "{{ shop.url | append: article.url }}",
                    "name": "{{ article.title | escape_once }}",
                    "author": {
                        "@type": "Person",
                        "name": "{{ article.author }}"
                    },
                    "datePublished": "{{ article.published_at }}",
                    "dateModified": "{{ article.published_at }}",
                    "headline": "{{ article.title | truncate: 110 }}",
                    "image": {
                        "@type": "ImageObject",
                        "url": "{{ article.image.src | img_url: "1024x1024" | prepend: "https:" }}",
                        "width": 1024,
                        "height": 1024
                    },
                    "publisher": {
                        "@type": "Organization",
                    {%- if logo != blank %}
                        "logo": {
                            "@type": "ImageObject",
                            "url": "{{ logo }}"
                        },
                    {%- endif %}
                        "name": "{{ shop.name | escape_once }}"
                    },
                    "mainEntityOfPage": "{{ shop.url | append: article.url }}"
                }
            }

    {%- elsif blog and article == nil -%}

            {%- assign position = position | plus: 1 -%},
            {
                "@type": "ListItem",
                "position": {{ position }},
                "item": {
                    "@type": "Blog",
                    "@id": "{{ shop.url | append: blog.url }}",
                    "name": "{{ blog.title | escape_once }}"
                }
            }
        
        {%- if current_tags != blank -%}
            {%- assign position = position | plus: 1 -%},
            {
                "@type": "ListItem",
                "position": {{ position }},
                "item": {
                    "@type": "Blog",
                    "@id": "{{ shop.url | append: blog.url | append: '/' | append: current_tags_url_part }}",
                    "name": "{{ breadcrumb_tags_item_name }}"
                }
            }
        {%- endif -%}
            
    {%- elsif product -%}

        {%- assign product_breadcrumb_url = shop.url | append: '/products/' | append: product.handle -%}

        {%- if collection and collection.url != blank -%}
        
            {%- assign position = position | plus: 1 -%}
            {%- assign product_breadcrumb_url = shop.url | append: collection.url | append: '/products/' | append: product.handle -%},
            {
                "@type": "ListItem",
                "position": {{ position }},
                "item": {
                    "@type": "CollectionPage",
                    "@id": "{{ shop.url | append: collection.url }}",
                    "name": "{{ collection.title | escape_once }}"
                }
            }
        {%- endif -%}

            {%- assign position = position | plus: 1 -%},
            {
                "@type": "ListItem",
                "position": {{ position }},
                "item": {
                    "@type": "WebPage",
                    "@id": "{{ product_breadcrumb_url }}",
                    "name": "{{ product.title | escape_once }}"
                }
            }

    {%- elsif collection and product == nil -%}

            {%- assign position = position | plus: 1 -%},
            {
                "@type": "ListItem",
                "position": {{ position }},
                "item": {
                    "@type": "CollectionPage",
                    "@id": "{{ shop.url | append: collection.url }}",
                    "name": "{{ collection.title | escape_once }}"
                }
            }
            
        {%- if current_tags != blank -%}
            {%- assign position = position | plus: 1 -%},
            {
                "@type": "ListItem",
                "position": {{ position }},
                "item": {
                    "@type": "CollectionPage",
                    "@id": "{{ shop.url | append: collection.url | append: '/' | append: current_tags_url_part }}",
                    "name": "{{ breadcrumb_tags_item_name }}"
                }
            }
        {%- endif -%}
            
    {%- endif %}
        ]
    }
</script>