{%- capture collection_json_ld %}
<!--JSON-LD data generated by Smart SEO-->
<script type="application/ld+json">
    {
        "@context": "http://schema.org",
        "@type": "ItemList",
        "name": "{{ smartseo_title }}",
        "url": "{{ shop.url | append: collection.url }}",
        "description": "{{ smartseo_description }}",
        "image": "https:{{ collection.image | img_url: "master" }}",
        "mainEntityOfPage": {
            "@type": "CollectionPage",
            "@id": "{{ shop.url | append: collection.url }}"
        },
        "itemListElement": [
            {%- for product in collection.products %}
            {
                "@type": "ListItem",
                "position": {{ forloop.index }},
                "url": "{{ shop.url | append: '/products/' | append: product.handle }}"
            }{%- unless forloop.last -%},{%- endunless -%}
            {%- endfor %}
        ]
    }
</script>
{%- endcapture %}
{{ collection_json_ld | strip_newlines | replace: '  ', '' | replace: ': ', ':' | replace: ' {', '{' }}