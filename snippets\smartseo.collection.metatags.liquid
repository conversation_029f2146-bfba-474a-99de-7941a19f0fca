{%- assign collection_individual_seo_metafield_namespace = 'collection_seo' -%}
{%- assign individual_seo_metafield_key = 'seo_tags' -%}
{%- assign shop_seo_collection_bulk_metafield_namespace = 'collection-bulk' -%}
{%- assign shop_seo_collection_bulk_metafield_key = 'seo-template' -%}
{%- assign metafield_parts_separator = '=||=' -%}
{%- assign template_value_parts_separator = ':||:' -%}
{%- assign is_langify_enabled = false -%}

{%- assign smartseo_title = page_title -%}
{%- assign smartseo_description = page_description -%}
{%- assign smartseo_keywords = blank -%}

{%- assign active_seo_template = blank -%}
{%- assign active_seo_template_timestamp = 0 -%}

{%- comment -%} Individual collection template {%- endcomment -%}
{%- assign collection_seo_template_metafield = collection.metafields[collection_individual_seo_metafield_namespace][individual_seo_metafield_key] -%}
{%- if collection_seo_template_metafield -%}
    {%- assign seo_template_metafield_parts = collection_seo_template_metafield | split: metafield_parts_separator -%}
    {%- assign seo_template_metafield_timestamp = seo_template_metafield_parts | last | times: 1 -%}
    {%- assign active_seo_template = seo_template_metafield_parts[0] -%}
    {%- assign active_seo_template_timestamp = seo_template_metafield_timestamp -%}
{%- endif -%}

{%- comment -%} Bulk template metafield {%- endcomment -%}
{%- assign seo_template_bulk_collection_metafield = shop.metafields[shop_seo_collection_bulk_metafield_namespace][shop_seo_collection_bulk_metafield_key] -%}
{%- if seo_template_bulk_collection_metafield -%}
    {%- assign seo_template_bulk_collection_metafield_parts = seo_template_bulk_collection_metafield | split: metafield_parts_separator -%}
    {%- assign seo_template_bulk_collection_metafield_timestamp = seo_template_bulk_collection_metafield_parts | last | times: 1 -%}
    {%- if seo_template_bulk_collection_metafield_timestamp > active_seo_template_timestamp -%}
        {%- assign active_seo_template = seo_template_bulk_collection_metafield_parts[0] -%}
        {%- assign active_seo_template_timestamp = seo_template_bulk_collection_metafield_timestamp -%}
    {%- endif -%}
{%- endif -%}

{%- if active_seo_template != blank -%}
    {%- if is_langify_enabled -%}
        {%- if active_seo_template contains '${title}' -%}
            {%- comment -%}ly_code_replace_for_[ collection.title ]_begin{%- endcomment -%}
            {%- capture langify_collection_title -%}{% include 'ly-title' with collection %}{{ ly_translation }}{%- endcapture -%}
            {%- comment -%}ly_code_replace_end{%- endcomment -%}
            {%- assign active_seo_template = active_seo_template | replace: '${title}', langify_collection_title -%}
        {%- endif -%}

        {%- if active_seo_template contains '${description}' -%}
            {%- comment -%}ly_code_replace_for_[ collection.description ]_begin{%- endcomment -%}
            {%- capture langify_collection_description -%}{% include 'ly-description' with collection %}{{ ly_translation }}{%- endcapture -%}
            {%- comment -%}ly_code_replace_end{%- endcomment -%}
            {%- assign active_seo_template = active_seo_template | replace: '${description}', langify_collection_description -%}
        {%- endif -%}

        {%- if active_seo_template contains '${current-tags}' -%}
            {%- capture collection_current_tags -%}
                {% assign collection_current_tags_str = current_tags | join: ', ' | append: ',' -%}
                {%- assign tag_translation_namespace = 'ta' | append: language %}
                {%- for tag_translation in shop.metafields[tag_translation_namespace] -%}
                    {%- assign srcToReplace = tag_translation | first %}
                    {%- assign srcToCheckFor = tag_translation | first | append: ',' -%}
                    {%- assign translated = tag_translation | last -%}
                    {%- if collection_current_tags_str contains srcToCheckFor -%}
                        {%- assign collection_current_tags_str = collection_current_tags_str | replace: srcToReplace, translated -%}
                    {%- endif -%}
                {%- endfor -%}{{ collection_current_tags_str }}
            {%- endcapture -%}
            {%- assign active_seo_template = active_seo_template | replace: '${current-tags}', collection_current_tags -%}
        {%- endif -%}

        {%- if active_seo_template contains '-product-titles}' -%}
            {%- comment -%}extract the number of product titles-begin{%- endcomment -%}
            {%- assign seo_template_token_parts = active_seo_template | split: '-product-titles}' -%}
            {%- assign product_number_template_part = seo_template_token_parts[0] -%}
            {%- assign product_number_template_part_parts = product_number_template_part | split: '${' -%}
            {%- assign product_number_string = product_number_template_part_parts | last -%}
            {%- assign product_number = product_number_string | plus: 0 -%}
            {%- if product_number_string == 'n' -%}
                {%- assign product_number = 30 -%}
            {%- endif -%}
            {%- comment -%}extract the number of product titles-end{%- endcomment -%}
            {%- comment -%}ly_code_replace_for_[ n-product-titles ]_begin{%- endcomment -%}
            {%- capture langify_product_titles -%}
                {%- for product in collection.products limit: product_number -%}
                    {%- comment -%}ly_code_replace_for_[ product.title ]_begin{%- endcomment -%}
                    {%- include 'ly-title' with product -%}{{ ly_translation }}
                    {%- comment -%}ly_code_replace_end{%- endcomment -%}
                    {%- if forloop.last != true -%}, {% endif %}
                {% endfor -%}
            {%- endcapture -%}
            {%- comment -%}ly_code_replace_end{%- endcomment -%}
            {%- assign token_string_to_replace = '${' | append: product_number_string | append: '-product-titles}' -%}
            {%- assign active_seo_template = active_seo_template | replace: token_string_to_replace, langify_product_titles -%}
        {%- endif -%}

        {%- if active_seo_template contains '-product-tags}' -%}
            {%- comment -%}extract the number of product tags-begin{%- endcomment -%}
            {%- assign seo_template_token_parts = active_seo_template | split: '-product-tags}' -%}
            {%- assign product_number_template_part = seo_template_token_parts[0] -%}
            {%- assign product_number_template_part_parts = product_number_template_part | split: '${' -%}
            {%- assign product_number_string = product_number_template_part_parts | last -%}
            {%- assign product_number = product_number_string | plus: 0 -%}
            {%- if product_number_string == 'n' -%}
                {%- assign product_number = 30 -%}
            {%- endif -%}
            {%- comment -%}extract the number of product tags-end{%- endcomment -%}
            {%- comment -%}ly_code_replace_for_[ n-product-tags ]_begin{%- endcomment -%}
            {%- capture limited_product_tags -%}
                    {%- for tag in collection.tags limit: product_number -%}{{ tag }},{%- endfor -%}
            {%- endcapture -%}
            {%- capture langify_product_tags -%}
                {%- assign tag_translation_namespace = 'ta' | append: language -%}
                {%- for tag_translation in shop.metafields[tag_translation_namespace] -%}
                    {%- assign srcToReplace = tag_translation | first -%}
                    {%- assign srcToCheckFor = tag_translation | first | append: ',' -%}
                    {%- assign translated = tag_translation | last -%}
                    {%- if limited_product_tags contains srcToCheckFor -%}
                        {%- assign limited_product_tags = limited_product_tags | replace: srcToReplace, translated -%}
                    {%- endif -%}
                {%- endfor -%}{{ limited_product_tags }}
            {%- endcapture -%}
            {%- comment -%}ly_code_replace_end{%- endcomment -%}
            {%- assign token_string_to_replace = '${' | append: product_number_string | append: '-product-tags}' -%}
            {%- assign active_seo_template = active_seo_template | replace: token_string_to_replace, langify_product_titles -%}
        {%- endif -%}

        {%- if active_seo_template contains '-product-types}' -%}
            {%- comment -%}extract the number of product types-begin{%- endcomment -%}
            {%- assign seo_template_token_parts = active_seo_template | split: '-product-types}' -%}
            {%- assign product_number_template_part = seo_template_token_parts[0] -%}
            {%- assign product_number_template_part_parts = product_number_template_part | split: '${' -%}
            {%- assign product_number_string = product_number_template_part_parts | last -%}
            {%- assign product_number = product_number_string | plus: 0 -%}
            {%- if product_number_string == 'n' -%}
                {%- assign product_number = 30 -%}
            {%- endif -%}
            {%- comment -%}extract the number of product types-end{%- endcomment -%}
            {%- comment -%}ly_code_replace_for_[ n-product-types ]_begin{%- endcomment -%}
            {%- capture langify_product_types -%}
                {%- assign i = 0 -%}
                {%- assign translated_types ='' -%}
                {%- for product in collection.products -%}
                    {%- comment -%}ly_code_replace_for_[ product.type ]_begin{%- endcomment -%}
                    {%- include 'ly-type' with product -%}
                    {%- comment -%}ly_code_replace_end{%- endcomment -%}
                    {%- assign ly_translation_string = ly_translation | append: ',' -%}
                    {%- if translated_types contains ly_translation_string -%}
                        {%- continue -%}
                    {%- else -%}
                        {%- assign translated_types = translated_types | append : ly_translation | append: ',' -%}
                        {%- increment i -%}
                        {%- if i == product_number -%}{%- break -%}{%- endif -%}
                    {%- endif -%}
                {%- endfor -%}{{ translated_types | append: '$#' | replace: ',$#', '' | replace: '$#', '' | strip }}
            {%- endcapture -%}
            {%- comment -%}ly_code_replace_end{%- endcomment -%}
            {%- assign token_string_to_replace = '${' | append: product_number_string | append: '-product-types}' -%}
            {%- assign active_seo_template = active_seo_template | replace: token_string_to_replace, langify_product_types -%}
        {%- endif -%}

        {%- if active_seo_template contains '-vendors}' -%}
            {%- comment -%}extract the number of collection vendors-begin{%- endcomment -%}
            {%- assign seo_template_token_parts = active_seo_template | split: '-vendors}' -%}
            {%- assign vendors_number_template_part = seo_template_token_parts[0] -%}
            {%- assign vendors_number_template_part_parts = vendors_number_template_part | split: '${' -%}
            {%- assign vendors_number_string = vendors_number_template_part_parts | last -%}
            {%- assign vendors_number = vendors_number_string | plus: 0 -%}
            {%- if vendors_number_string == 'n' -%}
                {%- assign vendors_number = 30 -%}
            {%- endif -%}
            {%- comment -%}extract the number of collection vendors-end{%- endcomment -%}
            {%- comment -%}ly_code_replace_for_[ n-vendors ]_begin{%- endcomment -%}
            {%- capture langify_collection_vendors -%}
                {%- assign i = 0 -%}
                {%- assign translated_vendors ='' -%}
                {%- for product in collection.products -%}
                    {%- comment -%}ly_code_replace_for_[ product.vendor ]_begin{%- endcomment -%}
                    {%- include 'ly-vendor' with product -%}
                    {%- comment -%}ly_code_replace_end{%- endcomment -%}
                    {%- assign ly_translation_string = ly_translation | append: ',' -%}
                    {%- if translated_vendors contains ly_translation_string -%}
                        {%- continue -%}
                    {%- else -%}
                        {%- assign translated_vendors = translated_vendors | append : ly_translation | append: ',' -%}
                        {%- increment i -%}
                        {%- if i == vendors_number -%}{%- break -%}{%- endif -%}
                    {%- endif -%}
                {%- endfor -%}{{ translated_vendors | append: '$#' | replace: ',$#', '' | replace: '$#', '' | strip }}
            {%- endcapture -%}
            {%- comment -%}ly_code_replace_end{%- endcomment -%}
            {%- assign token_string_to_replace = '${' | append: vendors_number_string | append: '-vendors}' -%}
            {%- assign active_seo_template = active_seo_template | replace: token_string_to_replace, langify_collection_vendors -%}
        {%- endif -%}

        {%- if active_seo_template contains '-product-titles-with-vendor-names}' -%}
            {%- comment -%}extract the number of product titles with vendor names-begin{%- endcomment -%}
            {%- assign seo_template_token_parts = active_seo_template | split: '-product-titles-with-vendor-names}' -%}
            {%- assign titles_vendors_number_template_part = seo_template_token_parts[0] -%}
            {%- assign titles_vendors_number_template_part_parts = titles_vendors_number_template_part | split: '${' -%}
            {%- assign titles_vendors_number_string = titles_vendors_number_template_part_parts | last -%}
            {%- assign titles_vendors_number = titles_vendors_number_string | plus: 0 -%}
            {%- if titles_vendors_number_string == 'n' -%}
                {%- assign titles_vendors_number = 30 -%}
            {%- endif -%}
            {%- comment -%}extract the number of product titles with vendor names-end{%- endcomment -%}
            {%- comment -%}ly_code_replace_for_[ n-vendors ]_begin{%- endcomment -%}
            {%- capture langify_product_titles_with_vendor_names -%}
                {%- for product in collection.products limit: titles_vendors_number -%}
                    {%- comment -%}ly_code_replace_for_[ product.vendor ]_begin{%- endcomment -%}
                    {%- include 'ly-vendor' with product -%}{{ ly_translation }}
                    {%- comment -%}ly_code_replace_end{%- endcomment -%}
                    {%- comment -%}ly_code_replace_for_[ product.title ]_begin{%- endcomment -%}
                    {%- include 'ly-title' with product -%}{{ ly_translation }}
                    {%- comment -%}ly_code_replace_end{%- endcomment -%}
                    {%- if forloop.last != true -%}, {% endif -%}
                {%- endfor -%}
            {%- endcapture -%}
            {%- comment -%}ly_code_replace_end{%- endcomment -%}
            {%- assign token_string_to_replace = '${' | append: titles_vendors_number_string | append: '-product-titles-with-vendor-names}' -%}
            {%- assign active_seo_template = active_seo_template | replace: token_string_to_replace, langify_product_titles_with_vendor_names -%}
        {%- endif -%}

        {%- assign active_seo_template = active_seo_template | replace: '${default-meta-title}', langify_title -%}
        {%- assign active_seo_template = active_seo_template | replace: '${default-meta-description}', langify_description -%}
        {%- assign active_seo_template = active_seo_template | replace: '${shop-name}', langify_shop_name -%}
        {%- assign active_seo_template = active_seo_template | replace: '${shop-description}', langify_shop_description -%}
    {%- else -%}
        {%- assign active_seo_template = active_seo_template | replace: '${title}', collection.title -%}
        {%- assign active_seo_template = active_seo_template | replace: '${description}', collection.description -%}

        {%- assign current_tags_string = current_tags | join : ', ' -%}
        {%- assign active_seo_template = active_seo_template | replace: '${current-tags}', current_tags_string -%}

        {%- if active_seo_template contains '-product-titles}' -%}
            {%- comment -%}extract the number of product titles-begin{%- endcomment -%}
            {%- assign seo_template_token_parts = active_seo_template | split: '-product-titles}' -%}
            {%- assign product_number_template_part = seo_template_token_parts[0] -%}
            {%- assign product_number_template_part_parts = product_number_template_part | split: '${' -%}
            {%- assign product_number_string = product_number_template_part_parts | last -%}
            {%- assign product_number = product_number_string | plus: 0 -%}
            {%- if product_number_string == 'n' -%}
                {%- assign product_number = 30 -%}
            {%- endif -%}
            {%- comment -%}extract the number of product titles-end{%- endcomment -%}
            {%- capture product_titles -%}
                {% for product in collection.products limit: product_number %}{{ product.title }}{% if forloop.last != true %}, {% endif %}{% endfor %}
            {%- endcapture -%}
            {%- assign token_string_to_replace = '${' | append: product_number_string | append: '-product-titles}' -%}
            {%- assign active_seo_template = active_seo_template | replace: token_string_to_replace, product_titles -%}
        {%- endif -%}

        {%- if active_seo_template contains '-product-tags}' -%}
            {%- comment -%}extract the number of product tags-begin{%- endcomment -%}
            {%- assign seo_template_token_parts = active_seo_template | split: '-product-tags}' -%}
            {%- assign product_number_template_part = seo_template_token_parts[0] -%}
            {%- assign product_number_template_part_parts = product_number_template_part | split: '${' -%}
            {%- assign product_number_string = product_number_template_part_parts | last -%}
            {%- assign product_number = product_number_string | plus: 0 -%}
            {%- if product_number_string == 'n' -%}
                {%- assign product_number = 30 -%}
            {%- endif -%}
            {%- comment -%}extract the number of product tags-end{%- endcomment -%}
            {%- capture product_tags -%}
                {% for tag in collection.tags limit: product_number %}{{ tag }}{% if forloop.last != true %}, {% endif %}{% endfor %}
            {%- endcapture -%}
            {%- assign token_string_to_replace = '${' | append: product_number_string | append: '-product-tags}' -%}
            {%- assign active_seo_template = active_seo_template | replace: token_string_to_replace, product_tags -%}
        {%- endif -%}

        {%- if active_seo_template contains '-product-types}' -%}
            {%- comment -%}extract the number of product types-begin{%- endcomment -%}
            {%- assign seo_template_token_parts = active_seo_template | split: '-product-types}' -%}
            {%- assign product_number_template_part = seo_template_token_parts[0] -%}
            {%- assign product_number_template_part_parts = product_number_template_part | split: '${' -%}
            {%- assign product_number_string = product_number_template_part_parts | last -%}
            {%- assign product_number = product_number_string | plus: 0 -%}
            {%- if product_number_string == 'n' -%}
                {%- assign product_number = 30 -%}
            {%- endif -%}
            {%- comment -%}extract the number of product types-end{%- endcomment -%}
            {%- capture product_types -%}
                {% for type in collection.all_types limit: product_number %}{{ type }}{% if forloop.last != true %}, {% endif %}{% endfor %}
            {%- endcapture -%}
            {%- assign token_string_to_replace = '${' | append: product_number_string | append: '-product-types}' -%}
            {%- assign active_seo_template = active_seo_template | replace: token_string_to_replace, product_types -%}
        {%- endif -%}

        {%- if active_seo_template contains '-vendors}' -%}
            {%- comment -%}extract the number of collection vendors-begin{%- endcomment -%}
            {%- assign seo_template_token_parts = active_seo_template | split: '-vendors}' -%}
            {%- assign vendors_number_template_part = seo_template_token_parts[0] -%}
            {%- assign vendors_number_template_part_parts = vendors_number_template_part | split: '${' -%}
            {%- assign vendors_number_string = vendors_number_template_part_parts | last -%}
            {%- assign vendors_number = vendors_number_string | plus: 0 -%}
            {%- if vendors_number_string == 'n' -%}
                {%- assign vendors_number = 30 -%}
            {%- endif -%}
            {%- comment -%}extract the number of collection vendors-end{%- endcomment -%}
            {%- capture collection_vendors -%}
                {% for vendor in collection.all_vendors limit: vendors_number %}{{ vendor }}{% if forloop.last != true %}, {% endif %}{% endfor %}
            {%- endcapture -%}
            {%- assign token_string_to_replace = '${' | append: vendors_number_string | append: '-vendors}' -%}
            {%- assign active_seo_template = active_seo_template | replace: token_string_to_replace, collection_vendors -%}
        {%- endif -%}

        {%- if active_seo_template contains '-product-titles-with-vendor-names}' -%}
            {%- comment -%}extract the number of product titles with vendor names-begin{%- endcomment -%}
            {%- assign seo_template_token_parts = active_seo_template | split: '-product-titles-with-vendor-names}' -%}
            {%- assign titles_vendors_number_template_part = seo_template_token_parts[0] -%}
            {%- assign titles_vendors_number_template_part_parts = titles_vendors_number_template_part | split: '${' -%}
            {%- assign titles_vendors_number_string = titles_vendors_number_template_part_parts | last -%}
            {%- assign titles_vendors_number = titles_vendors_number_string | plus: 0 -%}
            {%- if titles_vendors_number_string == 'n' -%}
                {%- assign titles_vendors_number = 30 -%}
            {%- endif -%}
            {%- comment -%}extract the number of product titles with vendor names-end{%- endcomment -%}
            {%- capture product_titles_with_vendor_names -%}
                {% for product in collection.products limit: titles_vendors_number %}{{ product.vendor }} {{ product.title }}{% if forloop.last != true %}, {% endif %}{% endfor %}
            {%- endcapture -%}
            {%- assign token_string_to_replace = '${' | append: titles_vendors_number_string | append: '-product-titles-with-vendor-names}' -%}
            {%- assign active_seo_template = active_seo_template | replace: token_string_to_replace, product_titles_with_vendor_names -%}
        {%- endif -%}

        {%- assign active_seo_template = active_seo_template | replace: '${default-meta-title}', page_title -%}
        {%- assign active_seo_template = active_seo_template | replace: '${default-meta-description}', page_description -%}
        {%- assign active_seo_template = active_seo_template | replace: '${shop-name}', shop.name -%}
        {%- assign active_seo_template = active_seo_template | replace: '${shop-description}', shop.description -%}
    {%- endif -%}

    {%- assign template_value_parts = active_seo_template | split: template_value_parts_separator -%}

    {%- assign smartseo_title = template_value_parts[0] | replace: 'null', '' | newline_to_br | replace: '<br />', ' ' | strip_newlines | replace: '   ', ' ' | replace: '  ', ' ' | replace: ', , ,', ',' | replace: ',,,', ',' | replace: ', ,', ',' | replace: ',,', ',' | replace: '| | |', '|' | replace: '|||', '|' | replace: '| |', '|' | replace: '||', '|' | replace: ': : :', ':' | replace: ':::', ':' | replace: ': :', ':' | replace: '::', ':' | strip | prepend: '$#' | append: '$#' | replace: '$# ,', '' | replace: '$#,', '' | replace: ', $#', '' | replace: ',$#', '' | replace: '$# |', '' | replace: '$#|', '' | replace: '| $#', '' | replace: '|$#', '' | replace: '$# :', '' | replace: '$#:', '' | replace: ': $#', '' | replace: ':$#', '' | replace: '$#', ''| replace: '\', '\\\\'  | strip | strip_html | escape_once | escape_once -%}
    {%- assign smartseo_full_description = template_value_parts[1] | replace: 'null', '' | newline_to_br | replace: '<br />', ' ' | strip_newlines | replace: '   ', ' ' | replace: '  ', ' ' | replace: ', , ,', ',' | replace: ',,,', ',' | replace: ', ,', ',' | replace: ',,', ',' | replace: '| | |', '|' | replace: '|||', '|' | replace: '| |', '|' | replace: '||', '|' | replace: ': : :', ':' | replace: ':::', ':' | replace: ': :', ':' | replace: '::', ':' | strip | prepend: '$#' | append: '$#' | replace: '$# ,', '' | replace: '$#,', '' | replace: ', $#', '' | replace: ',$#', '' | replace: '$# |', '' | replace: '$#|', '' | replace: '| $#', '' | replace: '|$#', '' | replace: '$# :', '' | replace: '$#:', '' | replace: ': $#', '' | replace: ':$#', '' | replace: '$#', ''| replace: '\', '\\\\'  | strip | strip_html | escape_once | escape_once -%}
    {%- assign smartseo_description = smartseo_full_description | truncate: 300 -%}
    {%- if template_value_parts.size == 3 -%}
        {%- assign keywords_template_value = template_value_parts[2] | replace: 'null', '' | newline_to_br | replace: '<br />', ' ' | strip_newlines | replace: '   ', ' ' | replace: '  ', ' ' | replace: ', , ,', ',' | replace: ',,,', ',' | replace: ', ,', ',' | replace: ',,', ',' | replace: '| | |', '|' | replace: '|||', '|' | replace: '| |', '|' | replace: '||', '|' | replace: ': : :', ':' | replace: ':::', ':' | replace: ': :', ':' | replace: '::', ':' | strip | prepend: '$#' | append: '$#' | replace: '$# ,', '' | replace: '$#,', '' | replace: ', $#', '' | replace: ',$#', '' | replace: '$# |', '' | replace: '$#|', '' | replace: '| $#', '' | replace: '|$#', '' | replace: '$# :', '' | replace: '$#:', '' | replace: ': $#', '' | replace: ':$#', '' | replace: '$#', ''| replace: '\', '\\\\'  | strip | strip_html | escape_once | escape_once -%}
        {%- if keywords_template_value != '' or keywords_template_value != blank or keywords_template_value != nil -%}
            {%- assign smartseo_keywords = keywords_template_value -%}
        {%- endif -%}
    {%- endif -%}
{%- endif -%}

<title>{{ smartseo_title }}</title>
<meta name='description' content='{{ smartseo_description }}' />
<meta name='smartseo-keyword' content='{{ smartseo_keywords }}' />
<meta name='smartseo-timestamp' content='{{ active_seo_template_timestamp }}' />

{%- assign stop_template_processing = true -%}