{%- capture default_template_description %}{{ page_description }}{% endcapture -%}
{%- capture default_template_title %}{{ page_title }}{% if current_tags %}{% capture meta_tags %}{% assign current_tags_str = current_tags | join: ', ' | append: ',' %}{% assign tag_translation_namespace = 'ta' | append: language %}{% for tag_translation in shop.metafields[tag_translation_namespace] %}{% assign srcToReplace = tag_translation | first %}{% assign srcToCheckFor = tag_translation | first | append: ',' %}{% assign translated = tag_translation | last %}{% if current_tags_str contains srcToCheckFor %}{% assign current_tags_str = current_tags_str | replace: srcToReplace, translated %}{% endif %}{% endfor %}{{ current_tags_str | append: '$#' | replace: ',$#', '' | replace: '$#', '' | strip }}{% endcapture %} &ndash; {{ 'general.meta.tags' | t: tags: meta_tags }}{% endif %}{% if current_page != 1 %} &ndash; {{ 'general.meta.page' | t: page: current_page }}{% endif %}{% assign lowercase_page_title = page_title | downcase %}{% assign lowercase_shop_name = shop.name | downcase %}{% unless lowercase_page_title contains lowercase_shop_name %} &ndash; {{ shop.name }}{% endunless %}{% endcapture %}{% capture smartseo_description %}{{ default_template_description | replace: 'null', '' | newline_to_br | replace: '<br />', ' ' | strip_newlines | replace: '   ', ' ' | replace: '  ', ' ' | replace: ', , ,', ',' | replace: ',,,', ',' | replace: ', ,', ',' | replace: ',,', ',' | replace: '| | |', '|' | replace: '|||', '|' | replace: '| |', '|' | replace: '||', '|' | replace: ': : :', ':' | replace: ':::', ':' | replace: ': :', ':' | replace: '::', ':' | strip | prepend: '$#' | append: '$#' | replace: '$# ,', '' | replace: '$#,', '' | replace: ', $#', '' | replace: ',$#', '' | replace: '$# |', '' | replace: '$#|', '' | replace: '| $#', '' | replace: '|$#', '' | replace: '$# :', '' | replace: '$#:', '' | replace: ': $#', '' | replace: ':$#', '' | replace: '$#', ''| replace: '\', '\\\\' | strip | strip_html | escape_once }}{% endcapture %}{% capture smartseo_full_description %}{{ default_template_description | replace: 'null', '' | newline_to_br | replace: '<br />', ' ' | strip_newlines | replace: '   ', ' ' | replace: '  ', ' ' | replace: ', , ,', ',' | replace: ',,,', ',' | replace: ', ,', ',' | replace: ',,', ',' | replace: '| | |', '|' | replace: '|||', '|' | replace: '| |', '|' | replace: '||', '|' | replace: ': : :', ':' | replace: ':::', ':' | replace: ': :', ':' | replace: '::', ':' | strip | prepend: '$#' | append: '$#' | replace: '$# ,', '' | replace: '$#,', '' | replace: ', $#', '' | replace: ',$#', '' | replace: '$# |', '' | replace: '$#|', '' | replace: '| $#', '' | replace: '|$#', '' | replace: '$# :', '' | replace: '$#:', '' | replace: ': $#', '' | replace: ':$#', '' | replace: '$#', ''| replace: '\', '\\\\' | strip | strip_html | escape_once }}{% endcapture %}{% capture smartseo_title %}{{ default_template_title | replace: 'null', '' | newline_to_br | replace: '<br />', ' ' | strip_newlines | replace: '   ', ' ' | replace: '  ', ' ' | replace: ', , ,', ',' | replace: ',,,', ',' | replace: ', ,', ',' | replace: ',,', ',' | replace: '| | |', '|' | replace: '|||', '|' | replace: '| |', '|' | replace: '||', '|' | replace: ': : :', ':' | replace: ':::', ':' | replace: ': :', ':' | replace: '::', ':' | strip | prepend: '$#' | append: '$#' | replace: '$# ,', '' | replace: '$#,', '' | replace: ', $#', '' | replace: ',$#', '' | replace: '$# |', '' | replace: '$#|', '' | replace: '| $#', '' | replace: '|$#', '' | replace: '$# :', '' | replace: '$#:', '' | replace: ': $#', '' | replace: ':$#', '' | replace: '$#', ''| replace: '\', '\\\\' | strip | strip_html | escape_once }}{% endcapture -%}

{%- assign page_seo_metafield_namespace = 'page_seo' -%}
{%- assign page_seo_metafield_key = 'seo_tags' -%}
{%- assign metafield_parts_separator = '=||=' -%}
{%- assign template_value_parts_separator = ':||:' -%}

{%- assign smartseo_keywords = blank -%}
{%- assign active_seo_template = blank -%}
{%- assign active_seo_template_timestamp = 0 -%}

{%- assign page_seo_template_metafield = page.metafields[page_seo_metafield_namespace][page_seo_metafield_key] -%}

{%- if page_seo_template_metafield -%}
    {%- assign seo_template_metafield_parts = page_seo_template_metafield | split: metafield_parts_separator -%}
    {%- assign active_seo_template = seo_template_metafield_parts[0] -%}
    {%- assign active_seo_template_timestamp = seo_template_metafield_parts | last | times: 1 -%}
{%- endif -%}

{%- if active_seo_template != blank -%}

    {%- assign template_value_parts = active_seo_template | split: template_value_parts_separator -%}

    {%- assign smartseo_title = template_value_parts[0] | replace: 'null', '' | newline_to_br | replace: '<br />', ' ' | strip_newlines | replace: '   ', ' ' | replace: '  ', ' ' | replace: ', , ,', ',' | replace: ',,,', ',' | replace: ', ,', ',' | replace: ',,', ',' | replace: '| | |', '|' | replace: '|||', '|' | replace: '| |', '|' | replace: '||', '|' | replace: ': : :', ':' | replace: ':::', ':' | replace: ': :', ':' | replace: '::', ':' | strip | prepend: '$#' | append: '$#' | replace: '$# ,', '' | replace: '$#,', '' | replace: ', $#', '' | replace: ',$#', '' | replace: '$# |', '' | replace: '$#|', '' | replace: '| $#', '' | replace: '|$#', '' | replace: '$# :', '' | replace: '$#:', '' | replace: ': $#', '' | replace: ':$#', '' | replace: '$#', ''| replace: '\', '\\\\' | strip | strip_html | escape_once -%}
    {%- assign smartseo_full_description = template_value_parts[1] | replace: 'null', '' | newline_to_br | replace: '<br />', ' ' | strip_newlines | replace: '   ', ' ' | replace: '  ', ' ' | replace: ', , ,', ',' | replace: ',,,', ',' | replace: ', ,', ',' | replace: ',,', ',' | replace: '| | |', '|' | replace: '|||', '|' | replace: '| |', '|' | replace: '||', '|' | replace: ': : :', ':' | replace: ':::', ':' | replace: ': :', ':' | replace: '::', ':' | strip | prepend: '$#' | append: '$#' | replace: '$# ,', '' | replace: '$#,', '' | replace: ', $#', '' | replace: ',$#', '' | replace: '$# |', '' | replace: '$#|', '' | replace: '| $#', '' | replace: '|$#', '' | replace: '$# :', '' | replace: '$#:', '' | replace: ': $#', '' | replace: ':$#', '' | replace: '$#', ''| replace: '\', '\\\\' | strip | strip_html | escape_once -%}
    {%- assign smartseo_description = smartseo_full_description | truncate: 300 -%}
    {%- if template_value_parts.size == 3 -%}
        {%- assign keywords_template_value = template_value_parts[2] | replace: 'null', '' | newline_to_br | replace: '<br />', ' ' | strip_newlines | replace: '   ', ' ' | replace: '  ', ' ' | replace: ', , ,', ',' | replace: ',,,', ',' | replace: ', ,', ',' | replace: ',,', ',' | replace: '| | |', '|' | replace: '|||', '|' | replace: '| |', '|' | replace: '||', '|' | replace: ': : :', ':' | replace: ':::', ':' | replace: ': :', ':' | replace: '::', ':' | strip | prepend: '$#' | append: '$#' | replace: '$# ,', '' | replace: '$#,', '' | replace: ', $#', '' | replace: ',$#', '' | replace: '$# |', '' | replace: '$#|', '' | replace: '| $#', '' | replace: '|$#', '' | replace: '$# :', '' | replace: '$#:', '' | replace: ': $#', '' | replace: ':$#', '' | replace: '$#', ''| replace: '\', '\\\\' | strip | strip_html | escape_once -%}
        {%- if keywords_template_value != '' or keywords_template_value != blank or keywords_template_value != nil -%}
            {%- assign smartseo_keywords = keywords_template_value -%}
        {%- endif -%}
    {%- endif -%}
{%- endif -%}

<title>{{ smartseo_title }}</title>
<meta name='description' content='{{ smartseo_description }}' />
<meta name='smartseo-keyword' content='{{ smartseo_keywords }}' />
<meta name='smartseo-timestamp' content='{{ active_seo_template_timestamp }}' />

{%- assign stop_template_processing = true -%}