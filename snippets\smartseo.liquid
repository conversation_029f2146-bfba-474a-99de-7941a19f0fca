{% assign stop_processing_templates = false %}
{% assign smartseoSettingsMetafieldNamespace = 'smartseo-settings' -%}
{% assign smartseoSettingsMetafieldKey = 'json-ld' -%}
{% assign smartseoSettings = shop.metafields[smartseoSettingsMetafieldNamespace][smartseoSettingsMetafieldKey] -%}

<meta name="smart-seo-integrated" content="true" />

{%- if template contains 'page' %}
    {%- assign stop_processing_templates = true %}
    {%- assign use_default_meta_tags = true %}
{%- endif -%}

{%- if stop_processing_templates == false and template contains 'product' %}
    {%- capture product_snippet_metatags %}
        {%- include 'smartseo.product.metatags' %}
    {%- endcapture -%}

    {%- unless product_snippet_metatags contains 'Liquid error: Could not find asset snippets/smartseo.' -%}
        {% include 'smartseo.product.metatags' %}
        {%- assign stop_processing_templates = true %}
    {%- endunless -%}

    {%- if smartseoSettings.EnableStructuredData == true %}
        {%- capture product_snippet_jsonld %}
            {%- include 'smartseo.product.jsonld' %}
        {%- endcapture -%}

        {%- unless product_snippet_jsonld contains 'Liquid error: Could not find asset snippets/smartseo.' -%}
            {% include 'smartseo.product.jsonld' %}
            {%- assign stop_processing_templates = true %}
        {%- endunless %}
    {%- endif %}
{%- endif -%}

{%- if stop_processing_templates == false and template contains 'collection' and template != 'list-collections' %}
    {%- capture collection_snippet_metatags %}
        {%- include 'smartseo.collection.metatags' %}
    {%- endcapture -%}

    {%- unless collection_snippet_metatags contains 'Liquid error: Could not find asset snippets/smartseo.' -%}
        {% include 'smartseo.collection.metatags' %}
        {%- assign stop_processing_templates = true %}
    {%- endunless -%}

    {%- if smartseoSettings.EnableStructuredData == true %}
        {%- capture collection_snippet_jsonld %}
            {%- include 'smartseo.collection.jsonld' %}
        {%- endcapture -%}

        {%- unless collection_snippet_jsonld contains 'Liquid error: Could not find asset snippets/smartseo.' -%}
            {% include 'smartseo.collection.jsonld' %}
            {%- assign stop_processing_templates = true %}
        {%- endunless %}
    {%- endif %}
{%- endif -%}

{%- if stop_processing_templates == false and template contains 'blog' %}
    {%- capture blog_snippet_metatags %}
        {%- include 'smartseo.blog.metatags' %}
    {%- endcapture -%}

    {%- unless blog_snippet_metatags contains 'Liquid error: Could not find asset snippets/smartseo.' -%}
        {% include 'smartseo.blog.metatags' %}
        {%- assign stop_processing_templates = true %}
    {%- endunless -%}

    {%- if smartseoSettings.EnableStructuredData == true %}
        {%- capture blog_snippet_jsonld %}
            {%- include 'smartseo.blog.jsonld' %}
        {%- endcapture -%}

        {%- unless blog_snippet_jsonld contains 'Liquid error: Could not find asset snippets/smartseo.' -%}
            {% include 'smartseo.blog.jsonld' %}
            {%- assign stop_processing_templates = true %}
        {%- endunless %}
    {%- endif %}
{%- endif -%}

{%- if stop_processing_templates == false and template contains 'article' %}
    {%- capture article_snippet_metatags %}
        {%- include 'smartseo.article.metatags' %}
    {%- endcapture -%}

    {%- unless article_snippet_metatags contains 'Liquid error: Could not find asset snippets/smartseo.' -%}
        {% include 'smartseo.article.metatags' %}
        {%- assign stop_processing_templates = true %}
    {%- endunless -%}

    {%- if smartseoSettings.EnableStructuredData == true %}
        {%- capture article_snippet_jsonld %}
            {%- include 'smartseo.article.jsonld' %}
        {%- endcapture -%}

        {%- unless article_snippet_jsonld contains 'Liquid error: Could not find asset snippets/smartseo.' -%}
            {% include 'smartseo.article.jsonld' %}
            {%- assign stop_processing_templates = true %}
        {%- endunless %}
    {%- endif %}
{%- endif -%}

{%- if template contains 'search' or template contains 'collection' -%}

    {%- capture no_index_snippet %}
        {%- include 'smartseo.no.index' %}
    {%- endcapture -%}

    {%- unless no_index_snippet contains 'Liquid error: Could not find asset snippets/smartseo.' -%}
        {% include 'smartseo.no.index' %}
    {%- endunless %}

{%- endif -%}

{%- if stop_processing_templates == false or use_default_meta_tags == true -%}

    {%- capture defalut_page_mata_snippet %}
        {%- include 'smartseo.default.page.meta' %}
    {%- endcapture -%}

    {%- unless defalut_page_mata_snippet contains 'Liquid error: Could not find asset snippets/smartseo.' -%}
        {% include 'smartseo.default.page.meta' %}
    {%- endunless %}

{%- endif -%}

{%- if smartseoSettings.EnableStructuredData == true %}
    {%- if template contains 'product' or template contains 'collection' or template contains 'blog' or template contains 'article' -%}

        {%- capture breadcrumbs_snippet_jsonld %}
            {%- include 'smartseo.breadcrumbs.jsonld' %}
        {%- endcapture -%}

        {%- unless breadcrumbs_snippet_jsonld contains 'Liquid error: Could not find asset snippets/smartseo.' -%}
            {% include 'smartseo.breadcrumbs.jsonld' %}
        {%- endunless -%}

    {%- endif -%}

    {%- capture base_info_snippet_jsonld %}
        {%- include 'smartseo.base.info.jsonld' %}
    {%- endcapture -%}

    {%- unless base_info_snippet_jsonld contains 'Liquid error: Could not find asset snippets/smartseo.' -%}
        {% include 'smartseo.base.info.jsonld' %}
    {%- endunless %}
{%- endif %}