{%- assign smartseoSettingsMetafieldNamespace = 'smartseo-settings' -%}
{%- assign smartseoSettingsMetafieldKey = 'json-ld' -%}
{%- assign smartseoSettings = shop.metafields[smartseoSettingsMetafieldNamespace][smartseoSettingsMetafieldKey] -%}
{%- assign productReviewsPlugin = smartseoSettings.ProductReviewsPlugin -%}

{%- assign reviewAppDescription = blank -%}
{%- assign ratingValue = blank -%}
{%- assign reviewCount = blank -%}

{%- if productReviewsPlugin == "shopify-product-reviews" and product.metafields.spr.reviews != blank -%}
    {%- assign ratingValueReviewArray = product.metafields.spr.reviews | split: 'meta itemprop="ratingValue" content="' -%}
    {%- assign ratingValueArr = ratingValueReviewArray[1] |split: '"' -%}
    {%- assign reviewValue = ratingValueArr[0] -%}
    {%- assign reviewsCountStrArray = product.metafields.spr.reviews | split: 'meta itemprop="reviewCount" content="' -%}
    {%- assign reviewsCountArr = reviewsCountStrArray[1] | split: '"' -%}
    {%- assign reviewsCount = reviewsCountArr[0] -%}
    {%- unless reviewsCount == blank -%}
        {%- assign reviewAppDescription = '"Shopify Product Reviews"' -%}
        {%- assign ratingValue = reviewValue -%}
        {%- assign reviewCount = reviewsCount -%}
    {%- endunless -%}
{%- endif -%}

{%- if productReviewsPlugin == "judgeme-reviews" and product.metafields.judgeme.badge != blank %}
    {%- assign ratingValueReviewArray = product.metafields.judgeme.badge | split: "data-average-rating='" -%}
    {%- assign ratingValueArr = ratingValueReviewArray[1] |split: "'" -%}
    {%- assign reviewValue = ratingValueArr[0] -%}
    {%- assign reviewsCountStrArray = product.metafields.judgeme.badge | split: "data-number-of-reviews='" -%}
    {%- assign reviewsCountArr = reviewsCountStrArray[1] | split: "'" -%}
    {%- assign reviewsCount = reviewsCountArr[0] -%}
    {%- unless reviewsCount == '0' -%}
        {%- assign reviewAppDescription = '"Judge.me Reviews"' -%}
        {%- assign ratingValue = reviewValue -%}
        {%- assign reviewCount = reviewsCount -%}
    {%- endunless -%}
{%- endif -%}

{%- if productReviewsPlugin == "yotpo-reviews" and product.metafields.yotpo.reviews_count and product.metafields.yotpo.reviews_count != '0' and product.metafields.yotpo.reviews_count != 0 %}
        {%- assign reviewAppDescription = '"Yotpo Reviews"' -%}
        {%- assign ratingValue = product.metafields.yotpo.reviews_average -%}
        {%- assign reviewCount = product.metafields.yotpo.reviews_count -%}
{%- elsif productReviewsPlugin == "orankl-reviews" and product.metafields.orankl.review_count and product.metafields.orankl.review_count != '0' and product.metafields.orankl.review_count != 0 %}
        {%- assign reviewAppDescription = '"Orankl Reviews"' -%}
        {%- assign ratingValue = product.metafields.orankl.rating -%}
        {%- assign reviewCount = product.metafields.orankl.review_count -%}
{%- elsif productReviewsPlugin == "loox-reviews" and product.metafields.loox.num_reviews and product.metafields.loox.num_reviews != '0' and product.metafields.loox.num_reviews != 0 %}
        {%- assign reviewAppDescription = '"Loox Reviews"' -%}
        {%- assign ratingValue = product.metafields.loox.avg_rating -%}
        {%- assign reviewCount = product.metafields.loox.num_reviews -%}
{%- elsif productReviewsPlugin == "socialshopwave-reviews" and product.metafields.ssw.count_rate and product.metafields.ssw.count_rate != '0' and product.metafields.ssw.count_rate != 0 %}
        {%- assign reviewAppDescription = '"SocialShopWave Reviews"' -%}
        {%- assign ratingValue = product.metafields.ssw.avg_rate -%}
        {%- assign reviewCount = product.metafields.ssw.count_rate -%}
{%- elsif productReviewsPlugin == "trust-reviews" and product.metafields.vnreviews.reviewCount and product.metafields.vnreviews.reviewCount != '0' and product.metafields.vnreviews.reviewCount != 0 %}
        {%- assign reviewAppDescription = '"Trust Reviews"' -%}
        {%- assign ratingValue = product.metafields.vnreviews.ratingValue -%}
        {%- assign reviewCount = product.metafields.vnreviews.reviewCount -%}
{%- elsif productReviewsPlugin == "stampedio-reviews" and product.metafields.stamped.reviews_count and product.metafields.stamped.reviews_count != '0' and product.metafields.stamped.reviews_count != 0 %}
        {%- assign reviewAppDescription = '"Stamped.io Product Reviews Addon"' -%}
        {%- assign ratingValue = product.metafields.stamped.reviews_average -%}
        {%- assign reviewCount = product.metafields.stamped.reviews_count -%}
{%- elsif productReviewsPlugin == "okendo-reviews" and product.metafields.okendo.ReviewCount and product.metafields.okendo.ReviewCount != '0' and product.metafields.okendo.ReviewCount != 0 -%}
        {%- assign reviewAppDescription = '"Okendo Reviews"' -%}
        {%- assign ratingValue = product.metafields.okendo.ReviewAverageValue -%}
        {%- assign reviewCount = product.metafields.okendo.ReviewCount -%}
{%- endif -%}

{%- assign isBarCodeAvailable = false -%}
{%- assign isValidGtinLength = false -%}
{%- assign gtinString = "gtin" -%}
{%- assign daysProductPriceValidUntil = 90 | times: 86400 %}

{%- if product.selected_or_first_available_variant.barcode != blank -%}

    {%- assign isBarCodeAvailable = true -%}

    {%- assign gtinStringLength = product.selected_or_first_available_variant.barcode | size -%}

    {%- if gtinStringLength == 8 or gtinStringLength == 12 or gtinStringLength == 13 or gtinStringLength == 14 -%}
        {%- assign isValidGtinLength = true -%}
        {%- assign gtinString = gtinString | append: gtinStringLength -%}
    {%- endif -%}

{%- endif -%}

{%- capture product_json_ld -%}
<!--JSON-LD data generated by Smart SEO-->
<script type="application/ld+json">
    {
        "@context": "http://schema.org/",
        "@type": "Product",
    {%- if isBarCodeAvailable and isValidGtinLength %}
        "{{gtinString}}": "{{product.selected_or_first_available_variant.barcode}}",
        "productId": "{{product.selected_or_first_available_variant.barcode}}",
    {%- elsif isBarCodeAvailable %}
        "productId": "{{product.selected_or_first_available_variant.barcode}}",
        "mpn": "{{product.selected_or_first_available_variant.barcode}}",
    {%- endif %}
        "url": "{{ shop.url | append: '/products/' | append: product.handle }}",
        "name": "{{ smartseo_title }}",
        "image": "https:{{ product.featured_image.src | img_url: "master" }}",
        "description": "{{ smartseo_description }}",
    {%- if collection_urls != blank %}
        "category": [
            {{ collection_urls }}
        ],
    {%- endif %}
        "brand": {
            "name": "{{ product.vendor | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}"
        },
    {%- if product.selected_or_first_available_variant.sku != blank %}
        "sku": "{{product.selected_or_first_available_variant.sku}}",
    {%- endif -%}
    {%- if product.selected_or_first_available_variant.weight != blank %}
        "weight": "{{ product.selected_or_first_available_variant.weight | weight_with_unit | replace: " ", "" }}",
    {%- endif -%}
    {%- if reviewAppDescription != blank and ratingValue and reviewCount %}
        "aggregateRating": {
            "@type": "AggregateRating",
            "description": {{ reviewAppDescription }},
            "ratingValue": "{{ ratingValue }}",
            "reviewCount": "{{ reviewCount }}"
        },
    {%- endif %}
        "offers": [
        {%- for variant in product.variants %}
            {
                "@type": "Offer" ,
            {%- if isBarCodeAvailable and isValidGtinLength %}
                "{{gtinString}}": "{{variant.barcode}}",
            {%- elsif isBarCodeAvailable %}
                "mpn": "{{variant.barcode}}",
            {%- endif %}
                "priceCurrency": "{{ shop.currency }}",
                "price": "{{variant.price | divided_by: 100}}.{{variant.price | modulo: 100}}" ,
                "priceValidUntil": "{{"now" | date: "%s" | plus: daysProductPriceValidUntil | date: "%Y-%m-%d"}}",
                "availability": "http://schema.org/{%- if variant.available -%}InStock{%- else -%}OutOfStock{%- endif -%}",
                "itemCondition": "http://schema.org/NewCondition",
            {%- if variant.sku != blank %}
                "sku": "{{ variant.sku }}",
            {%- endif -%}
            {%- if variant.title != "Default Title" %}
                "name": "{{ variant.title | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}",
            {%- endif %}
                "url": "{{ shop.url | append: variant.url }}",
                "seller": {
                    "@type": "Organization",
                    "name": "{{ shop.name | strip_newlines | strip_html | escape_once | replace: '\', '\\\\' }}"
                }
            }{%- unless forloop.last -%},{%- endunless -%}
        {%- endfor %}
        ]
    }
</script>
{%- endcapture %}
{{ product_json_ld | strip_newlines | replace: '  ', '' | replace: ': ', ':' | replace: ' {', '{' }}