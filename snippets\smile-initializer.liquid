{% assign smile_channel_key = shop.metafields['smile'].channel_key %}
{% if customer %}
  {% assign smile_api_secret = shop.metafields['smile'].api_secret %}
  {% assign smile_customer_accepts_marketing = customer.accepts_marketing %}
  {% assign smile_customer_email = customer.email %}
  {% assign smile_customer_first_name = customer.first_name %}
  {% assign smile_customer_id = customer.id %}
  {% assign smile_customer_last_name = customer.last_name %}
  {% assign smile_customer_orders_count = customer.orders_count %}
  {% assign smile_customer_tags = customer.tags | join: "," %}
  {% assign smile_customer_total_spent = customer.total_spent %}
  {% assign smile_digest = smile_channel_key
    | append: smile_customer_accepts_marketing
    | append: smile_customer_email
    | append: smile_customer_first_name
    | append: smile_customer_id
    | append: smile_customer_last_name
    | append: smile_customer_orders_count
    | append: smile_customer_tags
    | append: smile_customer_total_spent
    | hmac_sha256: smile_api_secret %}
{% endif %}

<div class="smile-shopify-init"
  data-channel-key="{{ smile_channel_key }}"
{% if customer %}
  data-customer-accepts-marketing="{{ smile_customer_accepts_marketing | escape }}"
  data-customer-email="{{ smile_customer_email | escape }}"
  data-customer-first-name="{{ smile_customer_first_name | escape }}"
  data-customer-id="{{ smile_customer_id | escape }}"
  data-customer-last-name="{{ smile_customer_last_name | escape }}"
  data-customer-orders-count="{{ smile_customer_orders_count | escape }}"
  data-customer-tags="{{ smile_customer_tags | escape }}"
  data-customer-total-spent="{{ smile_customer_total_spent | escape }}"
  data-digest="{{ smile_digest }}"
{% endif %}
></div>
