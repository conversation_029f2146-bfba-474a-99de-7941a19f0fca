<div class="artist-intro">
      
      <div class="block__img">
        <p><img class="artist-avi" src="{{ section.settings.image | img_url: "380x380" }}"></p>
        {% if section.settings.signature %}
        <p><img src="{{ section.settings.signature | img_url: "120x", scale: 2 }}" style="width: 120px"></p>
        {% endif %}
      </div>
      
      <div class="block__text">
        <h1 class="Heading u-h2" style="margin-bottom: 0px; font-weight: 500">{{ section.settings.name }}</h1>
        <h2 class="Heading u-h5">{{ section.settings.title }}</h2>
        {{ section.settings.content }}
        
        {% if section.settings.insta != '' %}
        <div class="block__social">
           <a href="https://instagram.com/{{ section.settings.insta }}"><img src="{{ 'insta.svg' | asset_url }}"> @{{ section.settings.insta }}</a>
        </div>
        {% endif %}
        
      </div>
      
</div>
    
    
<div class="artist-carousel">
   <h2 class="Heading u-h2" style="font-weight: 500">Artworks</h2>

      <div class="paintings-mobile">
        <p>Swipe to see more</p>
        <img src="{{ 'right.svg' | asset_url }}">
      </div>
      <div class="paintings-wrapper">
      <div class="carousel paintings-carousel" >

        {%- for block in section.blocks -%}

        <div class="carousel-cell">
          <div class="cell-wrap">
              <img src="{{ block.settings.image | img_url: "400x"}}" height="{{ block.settings.image.height }}" width="{{ block.settings.image.width }}" style="height: auto">
              <div class="art-desc">{{ block.settings.art_info }}</div>
          </div>
        </div>

        {% endfor %}

      </div>
    </div>
</div>

