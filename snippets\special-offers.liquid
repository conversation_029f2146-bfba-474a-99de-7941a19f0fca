{% comment %}

Do NOT change this file.
Please add following code in theme.liquid almost at the end, BEFORE the line {% render 'special-offers' %}


<script data-cfasync="false" data-no-instant>
  window.saso_config = {
    product_title_max_length: 40,
    crosssell_never_show_popup: false,
    show_upsell_only_in: "product, cart",
    hide_shown_upsells_for: 15, //minutes
    hide_closed_notifications_for: 10, //minutes
    upsell_variant_choose_option: "please select",
    upsell_variant_choose_message: "Please select an option",
    translate_percent_off: '% Off'
  }
</script>

{% endcomment %}


<script data-cfasync="false" data-no-instant>window.jQuery ||
  document.write("<script src='https://ajax.googleapis.com/ajax/libs/jquery/3.5.0/jquery.min.js'>\x3C/script>")
</script>


<script type="text/javascript">
  {% assign saso_page_type = '' %}
  window.saso = {
    shop_slug: "{{shop.permanent_domain | remove: '.myshopify.com' }}",
    money_format: "{{shop.money_format | replace: '"', "'"}}",
    customer: null,
    cart: null,
  }
  window.saso_extras = {}

  {% if customer %}
  window.saso.customer = {
    id: {{customer.id}},
    email: "{{customer.email}}",
    tags: {{customer.tags | json}},
    first_name: "{{customer.first_name}}",
    last_name: "{{customer.last_name}}",
  };
  {% endif %}

  {% assign saso_template = template | replace: '.', ' ' | truncatewords: 1, "" %}
  {% if saso_template contains 'collection' %}
  {% unless collection.id == blank %}
    {% assign saso_page_type = 'collection' %}
    window.saso.collection = {
      id: {{collection.id}}
    }
  {% endunless %}
  {% endif %}

  {% if saso_template contains 'product' %}
  {% unless product.id == blank %}
    {% assign saso_page_type = 'product' %}
    window.saso.product = {
      id: {{product.id}},
      price: {{product.price}},
    };
    window.saso.product_collections = []
    {% for c in product.collections %}
    window.saso.product_collections.push({{c.id}})
    {% endfor %}

    window.saso_extras.product = {{product | json}};
  {% endunless %}
  {% endif %}

  {% if saso_template contains 'cart' %}
  {% assign saso_page_type = 'cart' %}
  {% endif %}

  {% if cart %}
  	window.saso.cart = {{cart | json}}
    delete window.saso.cart.note
    window.saso.cart_collections = {}
    {% for item in cart.items %}
    	window.saso.cart_collections["v{{item.variant_id}}"] = []
        {% for c in item.product.collections %}
        window.saso.cart_collections["v{{item.variant_id}}"].push({{c.id}})
        {% endfor %}
    {% endfor %}
	if (typeof window.saso.cart.items == "object") {
    	for (var i=0; i<window.saso.cart.items.length; i++) {
        	["sku", "grams", "vendor", "url", "image", "handle", "requires_shipping", "product_type", "product_description"].map(function(a) {
          		delete window.saso.cart.items[i][a]
        	})
    	}
    }
  {% endif %}

  window.saso.page_type = "{{saso_page_type}}"
  if (typeof window.location == "object" && typeof window.location.pathname == "string") {
    if (window.location.pathname.indexOf('/checkouts/') > -1) {
      window.saso.page_type = "checkout"
    }
  }
</script>

{% if saso_page_type != '' %}
<style>
/* Magnific Popup CSS */
.mfp-bg{top:0;left:0;width:100%;height:100%;z-index:1042;overflow:hidden;position:fixed;background:#0b0b0b;opacity:.4;filter:alpha(opacity=40)}.mfp-wrap{top:0;left:0;width:100%;height:100%;z-index:1043;position:fixed;outline:0!important;-webkit-backface-visibility:hidden}.mfp-container{text-align:center;position:absolute;width:100%;height:100%;left:0;top:0;padding:0 8px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}.mfp-container:before{content:'';display:inline-block;height:100%;vertical-align:middle}.mfp-align-top .mfp-container:before{display:none}.mfp-content{position:relative;display:inline-block;vertical-align:middle;margin:0 auto;text-align:left;z-index:1045}
  .mfp-ajax-holder .mfp-content,.mfp-inline-holder .mfp-content{width:100%;cursor:auto}.mfp-ajax-cur{cursor:progress}.mfp-zoom-out-cur,.mfp-zoom-out-cur .mfp-image-holder .mfp-close{cursor:-moz-zoom-out;cursor:-webkit-zoom-out;cursor:zoom-out}.mfp-zoom{cursor:pointer;cursor:-webkit-zoom-in;cursor:-moz-zoom-in;cursor:zoom-in}.mfp-auto-cursor .mfp-content{cursor:auto}.mfp-arrow,.mfp-close,.mfp-counter,.mfp-preloader{-webkit-user-select:none;-moz-user-select:none;user-select:none}.mfp-loading.mfp-figure{display:none}.mfp-hide{display:none!important}.mfp-preloader{color:#CCC;position:absolute;top:50%;width:auto;text-align:center;margin-top:-.8em;left:8px;right:8px;z-index:1044}.mfp-preloader a{color:#CCC}.mfp-preloader a:hover{color:#FFF}.mfp-s-error .mfp-content,.mfp-s-ready .mfp-preloader{display:none}button.mfp-arrow,button.mfp-close{overflow:visible;cursor:pointer;background:0 0;border:0;-webkit-appearance:none;display:block;outline:0;padding:0;z-index:1046;-webkit-box-shadow:none;box-shadow:none}button::-moz-focus-inner{padding:0;border:0}.mfp-close{width:44px;height:44px;line-height:44px;position:absolute;right:0;top:0;text-decoration:none;text-align:center;opacity:.65;filter:alpha(opacity=65);padding:0 0 18px 10px;color:#FFF;font-style:normal;font-size:28px;font-family:Arial,Baskerville,monospace}.mfp-close:focus,.mfp-close:hover{opacity:1;filter:alpha(opacity=100)}.mfp-close:active{top:1px}.mfp-close-btn-in .mfp-close{color:#333}.mfp-iframe-holder .mfp-close,.mfp-image-holder .mfp-close{color:#FFF;right:-6px;text-align:right;padding-right:6px;width:100%}.mfp-counter{position:absolute;top:0;right:0;color:#CCC;font-size:12px;line-height:18px;white-space:nowrap}.mfp-arrow{position:absolute;opacity:.65;filter:alpha(opacity=65);margin:-55px 0 0;top:50%;padding:0;width:90px;height:110px;-webkit-tap-highlight-color:transparent}.mfp-arrow:active{margin-top:-54px}.mfp-arrow:focus,.mfp-arrow:hover{opacity:1;filter:alpha(opacity=100)}.mfp-arrow .mfp-a,.mfp-arrow .mfp-b,.mfp-arrow:after,.mfp-arrow:before{content:'';display:block;width:0;height:0;position:absolute;left:0;top:0;margin-top:35px;margin-left:35px;border:inset transparent}.mfp-arrow .mfp-a,.mfp-arrow:after{border-top-width:13px;border-bottom-width:13px;top:8px}.mfp-arrow .mfp-b,.mfp-arrow:before{border-top-width:21px;border-bottom-width:21px;opacity:.7}.mfp-arrow-left{left:0}.mfp-arrow-left .mfp-a,.mfp-arrow-left:after{border-right:17px solid #FFF;margin-left:31px}.mfp-arrow-left .mfp-b,.mfp-arrow-left:before{margin-left:25px;border-right:27px solid #3F3F3F}.mfp-arrow-right{right:0}.mfp-arrow-right .mfp-a,.mfp-arrow-right:after{border-left:17px solid #FFF;margin-left:39px}.mfp-arrow-right .mfp-b,.mfp-arrow-right:before{border-left:27px solid #3F3F3F}.mfp-iframe-holder{padding-top:40px;padding-bottom:40px}
  .mfp-iframe-holder .mfp-content{line-height:0;width:100%;max-width:900px}.mfp-iframe-holder .mfp-close{top:-40px}.mfp-iframe-scaler{width:100%;height:0;overflow:hidden;padding-top:56.25%}.mfp-iframe-scaler iframe{position:absolute;display:block;top:0;left:0;width:100%;height:100%;box-shadow:0 0 8px rgba(0,0,0,.6);background:#000}img.mfp-img{width:auto;max-width:100%;height:auto;display:block;line-height:0;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;padding:40px 0;margin:0 auto}.mfp-figure{line-height:0}.mfp-figure:after{content:'';position:absolute;left:0;top:40px;bottom:40px;display:block;right:0;width:auto;height:auto;z-index:-1;box-shadow:0 0 8px rgba(0,0,0,.6);background:#444}.mfp-figure small{color:#BDBDBD;display:block;font-size:12px;line-height:14px}.mfp-figure figure{margin:0}.mfp-bottom-bar{margin-top:-36px;position:absolute;top:100%;left:0;width:100%;cursor:auto}.mfp-title{text-align:left;line-height:18px;color:#F3F3F3;word-wrap:break-word;padding-right:36px}.mfp-image-holder .mfp-content{max-width:100%}.mfp-gallery .mfp-image-holder .mfp-figure{cursor:pointer}@media screen and (max-width:800px) and (orientation:landscape),screen and (max-height:300px){.mfp-img-mobile .mfp-image-holder{padding-left:0;padding-right:0}.mfp-img-mobile img.mfp-img{padding:0}.mfp-img-mobile .mfp-figure:after{top:0;bottom:0}.mfp-img-mobile .mfp-figure small{display:inline;margin-left:5px}.mfp-img-mobile .mfp-bottom-bar{background:rgba(0,0,0,.6);bottom:0;margin:0;top:auto;padding:3px 5px;position:fixed;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}.mfp-img-mobile .mfp-bottom-bar:empty{padding:0}.mfp-img-mobile .mfp-counter{right:5px;top:3px}.mfp-img-mobile .mfp-close{top:0;right:0;width:35px;height:35px;line-height:35px;background:rgba(0,0,0,.6);position:fixed;text-align:center;padding:0}}@media all and (max-width:900px){.mfp-arrow{-webkit-transform:scale(.75);transform:scale(.75)}.mfp-arrow-left{-webkit-transform-origin:0;transform-origin:0}.mfp-arrow-right{-webkit-transform-origin:100%;transform-origin:100%}.mfp-container{padding-left:6px;padding-right:6px}}.mfp-ie7 .mfp-img{padding:0}.mfp-ie7 .mfp-bottom-bar{width:600px;left:50%;margin-left:-300px;margin-top:5px;padding-bottom:5px}.mfp-ie7 .mfp-container{padding:0}.mfp-ie7 .mfp-content{padding-top:44px}.mfp-ie7 .mfp-close{top:0;right:0;padding-top:0}

.mfp-wrap {
  z-index: 2147483645;
}
.mfp-container {
	background-color: rgba(0, 0, 0, 0.1) !important;
}

</style>

{{ 'special-offers.scss.css' | asset_url | stylesheet_tag }}

{% render 'special-offers-templates' %}
{% endif %}


{% if template != 'cart' %}
        <script data-cfasync="false">
          jQuery(function(){
            jQuery(document).on('click', "input[name='checkout']:not(.saso-ignore), input[value='Checkout']:not(.saso-ignore), button[name='checkout']:not(.saso-ignore), [href$='checkout']:not(.saso-ignore), button[value='Checkout']:not(.saso-ignore), input[name='goto_pp'], button[name='goto_pp'], input[name='goto_gc'], button[name='goto_gc']", function(e){
              e.preventDefault();

              if (typeof sasoCheckout != "function") {
              	window.location = "/checkout";
              }

              jQuery.ajax({
                          cache: false,
                          contentType: "application/json; charset=utf-8",
                          dataType: "json",
                          type: "GET",
                          url: '/cart.js',
                          success: function(res) {
                            window.saso.cart = res
                            sasoCheckout()
                          }
                        })

            });
          });
        </script>
{% endif %}
