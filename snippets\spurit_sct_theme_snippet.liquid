<script>
  if(typeof(Spurit) === 'undefined'){
    var Spurit = {};
  }
  if(!Spurit.CountdownTimer){
    Spurit.CountdownTimer = {};
  }
  if(!Spurit.CountdownTimer.snippet){
    Spurit.CountdownTimer.snippet = {};
  }
  Spurit.CountdownTimer.snippet.shopHash = 'a52905e4707b975c8a9f820eca84ac9e';
  Spurit.CountdownTimer.snippet.signature = '569ee8c4704ad8334ed014beec94e84d';
  Spurit.CountdownTimer.snippet.userId = '211150';
  Spurit.CountdownTimer.snippet.cacheTimestamp = 1703570147;

  {% assign page = template | split: '.' | first %}
  {% if page == 'product' and product and product.id %}
    Spurit.CountdownTimer.snippet.productId = {{ product.id }};

    var variantStock = {};
    {% for variant in product.variants %}
        variantStock[{{- variant.id -}}] = {
          id: {{ variant.id }},
          inventory_quantity: {{ variant.inventory_quantity }},
        };
    {% endfor %}
    Spurit.CountdownTimer.snippet.variants = Object.values(variantStock);

  {% endif %}
  {% if page == 'collection' and collection and collection.id %}
  Spurit.CountdownTimer.snippet.collectionId = {{ collection.id }};
  {% endif %}
</script>

    <script src="https://amaicdn.com/timer-app/common.js"></script>
    <link href="https://amaicdn.com/timer-app/common.css" rel="stylesheet" type="text/css" media="all">
