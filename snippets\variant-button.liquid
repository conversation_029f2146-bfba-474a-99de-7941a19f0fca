{%- assign swatch_file_extension = 'png' -%}
{%- assign is_color = false -%}
{%- assign color_swatch_drop = option_drop -%}
{%- assign color_option_index = 0 -%}

<div class="variant-wrapper variant-wrapper--button js">
  
  <label class="variant__label" for="ProductSelect-{{ section_id }}-option-{{ forloop.index0 }}">
    Size
  </label>

  {% assign option_index = forloop.index %}
  <fieldset class="variant-input-wrap"
    name="{{ option.name }}"
    data-index="option{{ option_index }}"
    id="ProductSelect-{{ section_id }}-option-{{ forloop.index0 }}">
    
    
    
    
    {% for value in option.values %}
    {% unless value contains '% off)' %}
    
      {%- assign product_available = true -%}
    
      {% if product.options.size == 1 %}
        {%- assign product_available = product.variants[forloop.index0].available -%}
      {% endif %}
    
      <div
        class="variant-input"
        data-index="option{{ option_index }}"
        data-value="{{ value | escape }}">
        
        <input type="radio"
          {% if option.selected_value == value %} checked="checked"{% endif %}
          value="{{ value | escape }}"
          data-index="option{{ option_index }}"
          name="{{ option.name }}"
          class="variant__input-{{ section_id }}{% unless product_available %} disabled{% endunless %}"
          
          id="ProductSelect-{{ section_id }}-option-{{ option.name | handleize }}-{{ value | url_encode }}">
       
          <label for="ProductSelect-{{ section_id }}-option-{{ option.name | handleize }}-{{ value | url_encode }}"{% unless product_available %} class="disabled"{% endunless %}>{{ value | escape }}</label>
       
      </div>
    {%endunless%}
    {% endfor %}
    
    
    
    
    
    
  </fieldset>
</div>

{% comment %}
<hr class="hr--medium">
{% endcomment %}