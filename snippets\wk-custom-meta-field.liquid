{% assign wk_metafields = product.metafields.wk_custom_field %}
{% unless wk_metafields == empty %}
    <h1>Extra Information</h1>
	<table class="responsive-table" style="border:0px">
        <tbody >
            {% for wk_metafield in wk_metafields %}
                {% assign wk_temp_mf = wk_metafield | split : '", "' %}
                {% assign key = wk_temp_mf[0] | size | minus: 2 %} 
                {% assign value = wk_temp_mf[1] | size | minus:2 %}
          		{% assign wk_check_mf = wk_temp_mf[1] | slice: 0,value | split : ':' %}
                <tr>
                    <td style="border:none">{{ wk_temp_mf[0] | slice: 2,key | upcase}}</td>
                  	{% if wk_check_mf[0] == 'https' %}
                  		<td style="border:none" ><a>{{ wk_temp_mf[1] | slice: 0,value }}</a></td>
                  	{% else if%}
                    	<td style="border:none">{{ wk_temp_mf[1] | slice: 0,value }}</td>
                  	{% endif %}
                </tr>
            {% endfor %}
        </tbody>
	</table>
{% endunless %}