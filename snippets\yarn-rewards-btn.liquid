<style>
.yarn-rewards-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #106572; 
    padding: 15px;
    border-radius: 12px;
}

.yarn-rewards-content {
    color: #fff;
}

.yarn-rewards-content h2 {
    font-weight: 800;
    font-size: 1.5rem;
    line-height: normal;
}

.yarn-rewards-button {
    background-color: #fbd84a; 
    color: #222;
    font-weight: 800;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 0.1rem;
    padding: 10px 20px;
    border-radius: 12px;
}

.yarn-rewards-button:hover {
    background: linear-gradient(90deg,#fbd84a,#fbec9f)!important;
    color: #222; 
    -webkit-transition: color .45s cubic-bezier(.785,.135,.15,.86),border .45s cubic-bezier(.785,.135,.15,.86);
    transition: color .45s cubic-bezier(.785,.135,.15,.86),border .45s cubic-bezier(.785,.135,.15,.86);
}

.yarn-rewards-button:not([disabled]):hover {
    background-color: transparent;
}
</style>

<div class="yarn-rewards-container">
  {% if customer %}
    <div class="yarn-rewards-content">
      <h2>My Rewards</h2>
      <div class="yotpo-widget-instance" data-yotpo-instance-id="852054" data-yotpo-product-id="{{product.id}}" data-yotpo-variant-id="{{ product.selected_or_first_available_variant.id }}" data-yotpo-product-variants="{{ product.variants | json | url_encode }}" data-yotpo-product-collections="{{product.collections | json | url_encode}}" data-yotpo-product-tags="{{ product.tags | json | url_encode }}" data-yotpo-product-type="{{ product.type | json | url_encode }}"></div>
    </div>
    <a href="/pages/rewards" class="yarn-rewards-button">Account</a>
  {% else %}
    <div class="yarn-rewards-content">
      <h2>Join Yarn Rewards</h2>
      <div class="yotpo-widget-instance" data-yotpo-instance-id="852054" data-yotpo-product-id="{{product.id}}" data-yotpo-variant-id="{{ product.selected_or_first_available_variant.id }}" data-yotpo-product-variants="{{ product.variants | json | url_encode }}" data-yotpo-product-collections="{{product.collections | json | url_encode}}" data-yotpo-product-tags="{{ product.tags | json | url_encode }}" data-yotpo-product-type="{{ product.type | json | url_encode }}"></div>
    </div>
    <a href="/pages/rewards" class="yarn-rewards-button">Login</a>
  {% endif %}
</div>
