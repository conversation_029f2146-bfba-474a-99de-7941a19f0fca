<style>
.youpay-callout-container {
    margin-top: 10px;
}

.youpay-callout-content .youpay-callout-message-after {
    font-size: 12px;
}

.youpay-container.youpay-button-group[data-theme="vanilla"][data-id="code"] .youpay-button {
  font-size: 12px;
  letter-spacing: 0.2em;
}

.youpay-container.youpay-button-group[data-theme="vanilla"][data-type="cart"][data-id="code"] {
    padding-top: 10px;
    clear: both;
}

.youpay-text-static-desktop {
  display: none;
}

@media (min-width: 641px) {
  .youpay-container.youpay-button-group[data-theme="vanilla"][data-type="cart"][data-id="code"] {
    width: 224px;
  }

  .youpay-text-static-desktop {
    display: block;
  }
  
  .youpay-text-static-mobile {
    display: none;
  }
}

@media (max-width: 850px) {
    .youpay-enabled .Cart--expanded .Cart__NoteContainer {
        width: 50%;
    }
}

@media (max-width: 640px) {
    .youpay-enabled .Cart--expanded .Cart__NoteContainer {
        width: 100%;
    }
}
</style>