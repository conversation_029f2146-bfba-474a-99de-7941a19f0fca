{% assign configuration = item.properties._configuration | default: 0 %}
{% if configuration != 0 %}
	<a href="/apps/zakeke/c/{{ item.product.handle }}?variant={{ item.variant.id }}&composition={{ configuration }}" class="btn btn--small btn--secondary cart__remove">Edit</a>
{% endif %}

{% assign customization = item.properties.customization | default: 0 %}
{% if customization != 0 %}
	<a href="/apps/zakeke?pid={{ item.product_id }}&design={{ customization }}&locale={{ request.locale.iso_code }}" class="btn btn--small btn--secondary cart__remove">Edit</a>
{% endif %}