{% comment %}
	GEMPAGE BUILDER (https://apps.shopify.com/gempage)

	You SHOULD NOT modify source code in this page because
	It is automatically generated from GEMPAGE BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->


<link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.6.3/css/font-awesome.min.css" class="gf-style">
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-article-556764790918.css' | asset_url }}" class="gf_page_style">
<!--GEM_HEADER_END-->
{%- assign share_url = shop.url | append: article.url -%}
{%- assign twitter_text = article.title -%}
{%- assign pinterest_description = article.description | strip_html | truncatewords: 15 | url_param_escape -%}
{%- assign pinterest_image = article.image | img_url: '750x' | prepend: 'https:' -%}<article class="Article" data-section-id="{{ section.id }}" data-section-type="article">
  <aside class="ArticleToolbar hidden-phone">
    <div class="ArticleToolbar__Left">
      <span class="Heading Text--subdued u-h8 hidden-tablet">{{ 'blog.article.now_reading' | t }}</span>
      <span class="ArticleToolbar__ArticleTitle Heading u-h7">{{ article.title }}</span>
    </div>    <div class="ArticleToolbar__Right">
      {%- if section.settings.show_share_buttons -%}
        <div class="ArticleToolbar__ShareList">
          <span class="ArticleToolbar__ShareLabel Heading Text--subdued u-h8">{{ 'blog.article.share' | t }}</span>          <div class="HorizontalList">
            <a class="HorizontalList__Item Text--subdued Link" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'facebook' -%}</a>
            <a class="HorizontalList__Item Text--subdued Link" href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'twitter' -%}</a>
            <a class="HorizontalList__Item Text--subdued Link" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener">{%- include 'icon' with 'pinterest' -%}</a>
          </div>
        </div>
      {%- endif -%}      {%- if blog.next_article or blog.previous_article -%}
        <div class="ArticleToolbar__Nav">
          {%- if blog.next_article -%}
            <a href="{{ blog.next_article }}" class="ArticleToolbar__NavItem ArticleToolbar__NavItem--next Heading Text--subdued Link u-h8">{% include 'icon' with 'select-arrow-left' %} {{ 'blog.article.previous' | t }}</a>
          {%- endif -%}          {%- if blog.previous_article and blog.next_article -%}
            <span class="ArticleToolbar__NavItemSeparator"></span>
          {%- endif -%}          {%- if blog.previous_article -%}
            <a href="{{ blog.previous_article }}" class="ArticleToolbar__NavItem ArticleToolbar__NavItem--prev Heading Text--subdued Link u-h8">{{ 'blog.article.next' | t }} {% include 'icon' with 'select-arrow-right' %}</a>
          {%- endif -%}
        </div>
      {%- endif -%}
    </div>
  </aside>  {%- if article.image and section.settings.show_article_image -%}
    <div class="Article__ImageWrapper" style="background-image: url({{ article.image | img_url: '1x1' }})">
      <div class="Article__Image Image--lazyLoad Image--fadeIn"
           data-optimumx="1.4"
           data-bgset="{{ article.image | img_url: '400x' }} 400w, {{ article.image | img_url: '600x' }} 600w, {{ article.image | img_url: '800x' }} 800w, {{ article.image | img_url: '1200x' }} 1200w, {{ article.image | img_url: '1400x' }} 1400w, {{ article.image | img_url: '1600x' }} 1600w">
      </div>
    </div>
  {%- endif -%}  <div class="Article__Wrapper">
    <div class="Article__Content">
      <header class="Article__Header">
        <p class="article-back"><button onclick="window.history.back()">< Go Back</button></p>
        {%- capture article_meta -%}
          {%- if section.settings.show_date -%}
            <span class="Article__MetaItem">{{ article.published_at | date: format: 'month_day_year' }}</span>
          {%- endif -%}          {%- if section.settings.show_category and article.tags != empty -%}
            <span class="Article__MetaItem">{{ article.tags.first }}</span>
          {%- endif -%}
        {%- endcapture -%}        {%- if article_meta != blank -%}
          <div class="Article__Meta Heading Text--subdued u-h6">
            {{- article_meta -}}
          </div>
        {%- endif -%}        <h1 class="Article__Title Heading u-h1">{{ article.title }}</h1>
      </header>      <div class="Article__Body Rte">
        <!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor">
<div class="gryffeditor">
  <div data-label="Text Block" class="element-wrap" data-description data-key="text-block"> <div class="elm text-edit"><p><span style="font-weight: 400;">Aboriginal and Torres Strait Islander art has recently captured the hearts of the world and here at Yarn we are so lucky to be able to collaborate with some truly incredible First Nations artists. In this post we would like to introduce you to 10 upcoming and established artists you can support and follow! Each of these artists showcase their deep connection to their community, the land around us and their Indigenous culture. </span></p>
<p> </p>
<p>1. Caitlyn Davies-Plummer </p>
<p><span style="font-weight: 400;">Caitlyn Davies is a Barkindji woman behind the incredible Dustin-Koa Art, creating stunning contemporary Aboriginal Australian artwork. Most of her artwork features vibrant pinks and soft purples that really pop on the canvas. Her artwork showcases her heritage and skill as an Indigenous Australian artist. You can follow her on Instagram to see more of her fantastic work. @dustinkoaart on Instagram</span></p>
<p> <img src="https://cdn.shopify.com/s/files/1/0247/4021/files/Capture_480x480.jpg?v=1659393295" alt=""></p>
<p>2. Alkina Edwards </p>
<p><span style="font-weight: 400;">Alkina Edwards is a fantastic artist based on Yorta Yorta country in Echuca in Victoria. She uses soft pinks and browns with vibrant pops of native plants and flowers for pieces that really feel alive. Alkina uses both digital and traditional mediums for her work. You can check out her Instagram to see more of her wonderful art. @alkinascreations on Instagram<img src="https://cdn.shopify.com/s/files/1/0247/4021/files/CaptureA2.jpg?v=1660171632" alt=""></span></p>
<p> <img src="https://cdn.shopify.com/s/files/1/0247/4021/files/CaptureA.jpg?v=1660171565" alt=""></p>
<p>3. Bayley Mifsud</p>
<p><span style="font-weight: 400;">Bayley Mifsud is a contemporary Aboriginal artist and proud Kirrae and Peek Wurrong woman of the Gundjitmara nation. Her artist name is Merindah-Gunya which means “Beautiful Spirit” in Peek Wurrong language. Her art features bright oranges and pastel colours in a fun and vibrant style that really showcases her connection to Country and culture. @merindahgunya on Instagram</span></p>
<p><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/CaptureM2.jpg?v=1660171683" alt=""><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/CaptureM.jpg?v=1660171662" alt=""></p>
<p>4. Rebekah Lane</p>
<p><span style="font-weight: 400;">Rebekah Lane is a Dunghutti Biripi woman creating stunning Indigenous artwork. She uses both earth tones and blacks as well as bright pinks and greens in different pieces for a varied and wonderful portfolio that shows her connection to her heritage. You can check out her Instagram to see more of her fantastic work. @ginyaangart on Instagram</span></p>
<p><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/CaptureL2.jpg?v=1660173174" alt=""><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/CaptureL.jpg?v=1660173155" alt=""></p>
<p>5. Marlie Albert</p>
<p><span style="font-weight: 400;">Marlie Albert is a proud Baard Banoil woman from Broome Western Australia who creates wonderful Indigneous art. The earth tones and vibrant blues of her pieces show charm and character. A lot of her pieces feature Australian wildlife, from turtles to crocodiles, bringing her artwork to life. @marliealbert.artwork on Instagram</span></p>
<p><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/CaptureB2.jpg?v=1660172617" alt=""><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/CaptureB.jpg?v=1660172596" alt=""></p>
<p>6. Sheri Skele</p>
<p><span style="font-weight: 400;">Sheri Skele is a proud Bidjara woman and contemporary Aboriginal artist who creates vibrant paintings inspired by her culture and her own life experiences. Her artwork features vibrant pastel colours with earth tones. She calls her artwork ‘bigi nagala’, which means ‘I am dreaming’ in the Bidjara language. Her artwork shows her deep connection to her heritage and to Country. </span><a href="https://www.yarn.com.au/pages/artist-sheri-skele"><span style="font-weight: 400;">https://www.yarn.com.au/pages/artist-sheri-skele</span></a><span style="font-weight: 400;"> </span></p>
<p> <img src="https://cdn.shopify.com/s/files/1/0247/4021/files/Sheri-7_480x480.jpg?v=1659334339" alt=""></p>
<p><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/Sheri-6_480x480.jpg?v=1659334357" alt=""></p>
<p> </p>
<p>7. Robert Levi </p>
<p><span style="font-weight: 400;">Robert Levi is a Torres Strait Islander Kauraraig man who creates bold and authentic Indigenous art. His art is a way for him to pass on stories and keep the culture alive. He is passionate about inspiring the younger generations and advocating for authentic Indigenous art. His artwork features vibrant colours and bold figures as well as Australian wildlife. You can check out his Instagram to see more of his work. </span><a href="https://www.yarn.com.au/pages/artist-robert-levi"><span style="font-weight: 400;">https://www.yarn.com.au/pages/artist-robert-levi</span></a><span style="font-weight: 400;"> </span></p>
<p><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/Hammerhead_School_7957047d-fe8c-4613-97e1-767b3cabb9f2_480x480.png?v=1660175479" alt=""></p>
<p> <img src="https://cdn.shopify.com/s/files/1/0247/4021/files/Sea-Lifestyle-Low-Res_1_480x480.jpg?v=1659337273" alt=""></p>
<p>8. Luke Mallie</p>
<p><span style="font-weight: 400;">Luke Mallie is a contemporary Indigenous artist from Brisbane. He is a proud Aboriginal and Torres Strait Islander from Kuku Yalanji and Kubin Village country. His artwork is inspired by his family, culture and the tropical landscape around him and the designs are heavily influenced by pop culture, animation and modern design. He uses his artwork as a way to help others to see the beauty of Aboriginal Australian culture and artwork and to raise awareness of Indigenous issues. His artwork features bright vibrant colours and beautiful Australian wildlife. </span><a href="https://www.yarn.com.au/pages/artist-luke-mallie"><span style="font-weight: 400;">https://www.yarn.com.au/pages/artist-luke-mallie</span></a><span style="font-weight: 400;"> </span></p>
<p><span style="font-weight: 400;"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/CaptureLuke.jpg?v=1660173558" alt=""></span></p>
<p> <img src="https://cdn.shopify.com/s/files/1/0247/4021/files/Four_Elements_NoSymbols_80dpi_FINAL_LOW_RES_480x480.jpg?v=1659337384" alt=""></p>
<p>9. Charlie Chambers Jnr.</p>
<p><span style="font-weight: 400;">Charlie Chambers Jnr is an Australian Aboriginal artist belonging to the Jarowair tribe. His artwork is a way to tell the stories his Elders shared when he was young, keeping these stories about the community alive and sharing them with others. His artwork features strong earth tones as well as bright blues and greens. He uses very traditional styles to depict Australian animals and stories. </span><a href="https://www.yarn.com.au/pages/artist-charlie-chambers-jnr"><span style="font-weight: 400;">https://www.yarn.com.au/pages/artist-charlie-chambers-jnr</span></a><span style="font-weight: 400;"> </span></p>
<p> <img src="https://cdn.shopify.com/s/files/1/0247/4021/files/Healing-Land_-Rivers_-Sea-and-Ocean-low_res_1_1.jpg?v=1660172304" alt=""></p>
<p>10. Shara Delaney</p>
<p><span style="font-weight: 400;">Shara Delaney is a contemporary Australian Aboriginal artist from Quandamooka country. Her artwork is inspired by stories from her Elders and represents her identity as a strong saltwater woman with a connection to family, sand and sea. Her artwork features soft pastels as well as vibrant pinks and beautiful depictions of sea animals. </span><a href="https://www.yarn.com.au/pages/artist-shara-delaney"><span style="font-weight: 400;">https://www.yarn.com.au/pages/artist-shara-delaney</span></a><span style="font-weight: 400;"> </span></p>
<p><span style="font-weight: 400;"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/CaptureS.jpg?v=1660173582" alt=""></span></p>
<img src="https://cdn.shopify.com/s/files/1/0247/4021/files/Jara_Yaganya_FRAME_dc50ea36-4f03-4f28-a0ea-8bea1c7dcbbd_480x480.png?v=1660172330" alt=""><br>
<p><span style="font-weight: 400;">If you would like to see more of these artists' incredible work and support them, then you can check out their Instagram or keep an eye out on Yarn to see some of these artists' existing collabs with us and some exciting upcoming projects.</span></p></div></div>
</div><div id="divContentBk"></div></div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->
      </div>      {%- capture article_footer -%}
        {%- if section.settings.show_author -%}
          <span class="Article__Author Heading Text--subdued u-h6">{{ 'blog.article.written_by' | t: author: article.author }}</span>
        {%- endif -%}        {%- if section.settings.show_share_buttons -%}
          <div class="Article__ShareButtons ShareButtons">
            <a class="ShareButtons__Item ShareButtons__Item--facebook" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'facebook' -%}</a>
            <a class="ShareButtons__Item ShareButtons__Item--twitter" href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'twitter' -%}</a>
            <a class="ShareButtons__Item ShareButtons__Item--pinterest" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener">{%- include 'icon' with 'pinterest' -%}</a>
          </div>
        {%- endif -%}
      {%- endcapture -%}      {%- if article_footer != blank -%}
        <footer class="Article__Footer">
          {{ article_footer }}
        </footer>
      {%- endif -%}
    </div>    {%- if blog.comments_enabled? -%}
      {%- if article.comments_count > 0 -%}
        <div class="Article__Comments">
          <span class="Anchor" id="comments"></span>          <h2 class="Heading u-h1">{{ 'blog.article.comments_count' | t: count: article.comments_count }}</h2>          <div class="Article__CommentList">
            {%- paginate article.comments by 25 -%}
              {%- for comment in article.comments -%}
                <div class="ArticleComment">
                  <div class="ArticleComment__Body Rte">
                    {{ comment.content }}
                  </div>                  <div class="ArticleComment__Meta Heading Text--subdued u-h8">
                    <span class="ArticleComment__Author">{{ comment.author }}</span>
                    <span class="ArticleComment__Date">{{ comment.created_at | date: format: 'month_day_year' }}</span>
                  </div>
                </div>
              {%- endfor -%}              {% include 'pagination', hash: '#comments' %}
            {% assign dm_paginate_by = paginate.page_size %}{%- endpaginate -%}
          </div>
        </div>
      {%- endif -%}      <div class="Article__CommentFormWrapper">
        {% if article.comments_count == 0 %}
          <span class="Anchor" id="comments"></span>
        {%- endif -%}        <span class="Anchor" id="comment_form"></span>        <h2 class="Heading u-h1">{{ 'blog.comments.form_title' | t }}</h2>        {%- form 'new_comment', article, class: 'Article__CommentForm Form', id: '' -%}
          {%- if form.posted_successfully? -%}
            <p class="Form__Alert Alert Alert--success">
              {%- if blog.moderated? -%}
                {{- 'blog.comments.success_moderated' | t -}}
              {%- else -%}
                {{- 'blog.comments.success' | t -}}
              {%- endif -%}
            </p>
          {%- endif -%}          {%- if form.errors -%}
            <div class="Form__Alert Alert Alert--error">
              <ul class="Alert__ErrorList">
                {%- for field in form.errors -%}
                  {%- if field == 'form' -%}
                    <li class="Alert__ErrorItem">{{ form.errors.messages[field] }}</li>
                  {%- else -%}
                    <li class="Alert__ErrorItem"><strong>{{ form.errors.translated_fields[field] }}</strong> {{ form.errors.messages[field] }}</li>
                  {%- endif -%}
                {%- endfor -%}
              </ul>
            </div>
          {%- endif -%}          <div class="Form__Group">
            <div class="Form__Item">
              <input type="text" class="Form__Input" name="comment[author]" placeholder="{{ 'blog.comments.name_placeholder' | t }}" aria-label="{{ 'blog.comments.name_placeholder' | t }}" value="{{ form.author | escape | default: customer.name }}" required="required">
              <label class="Form__FloatingLabel">{{ 'blog.comments.name_placeholder' | t }}</label>
            </div>            <div class="Form__Item">
              <input type="email" class="Form__Input" name="comment[email]" placeholder="{{ 'blog.comments.email_placeholder' | t }}" aria-label="{{ 'blog.comments.email_placeholder' | t }}" value="{{ form.email | escape | default: customer.email }}" required="required">
              <label class="Form__FloatingLabel">{{ 'blog.comments.email_placeholder' | t }}</label>
            </div>
          </div>          <div class="Form__Item">
            <textarea name="comment[body]" rows="6" class="Form__Textarea" placeholder="{{ 'blog.comments.comment_placeholder' | t }}" aria-label="{{ 'blog.comments.comment_placeholder' | t }}" required="required">
              {{- form.body -}}
            </textarea>            <label class="Form__FloatingLabel">{{ 'blog.comments.comment_placeholder' | t }}</label>
          </div>          {%- if blog.moderated? -%}
            <p class="Form__Hint">{{ 'blog.comments.approval_notice' | t }}</p>
          {%- endif -%}          <button type="submit" class="Form__Submit Button Button--primary">{{ 'blog.comments.submit' | t }}</button>
        {%- endform -%}
      </div>
    {%- endif -%}
  </div>
  
  <div class="next-article-wrapper">
  <div class="Container Container--narrow" style="">
   
          <div class="col-50-wrapper">
            
              <div class="col col--50">
                
                
            
                {%- for article in blog.articles limit:2 -%}
                
                <div class="col article-min-wrapper">
                  <div class="article-block">
                  <a href="{{ article.url }}"></a>
                  <div class="block--image full-bg" style="background-image: url({{ article.image | img_url: '500x' }})">                  </div>
                  <div class="block--content">
                    <span class="article-tag p22 normal">{{ article.tags.first }}</span>
                    <h2 class="playfair">{{ article.title }}</h2>
                  </div>
                  </div>
                </div>
                
                {% endfor %}
                
                
              </div>
              
              <div class="col col--50">
                
                {%- for article in blog.articles limit:4 -%}
                  {% unless forloop.index0 < 2 %}
                  <div class="col article-min-wrapper">
                    <div class="article-block">
                    <a href="{{ article.url }}"></a>
                    <div class="block--image full-bg" style="background-image: url({{ article.image | img_url: '500x' }})">                    </div>
                    <div class="block--content">
                      <span class="article-tag p22 normal">{{ article.tags.first }}</span>
                      <h2 class="playfair">{{ article.title }}</h2>
                    </div>
                    </div>
                  </div>
                  {% endunless %}
                {% endfor %}
                
                
                
              </div>
            
          </div>
            
  </div>
  </div>
  
  
  
</article>{% if dm_paginate_by %}{% render 'spurit_dmr_collection_template_snippet', paginate_by: dm_paginate_by %}{% endif %}
{% section 'shop-now' %}
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		'{{ 'gem-article-556764790918.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->