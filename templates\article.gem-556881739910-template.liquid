{% comment %}
	GEMPAGE BUILDER (https://apps.shopify.com/gempage)

	You SHOULD NOT modify source code in this page because
	It is automatically generated from GEMPAGE BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->


<link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.6.3/css/font-awesome.min.css" class="gf-style">
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-article-556881739910.css' | asset_url }}" class="gf_page_style">
<!--GEM_HEADER_END-->
{%- assign share_url = shop.url | append: article.url -%}
{%- assign twitter_text = article.title -%}
{%- assign pinterest_description = article.description | strip_html | truncatewords: 15 | url_param_escape -%}
{%- assign pinterest_image = article.image | img_url: '750x' | prepend: 'https:' -%}<article class="Article" data-section-id="{{ section.id }}" data-section-type="article">
  <aside class="ArticleToolbar hidden-phone">
    <div class="ArticleToolbar__Left">
      <span class="Heading Text--subdued u-h8 hidden-tablet">{{ 'blog.article.now_reading' | t }}</span>
      <span class="ArticleToolbar__ArticleTitle Heading u-h7">{{ article.title }}</span>
    </div>    <div class="ArticleToolbar__Right">
      {%- if section.settings.show_share_buttons -%}
        <div class="ArticleToolbar__ShareList">
          <span class="ArticleToolbar__ShareLabel Heading Text--subdued u-h8">{{ 'blog.article.share' | t }}</span>          <div class="HorizontalList">
            <a class="HorizontalList__Item Text--subdued Link" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'facebook' -%}</a>
            <a class="HorizontalList__Item Text--subdued Link" href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'twitter' -%}</a>
            <a class="HorizontalList__Item Text--subdued Link" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener">{%- include 'icon' with 'pinterest' -%}</a>
          </div>
        </div>
      {%- endif -%}      {%- if blog.next_article or blog.previous_article -%}
        <div class="ArticleToolbar__Nav">
          {%- if blog.next_article -%}
            <a href="{{ blog.next_article }}" class="ArticleToolbar__NavItem ArticleToolbar__NavItem--next Heading Text--subdued Link u-h8">{% include 'icon' with 'select-arrow-left' %} {{ 'blog.article.previous' | t }}</a>
          {%- endif -%}          {%- if blog.previous_article and blog.next_article -%}
            <span class="ArticleToolbar__NavItemSeparator"></span>
          {%- endif -%}          {%- if blog.previous_article -%}
            <a href="{{ blog.previous_article }}" class="ArticleToolbar__NavItem ArticleToolbar__NavItem--prev Heading Text--subdued Link u-h8">{{ 'blog.article.next' | t }} {% include 'icon' with 'select-arrow-right' %}</a>
          {%- endif -%}
        </div>
      {%- endif -%}
    </div>
  </aside>  {%- if article.image and section.settings.show_article_image -%}
    <div class="Article__ImageWrapper" style="background-image: url({{ article.image | img_url: '1x1' }})">
      <div class="Article__Image Image--lazyLoad Image--fadeIn"
           data-optimumx="1.4"
           data-bgset="{{ article.image | img_url: '400x' }} 400w, {{ article.image | img_url: '600x' }} 600w, {{ article.image | img_url: '800x' }} 800w, {{ article.image | img_url: '1200x' }} 1200w, {{ article.image | img_url: '1400x' }} 1400w, {{ article.image | img_url: '1600x' }} 1600w">
      </div>
    </div>
  {%- endif -%}  <div class="Article__Wrapper">
    <div class="Article__Content">
      <header class="Article__Header">
        <p class="article-back"><button onclick="window.history.back()">< Go Back</button></p>
        {%- capture article_meta -%}
          {%- if section.settings.show_date -%}
            <span class="Article__MetaItem">{{ article.published_at | date: format: 'month_day_year' }}</span>
          {%- endif -%}          {%- if section.settings.show_category and article.tags != empty -%}
            <span class="Article__MetaItem">{{ article.tags.first }}</span>
          {%- endif -%}
        {%- endcapture -%}        {%- if article_meta != blank -%}
          <div class="Article__Meta Heading Text--subdued u-h6">
            {{- article_meta -}}
          </div>
        {%- endif -%}        <h1 class="Article__Title Heading u-h1">{{ article.title }}</h1>
      </header>      <div class="Article__Body Rte">
        <!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor">
<div class="gryffeditor">
  <div data-label="Text Block" class="element-wrap" data-description data-key="text-block"> <div class="elm text-edit"><p style="text-align: left;">We’re very excited to introduce proud Yuggera woman Dameila Thompson AKA Dameeeela. An artist of many talents she is best known as DJ, producer and radio host. With an ability to cater to any genre and audience she brings listeners everything from hip hop to acid to the classics. dameeeela has warmed up the stage for the likes of Charli XCX, Anderson Paak and Genesis amongst others. She has also made appearances at Splendour In The Grass, Falls Festival, Listen Out, Laneway and Groovin The Moo! <br><br>Recently we had the wonderful opportunity to sit down with dameeeela to yarn about her creative journey, some of the challenges as a performer at the height of COVID, the story behind her Spotify track ID, and her exciting collaboration with Tjaka for her first single release.<img src="https://cdn.shopify.com/s/files/1/0247/4021/files/dameeeela.jpg?v=1663899378" alt=""><em>Dameila Thompson. Courtesy of Yarn, 2022.</em></p>
<h3 style="text-align: left;"><strong>Tell us a bit about your heritage and where you grew up.</strong></h3>
<p>“My mob is the Yuggera mob from the Southside here in Meanjin…I grew up in Ipswich with my mum. Then, we made the big move into Brisbane, just as I started school.”</p>
<h3><strong>What got you into DJing and the music industry?</strong></h3>
<p>“I feel like it all started in Ipswich with my mum…I was in the front seat changing all the tapes. I would wake up in the morning and she’d be listening to rage so loud and she would video tape it so I could watch it later.<br>When I was in primary school I would run tapes over the radio and record all my favourite songs and talk over it…I would pretend that I was radio hosting, “like, this is TLC!”<br>Then, during high school, I was always, like, downloading massive packs of music and just listening through all of it and sending it over MSN to all my friends.<br>So, after that I finished school, and was like, “I’m gonna buy myself a lil’ DJ thing.” And then, I just taught myself. I watched some Youtube videos and got into clubs and started taking over when DJs were playing. I’m like, “Ooh, let me have a turn!” …And then, from there, I’ve just been DJ’ing.”</p>
<p style="text-align: center;"><strong><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/dameeeela_1.jpg?v=1663899576" alt=""></strong>d<em>ameeeela. Courtesy of Resident Advisor, 2022.</em></p>
<h3 style="text-align: left;"><strong>What artists influenced you growing up and what kind of messages spoke to you the most?</strong></h3>
<p>“Definitely TLC and Destiny’s Child: the strong independent women. And, strangely, I like Bob Marley and stuff like that. I remember, there was a Bob Marley poster in my house and at night it would look scary because he had these crazy dreadlocks everywhere. But, in the morning his message was always so peaceful and relaxing, and still to this day it’s so grounding to listen to his music, and music like that.”</p>
<h3><strong>Describe your creative process when you mix new tracks and plan sets. </strong></h3>
<p>“Because I play so many different genres and so many different shows, it’s important for me to think about who I’m playing to, and, from that I’m like, “what would be the peak song that they would want to hear?” And then, I just build the set around it, so when it comes up it takes you on a fun journey.”</p>
<h3><strong>Congratulations, you have your own track ID’s on Spotify, how do you feel about this huge accomplishment?</strong></h3>
<p>“I’m pretty sure I manifested that because all I’ve ever wanted was a Spotify track IDs. I would make, like, pretty much pretend track IDs…there was three of them, it was, like, “Dameeeela Electronic,” “Dameeeela Hip Hop,” and just “Dameeeela Radio” which was a mix of everything.</p>
<p>I ended up getting a lot of followers on those, and I just sort of updated them whenever I wanted, and kind of like, putting it out on Twitter, “when am I gonna get my own track IDs?”</p>
<p>Eventually, it just happened and it’s such a thing that I’m proud of because I get to put so many different genres in it and anyone that’s listening kinda has to listen to such a wide range of music in one sitting.”</p>
<h3><strong>Your track ID’s playlist is quite diverse, not just in genres but languages and cultures as well. Tell us about the significance of this inclusivity in your Spotify track IDs?</strong></h3>
<p>“I feel like we don’t listen to enough music from different languages in general, and that’s such a loss for us because there’s so much good music. Just because you don’t quite understand it, doesn’t mean we don’t enjoy it. And, being overseas a lot of my life…they listen to English music and they don’t even know the words, but they still appreciate the songs.</p>
<p>…As Australians, we have such a diverse, beautiful range of cultures that we live within, and I want that to be reflected in my playlist…It's just important to be sharing music from all over the world, it doesn’t really make sense to just play music from one place…it wouldn’t be sustainable and be pushing boundaries.”</p>
<h3><strong>How do you connect to culture and Country through music?</strong></h3>
<p>“At the moment, it feels like I connect to culture through music just by being with other mob DJs and learning so much from them. Every mob has such different sets of considerations and such a beautiful history. And, learning from those other DJs has just been so eye opening and so relatable at the same time.”</p>
<h3><strong>Tell us about the creative process for your debut release ‘The Shake Up’ ft. Tjaka.</strong></h3>
<p>“I was running a radio show on 4ZZZ, and it was a show based on having live acts come in and we would just stream the whole thing live on radio and Facebook and then later put it on Youtube.</p>
<p>I had been told about <a href="https://www.instagram.com/tjaka_music/" target="_blank" rel="noopener noreferrer">Tjaka</a>, so I immediately put them on the second show I was hosting, and it was their first ever performance. After that, I just knew for sure that we needed to make a song, because they are so unique.</p>
<p>Their dad invented the microphone that goes in your mouth when you play the didgeridoo…Yothu Yindi used them in the 90s. So, they’ve got these awesome gadgets and the electronic didgeridoo and it was just so cool. Then, at the same time they were playing electronic pads and dancing.</p>
<p>So, we came together and I just made a rough loop that I knew I wanted to be the hardest part of it. And then, we went into the studio…and Sampology just recorded them playing didgeridoo for a while, and then I took all of those recordings and I just picked parts that I liked.</p>
<p>…In the video clip, it was so astonishing for everyone that was there because they were really just playing it live over my DJ’ing…It’s such an ode to how cool the didgeridoo instrument can be.”</p>
<h3><strong>What’s been your favourite performances so far and why?</strong></h3>
<p>“There’s three that stand out. One of them is Feme Fale which is on New Years Eve, and I just couldn’t imagine a better way to bring in the New Year. It's my ideal space to play; I feel welcome and my people are there, and it’s just a dream to play there…</p>
<p>…Then, at Pitch Music Festival. It was a really outstanding set; it was, like, midnight on the last night and they had built a cage that I was DJ’ing in…I felt like I could just really experiment, so I just went genre, genre, genre, and there was a couple of big names there with me and they were really impressed. It ticked all the boxes, and that only just recently happened too, so I’m still on a bit of a buzz from that.”</p>
<h3><strong><b>Tell us about your work in creating spaces for First Nations peoples in the music industry?</b></strong></h3>
<p>“There’s all the behind the scenes and business bookings that I could talk about, and there’s the in-person, on-ground safety measurements that should be in place…Since the whole Black Lives Matter movement… we’re having people [in the industry] book more Aboriginal people on big lineups…but at the same time…we’re kind of wondering if in these lineups, we’re just getting put on them or whether we’re getting put on them…to tick an important [diversity] box.</p>
<p>…So, in this mob group chat I, we really want to make some kind of committee…for events to work through us so we can have a space at venues and give some suggestions…We are working towards it, like, I’m working in <a href="https://www.qmusic.com.au/concert-care/concert-care-diversity-advisory-group" target="_blank" rel="noopener noreferrer">QMusic’s Safety and Diversity Advisory Group</a> and they’re doing training at every venue coming up. They’re creating some safe spaces and they’ve been talking to every venue in Queensland from pubs to casinos to make sure that they’ve got some sort of safety measures.</p>
<p>…with <a href="https://supportact.org.au/get-help/first-nations-support-2/" target="_blank" rel="noopener noreferrer">Support Act</a> as well, I just did a massive workshop with them, which was for First Nations [peoples] and they’ve got free of charge Indigenous hotlines that you can call anytime for music industry workers, whether you’re a DJ or security guard or music soundboard person, Support Act is always there to help…They’ve certainly helped me out in many difficult times.</p>
<p>Through doing these Support Act workshops and being on the diversity board for QMusic is all this direct advocacy that I can do and have a say on behalf of mob…”</p>
<p style="text-align: center;"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/ee3f-5a02-4098-afd1-fe918932ebdc.jpg?v=1663899479" alt=""><em>dameeeela. Courtesy of Mixcloud, 2022.</em></p>
<h3><strong>How would you like to see others giving back? </strong></h3>
<p>“I think we need more representation and it’s also knowing your history. I feel like a lot of people in the music industry still were in school when we were being taught the Captain Cook settler story…Mob life is so much more complex than you could probably understand, so acquiring this knowledge is the best thing you could probably do.</p>
<p>I always say representation is so important, it’s not hard to share a post of First Nations artists, it’s so easy just to hit “share.”...So, when someone shares one of our posts, then there’s potentially 300 people that see this and think, “oh, maybe I could DJ.”</p>
<p>…I have this little timeline that I use to explain representation: Representation equals more of our babies seeing what they could potentially be one day; that they can dream big…And, then that leads to more opportunities later in life…I hate bringing it to this point, but it’s suicide prevention as our people die 2.5 times more just by suicide, and 20 years younger than white people as well."</p>
<h3><strong>What advice would you give to kids out there who are interested in getting into DJ’ing?</strong></h3>
<p><span style="font-weight: 400;">“I would say reach out to other mob DJs and build your support system before you jump into anything.”</span></p>
<span style="font-weight: 400;">Be sure to follow Dameeeela on Instagram </span><a href="https://www.instagram.com/dameeeela/" target="_blank" rel="noopener noreferrer"><span style="font-weight: 400;">@ɐlıəɯɐp</span></a><span style="font-weight: 400;">, and check out her Spotify ID’s and new track “The Shake Up” on </span><a href="https://open.spotify.com/artist/6AaLiQRx5xSWLWZFSOcItq" target="_blank" rel="noopener noreferrer"><span style="font-weight: 400;">Spotify</span></a><span style="font-weight: 400;">.</span></div></div>
</div><div id="divContentBk"></div></div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->
      </div>      {%- capture article_footer -%}
        {%- if section.settings.show_author -%}
          <span class="Article__Author Heading Text--subdued u-h6">{{ 'blog.article.written_by' | t: author: article.author }}</span>
        {%- endif -%}        {%- if section.settings.show_share_buttons -%}
          <div class="Article__ShareButtons ShareButtons">
            <a class="ShareButtons__Item ShareButtons__Item--facebook" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'facebook' -%}</a>
            <a class="ShareButtons__Item ShareButtons__Item--twitter" href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'twitter' -%}</a>
            <a class="ShareButtons__Item ShareButtons__Item--pinterest" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener">{%- include 'icon' with 'pinterest' -%}</a>
          </div>
        {%- endif -%}
      {%- endcapture -%}      {%- if article_footer != blank -%}
        <footer class="Article__Footer">
          {{ article_footer }}
        </footer>
      {%- endif -%}
    </div>    {%- if blog.comments_enabled? -%}
      {%- if article.comments_count > 0 -%}
        <div class="Article__Comments">
          <span class="Anchor" id="comments"></span>          <h2 class="Heading u-h1">{{ 'blog.article.comments_count' | t: count: article.comments_count }}</h2>          <div class="Article__CommentList">
            {%- paginate article.comments by 25 -%}
              {%- for comment in article.comments -%}
                <div class="ArticleComment">
                  <div class="ArticleComment__Body Rte">
                    {{ comment.content }}
                  </div>                  <div class="ArticleComment__Meta Heading Text--subdued u-h8">
                    <span class="ArticleComment__Author">{{ comment.author }}</span>
                    <span class="ArticleComment__Date">{{ comment.created_at | date: format: 'month_day_year' }}</span>
                  </div>
                </div>
              {%- endfor -%}              {% include 'pagination', hash: '#comments' %}
            {% assign dm_paginate_by = paginate.page_size %}{%- endpaginate -%}
          </div>
        </div>
      {%- endif -%}      <div class="Article__CommentFormWrapper">
        {% if article.comments_count == 0 %}
          <span class="Anchor" id="comments"></span>
        {%- endif -%}        <span class="Anchor" id="comment_form"></span>        <h2 class="Heading u-h1">{{ 'blog.comments.form_title' | t }}</h2>        {%- form 'new_comment', article, class: 'Article__CommentForm Form', id: '' -%}
          {%- if form.posted_successfully? -%}
            <p class="Form__Alert Alert Alert--success">
              {%- if blog.moderated? -%}
                {{- 'blog.comments.success_moderated' | t -}}
              {%- else -%}
                {{- 'blog.comments.success' | t -}}
              {%- endif -%}
            </p>
          {%- endif -%}          {%- if form.errors -%}
            <div class="Form__Alert Alert Alert--error">
              <ul class="Alert__ErrorList">
                {%- for field in form.errors -%}
                  {%- if field == 'form' -%}
                    <li class="Alert__ErrorItem">{{ form.errors.messages[field] }}</li>
                  {%- else -%}
                    <li class="Alert__ErrorItem"><strong>{{ form.errors.translated_fields[field] }}</strong> {{ form.errors.messages[field] }}</li>
                  {%- endif -%}
                {%- endfor -%}
              </ul>
            </div>
          {%- endif -%}          <div class="Form__Group">
            <div class="Form__Item">
              <input type="text" class="Form__Input" name="comment[author]" placeholder="{{ 'blog.comments.name_placeholder' | t }}" aria-label="{{ 'blog.comments.name_placeholder' | t }}" value="{{ form.author | escape | default: customer.name }}" required="required">
              <label class="Form__FloatingLabel">{{ 'blog.comments.name_placeholder' | t }}</label>
            </div>            <div class="Form__Item">
              <input type="email" class="Form__Input" name="comment[email]" placeholder="{{ 'blog.comments.email_placeholder' | t }}" aria-label="{{ 'blog.comments.email_placeholder' | t }}" value="{{ form.email | escape | default: customer.email }}" required="required">
              <label class="Form__FloatingLabel">{{ 'blog.comments.email_placeholder' | t }}</label>
            </div>
          </div>          <div class="Form__Item">
            <textarea name="comment[body]" rows="6" class="Form__Textarea" placeholder="{{ 'blog.comments.comment_placeholder' | t }}" aria-label="{{ 'blog.comments.comment_placeholder' | t }}" required="required">
              {{- form.body -}}
            </textarea>            <label class="Form__FloatingLabel">{{ 'blog.comments.comment_placeholder' | t }}</label>
          </div>          {%- if blog.moderated? -%}
            <p class="Form__Hint">{{ 'blog.comments.approval_notice' | t }}</p>
          {%- endif -%}          <button type="submit" class="Form__Submit Button Button--primary">{{ 'blog.comments.submit' | t }}</button>
        {%- endform -%}
      </div>
    {%- endif -%}
  </div>
  
  <div class="next-article-wrapper">
  <div class="Container Container--narrow" style="">
   
          <div class="col-50-wrapper">
            
              <div class="col col--50">
                
                
            
                {%- for article in blog.articles limit:2 -%}
                
                <div class="col article-min-wrapper">
                  <div class="article-block">
                  <a href="{{ article.url }}"></a>
                  <div class="block--image full-bg" style="background-image: url({{ article.image | img_url: '500x' }})">                  </div>
                  <div class="block--content">
                    <span class="article-tag p22 normal">{{ article.tags.first }}</span>
                    <h2 class="playfair">{{ article.title }}</h2>
                  </div>
                  </div>
                </div>
                
                {% endfor %}
                
                
              </div>
              
              <div class="col col--50">
                
                {%- for article in blog.articles limit:4 -%}
                  {% unless forloop.index0 < 2 %}
                  <div class="col article-min-wrapper">
                    <div class="article-block">
                    <a href="{{ article.url }}"></a>
                    <div class="block--image full-bg" style="background-image: url({{ article.image | img_url: '500x' }})">                    </div>
                    <div class="block--content">
                      <span class="article-tag p22 normal">{{ article.tags.first }}</span>
                      <h2 class="playfair">{{ article.title }}</h2>
                    </div>
                    </div>
                  </div>
                  {% endunless %}
                {% endfor %}
                
                
                
              </div>
            
          </div>
            
  </div>
  </div>
  
  
  
</article>{% if dm_paginate_by %}{% render 'spurit_dmr_collection_template_snippet', paginate_by: dm_paginate_by %}{% endif %}
{% section 'shop-now' %}
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		'{{ 'gem-article-556881739910.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->