{% comment %}
	GEMPAGE BUILDER (https://apps.shopify.com/gempage)

	You SHOULD NOT modify source code in this page because
	It is automatically generated from GEMPAGE BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->

<link data-instant-track rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.6.3/css/font-awesome.min.css" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-article-556909658246.css' | asset_url }}" class="gf_page_style">
<link data-instant-track class="gf_fonts" data-fonts="Gochi Hand" href="//fonts.googleapis.com/css2?family=Gochi Hand:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant Garamond" href="//fonts.googleapis.com/css2?family=Cormorant Garamond:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant" href="//fonts.googleapis.com/css2?family=Cormorant:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track rel="stylesheet" type="text/css" href= "https://d1um8515vdn9kb.cloudfront.net/libs/css/owl.carousel.min.css" class="gf_libs">
<!--GEM_HEADER_END-->
{%- assign share_url = shop.url | append: article.url -%}
{%- assign twitter_text = article.title -%}
{%- assign pinterest_description = article.description | strip_html | truncatewords: 15 | url_param_escape -%}
{%- assign pinterest_image = article.image | img_url: '750x' | prepend: 'https:' -%}<article class="Article" data-section-id="{{ section.id }}" data-section-type="article">
  <aside class="ArticleToolbar hidden-phone">
    <div class="ArticleToolbar__Left">
      <span class="Heading Text--subdued u-h8 hidden-tablet">{{ 'blog.article.now_reading' | t }}</span>
      <span class="ArticleToolbar__ArticleTitle Heading u-h7">{{ article.title }}</span>
    </div>    <div class="ArticleToolbar__Right">
      {%- if section.settings.show_share_buttons -%}
        <div class="ArticleToolbar__ShareList">
          <span class="ArticleToolbar__ShareLabel Heading Text--subdued u-h8">{{ 'blog.article.share' | t }}</span>          <div class="HorizontalList">
            <a class="HorizontalList__Item Text--subdued Link" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'facebook' -%}</a>
            <a class="HorizontalList__Item Text--subdued Link" href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'twitter' -%}</a>
            <a class="HorizontalList__Item Text--subdued Link" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener">{%- include 'icon' with 'pinterest' -%}</a>
          </div>
        </div>
      {%- endif -%}      {%- if blog.next_article or blog.previous_article -%}
        <div class="ArticleToolbar__Nav">
          {%- if blog.next_article -%}
            <a href="{{ blog.next_article }}" class="ArticleToolbar__NavItem ArticleToolbar__NavItem--next Heading Text--subdued Link u-h8">{% include 'icon' with 'select-arrow-left' %} {{ 'blog.article.previous' | t }}</a>
          {%- endif -%}          {%- if blog.previous_article and blog.next_article -%}
            <span class="ArticleToolbar__NavItemSeparator"></span>
          {%- endif -%}          {%- if blog.previous_article -%}
            <a href="{{ blog.previous_article }}" class="ArticleToolbar__NavItem ArticleToolbar__NavItem--prev Heading Text--subdued Link u-h8">{{ 'blog.article.next' | t }} {% include 'icon' with 'select-arrow-right' %}</a>
          {%- endif -%}
        </div>
      {%- endif -%}
    </div>
  </aside>  {%- if article.image and section.settings.show_article_image -%}
    <div class="Article__ImageWrapper" style="background-image: url({{ article.image | img_url: '1x1' }})">
      <div class="Article__Image Image--lazyLoad Image--fadeIn"
           data-optimumx="1.4"
           data-bgset="{{ article.image | img_url: '400x' }} 400w, {{ article.image | img_url: '600x' }} 600w, {{ article.image | img_url: '800x' }} 800w, {{ article.image | img_url: '1200x' }} 1200w, {{ article.image | img_url: '1400x' }} 1400w, {{ article.image | img_url: '1600x' }} 1600w">
      </div>
    </div>
  {%- endif -%}  <div class="Article__Wrapper">
    <div class="Article__Content">
      <header class="Article__Header">
        <p class="article-back"><button onclick="window.history.back()">< Go Back</button></p>
        {%- capture article_meta -%}
          {%- if section.settings.show_date -%}
            <span class="Article__MetaItem">{{ article.published_at | date: format: 'month_day_year' }}</span>
          {%- endif -%}          {%- if section.settings.show_category and article.tags != empty -%}
            <span class="Article__MetaItem">{{ article.tags.first }}</span>
          {%- endif -%}
        {%- endcapture -%}        {%- if article_meta != blank -%}
          <div class="Article__Meta Heading Text--subdued u-h6">
            {{- article_meta -}}
          </div>
        {%- endif -%}        <h1 class="Article__Title Heading u-h1">{{ article.title }}</h1>
      </header>      <div class="Article__Body Rte">
        <!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor">
<div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1666575212729" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1666575212729"><div class="module main-slider owl-carousel owl-theme " data-collg="3" data-colmd="3" data-colsm="3" data-colxs="1" data-marginlg="5" data-marginmd="5" data-marginsm="5" data-marginxs="5" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="0" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="0"><div class="item"><div data-index="1" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666575216222" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666575216222"><div class="elm gf-elm-center gf_elm-left-xs"><img src="//d1um8515vdn9kb.cloudfront.net/images/parallax.jpg" alt="" class="gf_image" data-gemlang="en"></div></div></div></div><div class="item"><div data-index="2" class="item-content"></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666575218665" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666575218665"><div class="elm gf-elm-center gf_elm-left-xs"><img src="//d1um8515vdn9kb.cloudfront.net/images/parallax.jpg" alt="" class="gf_image" data-gemlang="en"></div></div></div></div><div class="item"><div data-index="4" class="item-content"></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div><!--gfsplit--><div class="element-wrap" data-label="Text Block" id="e-1666575189634" data-icon="gpicon-textblock" data-ver="1" data-id="1666575189634"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><p>As the holiday season starts to approach, it's time to start thinking about where you can go for an amazing vacation. NSW has so much to offer in terms of holiday destinations and cultural experiences and we wanted to share some of the best spots we know of so you can get ready to relax. <br><br>1. Eden<br><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/Eden_Whale_Watching_Lookout.webp?v=1664339277" alt=""></p><p style="text-align: center;"><em>Whale watching lookout in Eden, (Visitnsw.com, 2022)</em></p><p>Sitting on the Country of the Yuin-Monaro Nations, the Sapphire Coast claims stunning natural views and a rich cultural history. A hidden gem of the southern NSW coast, the town of Eden offers gorgeous scenery with the surrounding Ben Boyd National Park and many whale watching spots, including boat tours out into the water to get closer to the aquatic wildlife. The Bundian Way Story Trail offers a snapshot of the Bundian Way, an ancient pathway between the Sapphire Coast and the Snowy Mountains that the people from Yuin, Ngarigo, Jaitmathang and Bidawal Country have walked for many thousands of years. <br><br>2. Byron Bay<br><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/ByronBay.jpg?v=1664339498" alt=""></p><p style="text-align: center;"><em>Gorgeous beaches at Byron Bay, (visitnsw.com, 2022).</em></p><p>Cavanbah is the Arakwal name for the Byron Bay area in the Bundjalung Nation. If you love surfing then Byron Bay is the place for you! With world renowned surfing spots and a plethora of learn-to-surf options, this is the perfect place to catch a wave. Byron also offers a range of resort and relaxation options with a reputation as the hippie capital of Australia. There are many fantastic Aboriginal experiences to be had while you enjoy your stay, including Explore Byron Bay tours, led by an Arakwal elder. There is also plenty of shopping and good food to enjoy while you’re here, and you might even spot a celebrity!<br><br>3. Hunter Valley<br><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/imageHighlightsSrc.adapt.740.medium.jpg?v=1664339782" alt=""></p><p style="text-align: center;"><em>Lots of exciting experiences in Hunter Valley, (australia.com, 2022).</em></p><p>If you love wine, then the Hunter Valley is the perfect holiday destination for you! It is one of Australia’s oldest wine regions and produces world renowned wines. There are so many places to relax at and enjoy the bounties of the valley, from family run wineries to fresh and local restaurants and farmers markets. The Wonnarua people are the traditional owners of the Hunter Valley and the rich cultural history is everywhere. There are several important cultural sites to visit, including Biaime Cave, depicting the Biaime dreaming. This tells the story of how Biaime created the valley. <br><br>4. Sydney<br><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/168272-Article.webp?v=1664340013" alt=""></p><p style="text-align: center;"><em>Sydney harbour sights, (visitnsw.com, 2022).</em></p><p>If you’re looking for somewhere that has it all, then Sydney is the place for you. A bustling metropolis with some of Australia's most recognisable landmarks, there is always something exciting to do here. Warrane is the Indigenous name for Sydney, home of the Gadigal people. Explore Sydney Harbour bay and all the iconic locations in walking distance as well as some of the city's historic inner suburbs. Sydney Taronga zoo is also a must see destination and a perfect place for the kids. There are some incredible Aboriginal cultural experiences to get excited about, including a heritage tour through the Royal Botanic Gardens. You can taste some of Australia's bush foods and learn about the history of traditional medicines and cooking methods.<br><br>5. Coffs Harbour<br><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/174236_0.webp?v=1664340208" alt=""></p><p style="text-align: center;"><em>Gorgeous coastal scenery at Coffs Harbour, (visitnsw.com, 2022).</em></p><p>A beautiful coastal getaway, Coffs Harbour offers gorgeous beaches and coastal views. The Gumbaynggirr people are the traditional custodians of the land, forming one of the largest coastal Aboriginal Nations in New South Wales. There is a rich cultural history that you can explore and the Giingan Gumbaynggirr Cultural Experience is a fantastic opportunity to learn more about the culture and language of the Gumbaynggirr people. The town is also home to one of Australia’s iconic big fruits, the Big Banana. You can go whale watching and Coffs Harbour is one of the only places in the world where you can swim with humpback whales!<br><br>6. Newcastle<br><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/177414.webp?v=1664340336" alt=""></p><p style="text-align: center;"><em>Nobbys beach at Newcastle, (visitnsw.com, 2022).&nbsp;</em></p><p>Mulubinba is the Indigenous name for Newcastle. The traditional custodians of the land are the Awabakal and Worimi peoples. Newcastle offers gorgeous sandy beaches and a bustling coastal city with lots of choices for things to do. It’s the ultimate weekend getaway, under three hours drive from Sydney, and only an hour from the Hunter Valley region. There are many Aboriginal cultural experiences to engage with, including sacred sites such as Whibayganba and Newcastle’s famous landmark Nobbys. Newcastle is a big enough city to have plenty of things to do and small enough to explore with ease.<br><br>When it comes time to pick a destination for your next holiday, it is important to consider all the things you would like to do and plan accordingly. NSW has such a diverse range of places to see and things to do, it’s guaranteed that you will find the perfect holiday destination for you and your family, no matter where you go. You could even do a road trip and see as many of these fantastic destinations as possible!</p></div></div><!--gfsplit--><script>window.__gemStoreData = {};window.__gemStoreData.gemKeyValid = {};window.__gemStoreData.gemKeyValid.bestWayAddToCart = 1;</script>
</div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->      </div>      {%- capture article_footer -%}
        {%- if section.settings.show_author -%}
          <span class="Article__Author Heading Text--subdued u-h6">{{ 'blog.article.written_by' | t: author: article.author }}</span>
        {%- endif -%}        {%- if section.settings.show_share_buttons -%}
          <div class="Article__ShareButtons ShareButtons">
            <a class="ShareButtons__Item ShareButtons__Item--facebook" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'facebook' -%}</a>
            <a class="ShareButtons__Item ShareButtons__Item--twitter" href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'twitter' -%}</a>
            <a class="ShareButtons__Item ShareButtons__Item--pinterest" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener">{%- include 'icon' with 'pinterest' -%}</a>
          </div>
        {%- endif -%}
      {%- endcapture -%}      {%- if article_footer != blank -%}
        <footer class="Article__Footer">
          {{ article_footer }}
        </footer>
      {%- endif -%}
    </div>    {%- if blog.comments_enabled? -%}
      {%- if article.comments_count > 0 -%}
        <div class="Article__Comments">
          <span class="Anchor" id="comments"></span>          <h2 class="Heading u-h1">{{ 'blog.article.comments_count' | t: count: article.comments_count }}</h2>          <div class="Article__CommentList">
            {%- paginate article.comments by 25 -%}
              {%- for comment in article.comments -%}
                <div class="ArticleComment">
                  <div class="ArticleComment__Body Rte">
                    {{ comment.content }}
                  </div>                  <div class="ArticleComment__Meta Heading Text--subdued u-h8">
                    <span class="ArticleComment__Author">{{ comment.author }}</span>
                    <span class="ArticleComment__Date">{{ comment.created_at | date: format: 'month_day_year' }}</span>
                  </div>
                </div>
              {%- endfor -%}              {% include 'pagination', hash: '#comments' %}
            {% assign dm_paginate_by = paginate.page_size %}{%- endpaginate -%}
          </div>
        </div>
      {%- endif -%}      <div class="Article__CommentFormWrapper">
        {% if article.comments_count == 0 %}
          <span class="Anchor" id="comments"></span>
        {%- endif -%}        <span class="Anchor" id="comment_form"></span>        <h2 class="Heading u-h1">{{ 'blog.comments.form_title' | t }}</h2>        {%- form 'new_comment', article, class: 'Article__CommentForm Form', id: '' -%}
          {%- if form.posted_successfully? -%}
            <p class="Form__Alert Alert Alert--success">
              {%- if blog.moderated? -%}
                {{- 'blog.comments.success_moderated' | t -}}
              {%- else -%}
                {{- 'blog.comments.success' | t -}}
              {%- endif -%}
            </p>
          {%- endif -%}          {%- if form.errors -%}
            <div class="Form__Alert Alert Alert--error">
              <ul class="Alert__ErrorList">
                {%- for field in form.errors -%}
                  {%- if field == 'form' -%}
                    <li class="Alert__ErrorItem">{{ form.errors.messages[field] }}</li>
                  {%- else -%}
                    <li class="Alert__ErrorItem"><strong>{{ form.errors.translated_fields[field] }}</strong> {{ form.errors.messages[field] }}</li>
                  {%- endif -%}
                {%- endfor -%}
              </ul>
            </div>
          {%- endif -%}          <div class="Form__Group">
            <div class="Form__Item">
              <input type="text" class="Form__Input" name="comment[author]" placeholder="{{ 'blog.comments.name_placeholder' | t }}" aria-label="{{ 'blog.comments.name_placeholder' | t }}" value="{{ form.author | escape | default: customer.name }}" required="required">
              <label class="Form__FloatingLabel">{{ 'blog.comments.name_placeholder' | t }}</label>
            </div>            <div class="Form__Item">
              <input type="email" class="Form__Input" name="comment[email]" placeholder="{{ 'blog.comments.email_placeholder' | t }}" aria-label="{{ 'blog.comments.email_placeholder' | t }}" value="{{ form.email | escape | default: customer.email }}" required="required">
              <label class="Form__FloatingLabel">{{ 'blog.comments.email_placeholder' | t }}</label>
            </div>
          </div>          <div class="Form__Item">
            <textarea name="comment[body]" rows="6" class="Form__Textarea" placeholder="{{ 'blog.comments.comment_placeholder' | t }}" aria-label="{{ 'blog.comments.comment_placeholder' | t }}" required="required">
              {{- form.body -}}
            </textarea>            <label class="Form__FloatingLabel">{{ 'blog.comments.comment_placeholder' | t }}</label>
          </div>          {%- if blog.moderated? -%}
            <p class="Form__Hint">{{ 'blog.comments.approval_notice' | t }}</p>
          {%- endif -%}          <button type="submit" class="Form__Submit Button Button--primary">{{ 'blog.comments.submit' | t }}</button>
        {%- endform -%}
      </div>
    {%- endif -%}
  </div>
  
  <div class="next-article-wrapper">
  <div class="Container Container--narrow" style="">
   
          <div class="col-50-wrapper">
            
              <div class="col col--50">
                
                
            
                {%- for article in blog.articles limit:2 -%}
                
                <div class="col article-min-wrapper">
                  <div class="article-block">
                  <a href="{{ article.url }}"></a>
                  <div class="block--image full-bg" style="background-image: url({{ article.image | img_url: '500x' }})">                  </div>
                  <div class="block--content">
                    <span class="article-tag p22 normal">{{ article.tags.first }}</span>
                    <h2 class="playfair">{{ article.title }}</h2>
                  </div>
                  </div>
                </div>
                
                {% endfor %}
                
                
              </div>
              
              <div class="col col--50">
                
                {%- for article in blog.articles limit:4 -%}
                  {% unless forloop.index0 < 2 %}
                  <div class="col article-min-wrapper">
                    <div class="article-block">
                    <a href="{{ article.url }}"></a>
                    <div class="block--image full-bg" style="background-image: url({{ article.image | img_url: '500x' }})">                    </div>
                    <div class="block--content">
                      <span class="article-tag p22 normal">{{ article.tags.first }}</span>
                      <h2 class="playfair">{{ article.title }}</h2>
                    </div>
                    </div>
                  </div>
                  {% endunless %}
                {% endfor %}
                
                
                
              </div>
            
          </div>
            
  </div>
  </div>
  
  
  
</article>{% if dm_paginate_by %}{% render 'spurit_dmr_collection_template_snippet', paginate_by: dm_paginate_by %}{% endif %}
{% section 'shop-now' %}
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		"https://d1um8515vdn9kb.cloudfront.net/libs/js/owl.carousel.min.js",
		'{{ 'gem-article-556909658246.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->