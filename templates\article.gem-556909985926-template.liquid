{% comment %}
	GEMPAGE BUILDER (https://apps.shopify.com/gempage)

	You SHOULD NOT modify source code in this page because
	It is automatically generated from GEMPAGE BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->

<link data-instant-track rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.6.3/css/font-awesome.min.css" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-article-556909985926.css' | asset_url }}" class="gf_page_style">
<!--GEM_HEADER_END-->
{%- assign share_url = shop.url | append: article.url -%}
{%- assign twitter_text = article.title -%}
{%- assign pinterest_description = article.description | strip_html | truncatewords: 15 | url_param_escape -%}
{%- assign pinterest_image = article.image | img_url: '750x' | prepend: 'https:' -%}<article class="Article" data-section-id="{{ section.id }}" data-section-type="article">
  <aside class="ArticleToolbar hidden-phone">
    <div class="ArticleToolbar__Left">
      <span class="Heading Text--subdued u-h8 hidden-tablet">{{ 'blog.article.now_reading' | t }}</span>
      <span class="ArticleToolbar__ArticleTitle Heading u-h7">{{ article.title }}</span>
    </div>    <div class="ArticleToolbar__Right">
      {%- if section.settings.show_share_buttons -%}
        <div class="ArticleToolbar__ShareList">
          <span class="ArticleToolbar__ShareLabel Heading Text--subdued u-h8">{{ 'blog.article.share' | t }}</span>          <div class="HorizontalList">
            <a class="HorizontalList__Item Text--subdued Link" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'facebook' -%}</a>
            <a class="HorizontalList__Item Text--subdued Link" href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'twitter' -%}</a>
            <a class="HorizontalList__Item Text--subdued Link" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener">{%- include 'icon' with 'pinterest' -%}</a>
          </div>
        </div>
      {%- endif -%}      {%- if blog.next_article or blog.previous_article -%}
        <div class="ArticleToolbar__Nav">
          {%- if blog.next_article -%}
            <a href="{{ blog.next_article }}" class="ArticleToolbar__NavItem ArticleToolbar__NavItem--next Heading Text--subdued Link u-h8">{% include 'icon' with 'select-arrow-left' %} {{ 'blog.article.previous' | t }}</a>
          {%- endif -%}          {%- if blog.previous_article and blog.next_article -%}
            <span class="ArticleToolbar__NavItemSeparator"></span>
          {%- endif -%}          {%- if blog.previous_article -%}
            <a href="{{ blog.previous_article }}" class="ArticleToolbar__NavItem ArticleToolbar__NavItem--prev Heading Text--subdued Link u-h8">{{ 'blog.article.next' | t }} {% include 'icon' with 'select-arrow-right' %}</a>
          {%- endif -%}
        </div>
      {%- endif -%}
    </div>
  </aside>  {%- if article.image and section.settings.show_article_image -%}
    <div class="Article__ImageWrapper" style="background-image: url({{ article.image | img_url: '1x1' }})">
      <div class="Article__Image Image--lazyLoad Image--fadeIn"
           data-optimumx="1.4"
           data-bgset="{{ article.image | img_url: '400x' }} 400w, {{ article.image | img_url: '600x' }} 600w, {{ article.image | img_url: '800x' }} 800w, {{ article.image | img_url: '1200x' }} 1200w, {{ article.image | img_url: '1400x' }} 1400w, {{ article.image | img_url: '1600x' }} 1600w">
      </div>
    </div>
  {%- endif -%}  <div class="Article__Wrapper">
    <div class="Article__Content">
      <header class="Article__Header">
        <p class="article-back"><button onclick="window.history.back()">< Go Back</button></p>
        {%- capture article_meta -%}
          {%- if section.settings.show_date -%}
            <span class="Article__MetaItem">{{ article.published_at | date: format: 'month_day_year' }}</span>
          {%- endif -%}          {%- if section.settings.show_category and article.tags != empty -%}
            <span class="Article__MetaItem">{{ article.tags.first }}</span>
          {%- endif -%}
        {%- endcapture -%}        {%- if article_meta != blank -%}
          <div class="Article__Meta Heading Text--subdued u-h6">
            {{- article_meta -}}
          </div>
        {%- endif -%}        <h1 class="Article__Title Heading u-h1">{{ article.title }}</h1>
      </header>      <div class="Article__Body Rte">
        <!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor">
<div class="gryffeditor">
  <div data-label="Text Block"><div class="elm text-edit"><p>Another repatriation win, as six Warumungu artefacts are brought home after more than 100 years off Country. The artefacts were taken in the late 19th century by two men, anthropologist Baldwin Spencer and telegraph station master James Field. They took them from the Warumungu community in the Tennant Creek region of the Northern Territory (SBS, 2022).<br><br>The artefacts have been kept in the Tūhura Otago Museum in Dunedin in New Zealand since 1910 after trading with Fredrick Vincent Knap, an archaeologist and ethnologist, and the Museum Victorian in 1923 and 1937. The objects that are being returned include Kalpunta (boomerang), palya/kupija (adze) and marttan (stone knives), (SBS, 2022).<br><br>After negotiations between the museum and the Australian Institute of Aboriginal and Torres Strait Islander Studies’ Return of Cultural Heritage team, the six objects are being returned home (SBS, 2022).<br><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/download_677288b2-1b1f-43d0-88e0-bb5154d879ee.jpg?v=1664346604" alt=""></p>
<p style="text-align: center;"><em>Warumungu men and AIATSIS staff who worked to bring the items home, (NITV. 2022).</em></p>
<p>Michael Jones was one of the many senior Warumungu men who met virtually with staff of the Tūhura Otago Museum as well as members of the Maori Advisory Committee. Mr Jones has said that he is glad to have the objects come home (SBS, 2022). <br><br>“The museums are respecting us, and they’ve been thinking about us. They weren’t the ones who took them, they just ended up there. We can still teach young people about these old things and our culture,” Mr Jones said (SBS, 2022). <br><br>The Warumungu men spoke with the museum about the importance of the objects, their cultural significance and why they belonged back on Country (SBS, 2022). <br><br>The Warumungu men and AIATSIS had submitted a repatriation request and a research report which was approved in June this year. Later this year, a delegation of Warumungu people and the Return of Cultural Heritage team will travel to New Zealand to bring the objects home. Robert Morris, Tūhura Otago Museum’s Director of Collections, Research and Education has said that they are looking forward to welcoming the team to Dunedin to hand over the objects (SBS, 2022). <br><br>The objects are going to be displayed in Tennant Creek at the Nyinkka Nyunyu Art and Culture Centre (SBS, 2022).</p></div></div>
</div><div id="divContentBk"></div></div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->
      </div>      {%- capture article_footer -%}
        {%- if section.settings.show_author -%}
          <span class="Article__Author Heading Text--subdued u-h6">{{ 'blog.article.written_by' | t: author: article.author }}</span>
        {%- endif -%}        {%- if section.settings.show_share_buttons -%}
          <div class="Article__ShareButtons ShareButtons">
            <a class="ShareButtons__Item ShareButtons__Item--facebook" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'facebook' -%}</a>
            <a class="ShareButtons__Item ShareButtons__Item--twitter" href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'twitter' -%}</a>
            <a class="ShareButtons__Item ShareButtons__Item--pinterest" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener">{%- include 'icon' with 'pinterest' -%}</a>
          </div>
        {%- endif -%}
      {%- endcapture -%}      {%- if article_footer != blank -%}
        <footer class="Article__Footer">
          {{ article_footer }}
        </footer>
      {%- endif -%}
    </div>    {%- if blog.comments_enabled? -%}
      {%- if article.comments_count > 0 -%}
        <div class="Article__Comments">
          <span class="Anchor" id="comments"></span>          <h2 class="Heading u-h1">{{ 'blog.article.comments_count' | t: count: article.comments_count }}</h2>          <div class="Article__CommentList">
            {%- paginate article.comments by 25 -%}
              {%- for comment in article.comments -%}
                <div class="ArticleComment">
                  <div class="ArticleComment__Body Rte">
                    {{ comment.content }}
                  </div>                  <div class="ArticleComment__Meta Heading Text--subdued u-h8">
                    <span class="ArticleComment__Author">{{ comment.author }}</span>
                    <span class="ArticleComment__Date">{{ comment.created_at | date: format: 'month_day_year' }}</span>
                  </div>
                </div>
              {%- endfor -%}              {% include 'pagination', hash: '#comments' %}
            {% assign dm_paginate_by = paginate.page_size %}{%- endpaginate -%}
          </div>
        </div>
      {%- endif -%}      <div class="Article__CommentFormWrapper">
        {% if article.comments_count == 0 %}
          <span class="Anchor" id="comments"></span>
        {%- endif -%}        <span class="Anchor" id="comment_form"></span>        <h2 class="Heading u-h1">{{ 'blog.comments.form_title' | t }}</h2>        {%- form 'new_comment', article, class: 'Article__CommentForm Form', id: '' -%}
          {%- if form.posted_successfully? -%}
            <p class="Form__Alert Alert Alert--success">
              {%- if blog.moderated? -%}
                {{- 'blog.comments.success_moderated' | t -}}
              {%- else -%}
                {{- 'blog.comments.success' | t -}}
              {%- endif -%}
            </p>
          {%- endif -%}          {%- if form.errors -%}
            <div class="Form__Alert Alert Alert--error">
              <ul class="Alert__ErrorList">
                {%- for field in form.errors -%}
                  {%- if field == 'form' -%}
                    <li class="Alert__ErrorItem">{{ form.errors.messages[field] }}</li>
                  {%- else -%}
                    <li class="Alert__ErrorItem"><strong>{{ form.errors.translated_fields[field] }}</strong> {{ form.errors.messages[field] }}</li>
                  {%- endif -%}
                {%- endfor -%}
              </ul>
            </div>
          {%- endif -%}          <div class="Form__Group">
            <div class="Form__Item">
              <input type="text" class="Form__Input" name="comment[author]" placeholder="{{ 'blog.comments.name_placeholder' | t }}" aria-label="{{ 'blog.comments.name_placeholder' | t }}" value="{{ form.author | escape | default: customer.name }}" required="required">
              <label class="Form__FloatingLabel">{{ 'blog.comments.name_placeholder' | t }}</label>
            </div>            <div class="Form__Item">
              <input type="email" class="Form__Input" name="comment[email]" placeholder="{{ 'blog.comments.email_placeholder' | t }}" aria-label="{{ 'blog.comments.email_placeholder' | t }}" value="{{ form.email | escape | default: customer.email }}" required="required">
              <label class="Form__FloatingLabel">{{ 'blog.comments.email_placeholder' | t }}</label>
            </div>
          </div>          <div class="Form__Item">
            <textarea name="comment[body]" rows="6" class="Form__Textarea" placeholder="{{ 'blog.comments.comment_placeholder' | t }}" aria-label="{{ 'blog.comments.comment_placeholder' | t }}" required="required">
              {{- form.body -}}
            </textarea>            <label class="Form__FloatingLabel">{{ 'blog.comments.comment_placeholder' | t }}</label>
          </div>          {%- if blog.moderated? -%}
            <p class="Form__Hint">{{ 'blog.comments.approval_notice' | t }}</p>
          {%- endif -%}          <button type="submit" class="Form__Submit Button Button--primary">{{ 'blog.comments.submit' | t }}</button>
        {%- endform -%}
      </div>
    {%- endif -%}
  </div>
  
  <div class="next-article-wrapper">
  <div class="Container Container--narrow" style="">
   
          <div class="col-50-wrapper">
            
              <div class="col col--50">
                
                
            
                {%- for article in blog.articles limit:2 -%}
                
                <div class="col article-min-wrapper">
                  <div class="article-block">
                  <a href="{{ article.url }}"></a>
                  <div class="block--image full-bg" style="background-image: url({{ article.image | img_url: '500x' }})">                  </div>
                  <div class="block--content">
                    <span class="article-tag p22 normal">{{ article.tags.first }}</span>
                    <h2 class="playfair">{{ article.title }}</h2>
                  </div>
                  </div>
                </div>
                
                {% endfor %}
                
                
              </div>
              
              <div class="col col--50">
                
                {%- for article in blog.articles limit:4 -%}
                  {% unless forloop.index0 < 2 %}
                  <div class="col article-min-wrapper">
                    <div class="article-block">
                    <a href="{{ article.url }}"></a>
                    <div class="block--image full-bg" style="background-image: url({{ article.image | img_url: '500x' }})">                    </div>
                    <div class="block--content">
                      <span class="article-tag p22 normal">{{ article.tags.first }}</span>
                      <h2 class="playfair">{{ article.title }}</h2>
                    </div>
                    </div>
                  </div>
                  {% endunless %}
                {% endfor %}
                
                
                
              </div>
            
          </div>
            
  </div>
  </div>
  
  
  
</article>{% if dm_paginate_by %}{% render 'spurit_dmr_collection_template_snippet', paginate_by: dm_paginate_by %}{% endif %}
{% section 'shop-now' %}
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		'{{ 'gem-article-556909985926.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->