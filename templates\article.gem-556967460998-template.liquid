{% comment %}
	GEMPAGE BUILDER (https://apps.shopify.com/gempage)

	You SHOULD NOT modify source code in this page because
	It is automatically generated from GEMPAGE BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->

<link data-instant-track rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.6.3/css/font-awesome.min.css" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-article-556967460998.css' | asset_url }}" class="gf_page_style">
<link data-instant-track class="gf_fonts" data-fonts="Gochi Hand" href="//fonts.googleapis.com/css2?family=Gochi Hand:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant Garamond" href="//fonts.googleapis.com/css2?family=Cormorant Garamond:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant" href="//fonts.googleapis.com/css2?family=Cormorant:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<!--GEM_HEADER_END-->
{%- assign share_url = shop.url | append: article.url -%}
{%- assign twitter_text = article.title -%}
{%- assign pinterest_description = article.description | strip_html | truncatewords: 15 | url_param_escape -%}
{%- assign pinterest_image = article.image | img_url: '750x' | prepend: 'https:' -%}<article class="Article" data-section-id="{{ section.id }}" data-section-type="article">
  <aside class="ArticleToolbar hidden-phone">
    <div class="ArticleToolbar__Left">
      <span class="Heading Text--subdued u-h8 hidden-tablet">{{ 'blog.article.now_reading' | t }}</span>
      <span class="ArticleToolbar__ArticleTitle Heading u-h7">{{ article.title }}</span>
    </div>    <div class="ArticleToolbar__Right">
      {%- if section.settings.show_share_buttons -%}
        <div class="ArticleToolbar__ShareList">
          <span class="ArticleToolbar__ShareLabel Heading Text--subdued u-h8">{{ 'blog.article.share' | t }}</span>          <div class="HorizontalList">
            <a class="HorizontalList__Item Text--subdued Link" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'facebook' -%}</a>
            <a class="HorizontalList__Item Text--subdued Link" href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'twitter' -%}</a>
            <a class="HorizontalList__Item Text--subdued Link" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener">{%- include 'icon' with 'pinterest' -%}</a>
          </div>
        </div>
      {%- endif -%}      {%- if blog.next_article or blog.previous_article -%}
        <div class="ArticleToolbar__Nav">
          {%- if blog.next_article -%}
            <a href="{{ blog.next_article }}" class="ArticleToolbar__NavItem ArticleToolbar__NavItem--next Heading Text--subdued Link u-h8">{% include 'icon' with 'select-arrow-left' %} {{ 'blog.article.previous' | t }}</a>
          {%- endif -%}          {%- if blog.previous_article and blog.next_article -%}
            <span class="ArticleToolbar__NavItemSeparator"></span>
          {%- endif -%}          {%- if blog.previous_article -%}
            <a href="{{ blog.previous_article }}" class="ArticleToolbar__NavItem ArticleToolbar__NavItem--prev Heading Text--subdued Link u-h8">{{ 'blog.article.next' | t }} {% include 'icon' with 'select-arrow-right' %}</a>
          {%- endif -%}
        </div>
      {%- endif -%}
    </div>
  </aside>  {%- if article.image and section.settings.show_article_image -%}
    <div class="Article__ImageWrapper" style="background-image: url({{ article.image | img_url: '1x1' }})">
      <div class="Article__Image Image--lazyLoad Image--fadeIn"
           data-optimumx="1.4"
           data-bgset="{{ article.image | img_url: '400x' }} 400w, {{ article.image | img_url: '600x' }} 600w, {{ article.image | img_url: '800x' }} 800w, {{ article.image | img_url: '1200x' }} 1200w, {{ article.image | img_url: '1400x' }} 1400w, {{ article.image | img_url: '1600x' }} 1600w">
      </div>
    </div>
  {%- endif -%}  <div class="Article__Wrapper">
    <div class="Article__Content">
      <header class="Article__Header">
        <p class="article-back"><button onclick="window.history.back()">< Go Back</button></p>
        {%- capture article_meta -%}
          {%- if section.settings.show_date -%}
            <span class="Article__MetaItem">{{ article.published_at | date: format: 'month_day_year' }}</span>
          {%- endif -%}          {%- if section.settings.show_category and article.tags != empty -%}
            <span class="Article__MetaItem">{{ article.tags.first }}</span>
          {%- endif -%}
        {%- endcapture -%}        {%- if article_meta != blank -%}
          <div class="Article__Meta Heading Text--subdued u-h6">
            {{- article_meta -}}
          </div>
        {%- endif -%}        <h1 class="Article__Title Heading u-h1">{{ article.title }}</h1>
      </header>      <div class="Article__Body Rte">
        <!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor">
<div data-label="Row" id="r-1666574669244" class="gf_row gf_row-gap-0 gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1666574669244" data-row-gap="0px" data-extraclass=""><div class="gf_col-md-12 gf_column" id="c-1542787188922" data-id="1542787188922" style="min-height: auto;"><div data-label="Hero Banner" data-key="hero-banner" id="m-1666574669178" class="module-wrap" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1666574669178"><div class="module " data-image="https://d1um8515vdn9kb.cloudfront.net/images/hero.jpg" data-image-lg="https://ucarecdn.com/c273a1da-455f-43ba-9845-d946fbd64252/-/format/auto/-/preview/3000x3000/-/quality/lighter/" data-image-md="https://ucarecdn.com/c273a1da-455f-43ba-9845-d946fbd64252/-/format/auto/-/preview/3000x3000/-/quality/lighter/" data-image-sm="https://ucarecdn.com/c273a1da-455f-43ba-9845-d946fbd64252/-/format/auto/-/preview/3000x3000/-/quality/lighter/" data-image-xs="https://ucarecdn.com/c273a1da-455f-43ba-9845-d946fbd64252/-/format/auto/-/preview/3000x3000/-/quality/lighter/" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" id="r-1666574669191" class="gf_row" data-icon="gpicon-row" data-id="1666574669191"><div class="gf_col-md-12 gf_column" id="c-1542787340914" data-id="1542787340914"><div data-label="Heading" id="e-1666574669124" class="element-wrap" data-icon="gpicon-heading" data-ver="1" data-id="1666574669124"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-lg gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">OUR STORY</h1></div></div><div data-label="Text Block" id="e-1666574669242" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666574669242"><div class="elm text-edit gf-elm-left gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p><font color="#ffffff">From humble beginnings as a coffee seed to dominating the landscape and lives of many a place and people, coffee in our opinion is one of the finest ingredients the world has to offer.</font></p></div></div><div data-label="Button" id="e-1666574669133" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="2" data-id="1666574669133"><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-exc="" style="padding-top: 0px; padding-bottom: 0px;"><span>Read more</span></a></div></div></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:#000000;opacity:0.4"></div></div></div></div><!--gfsplit--><div data-label="Row" id="r-1666574669252" class="gf_row" data-icon="gpicon-row" data-id="1666574669252" data-extraclass=""><div class="gf_column gf_col-md-6" id="c-1542787611695" data-id="1542787611695" style="min-height: auto;"><div data-label="Row" id="r-1666574669141" class="gf_row gf_row-gap-20" data-icon="gpicon-row" data-id="1666574669141" data-row-gap="20px" data-extraclass=""><div class="gf_col-md-12 gf_column" id="c-1542787683035" data-id="1542787683035" style="min-height: auto;"><div data-label="Heading" id="e-1666574669156" class="element-wrap" data-icon="gpicon-heading" data-ver="1" data-id="1666574669156"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-xs gf-elm-left-sm" data-gemlang="en" data-exc=""><h2 class="gf_gs-text-heading-2">FROM THE BEGINNING</h2></div></div><div data-label="Text Block" id="e-1666574669198" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666574669198"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>Our coffee story is much wider, much higher and much deeper than meets the eye. We began making coffee at Seven Wonders the first week of January 2003 on our first espresso machine. Less than 3 months later we had won the Irish Barista Championship (the main coffee making competition in the world by country) and were off to Boston to represent Ireland in the World Barista Championship.</p></div></div></div></div></div><div class="gf_column gf_col-md-6" id="c-1542787635624" data-id="1542787635624"><div data-label="Row" id="r-1666574669233" class="gf_row gf_row-gap-20" data-icon="gpicon-row" data-id="1666574669233" data-row-gap="20px" data-extraclass=""><div class="gf_col-md-12 gf_column" id="c-1542787763609" data-id="1542787763609" style="min-height: auto;"><div data-label="Image" id="e-1666574669273" class="element-wrap" data-icon="gpicon-image" data-ver="1" data-id="1666574669273"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/4d65a80f-ac60-4d02-a4a6-5b36fa134257/-/format/auto/-/preview/3000x3000/-/quality/lighter/" alt="" class="gf_image" data-gemlang="en"></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" id="r-1666574669136" class="gf_row" data-icon="gpicon-row" data-id="1666574669136" data-extraclass=""><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-12" id="c-1542788047730" data-id="1542788047730" style="min-height: auto;"><div data-label="Row" id="r-1666574669193" class="gf_row gf_row-gap-20" data-icon="gpicon-row" data-id="1666574669193" data-row-gap="20px" data-extraclass="" style="float: left;"><div class="gf_col-md-12 gf_column" id="c-1542787683035" data-id="1542787683035" style="min-height: auto;"><div data-label="Heading" id="e-1666574669163" class="element-wrap" data-icon="gpicon-heading" data-ver="1" data-id="1666574669163"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><h2 class="gf_gs-text-heading-2">A NEW EXPERIENCE</h2></div></div><div data-label="Text Block" id="e-1666574669159" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666574669159"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>Coffee tastes have changed so much that I remember around 2012. It's is after all the seed of a fruit. From then on, we began to work at a gradual pace, turning up the brightness a little, season after season. Today we can proudly craft a coffee with notes of lemon meringue, strawberry or passionfruit and find people on all sides of the counter are learning and developing their pallets.</p></div></div></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-12" id="c-1542788029687" data-id="1542788029687" style="min-height: auto;"><div data-label="Row" id="r-1666574669177" class="gf_row" data-icon="gpicon-row" data-id="1666574669177"><div class="gf_col-md-12 gf_column" id="c-1542788052652" data-id="1542788052652"><div data-label="Image" id="e-1666574669206" class="element-wrap" data-icon="gpicon-image" data-ver="1" data-id="1666574669206"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/e3170a17-7a43-46b2-b179-bddea0996258/-/format/auto/-/preview/3000x3000/-/quality/lighter/" alt="" class="gf_image" data-gemlang="en"></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" id="r-1666574669121" class="gf_row gf_row-gap-0 gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1666574669121" data-row-gap="0px" data-extraclass=""><div class="gf_col-md-12 gf_column" id="c-1542788117962" data-id="1542788117962" style="min-height: auto;"><div data-label="Hero Banner" id="m-1666574669139" class="module-wrap" data-icon="gpicon-herobanner" data-ver="1" data-id="1666574669139"><div class="module " data-image="https://d3dfaj4bukarbm.cloudfront.net/production/placeholders/banner-1.svg" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" id="r-1666574669219" class="gf_row" data-icon="gpicon-row" data-id="1666574669219"><div class="gf_col-md-12 gf_column" id="c-1542788204123" data-id="1542788204123"><div data-label="Heading" id="e-1666574669186" class="element-wrap" data-icon="gpicon-heading" data-ver="1" data-id="1666574669186"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h2 class="gf_gs-text-heading-2">SHARING OUR KNOWLEDGE</h2></div></div><div data-label="Text Block" id="e-1666574669185" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666574669185"><div class="elm text-edit gf-elm-left gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p><font color="#f7f6f6">We are committed not only to bringing you the best coffee we can find but also the best information about  its health benefits</font></p></div></div><div data-label="Button" id="e-1666574669194" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="2" data-id="1666574669194"><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-exc=""><span>read more</span></a></div></div></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:#ff4b03;opacity:0.8"></div></div></div></div><!--gfsplit--><script>window.__gemStoreData = {};window.__gemStoreData.gemKeyValid = {};window.__gemStoreData.gemKeyValid.bestWayAddToCart = 1;</script>
</div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->      </div>      {%- capture article_footer -%}
        {%- if section.settings.show_author -%}
          <span class="Article__Author Heading Text--subdued u-h6">{{ 'blog.article.written_by' | t: author: article.author }}</span>
        {%- endif -%}        {%- if section.settings.show_share_buttons -%}
          <div class="Article__ShareButtons ShareButtons">
            <a class="ShareButtons__Item ShareButtons__Item--facebook" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'facebook' -%}</a>
            <a class="ShareButtons__Item ShareButtons__Item--twitter" href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'twitter' -%}</a>
            <a class="ShareButtons__Item ShareButtons__Item--pinterest" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener">{%- include 'icon' with 'pinterest' -%}</a>
          </div>
        {%- endif -%}
      {%- endcapture -%}      {%- if article_footer != blank -%}
        <footer class="Article__Footer">
          {{ article_footer }}
        </footer>
      {%- endif -%}
    </div>    {%- if blog.comments_enabled? -%}
      {%- if article.comments_count > 0 -%}
        <div class="Article__Comments">
          <span class="Anchor" id="comments"></span>          <h2 class="Heading u-h1">{{ 'blog.article.comments_count' | t: count: article.comments_count }}</h2>          <div class="Article__CommentList">
            {%- paginate article.comments by 25 -%}
              {%- for comment in article.comments -%}
                <div class="ArticleComment">
                  <div class="ArticleComment__Body Rte">
                    {{ comment.content }}
                  </div>                  <div class="ArticleComment__Meta Heading Text--subdued u-h8">
                    <span class="ArticleComment__Author">{{ comment.author }}</span>
                    <span class="ArticleComment__Date">{{ comment.created_at | date: format: 'month_day_year' }}</span>
                  </div>
                </div>
              {%- endfor -%}              {% include 'pagination', hash: '#comments' %}
            {% assign dm_paginate_by = paginate.page_size %}{%- endpaginate -%}
          </div>
        </div>
      {%- endif -%}      <div class="Article__CommentFormWrapper">
        {% if article.comments_count == 0 %}
          <span class="Anchor" id="comments"></span>
        {%- endif -%}        <span class="Anchor" id="comment_form"></span>        <h2 class="Heading u-h1">{{ 'blog.comments.form_title' | t }}</h2>        {%- form 'new_comment', article, class: 'Article__CommentForm Form', id: '' -%}
          {%- if form.posted_successfully? -%}
            <p class="Form__Alert Alert Alert--success">
              {%- if blog.moderated? -%}
                {{- 'blog.comments.success_moderated' | t -}}
              {%- else -%}
                {{- 'blog.comments.success' | t -}}
              {%- endif -%}
            </p>
          {%- endif -%}          {%- if form.errors -%}
            <div class="Form__Alert Alert Alert--error">
              <ul class="Alert__ErrorList">
                {%- for field in form.errors -%}
                  {%- if field == 'form' -%}
                    <li class="Alert__ErrorItem">{{ form.errors.messages[field] }}</li>
                  {%- else -%}
                    <li class="Alert__ErrorItem"><strong>{{ form.errors.translated_fields[field] }}</strong> {{ form.errors.messages[field] }}</li>
                  {%- endif -%}
                {%- endfor -%}
              </ul>
            </div>
          {%- endif -%}          <div class="Form__Group">
            <div class="Form__Item">
              <input type="text" class="Form__Input" name="comment[author]" placeholder="{{ 'blog.comments.name_placeholder' | t }}" aria-label="{{ 'blog.comments.name_placeholder' | t }}" value="{{ form.author | escape | default: customer.name }}" required="required">
              <label class="Form__FloatingLabel">{{ 'blog.comments.name_placeholder' | t }}</label>
            </div>            <div class="Form__Item">
              <input type="email" class="Form__Input" name="comment[email]" placeholder="{{ 'blog.comments.email_placeholder' | t }}" aria-label="{{ 'blog.comments.email_placeholder' | t }}" value="{{ form.email | escape | default: customer.email }}" required="required">
              <label class="Form__FloatingLabel">{{ 'blog.comments.email_placeholder' | t }}</label>
            </div>
          </div>          <div class="Form__Item">
            <textarea name="comment[body]" rows="6" class="Form__Textarea" placeholder="{{ 'blog.comments.comment_placeholder' | t }}" aria-label="{{ 'blog.comments.comment_placeholder' | t }}" required="required">
              {{- form.body -}}
            </textarea>            <label class="Form__FloatingLabel">{{ 'blog.comments.comment_placeholder' | t }}</label>
          </div>          {%- if blog.moderated? -%}
            <p class="Form__Hint">{{ 'blog.comments.approval_notice' | t }}</p>
          {%- endif -%}          <button type="submit" class="Form__Submit Button Button--primary">{{ 'blog.comments.submit' | t }}</button>
        {%- endform -%}
      </div>
    {%- endif -%}
  </div>
  
  <div class="next-article-wrapper">
  <div class="Container Container--narrow" style="">
   
          <div class="col-50-wrapper">
            
              <div class="col col--50">
                
                
            
                {%- for article in blog.articles limit:2 -%}
                
                <div class="col article-min-wrapper">
                  <div class="article-block">
                  <a href="{{ article.url }}"></a>
                  <div class="block--image full-bg" style="background-image: url({{ article.image | img_url: '500x' }})">                  </div>
                  <div class="block--content">
                    <span class="article-tag p22 normal">{{ article.tags.first }}</span>
                    <h2 class="playfair">{{ article.title }}</h2>
                  </div>
                  </div>
                </div>
                
                {% endfor %}
                
                
              </div>
              
              <div class="col col--50">
                
                {%- for article in blog.articles limit:4 -%}
                  {% unless forloop.index0 < 2 %}
                  <div class="col article-min-wrapper">
                    <div class="article-block">
                    <a href="{{ article.url }}"></a>
                    <div class="block--image full-bg" style="background-image: url({{ article.image | img_url: '500x' }})">                    </div>
                    <div class="block--content">
                      <span class="article-tag p22 normal">{{ article.tags.first }}</span>
                      <h2 class="playfair">{{ article.title }}</h2>
                    </div>
                    </div>
                  </div>
                  {% endunless %}
                {% endfor %}
                
                
                
              </div>
            
          </div>
            
  </div>
  </div>
  
  
  
</article>{% if dm_paginate_by %}{% render 'spurit_dmr_collection_template_snippet', paginate_by: dm_paginate_by %}{% endif %}
{% section 'shop-now' %}
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		"https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv2herobanner.js",
		'{{ 'gem-article-556967460998.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->