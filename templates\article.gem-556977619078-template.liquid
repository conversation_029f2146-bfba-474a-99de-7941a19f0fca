{% comment %}
	GEMPAGE BUILDER (https://apps.shopify.com/gempage)

	You SHOULD NOT modify source code in this page because
	It is automatically generated from GEMPAGE BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->

<link data-instant-track rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.6.3/css/font-awesome.min.css" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-article-556977619078.css' | asset_url }}" class="gf_page_style">
<link data-instant-track class="gf_fonts" data-fonts="Gochi Hand" href="//fonts.googleapis.com/css2?family=Gochi Hand:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant Garamond" href="//fonts.googleapis.com/css2?family=Cormorant Garamond:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant" href="//fonts.googleapis.com/css2?family=Cormorant:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<!--GEM_HEADER_END-->
{%- assign share_url = shop.url | append: article.url -%}
{%- assign twitter_text = article.title -%}
{%- assign pinterest_description = article.description | strip_html | truncatewords: 15 | url_param_escape -%}
{%- assign pinterest_image = article.image | img_url: '750x' | prepend: 'https:' -%}<article class="Article" data-section-id="{{ section.id }}" data-section-type="article">
  <aside class="ArticleToolbar hidden-phone">
    <div class="ArticleToolbar__Left">
      <span class="Heading Text--subdued u-h8 hidden-tablet">{{ 'blog.article.now_reading' | t }}</span>
      <span class="ArticleToolbar__ArticleTitle Heading u-h7">{{ article.title }}</span>
    </div>    <div class="ArticleToolbar__Right">
      {%- if section.settings.show_share_buttons -%}
        <div class="ArticleToolbar__ShareList">
          <span class="ArticleToolbar__ShareLabel Heading Text--subdued u-h8">{{ 'blog.article.share' | t }}</span>          <div class="HorizontalList">
            <a class="HorizontalList__Item Text--subdued Link" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'facebook' -%}</a>
            <a class="HorizontalList__Item Text--subdued Link" href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'twitter' -%}</a>
            <a class="HorizontalList__Item Text--subdued Link" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener">{%- include 'icon' with 'pinterest' -%}</a>
          </div>
        </div>
      {%- endif -%}      {%- if blog.next_article or blog.previous_article -%}
        <div class="ArticleToolbar__Nav">
          {%- if blog.next_article -%}
            <a href="{{ blog.next_article }}" class="ArticleToolbar__NavItem ArticleToolbar__NavItem--next Heading Text--subdued Link u-h8">{% include 'icon' with 'select-arrow-left' %} {{ 'blog.article.previous' | t }}</a>
          {%- endif -%}          {%- if blog.previous_article and blog.next_article -%}
            <span class="ArticleToolbar__NavItemSeparator"></span>
          {%- endif -%}          {%- if blog.previous_article -%}
            <a href="{{ blog.previous_article }}" class="ArticleToolbar__NavItem ArticleToolbar__NavItem--prev Heading Text--subdued Link u-h8">{{ 'blog.article.next' | t }} {% include 'icon' with 'select-arrow-right' %}</a>
          {%- endif -%}
        </div>
      {%- endif -%}
    </div>
  </aside>  {%- if article.image and section.settings.show_article_image -%}
    <div class="Article__ImageWrapper" style="background-image: url({{ article.image | img_url: '1x1' }})">
      <div class="Article__Image Image--lazyLoad Image--fadeIn"
           data-optimumx="1.4"
           data-bgset="{{ article.image | img_url: '400x' }} 400w, {{ article.image | img_url: '600x' }} 600w, {{ article.image | img_url: '800x' }} 800w, {{ article.image | img_url: '1200x' }} 1200w, {{ article.image | img_url: '1400x' }} 1400w, {{ article.image | img_url: '1600x' }} 1600w">
      </div>
    </div>
  {%- endif -%}  <div class="Article__Wrapper">
    <div class="Article__Content">
      <header class="Article__Header">
        <p class="article-back"><button onclick="window.history.back()">< Go Back</button></p>
        {%- capture article_meta -%}
          {%- if section.settings.show_date -%}
            <span class="Article__MetaItem">{{ article.published_at | date: format: 'month_day_year' }}</span>
          {%- endif -%}          {%- if section.settings.show_category and article.tags != empty -%}
            <span class="Article__MetaItem">{{ article.tags.first }}</span>
          {%- endif -%}
        {%- endcapture -%}        {%- if article_meta != blank -%}
          <div class="Article__Meta Heading Text--subdued u-h6">
            {{- article_meta -}}
          </div>
        {%- endif -%}        <h1 class="Article__Title Heading u-h1">{{ article.title }}</h1>
      </header>      <div class="Article__Body Rte">
        <!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor">
<div data-label="Row" data-key="row" id="r-1666922300678" class="gf_row" data-icon="gpicon-row" data-id="1666922300678" data-extraclass=""><div class="gf_column gf_col-md-3 gf_col-sm-3 gf_col-xs-12 gf_col-lg-2" id="c-1614760291866" data-id="1614760291866"><div data-label="Row" data-key="row" id="r-1666922300708" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1666922300708" data-row-gap="0px" data-extraclass=""><div class="gf_col-lg-12 gf_column" id="c-1614760857017" data-id="1614760857017"><div data-label="Image" data-key="image" id="e-1666922300653" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666922300653" data-resolution="100x100"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-left-xs" data-exc=""><img src="https://ucarecdn.com/e0ea0887-ab38-4fe2-bc43-5c6c367e7ed5/-/format/auto/-/preview/100x100/-/quality/lighter/" alt="" class="gf_image" data-gemlang="en" data-width="70px" data-height="70px" title="" natural-width="100" natural-height="99"></div></div><div data-label="Text Block" data-key="text-block" id="e-1666922300762" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666922300762"><div class="elm text-edit gf-elm-left gf-elm-center-md gf-elm-center-sm gf-elm-left-xs gf-elm-center-lg gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p><strong>Lily Phi</strong></p></div></div><div data-label="Text Block" data-key="text-block" id="e-1666922300755" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666922300755"><div class="elm text-edit gf-elm-left gf-elm-center-md gf-elm-center-sm gf-elm-left-xs gf-elm-center-lg gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>April 29,2020</p></div></div></div></div></div><div class="gf_column gf_col-md-9 gf_col-sm-9 gf_col-xs-12 gf_col-lg-10" id="c-1614760295764" data-id="1614760295764"><div data-label="Row" data-key="row" id="r-1666922300646" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1666922300646" data-extraclass="" data-row-gap="0px"><div class="gf_col-lg-12 gf_column" id="c-1615178530067" data-id="1615178530067"><div data-label="Text Block" data-key="text-block" id="e-1666922300691" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666922300691"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>New Product: Blog</p></div></div><div data-label="Text Block" data-key="text-block" id="e-1666922300763" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666922300763"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p><p><span style="color: inherit; font-family: inherit; font-size: inherit; text-align: inherit; letter-spacing: 0px;"><br></span></p><p><span style="color: inherit; font-family: inherit; font-size: inherit; text-align: inherit; letter-spacing: 0px;">It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English.</span></p></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" id="r-1666922300633" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1666922300633" data-extraclass=""><div class="gf_col-lg-12 gf_column" id="c-1614761078620" data-id="1614761078620"><div data-label="Image" data-key="image" id="e-1666922300751" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666922300751" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/6d549178-007a-475c-b2f2-9e972b3653b7/-/format/auto/-/preview/3000x3000/-/quality/lighter/" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" natural-width="3000" natural-height="1561"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" id="r-1666922300693" class="gf_row" data-icon="gpicon-row" data-id="1666922300693" data-extraclass=""><div class="gf_column gf_col-md-3 gf_col-xs-12 gf_col-lg-3 gf_col-sm-12" id="c-1614760291866" data-id="1614760291866"><div data-label="Row" data-key="row" id="r-1666922300754" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1666922300754" data-row-gap="0px" data-extraclass=""><div class="gf_col-lg-12 gf_column" id="c-1614760857017" data-id="1614760857017"><div data-label="Image" data-key="image" id="e-1666922300705" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666922300705" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-right-lg gf-elm-right-md gf-elm-left-xs gf-elm-left-sm" data-exc=""><img src="https://ucarecdn.com/86fb0746-6c38-4b4d-acbd-8cbba01252a7/-/format/auto/-/preview/3000x3000/-/quality/lighter/" alt="" class="gf_image" data-gemlang="en" data-width="auto" data-height="auto" title="" natural-width="40" natural-height="28"></div></div><div data-label="Text Block" data-key="text-block" id="e-1666922300697" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666922300697"><div class="elm text-edit gf-elm-left gf-elm-right-lg gf-elm-right-md gf-elm-left-xs gf-elm-left-sm gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>“Treat your skin with 100% organic skincare product from GemSkin. Be beautiful, be natural, be you.”</p></div></div><div data-label="Separator" data-key="separator" id="e-1666922300721" class="element-wrap" data-icon="gpicon-separator" data-ver="1.0" data-id="1666922300721"><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-xs gf-elm-center-sm" data-align="left" data-exc=""><hr class="gf_separator"></div></div></div></div></div><div class="gf_column gf_col-md-9 gf_col-xs-12 gf_col-lg-9 gf_col-sm-12" id="c-1614760295764" data-id="1614760295764"><div data-label="Text Block" data-key="text-block" id="e-1666922300652" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666922300652"><div class="elm text-edit gf-elm-left gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf-elm-left-lg gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>About GemSkin</p></div></div><div data-label="Text Block" data-key="text-block" id="e-1666922300686" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666922300686"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p><p><span style="color: inherit; font-family: inherit; font-size: inherit; text-align: inherit; letter-spacing: 0px;"><br></span></p><p><span style="color: inherit; font-family: inherit; font-size: inherit; text-align: inherit; letter-spacing: 0px;">It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English.</span></p></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" id="r-1666922300643" class="gf_row" data-icon="gpicon-row" data-id="1666922300643" data-extraclass=""><div class="gf_col-lg-12 gf_column" id="c-1614761490772" data-id="1614761490772"><div data-label="Text Block" data-key="text-block" id="e-1666922300734" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666922300734"><div class="elm text-edit gf-elm-left gf-elm-center-md gf-elm-left-xs gf-elm-left-sm gf-elm-left-lg gf_gs-text-paragraph-1" data-gemlang="en" data-exc="">What's Included?</div></div><div data-label="Image" data-key="image" id="e-1666922300658" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666922300658" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/69ba5833-3625-42e6-81ca-09f926d4faa2/-/format/auto/-/preview/3000x3000/-/quality/lighter/" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" natural-width="1990" natural-height="1068"></div></div><div data-label="Row" data-key="row" id="r-1666922300700" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1666922300700" style="display: block;" data-row-gap="0px" data-extraclass=""><div class="gf_col-lg-12 gf_column" id="c-1614761652395" data-id="1614761652395"><div data-label="Icon List" data-key="icon-list" id="m-1666922300675" class="module-wrap" data-icon="gpicon-iconlist" data-ver="1.0" data-id="1666922300675" style="display: block;"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li style="margin-bottom: 15px"><span class="gf-il-icon item-content" data-index="1" data-key="content" style="width: 45px"><div data-label="Text Block" data-key="text-block" id="e-1666922300744" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666922300744"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>01</p></div></div></span><div class="gf-il-content item-content" data-index="1" data-key="content1" style="padding-left: 45px"><div data-label="Text Block" id="e-1666922300675-2" class="element-wrap" data-id="1666922300675-2"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-exc=""><p style="text-align: inherit!important;"><strong>GemSkin Oil-Free Face Wash</strong></p><p style="text-align: inherit!important;">This oil-free formula is clinically proven to clean deep down into pores gently while preventing future breakouts.</p></div></div></div></li><li style="margin-bottom: 15px"><span class="gf-il-icon item-content" data-index="2" data-key="content" style="width: 45px"><div data-label="Text Block" data-key="text-block" id="e-1666922300752" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666922300752"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>02</p></div></div></span><div class="gf-il-content item-content" data-index="2" data-key="content1" style="padding-left: 45px"><div data-label="Text Block" id="e-1666922300715" class="element-wrap" data-id="1666922300715"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-exc=""><p style="text-align: inherit!important;"><b>GemSkin Acne Control Toner</b></p><p style="text-align: inherit!important;">Treat your acne even before it emerges with this green tea extract toner which soothes your skin and reduces irritation.</p></div></div></div></li><li style="margin-bottom: 15px"><span class="gf-il-icon item-content" data-index="3" data-key="content" style="width: 45px"><div data-label="Text Block" data-key="text-block" id="e-1666922300687" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666922300687"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>03</p></div></div></span><div class="gf-il-content item-content" data-index="3" data-key="content1" style="padding-left: 45px"><div data-label="Text Block" id="e-1666922300673" class="element-wrap" data-id="1666922300673"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-exc=""><p style="text-align: inherit!important;"><b>GemSkin Hydrating Gel</b></p><p style="text-align: inherit!important;">With SPF 30, this should be your go-to moisturizer every day. Protective and suitable for sensitive acne-prone skin.</p></div></div></div></li></ul></div></div></div></div></div></div><!--gfsplit--><script>window.__gemStoreData = {};window.__gemStoreData.gemKeyValid = {};window.__gemStoreData.gemKeyValid.bestWayAddToCart = 1;</script>
</div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->      </div>      {%- capture article_footer -%}
        {%- if section.settings.show_author -%}
          <span class="Article__Author Heading Text--subdued u-h6">{{ 'blog.article.written_by' | t: author: article.author }}</span>
        {%- endif -%}        {%- if section.settings.show_share_buttons -%}
          <div class="Article__ShareButtons ShareButtons">
            <a class="ShareButtons__Item ShareButtons__Item--facebook" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'facebook' -%}</a>
            <a class="ShareButtons__Item ShareButtons__Item--twitter" href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'twitter' -%}</a>
            <a class="ShareButtons__Item ShareButtons__Item--pinterest" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener">{%- include 'icon' with 'pinterest' -%}</a>
          </div>
        {%- endif -%}
      {%- endcapture -%}      {%- if article_footer != blank -%}
        <footer class="Article__Footer">
          {{ article_footer }}
        </footer>
      {%- endif -%}
    </div>    {%- if blog.comments_enabled? -%}
      {%- if article.comments_count > 0 -%}
        <div class="Article__Comments">
          <span class="Anchor" id="comments"></span>          <h2 class="Heading u-h1">{{ 'blog.article.comments_count' | t: count: article.comments_count }}</h2>          <div class="Article__CommentList">
            {%- paginate article.comments by 25 -%}
              {%- for comment in article.comments -%}
                <div class="ArticleComment">
                  <div class="ArticleComment__Body Rte">
                    {{ comment.content }}
                  </div>                  <div class="ArticleComment__Meta Heading Text--subdued u-h8">
                    <span class="ArticleComment__Author">{{ comment.author }}</span>
                    <span class="ArticleComment__Date">{{ comment.created_at | date: format: 'month_day_year' }}</span>
                  </div>
                </div>
              {%- endfor -%}              {% include 'pagination', hash: '#comments' %}
            {% assign dm_paginate_by = paginate.page_size %}{%- endpaginate -%}
          </div>
        </div>
      {%- endif -%}      <div class="Article__CommentFormWrapper">
        {% if article.comments_count == 0 %}
          <span class="Anchor" id="comments"></span>
        {%- endif -%}        <span class="Anchor" id="comment_form"></span>        <h2 class="Heading u-h1">{{ 'blog.comments.form_title' | t }}</h2>        {%- form 'new_comment', article, class: 'Article__CommentForm Form', id: '' -%}
          {%- if form.posted_successfully? -%}
            <p class="Form__Alert Alert Alert--success">
              {%- if blog.moderated? -%}
                {{- 'blog.comments.success_moderated' | t -}}
              {%- else -%}
                {{- 'blog.comments.success' | t -}}
              {%- endif -%}
            </p>
          {%- endif -%}          {%- if form.errors -%}
            <div class="Form__Alert Alert Alert--error">
              <ul class="Alert__ErrorList">
                {%- for field in form.errors -%}
                  {%- if field == 'form' -%}
                    <li class="Alert__ErrorItem">{{ form.errors.messages[field] }}</li>
                  {%- else -%}
                    <li class="Alert__ErrorItem"><strong>{{ form.errors.translated_fields[field] }}</strong> {{ form.errors.messages[field] }}</li>
                  {%- endif -%}
                {%- endfor -%}
              </ul>
            </div>
          {%- endif -%}          <div class="Form__Group">
            <div class="Form__Item">
              <input type="text" class="Form__Input" name="comment[author]" placeholder="{{ 'blog.comments.name_placeholder' | t }}" aria-label="{{ 'blog.comments.name_placeholder' | t }}" value="{{ form.author | escape | default: customer.name }}" required="required">
              <label class="Form__FloatingLabel">{{ 'blog.comments.name_placeholder' | t }}</label>
            </div>            <div class="Form__Item">
              <input type="email" class="Form__Input" name="comment[email]" placeholder="{{ 'blog.comments.email_placeholder' | t }}" aria-label="{{ 'blog.comments.email_placeholder' | t }}" value="{{ form.email | escape | default: customer.email }}" required="required">
              <label class="Form__FloatingLabel">{{ 'blog.comments.email_placeholder' | t }}</label>
            </div>
          </div>          <div class="Form__Item">
            <textarea name="comment[body]" rows="6" class="Form__Textarea" placeholder="{{ 'blog.comments.comment_placeholder' | t }}" aria-label="{{ 'blog.comments.comment_placeholder' | t }}" required="required">
              {{- form.body -}}
            </textarea>            <label class="Form__FloatingLabel">{{ 'blog.comments.comment_placeholder' | t }}</label>
          </div>          {%- if blog.moderated? -%}
            <p class="Form__Hint">{{ 'blog.comments.approval_notice' | t }}</p>
          {%- endif -%}          <button type="submit" class="Form__Submit Button Button--primary">{{ 'blog.comments.submit' | t }}</button>
        {%- endform -%}
      </div>
    {%- endif -%}
  </div>
  
  <div class="next-article-wrapper">
  <div class="Container Container--narrow" style="">
   
          <div class="col-50-wrapper">
            
              <div class="col col--50">
                
                
            
                {%- for article in blog.articles limit:2 -%}
                
                <div class="col article-min-wrapper">
                  <div class="article-block">
                  <a href="{{ article.url }}"></a>
                  <div class="block--image full-bg" style="background-image: url({{ article.image | img_url: '500x' }})">                  </div>
                  <div class="block--content">
                    <span class="article-tag p22 normal">{{ article.tags.first }}</span>
                    <h2 class="playfair">{{ article.title }}</h2>
                  </div>
                  </div>
                </div>
                
                {% endfor %}
                
                
              </div>
              
              <div class="col col--50">
                
                {%- for article in blog.articles limit:4 -%}
                  {% unless forloop.index0 < 2 %}
                  <div class="col article-min-wrapper">
                    <div class="article-block">
                    <a href="{{ article.url }}"></a>
                    <div class="block--image full-bg" style="background-image: url({{ article.image | img_url: '500x' }})">                    </div>
                    <div class="block--content">
                      <span class="article-tag p22 normal">{{ article.tags.first }}</span>
                      <h2 class="playfair">{{ article.title }}</h2>
                    </div>
                    </div>
                  </div>
                  {% endunless %}
                {% endfor %}
                
                
                
              </div>
            
          </div>
            
  </div>
  </div>
  
  
  
</article>{% if dm_paginate_by %}{% render 'spurit_dmr_collection_template_snippet', paginate_by: dm_paginate_by %}{% endif %}
{% section 'shop-now' %}
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		'{{ 'gem-article-556977619078.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->