{% comment %}
	GEMPAGE BUILDER (https://apps.shopify.com/gempage)

	You SHOULD NOT modify source code in this page because
	It is automatically generated from GEMPAGE BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->

<link data-instant-track rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.6.3/css/font-awesome.min.css" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-article-556983746694.css' | asset_url }}" class="gf_page_style">
<link data-instant-track class="gf_fonts" data-fonts="Gochi Hand" href="//fonts.googleapis.com/css2?family=Gochi Hand:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant Garamond" href="//fonts.googleapis.com/css2?family=Cormorant Garamond:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant" href="//fonts.googleapis.com/css2?family=Cormorant:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<!--GEM_HEADER_END-->
{%- assign share_url = shop.url | append: article.url -%}
{%- assign twitter_text = article.title -%}
{%- assign pinterest_description = article.description | strip_html | truncatewords: 15 | url_param_escape -%}
{%- assign pinterest_image = article.image | img_url: '750x' | prepend: 'https:' -%}<article class="Article" data-section-id="{{ section.id }}" data-section-type="article">
  <aside class="ArticleToolbar hidden-phone">
    <div class="ArticleToolbar__Left">
      <span class="Heading Text--subdued u-h8 hidden-tablet">{{ 'blog.article.now_reading' | t }}</span>
      <span class="ArticleToolbar__ArticleTitle Heading u-h7">{{ article.title }}</span>
    </div>    <div class="ArticleToolbar__Right">
      {%- if section.settings.show_share_buttons -%}
        <div class="ArticleToolbar__ShareList">
          <span class="ArticleToolbar__ShareLabel Heading Text--subdued u-h8">{{ 'blog.article.share' | t }}</span>          <div class="HorizontalList">
            <a class="HorizontalList__Item Text--subdued Link" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'facebook' -%}</a>
            <a class="HorizontalList__Item Text--subdued Link" href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'twitter' -%}</a>
            <a class="HorizontalList__Item Text--subdued Link" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener">{%- include 'icon' with 'pinterest' -%}</a>
          </div>
        </div>
      {%- endif -%}      {%- if blog.next_article or blog.previous_article -%}
        <div class="ArticleToolbar__Nav">
          {%- if blog.next_article -%}
            <a href="{{ blog.next_article }}" class="ArticleToolbar__NavItem ArticleToolbar__NavItem--next Heading Text--subdued Link u-h8">{% include 'icon' with 'select-arrow-left' %} {{ 'blog.article.previous' | t }}</a>
          {%- endif -%}          {%- if blog.previous_article and blog.next_article -%}
            <span class="ArticleToolbar__NavItemSeparator"></span>
          {%- endif -%}          {%- if blog.previous_article -%}
            <a href="{{ blog.previous_article }}" class="ArticleToolbar__NavItem ArticleToolbar__NavItem--prev Heading Text--subdued Link u-h8">{{ 'blog.article.next' | t }} {% include 'icon' with 'select-arrow-right' %}</a>
          {%- endif -%}
        </div>
      {%- endif -%}
    </div>
  </aside>  {%- if article.image and section.settings.show_article_image -%}
    <div class="Article__ImageWrapper" style="background-image: url({{ article.image | img_url: '1x1' }})">
      <div class="Article__Image Image--lazyLoad Image--fadeIn"
           data-optimumx="1.4"
           data-bgset="{{ article.image | img_url: '400x' }} 400w, {{ article.image | img_url: '600x' }} 600w, {{ article.image | img_url: '800x' }} 800w, {{ article.image | img_url: '1200x' }} 1200w, {{ article.image | img_url: '1400x' }} 1400w, {{ article.image | img_url: '1600x' }} 1600w">
      </div>
    </div>
  {%- endif -%}  <div class="Article__Wrapper">
    <div class="Article__Content">
      <header class="Article__Header">
        <p class="article-back"><button onclick="window.history.back()">< Go Back</button></p>
        {%- capture article_meta -%}
          {%- if section.settings.show_date -%}
            <span class="Article__MetaItem">{{ article.published_at | date: format: 'month_day_year' }}</span>
          {%- endif -%}          {%- if section.settings.show_category and article.tags != empty -%}
            <span class="Article__MetaItem">{{ article.tags.first }}</span>
          {%- endif -%}
        {%- endcapture -%}        {%- if article_meta != blank -%}
          <div class="Article__Meta Heading Text--subdued u-h6">
            {{- article_meta -}}
          </div>
        {%- endif -%}        <h1 class="Article__Title Heading u-h1">{{ article.title }}</h1>
      </header>      <div class="Article__Body Rte">
        
<!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor">
<div data-label="Row" data-key="row" data-atomgroup="row" id="r-1667259520741" class="gf_row gf_row-fluid gf_row-no-padding gf_equal-height" data-icon="gpicon-row" data-id="1667259520741" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1667259520759" data-id="1667259520759" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1667259568427" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259568427"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p><u>Fashion</u></p></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1667259545091" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259545091"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt&nbsp;</p></div></div><div data-label="Row" data-key="row" id="r-1667259175793" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1667259175793" data-row-gap="0px" data-extraclass="" style="min-height: auto;"><div class="gf_col-lg-12 gf_column" id="c-1614760857017" data-id="1614760857017"><div data-label="Image" data-key="image" id="e-1667259175670" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1667259175670" data-resolution="100x100"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-left-xs" data-exc=""><img src="https://ucarecdn.com/e0ea0887-ab38-4fe2-bc43-5c6c367e7ed5/-/format/auto/-/preview/100x100/-/quality/lighter/" alt="" class="gf_image" data-gemlang="en" data-width="70px" data-height="70px" title="" natural-width="100" natural-height="99"></div></div><div data-label="Text Block" data-key="text-block" id="e-1667259175787" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259175787"><div class="elm text-edit gf-elm-left gf-elm-center-md gf-elm-center-sm gf-elm-left-xs gf-elm-center-lg gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p><strong>Lily Phi</strong></p></div></div><div data-label="Text Block" data-key="text-block" id="e-1667259175794" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259175794"><div class="elm text-edit gf-elm-left gf-elm-center-md gf-elm-center-sm gf-elm-left-xs gf-elm-center-lg gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>April 29,2020</p></div></div></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1667259522058" data-id="1667259522058" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1667259547955" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1667259547955"><div class="elm gf-elm-center gf_elm-left-xs"><img src="//d1um8515vdn9kb.cloudfront.net/images/parallax.jpg" alt="" class="gf_image" data-gemlang="en"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" id="r-1667259175750" class="gf_row" data-icon="gpicon-row" data-id="1667259175750" data-extraclass="" data-layout-lg="12" data-layout-md="12" data-layout-sm="12" data-layout-xs="12" style="display: block;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1614760291866" data-id="1614760291866"><div data-label="Row" data-key="row" id="r-1667259175778" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1667259175778" data-extraclass="" data-row-gap="0px" data-layout-lg="12" data-layout-md="12" data-layout-sm="12" data-layout-xs="12"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1615178530067" data-id="1615178530067"><div data-label="Text Block" data-key="text-block" id="e-1667259175710" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259175710"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>Gifting Guide Gone Good</p></div></div><div data-label="Text Block" data-key="text-block" id="e-1667259175811" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259175811"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p><p><span style="color: inherit; font-family: inherit; font-size: inherit; text-align: inherit; letter-spacing: 0px;"><br></span></p><p><span style="color: inherit; font-family: inherit; font-size: inherit; text-align: inherit; letter-spacing: 0px;">It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English.</span></p></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" id="r-1667259175709" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1667259175709" data-extraclass=""><div class="gf_col-lg-12 gf_column" id="c-1614761078620" data-id="1614761078620"><div data-label="Image" data-key="image" id="e-1667259175807" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1667259175807" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/7e301bbe-1953-4add-9abf-414ce8e40c5b/-/format/auto/-/preview/3000x3000/-/quality/lighter/gift-boxes-2021-08-26-15-48-01-utc.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" natural-width="1920" natural-height="618" width="1920" height="618"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" id="r-1667259175764" class="gf_row" data-icon="gpicon-row" data-id="1667259175764" data-extraclass=""><div class="gf_column gf_col-md-3 gf_col-xs-12 gf_col-lg-3 gf_col-sm-12" id="c-1614760291866" data-id="1614760291866"><div data-label="Row" data-key="row" id="r-1667259175760" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1667259175760" data-row-gap="0px" data-extraclass=""><div class="gf_col-lg-12 gf_column" id="c-1614760857017" data-id="1614760857017"><div data-label="Image" data-key="image" id="e-1667259175785" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1667259175785" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-right-lg gf-elm-right-md gf-elm-left-xs gf-elm-left-sm" data-exc=""><img src="https://ucarecdn.com/86fb0746-6c38-4b4d-acbd-8cbba01252a7/-/format/auto/-/preview/3000x3000/-/quality/lighter/" alt="" class="gf_image" data-gemlang="en" data-width="auto" data-height="auto" title="" natural-width="40" natural-height="28"></div></div><div data-label="Text Block" data-key="text-block" id="e-1667259175752" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259175752"><div class="elm text-edit gf-elm-left gf-elm-right-lg gf-elm-right-md gf-elm-left-xs gf-elm-left-sm gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>"Got a quote, omg you should so put it here"</p></div></div><div data-label="Separator" data-key="separator" id="e-1667259175706" class="element-wrap" data-icon="gpicon-separator" data-ver="1.0" data-id="1667259175706"><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-xs gf-elm-center-sm" data-align="left" data-exc=""><hr class="gf_separator"></div></div></div></div></div><div class="gf_column gf_col-md-9 gf_col-xs-12 gf_col-lg-9 gf_col-sm-12" id="c-1614760295764" data-id="1614760295764"><div data-label="Text Block" data-key="text-block" id="e-1667259175758" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259175758"><div class="elm text-edit gf-elm-left gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf-elm-left-lg gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>About Super Cool Stuff</p></div></div><div data-label="Text Block" data-key="text-block" id="e-1667259175736" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259175736"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p><p><span style="color: inherit; font-family: inherit; font-size: inherit; text-align: inherit; letter-spacing: 0px;"><br></span></p><p><span style="color: inherit; font-family: inherit; font-size: inherit; text-align: inherit; letter-spacing: 0px;">It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English.</span></p></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1667259334467" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1667259334467" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1646034700825" data-id="1646034700825"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1667259334531" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1667259334531" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1646272865563" data-id="1646272865563"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1667259334536" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259334536"><div class="elm text-edit gf-elm-left gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>Best Choice For You</p></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1667259334507" class="element-wrap" data-icon="gpicon-heading" data-ver="1" data-id="1667259334507"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-xs gf-elm-center-sm" data-gemlang="en" data-exc=""><h2 class="gf_gs-text-heading-2"><strong>Featured Products</strong></h2></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1667259334554" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1667259334554" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="12+12" data-layout-xs="12+12" data-row-gap="0px"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-12 gf_col-xs-12" id="c-1646035064499" data-id="1646035064499"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1667259334594" class="gf_row" data-icon="gpicon-row" data-id="1667259334594" data-extraclass="" data-layout-lg="6+6" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="6+6"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-6" id="c-1646277955835" data-id="1646277955835"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1667259334533" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1667259334533" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="40004044750982" style="">{% assign product = all_products['test-bundle-111'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1667259334486" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1667259334486" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1646306981338" data-id="1646306981338"><div class="module-wrap" id="m-1667259334533-0" data-id="1667259334533-0" data-label="(P) Image" data-icon="gpicon-product-image" data-ver="1.1"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="1">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '1' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif videoMedia and videoMedia.sources %}{% capture imageCapture %}<video controls="" autoplay="" muted="" data-id="{{videoMedia.id}}" class="gf_product-image-thumb gf_product-video-thumb gf_video-only" style="width: 100%">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video><img class="gf_product-image gf_featured-image gf_hidden-important" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if '0' == '1' %}<a href="/products/{{ product.handle }}" class="img-holder">{% endif %}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>Best value </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images.last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{% if '0' == '1' %}</a>{% endif %}</div></div><div class="module-wrap" id="m-1667259334533-1" data-id="1667259334533-1" data-label="(P) Title" data-icon="gpicon-product-title" data-ver="1.0"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div data-label="Icon List Hoz" data-key="icon-list-hoz" data-atomgroup="module" id="m-1667259334480" class="module-wrap" data-icon="gpicon-iconlist2" data-ver="1.0" data-id="1667259334480"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li class="item" style="width: 18px;"><div data-index="1" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334480-1" class="element-wrap" data-icon="eicon-post" data-id="1667259334480-1"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 18px;"><div data-index="2" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334602" class="element-wrap" data-icon="eicon-post" data-id="1667259334602"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 18px;"><div data-index="3" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334505" class="element-wrap" data-icon="eicon-post" data-id="1667259334505"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 18px;"><div data-index="4" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334502" class="element-wrap" data-icon="eicon-post" data-id="1667259334502"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 18px;"><div data-index="5" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334511" class="element-wrap" data-icon="eicon-post" data-id="1667259334511"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li></ul></div></div><div class="module-wrap" id="m-1667259334533-2" data-id="1667259334533-2" data-label="(P) Price" data-icon="gpicon-product-price" data-ver="1.4"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div data-label="(P) Cart Button" data-key="p-cart-button" data-atomgroup="child-product" id="m-1667259334543" class="module-wrap" data-icon="gpicon-product-cartbutton" data-ver="1" data-id="1667259334543"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module-left-sm gf_module-left-xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="1">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-6" id="c-1646277968996" data-id="1646277968996"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1667259334492" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1667259334492" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="40004044750982" style="">{% assign product = all_products['test-bundle-111'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1667259334450" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1667259334450" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1646306981338" data-id="1646306981338"><div class="module-wrap" id="m-1667259334492-0" data-id="1667259334492-0" data-label="(P) Image" data-icon="gpicon-product-image" data-ver="1.1"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="1">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '1' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif videoMedia and videoMedia.sources %}{% capture imageCapture %}<video controls="" autoplay="" muted="" data-id="{{videoMedia.id}}" class="gf_product-image-thumb gf_product-video-thumb gf_video-only" style="width: 100%">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video><img class="gf_product-image gf_featured-image gf_hidden-important" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if '0' == '1' %}<a href="/products/{{ product.handle }}" class="img-holder">{% endif %}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>Most popular </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images.last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{% if '0' == '1' %}</a>{% endif %}</div></div><div class="module-wrap" id="m-1667259334492-1" data-id="1667259334492-1" data-label="(P) Title" data-icon="gpicon-product-title" data-ver="1.0"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div data-label="Icon List Hoz" data-key="icon-list-hoz" data-atomgroup="module" id="m-1667259334488" class="module-wrap" data-icon="gpicon-iconlist2" data-ver="1.0" data-id="1667259334488"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li class="item" style="width: 18px;"><div data-index="1" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334488-1" class="element-wrap" data-icon="eicon-post" data-id="1667259334488-1"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 18px;"><div data-index="2" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334542" class="element-wrap" data-icon="eicon-post" data-id="1667259334542"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 18px;"><div data-index="3" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334514" class="element-wrap" data-icon="eicon-post" data-id="1667259334514"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 18px;"><div data-index="4" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334457" class="element-wrap" data-icon="eicon-post" data-id="1667259334457"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 18px;"><div data-index="5" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334495" class="element-wrap" data-icon="eicon-post" data-id="1667259334495"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li></ul></div></div><div class="module-wrap" id="m-1667259334492-2" data-id="1667259334492-2" data-label="(P) Price" data-icon="gpicon-product-price" data-ver="1.4"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div data-label="(P) Cart Button" data-key="p-cart-button" data-atomgroup="child-product" id="m-1667259334590" class="module-wrap" data-icon="gpicon-product-cartbutton" data-ver="1" data-id="1667259334590"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module-left-sm gf_module-left-xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="1">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-12 gf_col-xs-12" id="c-1646035226587" data-id="1646035226587"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1667259334460" class="gf_row" data-icon="gpicon-row" data-id="1667259334460" data-extraclass="" data-layout-lg="6+6" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="6+6"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-6" id="c-1646277955835" data-id="1646277955835"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1667259334565" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1667259334565" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="40004044750982">{% assign product = all_products['test-bundle-111'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1667259334549" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1667259334549" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1646306981338" data-id="1646306981338"><div data-label="(P) Image" data-key="p-image" data-atomgroup="child-product" id="m-1667259334601" class="module-wrap" data-icon="gpicon-product-image" data-ver="1.1" data-id="1667259334601"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif videoMedia and videoMedia.sources %}{% capture imageCapture %}<video controls="" autoplay="" muted="" data-id="{{videoMedia.id}}" class="gf_product-image-thumb gf_product-video-thumb gf_video-only" style="width: 100%">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video><img class="gf_product-image gf_featured-image gf_hidden-important" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if '0' == '1' %}<a href="/products/{{ product.handle }}" class="img-holder">{% endif %}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images.last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{% if '0' == '1' %}</a>{% endif %}</div></div><div class="module-wrap" id="m-1667259334565-1" data-id="1667259334565-1" data-label="(P) Title" data-icon="gpicon-product-title" data-ver="1.0"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div data-label="Icon List Hoz" data-key="icon-list-hoz" data-atomgroup="module" id="m-1667259334494" class="module-wrap" data-icon="gpicon-iconlist2" data-ver="1.0" data-id="1667259334494"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li class="item" style="width: 18px;"><div data-index="1" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334494-1" class="element-wrap" data-icon="eicon-post" data-id="1667259334494-1"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 18px;"><div data-index="2" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334469" class="element-wrap" data-icon="eicon-post" data-id="1667259334469"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 18px;"><div data-index="3" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334475" class="element-wrap" data-icon="eicon-post" data-id="1667259334475"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 18px;"><div data-index="4" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334572" class="element-wrap" data-icon="eicon-post" data-id="1667259334572"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 18px;"><div data-index="5" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334491" class="element-wrap" data-icon="eicon-post" data-id="1667259334491"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li></ul></div></div><div class="module-wrap" id="m-1667259334565-2" data-id="1667259334565-2" data-label="(P) Price" data-icon="gpicon-product-price" data-ver="1.4"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div data-label="(P) Cart Button" data-key="p-cart-button" data-atomgroup="child-product" id="m-1667259334571" class="module-wrap" data-icon="gpicon-product-cartbutton" data-ver="1" data-id="1667259334571"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module-left-sm gf_module-left-xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="1">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-6" id="c-1646277968996" data-id="1646277968996"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1667259334599" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1667259334599" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="40004044750982">{% assign product = all_products['test-bundle-111'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1667259334562" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1667259334562" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1646306981338" data-id="1646306981338"><div data-label="(P) Image" data-key="p-image" data-atomgroup="child-product" id="m-1667259334556" class="module-wrap" data-icon="gpicon-product-image" data-ver="1.1" data-id="1667259334556"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif videoMedia and videoMedia.sources %}{% capture imageCapture %}<video controls="" autoplay="" muted="" data-id="{{videoMedia.id}}" class="gf_product-image-thumb gf_product-video-thumb gf_video-only" style="width: 100%">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video><img class="gf_product-image gf_featured-image gf_hidden-important" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if '0' == '1' %}<a href="/products/{{ product.handle }}" class="img-holder">{% endif %}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images.last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{% if '0' == '1' %}</a>{% endif %}</div></div><div class="module-wrap" id="m-1667259334599-1" data-id="1667259334599-1" data-label="(P) Title" data-icon="gpicon-product-title" data-ver="1.0"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div data-label="Icon List Hoz" data-key="icon-list-hoz" data-atomgroup="module" id="m-1667259334589" class="module-wrap" data-icon="gpicon-iconlist2" data-ver="1.0" data-id="1667259334589"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li class="item" style="width: 18px;"><div data-index="1" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334589-1" class="element-wrap" data-icon="eicon-post" data-id="1667259334589-1"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 18px;"><div data-index="2" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334482" class="element-wrap" data-icon="eicon-post" data-id="1667259334482"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 18px;"><div data-index="3" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334551" class="element-wrap" data-icon="eicon-post" data-id="1667259334551"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 18px;"><div data-index="4" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334493" class="element-wrap" data-icon="eicon-post" data-id="1667259334493"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 18px;"><div data-index="5" data-key="content" class="item-content"><div data-label="Icon" id="e-1667259334524" class="element-wrap" data-icon="eicon-post" data-id="1667259334524"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap" style="color: rgb(250, 173, 20);"><i class="gf_icon fa fa-star"></i></div></div></div></div></li></ul></div></div><div class="module-wrap" id="m-1667259334599-2" data-id="1667259334599-2" data-label="(P) Price" data-icon="gpicon-product-price" data-ver="1.4"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div data-label="(P) Cart Button" data-key="p-cart-button" data-atomgroup="child-product" id="m-1667259334603" class="module-wrap" data-icon="gpicon-product-cartbutton" data-ver="1" data-id="1667259334603"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module-left-sm gf_module-left-xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="1">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" id="r-1667259175823" class="gf_row" data-icon="gpicon-row" data-id="1667259175823" data-extraclass=""><div class="gf_col-lg-12 gf_column" id="c-1614761490772" data-id="1614761490772"><div data-label="Text Block" data-key="text-block" id="e-1667259175791" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259175791"><div class="elm text-edit gf-elm-left gf-elm-center-md gf-elm-left-xs gf-elm-left-sm gf-elm-left-lg gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>Why do more when you can do less?</p></div></div><div data-label="Image" data-key="image" id="e-1667259175723" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1667259175723" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/4ccf7cce-5742-42da-91e3-59080a3632f2/-/format/auto/-/preview/3000x3000/-/quality/lighter/Artboard%20%E2%80%93%208.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" natural-width="1280" natural-height="566" width="1280" height="566"></div></div><div data-label="Row" data-key="row" id="r-1667259728251" class="gf_row" data-icon="gpicon-row" data-id="1667259728251" data-extraclass=""><div class="gf_column gf_col-md-3 gf_col-xs-12 gf_col-lg-3 gf_col-sm-12" id="c-1614760291866" data-id="1614760291866"><div data-label="Row" data-key="row" id="r-1667259728329" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1667259728329" data-row-gap="0px" data-extraclass=""><div class="gf_col-lg-12 gf_column" id="c-1614760857017" data-id="1614760857017"><div data-label="Image" data-key="image" id="e-1667259728239" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1667259728239" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-right-lg gf-elm-right-md gf-elm-left-xs gf-elm-left-sm" data-exc=""><img src="https://ucarecdn.com/86fb0746-6c38-4b4d-acbd-8cbba01252a7/-/format/auto/-/preview/3000x3000/-/quality/lighter/" alt="" class="gf_image" data-gemlang="en" data-width="auto" data-height="auto" title="" natural-width="40" natural-height="28"></div></div><div data-label="Text Block" data-key="text-block" id="e-1667259728295" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259728295"><div class="elm text-edit gf-elm-left gf-elm-right-lg gf-elm-right-md gf-elm-left-xs gf-elm-left-sm gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>"Got a quote, omg you should so put it here"</p></div></div><div data-label="Separator" data-key="separator" id="e-1667259728305" class="element-wrap" data-icon="gpicon-separator" data-ver="1.0" data-id="1667259728305"><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-xs gf-elm-center-sm" data-align="left" data-exc=""><hr class="gf_separator"></div></div></div></div></div><div class="gf_column gf_col-md-9 gf_col-xs-12 gf_col-lg-9 gf_col-sm-12" id="c-1614760295764" data-id="1614760295764"><div data-label="Text Block" data-key="text-block" id="e-1667259728290" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259728290"><div class="elm text-edit gf-elm-left gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf-elm-left-lg gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>About Super Cool Stuff</p></div></div><div data-label="Text Block" data-key="text-block" id="e-1667259728291" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259728291"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p><p><span style="color: inherit; font-family: inherit; font-size: inherit; text-align: inherit; letter-spacing: 0px;"><br></span></p><p><span style="color: inherit; font-family: inherit; font-size: inherit; text-align: inherit; letter-spacing: 0px;">It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English.</span></p></div></div></div></div><div data-label="Row" data-key="row" id="r-1667259175748" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1667259175748" style="display: block;" data-row-gap="0px" data-extraclass=""><div class="gf_col-lg-12 gf_column" id="c-1614761652395" data-id="1614761652395"><div data-label="Icon List" data-key="icon-list" id="m-1667259175775" class="module-wrap" data-icon="gpicon-iconlist" data-ver="1.0" data-id="1667259175775" style="display: block;"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li style="margin-bottom: 15px"><span class="gf-il-icon item-content" data-index="1" data-key="content" style="width: 45px"><div data-label="Text Block" data-key="text-block" id="e-1667259175828" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259175828"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>01</p></div></div></span><div class="gf-il-content item-content" data-index="1" data-key="content1" style="padding-left: 45px"><div data-label="Text Block" id="e-1667259175775-2" class="element-wrap" data-id="1667259175775-2"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-exc=""><p style="text-align: inherit!important;"><strong>GemSkin Oil-Free Face Wash</strong></p><p style="text-align: inherit!important;">This oil-free formula is clinically proven to clean deep down into pores gently while preventing future breakouts.</p></div></div></div></li><li style="margin-bottom: 15px"><span class="gf-il-icon item-content" data-index="2" data-key="content" style="width: 45px"><div data-label="Text Block" data-key="text-block" id="e-1667259175733" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259175733"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>02</p></div></div></span><div class="gf-il-content item-content" data-index="2" data-key="content1" style="padding-left: 45px"><div data-label="Text Block" id="e-1667259175721" class="element-wrap" data-id="1667259175721"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-exc=""><p style="text-align: inherit!important;"><b>GemSkin Acne Control Toner</b></p><p style="text-align: inherit!important;">Treat your acne even before it emerges with this green tea extract toner which soothes your skin and reduces irritation.</p></div></div></div></li><li style="margin-bottom: 15px"><span class="gf-il-icon item-content" data-index="3" data-key="content" style="width: 45px"><div data-label="Text Block" data-key="text-block" id="e-1667259175755" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259175755"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>03</p></div></div></span><div class="gf-il-content item-content" data-index="3" data-key="content1" style="padding-left: 45px"><div data-label="Text Block" id="e-1667259175714" class="element-wrap" data-id="1667259175714"><div class="elm text-edit gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-paragraph-1" data-exc=""><p style="text-align: inherit!important;"><b>GemSkin Hydrating Gel</b></p><p style="text-align: inherit!important;">With SPF 30, this should be your go-to moisturizer every day. Protective and suitable for sensitive acne-prone skin.</p></div></div></div></li></ul></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" class="gf_row gf_row-gap-0" id="r-1667259755450" data-icon="gpicon-row" data-id="1667259755450" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-md-12 gf_col-sm-12 gf_col-xs-12 gf_col-lg-12" id="c-1667259755460" data-id="1667259755460"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1667259909576" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259909576"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p><strong>Love Our Blogs Read More Articles Now!</strong></p></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1667259939591" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1667259939591"><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>Read More</span></a></div></div></div></div><!--gfsplit--><script>window.__gemStoreData = {};window.__gemStoreData.gemKeyValid = {};window.__gemStoreData.gemKeyValid.bestWayAddToCart = 1;</script>
</div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->
      </div>      {%- capture article_footer -%}
        {%- if section.settings.show_author -%}
          <span class="Article__Author Heading Text--subdued u-h6">{{ 'blog.article.written_by' | t: author: article.author }}</span>
        {%- endif -%}        {%- if section.settings.show_share_buttons -%}
          <div class="Article__ShareButtons ShareButtons">
            <a class="ShareButtons__Item ShareButtons__Item--facebook" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'facebook' -%}</a>
            <a class="ShareButtons__Item ShareButtons__Item--twitter" href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'twitter' -%}</a>
            <a class="ShareButtons__Item ShareButtons__Item--pinterest" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener">{%- include 'icon' with 'pinterest' -%}</a>
          </div>
        {%- endif -%}
      {%- endcapture -%}      {%- if article_footer != blank -%}
        <footer class="Article__Footer">
          {{ article_footer }}
        </footer>
      {%- endif -%}
    </div>    {%- if blog.comments_enabled? -%}
      {%- if article.comments_count > 0 -%}
        <div class="Article__Comments">
          <span class="Anchor" id="comments"></span>          <h2 class="Heading u-h1">{{ 'blog.article.comments_count' | t: count: article.comments_count }}</h2>          <div class="Article__CommentList">
            {%- paginate article.comments by 25 -%}
              {%- for comment in article.comments -%}
                <div class="ArticleComment">
                  <div class="ArticleComment__Body Rte">
                    {{ comment.content }}
                  </div>                  <div class="ArticleComment__Meta Heading Text--subdued u-h8">
                    <span class="ArticleComment__Author">{{ comment.author }}</span>
                    <span class="ArticleComment__Date">{{ comment.created_at | date: format: 'month_day_year' }}</span>
                  </div>
                </div>
              {%- endfor -%}              {% include 'pagination', hash: '#comments' %}
            {% assign dm_paginate_by = paginate.page_size %}{%- endpaginate -%}
          </div>
        </div>
      {%- endif -%}      <div class="Article__CommentFormWrapper">
        {% if article.comments_count == 0 %}
          <span class="Anchor" id="comments"></span>
        {%- endif -%}        <span class="Anchor" id="comment_form"></span>        <h2 class="Heading u-h1">{{ 'blog.comments.form_title' | t }}</h2>        {%- form 'new_comment', article, class: 'Article__CommentForm Form', id: '' -%}
          {%- if form.posted_successfully? -%}
            <p class="Form__Alert Alert Alert--success">
              {%- if blog.moderated? -%}
                {{- 'blog.comments.success_moderated' | t -}}
              {%- else -%}
                {{- 'blog.comments.success' | t -}}
              {%- endif -%}
            </p>
          {%- endif -%}          {%- if form.errors -%}
            <div class="Form__Alert Alert Alert--error">
              <ul class="Alert__ErrorList">
                {%- for field in form.errors -%}
                  {%- if field == 'form' -%}
                    <li class="Alert__ErrorItem">{{ form.errors.messages[field] }}</li>
                  {%- else -%}
                    <li class="Alert__ErrorItem"><strong>{{ form.errors.translated_fields[field] }}</strong> {{ form.errors.messages[field] }}</li>
                  {%- endif -%}
                {%- endfor -%}
              </ul>
            </div>
          {%- endif -%}          <div class="Form__Group">
            <div class="Form__Item">
              <input type="text" class="Form__Input" name="comment[author]" placeholder="{{ 'blog.comments.name_placeholder' | t }}" aria-label="{{ 'blog.comments.name_placeholder' | t }}" value="{{ form.author | escape | default: customer.name }}" required="required">
              <label class="Form__FloatingLabel">{{ 'blog.comments.name_placeholder' | t }}</label>
            </div>            <div class="Form__Item">
              <input type="email" class="Form__Input" name="comment[email]" placeholder="{{ 'blog.comments.email_placeholder' | t }}" aria-label="{{ 'blog.comments.email_placeholder' | t }}" value="{{ form.email | escape | default: customer.email }}" required="required">
              <label class="Form__FloatingLabel">{{ 'blog.comments.email_placeholder' | t }}</label>
            </div>
          </div>          <div class="Form__Item">
            <textarea name="comment[body]" rows="6" class="Form__Textarea" placeholder="{{ 'blog.comments.comment_placeholder' | t }}" aria-label="{{ 'blog.comments.comment_placeholder' | t }}" required="required">
              {{- form.body -}}
            </textarea>            <label class="Form__FloatingLabel">{{ 'blog.comments.comment_placeholder' | t }}</label>
          </div>          {%- if blog.moderated? -%}
            <p class="Form__Hint">{{ 'blog.comments.approval_notice' | t }}</p>
          {%- endif -%}          <button type="submit" class="Form__Submit Button Button--primary">{{ 'blog.comments.submit' | t }}</button>
        {%- endform -%}
      </div>
    {%- endif -%}
  </div>
  
  <div class="next-article-wrapper">
  <div class="Container Container--narrow" style="">
   
          <div class="col-50-wrapper">
            
              <div class="col col--50">
                
                
            
                {%- for article in blog.articles limit:2 -%}
                
                <div class="col article-min-wrapper">
                  <div class="article-block">
                  <a href="{{ article.url }}"></a>
                  <div class="block--image full-bg" style="background-image: url({{ article.image | img_url: '500x' }})">                  </div>
                  <div class="block--content">
                    <span class="article-tag p22 normal">{{ article.tags.first }}</span>
                    <h2 class="playfair">{{ article.title }}</h2>
                  </div>
                  </div>
                </div>
                
                {% endfor %}
                
                
              </div>
              
              <div class="col col--50">
                
                {%- for article in blog.articles limit:4 -%}
                  {% unless forloop.index0 < 2 %}
                  <div class="col article-min-wrapper">
                    <div class="article-block">
                    <a href="{{ article.url }}"></a>
                    <div class="block--image full-bg" style="background-image: url({{ article.image | img_url: '500x' }})">                    </div>
                    <div class="block--content">
                      <span class="article-tag p22 normal">{{ article.tags.first }}</span>
                      <h2 class="playfair">{{ article.title }}</h2>
                    </div>
                    </div>
                  </div>
                  {% endunless %}
                {% endfor %}
                
                
                
              </div>
            
          </div>
            
  </div>
  </div>
  
  
  
</article>{% if dm_paginate_by %}{% render 'spurit_dmr_collection_template_snippet', paginate_by: dm_paginate_by %}{% endif %}
{% section 'shop-now' %}
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		"https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv3product.js",
		 "https://www.youtube.com/player_api",
		'{{ 'gem-article-556983746694.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->