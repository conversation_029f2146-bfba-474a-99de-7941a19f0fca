{% comment %}
	GEMPAGES BUILDER (https://apps.shopify.com/gempages)

	You SHOULD NOT modify source code in this file because
	It is automatically generated from GEMPAGES BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->


<link rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/libs/css/fontawesome-4.6.3.1.min.css" class="gf-style">
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-article-559137783942.css' | asset_url }}" class="gf_page_style">
<link data-instant-track class="gf_fonts" data-fonts="Gochi Hand" href="//fonts.googleapis.com/css2?family=Gochi Hand:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant Garamond" href="//fonts.googleapis.com/css2?family=Cormorant Garamond:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant" href="//fonts.googleapis.com/css2?family=Cormorant:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Bebas Neue" href="//fonts.googleapis.com/css2?family=Bebas Neue:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Anton" href="//fonts.googleapis.com/css2?family=Anton:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Aladin" href="//fonts.googleapis.com/css2?family=Aladin:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Acme" href="//fonts.googleapis.com/css2?family=Acme:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Alfa Slab One" href="//fonts.googleapis.com/css2?family=Alfa Slab One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Allan" href="//fonts.googleapis.com/css2?family=Allan:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Boogaloo" href="//fonts.googleapis.com/css2?family=Boogaloo:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Dancing Script" href="//fonts.googleapis.com/css2?family=Dancing Script:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Albert Sans" href="//fonts.googleapis.com/css2?family=Albert Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Dela Gothic One" href="//fonts.googleapis.com/css2?family=Dela Gothic One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Gasoek One" href="//fonts.googleapis.com/css2?family=Gasoek One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Lora" href="//fonts.googleapis.com/css2?family=Lora:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Agbalumo" href="//fonts.googleapis.com/css2?family=Agbalumo:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<!--GEM_HEADER_END-->
{%- assign share_url = shop.url | append: article.url -%}
{%- assign twitter_text = article.title -%}
{%- assign pinterest_description = article.description | strip_html | truncatewords: 15 | url_param_escape -%}
{%- assign pinterest_image = article.image | img_url: '750x' | prepend: 'https:' -%}<article class="Article" data-section-id="{{ section.id }}" data-section-type="article">
  <aside class="ArticleToolbar hidden-phone">
    <div class="ArticleToolbar__Left">
      <span class="Heading Text--subdued u-h8 hidden-tablet">{{ 'blog.article.now_reading' | t }}</span>
      <span class="ArticleToolbar__ArticleTitle Heading u-h7">{{ article.title }}</span>
    </div>    <div class="ArticleToolbar__Right">
      {%- if section.settings.show_share_buttons -%}
        <div class="ArticleToolbar__ShareList">
          <span class="ArticleToolbar__ShareLabel Heading Text--subdued u-h8">{{ 'blog.article.share' | t }}</span>          <div class="HorizontalList">
            <a class="HorizontalList__Item Text--subdued Link" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'facebook' -%}</a>
            <a class="HorizontalList__Item Text--subdued Link" href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'twitter' -%}</a>
            <a class="HorizontalList__Item Text--subdued Link" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener">{%- include 'icon' with 'pinterest' -%}</a>
          </div>
        </div>
      {%- endif -%}      {%- if blog.next_article or blog.previous_article -%}
        <div class="ArticleToolbar__Nav">
          {%- if blog.next_article -%}
            <a href="{{ blog.next_article }}" class="ArticleToolbar__NavItem ArticleToolbar__NavItem--next Heading Text--subdued Link u-h8">{% include 'icon' with 'select-arrow-left' %} {{ 'blog.article.previous' | t }}</a>
          {%- endif -%}          {%- if blog.previous_article and blog.next_article -%}
            <span class="ArticleToolbar__NavItemSeparator"></span>
          {%- endif -%}          {%- if blog.previous_article -%}
            <a href="{{ blog.previous_article }}" class="ArticleToolbar__NavItem ArticleToolbar__NavItem--prev Heading Text--subdued Link u-h8">{{ 'blog.article.next' | t }} {% include 'icon' with 'select-arrow-right' %}</a>
          {%- endif -%}
        </div>
      {%- endif -%}
    </div>
  </aside>  {%- if article.image and section.settings.show_article_image -%}
    <div class="Article__ImageWrapper" style="background-image: url({{ article.image | img_url: '1x1' }})">
      <div class="Article__Image Image--lazyLoad Image--fadeIn"
           data-optimumx="1.4"
           data-bgset="{{ article.image | img_url: '400x' }} 400w, {{ article.image | img_url: '600x' }} 600w, {{ article.image | img_url: '800x' }} 800w, {{ article.image | img_url: '1200x' }} 1200w, {{ article.image | img_url: '1400x' }} 1400w, {{ article.image | img_url: '1600x' }} 1600w">
      </div>
    </div>
  {%- endif -%}  <div class="Article__Wrapper">
    <div class="Article__Content">
      <header class="Article__Header">
        <p class="article-back"><button onclick="window.history.back()">< Go Back</button></p>
        {%- capture article_meta -%}
          {%- if section.settings.show_date -%}
            <span class="Article__MetaItem">{{ article.published_at | date: format: 'month_day_year' }}</span>
          {%- endif -%}          {%- if section.settings.show_category and article.tags != empty -%}
            <span class="Article__MetaItem">{{ article.tags.first }}</span>
          {%- endif -%}
        {%- endcapture -%}        {%- if article_meta != blank -%}
          <div class="Article__Meta Heading Text--subdued u-h6">
            {{- article_meta -}}
          </div>
        {%- endif -%}        <h1 class="Article__Title Heading u-h1">{{ article.title }}</h1>
      </header>      <div class="Article__Body Rte">
        
<!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1667259520741" class="gf_row gf_row-fluid gf_row-no-padding gf_equal-height gf_row-gap-0" data-icon="gpicon-row" data-id="1667259520741" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible;" data-row-gap="0px"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1667259520759" data-id="1667259520759" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1667259568427" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259568427"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p><u>Community</u></p></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1667259545091" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259545091"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>Meet Gomeroi Artist Caitlin Trindall</p></div></div><div data-label="Row" data-key="row" id="r-1667259175793" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1667259175793" data-row-gap="0px" data-extraclass="" style="min-height: auto;"><div class="gf_col-lg-12 gf_column" id="c-1614760857017" data-id="1614760857017"><div data-label="Text Block" data-key="text-block" id="e-1667259175787" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259175787" style="display: block;"><div class="elm text-edit gf-elm-left gf-elm-center-md gf-elm-center-sm gf-elm-center-lg gf_gs-text-paragraph-1 gf-elm-center-xs" data-gemlang="en" data-exc=""><p><br></p></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1722551897783" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1722551897783"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>INTERVIEWED BY TEAGAN KUM SING</p><p>EDITED BY LAURA HALL</p></div></div><div data-label="Text Block" data-key="text-block" id="e-1667259175794" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259175794" style=""><div class="elm text-edit gf-elm-left gf-elm-center-md gf-elm-center-sm gf-elm-center-lg gf_gs-text-paragraph-1 gf-elm-center-xs" data-gemlang="en" data-exc=""><p>25 SEPTEMBER 2024</p></div></div></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1667259522058" data-id="1667259522058" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1667259547955" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1667259547955" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><a href="https://www.yarn.com.au/pages/gift-shop" target="" aria-label="Go to https://www.yarn.com.au/pages/gift-shop"><img src="https://ucarecdn.com/57d1cf83-f066-4af3-994b-cc387e174dad/-/format/auto/-/preview/3000x3000/-/quality/lighter/caitlin-trindall-artist-termite-mound-country.jpg" alt="Caitlin Trindall stood in front of a huge termite mound " class="gf_image" data-gemlang="en" data-width="75%" data-height="auto" title="" width="3000" height="3000" natural-width="3000" natural-height="3000" loading="lazy"></a></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" id="r-1667804684251" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1667804684251" data-extraclass="" data-layout-lg="12" data-layout-md="12" data-layout-sm="12" data-layout-xs="12" style="display: block;" data-row-gap="0px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1614760291866" data-id="1614760291866"><div data-label="Row" data-key="row" id="r-1687828871776" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1687828871776" data-extraclass="" data-row-gap="0px" data-layout-lg="12" data-layout-md="12" data-layout-sm="12" data-layout-xs="12"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1615178530067" data-id="1615178530067"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1720486739649" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1720486739649"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><p>There’s nothing so fabulous as featuring our incredible collaborating artists! Today we're sharing a conversation with Gomeroi artist <a href="https://www.yarn.com.au/pages/artist-caitlin-trindall" class="" target="_blank" aria-label="Go to https://www.yarn.com.au/pages/artist-caitlin-trindall">Caitlin Trindall</a>.&nbsp;<span style="color: inherit; font-size: inherit; font-weight: inherit; letter-spacing: 0px; text-align: inherit; font-family: inherit; outline: none;">Currently travelling full-time across Australia in her van with her family while juggling her successful business,&nbsp;</span><a href="https://www.miriiart.com/" class="" target="_blank" style="font-size: inherit; font-weight: inherit; letter-spacing: 0px; text-align: inherit; font-family: inherit; background-color: rgb(255, 255, 255);" aria-label="Go to https://www.miriiart.com/"><u>Mirii Art</u></a><span style="color: inherit; font-size: inherit; font-weight: inherit; letter-spacing: 0px; text-align: inherit; font-family: inherit;">, she’s doing what many people dream of.&nbsp;</span><span style="color: inherit; font-family: inherit; font-size: inherit; font-weight: inherit; text-align: inherit; letter-spacing: 0px;">We</span><span style="color: inherit; font-family: inherit; font-size: inherit; font-weight: inherit; text-align: inherit; letter-spacing: 0px; outline: none;">&nbsp;interviewed Caitlin about her new polo and fashion collection, Grounded in Country, and how she manages her family and career while on the road.&nbsp;</span></p><p><br></p><p>Caitlin creates art inspired by her own life experiences and brings First Nations Culture and art into new spaces. She elegantly combines traditional symbols to share stories and messages with bright, vibrant colours to create a modern, contemporary feel. She is passionate about protecting and sharing Culture, and&nbsp;<span style="color: inherit; font-family: inherit; font-size: inherit; font-weight: inherit; text-align: inherit; letter-spacing: 0px;">delivers creative workshops within the community, embedded with Cultural knowledge, to ensure Aboriginal knowledge and traditions are passed on to future generations.&nbsp;</span></p><p><br></p><p><i>This interview has been edited for clarity.&nbsp;</i></p><p><br></p></div></div><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1668058586036" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1668058586036" data-resolution="3000x3000" style=""><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/6637baa0-6bed-4e2e-8722-df095e21bc56/-/format/auto/-/preview/3000x3000/-/quality/lighter/Caitlin%20Trindall%20Prolfile%20Images28.jpg" alt="Caitlin Trindall sat in front of grass and a canvas holding paint brushes" class="gf_image" data-gemlang="en" width="3000" height="2143" data-width="100%" data-height="auto" title="" natural-width="3000" natural-height="2143" loading="lazy"></div></div></div></div><div data-label="Row" data-key="row" id="r-1720486681825" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1720486681825" data-extraclass="" data-row-gap="0px" data-layout-lg="12" data-layout-md="12" data-layout-sm="12" data-layout-xs="12"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1615178530067" data-id="1615178530067"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1722208528680" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1722208528680"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-left-lg" data-gemlang="en" data-exc=""><h3 class="gf_gs-text-heading-2">What themes does this collection include?</h3></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1722216707979" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1722216707979" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-md gf-elm-left-sm gf-elm-left-lg gf-elm-left-xs" data-gemlang="en" data-exc=""><div>Our Country deserves to be celebrated and honoured each and every day. Grounded in Country showcases elements of Country that remind me of some of my happiest moments.&nbsp;</div><div><br></div><div>This collection represents a strong sense of connection to Country; our connection to the land, the water, the sky, our people, as well as the beautiful flora and fauna that share Country with us.</div></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1727218473741" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1727218473741"><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/pages/grounded-in-country-caitlin-trindall" target="_blank" data-scroll-speed="2000" data-exc="" aria-label="Go to https://www.yarn.com.au/pages/grounded-in-country-caitlin-trindall"><span>Discover Grounded in Country Collection</span></a></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1727159790120" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1727159790120"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-left-lg" data-gemlang="en" data-exc=""><h3 class="gf_gs-text-heading-2">Is there a message you want to communicate with this?</h3></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1727159812297" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1727159812297" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-md gf-elm-left-sm gf-elm-left-lg gf-elm-left-xs" data-gemlang="en" data-exc="">Take time to appreciate all that Country has to offer. Ground your feet, breathe deeply, and allow yourself to tap into your senses and feel connected to Country. I want these pieces to be a conversation starter; for people to explore, learn and <a href="https://www.yarn.com.au/blogs/yarn-in-the-community/caring-for-country-a-place-we-call-home" class="" target="_blank" aria-label="Go to https://www.yarn.com.au/blogs/yarn-in-the-community/caring-for-country-a-place-we-call-home">respect Country</a>.</div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1727159835378" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1727159835378"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-left-lg" data-gemlang="en" data-exc=""><h3 class="gf_gs-text-heading-2">How did it feel to create these pieces?</h3></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1727159841676" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1727159841676" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-md gf-elm-left-sm gf-elm-left-lg gf-elm-left-xs" data-gemlang="en" data-exc="">Creating these pieces took me to my happy place. When I’m out on Country, camping among some of the most beautiful landscapes and making memories with my family and our friends. When I’m healing through the sun’s warmth, enjoying the sights of the flowers and fruits blooming, and resetting with every wave of water washing over me.&nbsp;<div><br></div><div>This collection makes me feel grounded, centred, and at peace.</div><div><br></div></div></div><div data-label="Youtube" data-key="youtube" data-atomgroup="module" id="m-1727223491163" class="module-wrap" data-icon="gpicon-youtube" data-ver="1" data-id="1727223491163"><div class="module gf_module- " data-url="https://www.youtube.com/watch?v=We1AI8K4uv4" data-width="" data-height="" data-responsive="1" data-sound="1" data-autoplay="0" data-loop="1" data-controls="1" data-showinfo="" data-modestbranding="0" data-fs="1" data-rel="" data-hd="" data-start="" data-end=""></div><div class="gf_youtube-overlay"></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1727159904608" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1727159904608"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-left-lg" data-gemlang="en" data-exc=""><h3 class="gf_gs-text-heading-2">How do you want people to feel when they wear your clothing collection?</h3></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1727159988326" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1727159988326" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-md gf-elm-left-sm gf-elm-left-lg gf-elm-left-xs" data-gemlang="en" data-exc="">My hope is that people wearing these pieces will feel empowered and inspired. I’d like to think people wearing them will feel a sense of pride and connection, and radiate positive energy. I want them to feel the way I felt when I created them: grounded, centred and at peace.</div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1727160253458" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1727160253458"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-left-lg" data-gemlang="en" data-exc=""><h3 class="gf_gs-text-heading-2">The photo shoot for Grounded in Country was taken in Darwin. How was that experience?</h3></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1727160339167" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1727160339167" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-md gf-elm-left-sm gf-elm-left-lg gf-elm-left-xs" data-gemlang="en" data-exc="">I had the most wonderful day shooting my latest collection ‘Grounded on&nbsp;<span style="letter-spacing: 0px;">Country’ on Larrakia Country in Darwin! This was a new experience for me&nbsp;</span><span style="letter-spacing: 0px;">and I didn’t know what to expect.&nbsp;</span><span style="letter-spacing: 0px;">The <a href="https://www.harryvick.com/" class="" target="_blank" aria-label="Go to https://www.harryvick.com/">photographer Harry</a> was so easy to work with.&nbsp;</span><div><span style="letter-spacing: 0px;"><br></span></div><div><span style="letter-spacing: 0px;">And the models, Tiesha and Nyasha, were beautiful inside and out. It was such a full circle moment to be on the road with my family, working with the team to showcase my designs on a clothing range. Having Harry document such precious moments with my family is a memory I won’t ever forget.&nbsp;</span></div></div></div><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1720410178582" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1720410178582" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/20a2f2bc-ade5-40eb-bcea-52a3f7422ba4/-/format/auto/-/preview/3000x3000/-/quality/lighter/grounded-on-country-caitlin-trindall-models-collection.jpg" alt="Caitlin Trindall stood between 2 models wearing clothes with her artwork" class="gf_image" data-gemlang="en" width="3000" height="2143" data-width="100%" data-height="auto" title="" natural-width="3000" natural-height="2143" loading="lazy"></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1727159702751" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1727159702751"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-gemlang="en" data-exc=""><h2 class="gf_gs-text-heading-2">Travelling & Grounding in Country</h2></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1724220273187" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1724220273187" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-md gf-elm-left-sm gf-elm-left-lg gf-elm-left-xs" data-gemlang="en" data-exc=""><div>While living remotely, Caitlin balances her family life with managing her business. Travelling with her husband and young son is surely a challenge, but one worth taking on.&nbsp;</div><div>&nbsp;</div><div>We asked her some questions about her experiences <a href="https://www.yarn.com.au/blogs/yarn-in-the-community/travelling-across-australia-through-indigenous-songlines-1" class="" target="_blank" aria-label="Go to https://www.yarn.com.au/blogs/yarn-in-the-community/travelling-across-australia-through-indigenous-songlines-1">travelling Country</a>. Here, she gives us a little insight into her work/life balance.&nbsp;</div></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1727160409230" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1727160409230"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-left-lg" data-gemlang="en" data-exc=""><h3 class="gf_gs-text-heading-2">How did the idea to get on the road start?</h3></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1727160428718" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1727160428718" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-md gf-elm-left-sm gf-elm-left-lg gf-elm-left-xs" data-gemlang="en" data-exc=""><div>We did a 4-month camping trip with our friends a couple of years ago. It was the most incredible and life-changing experience.&nbsp;</div><div><br></div><div>We fell pregnant during that trip, and we always had in mind that we’d continue to travel and adventure with our little one. These experiences are so important to us, and we knew we wanted to instil these values with our son too.&nbsp;</div><div><br></div><div>After lots and lots of conversations, planning, budgeting, and spreadsheets, we chose a date to leave and hustled to make it happen. We had to make big life changes and compromises to get to this point. But it’s so worth it to make our dreams a reality!</div><div><br></div></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1727160496233" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1727160496233"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-left-lg" data-gemlang="en" data-exc=""><h3 class="gf_gs-text-heading-2">Do you have any goals for your trip?</h3></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1727160502821" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1727160502821" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-md gf-elm-left-sm gf-elm-left-lg gf-elm-left-xs" data-gemlang="en" data-exc=""><div>First and foremost, my goal is to slow down and be fully present in my role as a Mum to our beautiful son, Jacob. In terms of <a href="https://www.miriiart.com/" class="" target="_blank" aria-label="Go to https://www.miriiart.com/">Mirii Art</a>, I am hoping to use this as an opportunity to elevate my business.&nbsp;</div><div><br></div><div>I am constantly inspired by Country and my experiences travelling each day and I am loving creating new designs reflecting this. Think fresh, bright, summer vibes!</div></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1727160599539" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1727160599539"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-left-lg" data-gemlang="en" data-exc=""><h3 class="gf_gs-text-heading-2">What it is like to travel with a baby?</h3></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1727160609165" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1727160609165" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-md gf-elm-left-sm gf-elm-left-lg gf-elm-left-xs" data-gemlang="en" data-exc="">Beautiful chaos haha.<div><br></div><div>Our son turned one the week before we left and we didn’t know what to expect. It took a couple of weeks on the road to get into a groove with a new routine.&nbsp;</div><div><br></div><div>We have plenty of activities for him in the car. And when we stop, Country is his playground. He’s constantly on the go, climbing and chatting away all day long.&nbsp;</div><div><br></div><div>It’s amazing watching him learn through this experience. We love doing life on the road with him.</div></div></div><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1727220344314" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1727220344314" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/8f2a0a13-9e5d-4a5b-b70e-dc72a32dab6e/-/format/auto/-/preview/3000x3000/-/quality/lighter/cailtin-trindall-family-husband-baby.jpg" alt="Caitlin holding a scarf with her artwork next to her husband and baby " class="gf_image" data-gemlang="en" width="3000" height="2143" data-width="100%" data-height="auto" title="" natural-width="3000" natural-height="2143" loading="lazy"></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1727160565046" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1727160565046"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-left-lg" data-gemlang="en" data-exc=""><h3 class="gf_gs-text-heading-2">Where are you heading?</h3></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1727160578456" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1727160578456" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-md gf-elm-left-sm gf-elm-left-lg gf-elm-left-xs" data-gemlang="en" data-exc=""><div>We started from home in Sydney (<a href="https://www.yarn.com.au/blogs/yarn-in-the-community/tourism-australia-to-adopt-dual-place-name-approach-for-major-cities-and-tourist-hotspots" class="" target="_blank" aria-label="Go to https://www.yarn.com.au/blogs/yarn-in-the-community/tourism-australia-to-adopt-dual-place-name-approach-for-major-cities-and-tourist-hotspots">Warrane</a>) and have made our way through Darwin (Gulumerrdgen) and towards Broome (Rubibi). We’ve given ourselves 6 months to play with, and we’re going with the flow, finding beautiful hidden gems each day. Who knows where we’ll end up next…</div></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1727160630067" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1727160630067"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-left-lg" data-gemlang="en" data-exc=""><h3 class="gf_gs-text-heading-2">How do you manage your time and work on the road?</h3></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1727160645997" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1727160645997" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-md gf-elm-left-sm gf-elm-left-lg gf-elm-left-xs" data-gemlang="en" data-exc="">Being a business owner, I often find it hard to switch off from work. I’ve made a conscious effort on this trip to give myself ‘work-free time’ so I can enjoy precious moments with my family and friends.&nbsp;<div><br></div><div>I do a lot of my admin and project work of an evening once my son is asleep. My online meetings take place anytime, anywhere; from riverbeds in remote bushlands to caravan parks, outback pubs, and roadside stops.</div></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1727160681984" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1727160681984"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-left-lg" data-gemlang="en" data-exc=""><h3 class="gf_gs-text-heading-2">What moments inspire you to take time to paint, and where are you finding the most inspiration from?</h3></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1727160700958" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1727160700958" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-md gf-elm-left-sm gf-elm-left-lg gf-elm-left-xs" data-gemlang="en" data-exc="">Being a mixed media artist, a lot of the work I’m doing on the road is digitally designed with my iPad. My artworks are always inspired by Country, and I’m constantly inspired by the beautiful landscapes of the places I’m visiting.<div><br></div><div>I take time for ‘mindfulness moments’ on Country, and when I connect with a place that makes me feel grounded, or energised, or cleansed, that’s when I’m most inspired. I try to capture these moments and emotions in each of my artworks.</div><div><br></div></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1727220201992" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1727220201992"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/caitlin-trindall-grounded-country-collection" target="_blank" data-scroll-speed="2000" data-exc="" aria-label="Go to https://www.yarn.com.au/collections/caitlin-trindall-grounded-country-collection" role="button"><span>Explore Caitlin's Collection</span></a></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1727160724227" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1727160724227"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-gemlang="en" data-exc=""><h2 class="gf_gs-text-heading-2">Travelling & Grounding in Country</h2></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1727160739240" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1727160739240" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-md gf-elm-left-sm gf-elm-left-lg gf-elm-left-xs" data-gemlang="en" data-exc="">Caitlin’s new clothing collection is already a best-seller. Check out her new four artworks inspired by her experiences, now available on polos, blouses, scarves, and more. Each sale in this collection pays royalties to Caitlin, supporting her career and creativity.&nbsp;<br><div><br></div></div></div><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1727221693746" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1727221693746" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/09128275-030d-481d-a67d-9ed408e53623/-/format/auto/-/preview/3000x3000/-/quality/lighter/cailtin-trindall-sand-bush-tucker-gathering-scarf.jpg" alt="Caitlin holding a scarf with her artwork on the beach next to a river " class="gf_image" data-gemlang="en" width="3000" height="2143" data-width="100%" data-height="auto" title="" natural-width="3000" natural-height="2143" loading="lazy"></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" class="gf_row gf_row-gap-0" id="r-1667259755450" data-icon="gpicon-row" data-id="1667259755450" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-md-12 gf_col-sm-12 gf_col-xs-12 gf_col-lg-12" id="c-1667259755460" data-id="1667259755460"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1667259909576" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667259909576"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p><strong>Love Our Blogs? Read More Articles Now!</strong></p></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1667437660178" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1667437660178"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>Yarn Blog is here to bring you the latest and greatest in First Nations Culture,&nbsp; news, and shopping. You can find updates on all our best products as well as info about our fantastic collaborating artists right here. Click below to start reading more of our exciting posts!</p></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1722298995492" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1722298995492"><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/blogs/yarn-in-the-community" target="_blank" data-scroll-speed="2000" data-exc="" aria-label="Go to https://www.yarn.com.au/blogs/yarn-in-the-community"><span>Read More</span></a></div></div></div></div><!--gfsplit--><script>window.__gemStoreData = {};window.__gemStoreData.gemKeyValid = {};window.__gemStoreData.gemKeyValid.bestWayAddToCart = 1;</script></div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->
      </div>      {%- capture article_footer -%}
        {%- if section.settings.show_author -%}
          <span class="Article__Author Heading Text--subdued u-h6">{{ 'blog.article.written_by' | t: author: article.author }}</span>
        {%- endif -%}        {%- if section.settings.show_share_buttons -%}
          <div class="Article__ShareButtons ShareButtons">
            <a class="ShareButtons__Item ShareButtons__Item--facebook" href="https://www.facebook.com/sharer.php?u={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'facebook' -%}</a>
            <a class="ShareButtons__Item ShareButtons__Item--twitter" href="https://twitter.com/share?{% if twitter_text != blank %}text={{twitter_text}}&{% endif %}url={{ share_url }}" target="_blank" rel="noopener">{%- include 'icon' with 'twitter' -%}</a>
            <a class="ShareButtons__Item ShareButtons__Item--pinterest" href="https://pinterest.com/pin/create/button/?url={{ share_url }}{% if pinterest_image != blank %}&media={{ pinterest_image }}{% endif %}&description={{ pinterest_description }}" target="_blank" rel="noopener">{%- include 'icon' with 'pinterest' -%}</a>
          </div>
        {%- endif -%}
      {%- endcapture -%}      {%- if article_footer != blank -%}
        <footer class="Article__Footer">
          {{ article_footer }}
        </footer>
      {%- endif -%}
    </div>    {%- if blog.comments_enabled? -%}
      {%- if article.comments_count > 0 -%}
        <div class="Article__Comments">
          <span class="Anchor" id="comments"></span>          <h2 class="Heading u-h1">{{ 'blog.article.comments_count' | t: count: article.comments_count }}</h2>          <div class="Article__CommentList">
            {%- paginate article.comments by 25 -%}
              {%- for comment in article.comments -%}
                <div class="ArticleComment">
                  <div class="ArticleComment__Body Rte">
                    {{ comment.content }}
                  </div>                  <div class="ArticleComment__Meta Heading Text--subdued u-h8">
                    <span class="ArticleComment__Author">{{ comment.author }}</span>
                    <span class="ArticleComment__Date">{{ comment.created_at | date: format: 'month_day_year' }}</span>
                  </div>
                </div>
              {%- endfor -%}              {% include 'pagination', hash: '#comments' %}
            {% assign dm_paginate_by = paginate.page_size %}{%- endpaginate -%}
          </div>
        </div>
      {%- endif -%}      <div class="Article__CommentFormWrapper">
        {% if article.comments_count == 0 %}
          <span class="Anchor" id="comments"></span>
        {%- endif -%}        <span class="Anchor" id="comment_form"></span>        <h2 class="Heading u-h1">{{ 'blog.comments.form_title' | t }}</h2>        {%- form 'new_comment', article, class: 'Article__CommentForm Form', id: '' -%}
          {%- if form.posted_successfully? -%}
            <p class="Form__Alert Alert Alert--success">
              {%- if blog.moderated? -%}
                {{- 'blog.comments.success_moderated' | t -}}
              {%- else -%}
                {{- 'blog.comments.success' | t -}}
              {%- endif -%}
            </p>
          {%- endif -%}          {%- if form.errors -%}
            <div class="Form__Alert Alert Alert--error">
              <ul class="Alert__ErrorList">
                {%- for field in form.errors -%}
                  {%- if field == 'form' -%}
                    <li class="Alert__ErrorItem">{{ form.errors.messages[field] }}</li>
                  {%- else -%}
                    <li class="Alert__ErrorItem"><strong>{{ form.errors.translated_fields[field] }}</strong> {{ form.errors.messages[field] }}</li>
                  {%- endif -%}
                {%- endfor -%}
              </ul>
            </div>
          {%- endif -%}          <div class="Form__Group">
            <div class="Form__Item">
              <input type="text" class="Form__Input" name="comment[author]" placeholder="{{ 'blog.comments.name_placeholder' | t }}" aria-label="{{ 'blog.comments.name_placeholder' | t }}" value="{{ form.author | escape | default: customer.name }}" required="required">
              <label class="Form__FloatingLabel">{{ 'blog.comments.name_placeholder' | t }}</label>
            </div>            <div class="Form__Item">
              <input type="email" class="Form__Input" name="comment[email]" placeholder="{{ 'blog.comments.email_placeholder' | t }}" aria-label="{{ 'blog.comments.email_placeholder' | t }}" value="{{ form.email | escape | default: customer.email }}" required="required">
              <label class="Form__FloatingLabel">{{ 'blog.comments.email_placeholder' | t }}</label>
            </div>
          </div>          <div class="Form__Item">
            <textarea name="comment[body]" rows="6" class="Form__Textarea" placeholder="{{ 'blog.comments.comment_placeholder' | t }}" aria-label="{{ 'blog.comments.comment_placeholder' | t }}" required="required">
              {{- form.body -}}
            </textarea>            <label class="Form__FloatingLabel">{{ 'blog.comments.comment_placeholder' | t }}</label>
          </div>          {%- if blog.moderated? -%}
            <p class="Form__Hint">{{ 'blog.comments.approval_notice' | t }}</p>
          {%- endif -%}          <button type="submit" class="Form__Submit Button Button--primary">{{ 'blog.comments.submit' | t }}</button>
        {%- endform -%}
      </div>
    {%- endif -%}
  </div>
  
  <div class="next-article-wrapper">
  <div class="Container Container--narrow" style="">
   
          <div class="col-50-wrapper">
            
              <div class="col col--50">
                
                
            
                {%- for article in blog.articles limit:2 -%}
                
                <div class="col article-min-wrapper">
                  <div class="article-block">
                  <a href="{{ article.url }}"></a>
                  <div class="block--image full-bg" style="background-image: url({{ article.image | img_url: '500x' }})">                  </div>
                  <div class="block--content">
                    <span class="article-tag p22 normal">{{ article.tags.first }}</span>
                    <h2 class="playfair">{{ article.title }}</h2>
                  </div>
                  </div>
                </div>
                
                {% endfor %}
                
                
              </div>
              
              <div class="col col--50">
                
                {%- for article in blog.articles limit:4 -%}
                  {% unless forloop.index0 < 2 %}
                  <div class="col article-min-wrapper">
                    <div class="article-block">
                    <a href="{{ article.url }}"></a>
                    <div class="block--image full-bg" style="background-image: url({{ article.image | img_url: '500x' }})">                    </div>
                    <div class="block--content">
                      <span class="article-tag p22 normal">{{ article.tags.first }}</span>
                      <h2 class="playfair">{{ article.title }}</h2>
                    </div>
                    </div>
                  </div>
                  {% endunless %}
                {% endfor %}
                
                
                
              </div>
            
          </div>
            
  </div>
  </div>
  
  
  
</article>{% if dm_paginate_by %}{% render 'spurit_dmr_collection_template_snippet', paginate_by: dm_paginate_by %}{% endif %}
{% section 'shop-now' %}
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		"https://d1um8515vdn9kb.cloudfront.net/libs/js/gfyoutube.js",
		 "https://www.youtube.com/player_api",
		'{{ 'gem-article-559137783942.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->