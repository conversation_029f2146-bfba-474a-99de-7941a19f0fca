{% assign has_free_gift = false %}
{% assign cart_total = cart.total_price %}
{% assign free_gift_variant_id = 0 %}

{% for item in cart.items %}
  {% if item.product.tags contains 'freegift' %}
    {% assign has_free_gift = true %}
    {% assign free_gift_variant_id = item.variant.id %}
    {% break %}
  {% endif %}
{% endfor %}

{% if has_free_gift and cart_total < 9900 %}
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      var observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
          var checkoutButton = document.querySelector('.Cart__Checkout.Button.Button--primary.Button--full.bold_clone');
          if (checkoutButton) {
            checkoutButton.disabled = true;
            checkoutButton.style.pointerEvents = 'none';
            checkoutButton.style.opacity = '0.5';
            observer.disconnect(); 
          }
        });
      });
    
      observer.observe(document.body, { childList: true, subtree: true });
    
      alert("Your cart total must be at least $99 to qualify for free gifts.");

      function removeFreeGift(variantId) {
        console.log("Attempting to remove free gift with variant ID:", variantId);
        fetch('/cart/change.js', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            id: variantId,
            quantity: 0
          })
        })
        .then(response => {
          console.log('Response Status:', response.status); // Log the response status
          response.text().then(text => console.log('Response Body:', text)); // Log the response body
          if (response.ok) {
            alert('Free gift removed as the cart total is less than $99.');
            location.reload();
          } else {
            alert('Error removing free gift. Please try again or contact support.');
          }
        })
        .catch(error => {
          console.error('Fetch error:', error);
        });
      }
    });
  </script>
{% endif %}

{{ 'cart.min.js' | asset_url | script_tag }}

<script type="text/javascript">
  //jQuery(function() {
    CartJS.init({{ cart | json }});
  //});

    function handleYarnChange(e) {

      setTimeout(() => {
         window.location.href = 'https://www.yarn.com.au/cart';
      }, "500");

       
    }

</script>


{% section 'section-cart' %}

<div style="clear: both"></div>

{% include 'cart-protection' %}

{% section 'cart-template' %}

{% section 'product-recommendations' %}


<div class="saso-cart-offers" style="display: none!important"></div>


<script>
  
  jQuery(function($) {

    
    
    $('.QuantitySelector__Button, .CartItem__Remove').click( function(e) {
      e.preventDefault();
		let href = $(this).attr('href');
        window.location.href = 'https://www.yarn.com.au/' + href;
    });
    
    
    $('.scrollme').on('click', function (el) {
      el.preventDefault();

      $('html, body').animate({
        scrollTop: $($(this).attr('href')).offset().top
      }, 700, 'linear');
    });
    
  
  
  setTimeout(function(){
    $('.GiftList').flickity({
        // options
        "prevNextButtons": true,
        "pageDots": false,
        "wrapAround": false,
        "contain": true,
        "cellAlign": "center",
        "dragThreshold": 8,
        "groupCells": true,
        "arrowShape": {"x0": 20, "x1": 60, "y1": 40, "x2": 60, "y2": 35, "x3": 25}

      });
    $('.GiftList').addClass('flickity-enabled');
  }, 2000);
  

    
    function updateImgUrl() {
      $('.saso-crosssell-nav img').each( function() {
        let imgSrc = $(this).attr("src");
        imgSrc = imgSrc.replace("_medium", "_600x600");
        $(this).attr("src", imgSrc);
      });

     }
        

    /*
    setTimeout(function(){ 
      $('.CartItem__PriceList .discounted_price').each( function() {
            if($(this).text() == '$0') {
               $('.saso-cart-offers').html('<h3 class="SectionHeader__Heading Heading u-h3" style="text-align: center">Congratulations.  Your gift is now in your cart</h3>');
               return;
            }
       });
     }, 6000);
     */

    /*
    // define a new observer
      var obs = new MutationObserver(function(mutations, observer) {
          // look through all mutations that just occured
          for(var i=0; i<mutations.length; ++i) {
              // look through all added nodes of this mutation
              for(var j=0; j<mutations[i].addedNodes.length; ++j) {
                  // was a child added with ID of 'bar'?
                  if(mutations[i].addedNodes[j].className == "saso-cross-sell-popup ") {
                      //console.log("bar was added!");
                      setTimeout(function(){ 
                        updateImgUrl();

                      }, 500);

                  }

              }
          }
      });
    

      // have the observer observe foo for changes in children
      obs.observe($(".saso-cart-offers").get(0), {
        childList: true
      });
    
   */
    
  });


function fun() {  
event.preventDefault();
console.log('hey');
console.log(event.target.dataset.quantity);
}  

  
</script>
