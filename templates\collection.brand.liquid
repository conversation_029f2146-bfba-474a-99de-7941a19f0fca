
<div class="brand-coll-intro boost-pfs-filter-collection-description" style="">
  {% if collection.metafields.banner != blank %}
  <div class="banners">
    
    {% if collection.metafields.banner.desktop != blank %}
      <img class="banner-desk" src="{{ collection.metafields.banner.desktop | img_url:"master" }}"> 
    {% endif %}
    
    {% if collection.metafields.banner.mobile != blank %}
      <img class="banner-mob" src="{{ collection.metafields.banner.mobile | img_url:"master" }}">
    {% endif %}
    
  </div>
  {% endif %}

  {{- collection.description -}}

    {% if collection.metafields.custom.description != blank %}
    <div class="cl-intro">
      <div class="wrap">
        <h1 class="heading u-h3">{{ collection.title }}</h1>
        <input type="checkbox" name="checkbox" id="checkbox_id" checked>
          <p>{{ collection.metafields.custom.description }}</p>
        <label for="checkbox_id"></label>
      </div>
    </div>
    {% endif %}
    
</div>



<script>
  
  let clintrop = document.querySelector(".cl-intro p");
  
  if (clintrop) {
    clintrosnip = "<h5>" + clintrop.textContent.split(' ').slice(0, 10).join(' ') + "...</h5>";
    console.log(clintrosnip);
    clintrop.insertAdjacentHTML("afterend", clintrosnip );
  }
  
  // Create a media condition that targets viewports at least 768px wide
  const mediaQuery = window.matchMedia('(min-width: 768px)')
  // Check if the media query is true
  if (mediaQuery.matches) {

    let clintrolabel = document.querySelector(".cl-intro label");
    if(clintrolabel) {
      clintrolabel.click();
    }
  }
</script>

{% section 'collection-template' %}
{% section 'recently-viewed-products' %}


{% section 'collection-footer' %}