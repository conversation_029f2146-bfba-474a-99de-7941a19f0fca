{% if collection.metafields.banner != blank %}
<div class="brand-coll-intro boost-pfs-filter-collection-description" style="">
  <div class="banners">
    
    {% if collection.metafields.banner.desktop != blank %}
      <img class="banner-desk" src="{{ collection.metafields.banner.desktop | img_url:"master" }}"> 
    {% endif %}
    
    {% if collection.metafields.banner.mobile != blank %}
      <img class="banner-mob" src="{{ collection.metafields.banner.mobile | img_url:"master" }}">
    {% endif %}
    
  </div>
</div>
{% endif %}


{% section 'collection-template' %}
{% section 'product-recommendations' %}
{% section 'recently-viewed-products' %}
            
{%- if collection.description != blank -%}


  <div class="collection-desc" style="">
    <div class="Container">
       {{- collection.description -}}
    </div>
  </div>


{%- endif -%}

{% section 'collection-footer' %}



<script>

 setTimeout(function(){
    $('.GiftList').flickity({
        // options
        "prevNextButtons": true,
        "pageDots": false,
        "wrapAround": false,
        "contain": true,
        "cellAlign": "center",
        "dragThreshold": 8,
        "groupCells": true,
        "draggable": true,
        "arrowShape": {"x0": 20, "x1": 60, "y1": 40, "x2": 60, "y2": 35, "x3": 25}

      });
    $('.GiftList').addClass('flickity-enabled');
    }, 2000);

</script>
