<link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.css"
    />

{%- comment -%} Multiple Addtocart App Custom Collection Template {%- endcomment -%}

{{ 'multiple-addtocart.css' | asset_url | stylesheet_tag }}

{%- comment -%} Multiple Addtocart App - General Settings {%- endcomment -%}

{%- if shop.metafields.multicartapp.button_text == blank -%}
{%- assign button_text = 'Add to Cart' -%}
{%- else -%}
{%- assign button_text = shop.metafields.multicartapp.button_text -%}
{%- endif -%}

{%- if shop.metafields.multicartapp.bar_position == blank -%}
{%- assign bar_position = 'top_bottom' -%}
{%- else -%}
{%- assign bar_position = shop.metafields.multicartapp.bar_position -%}
{%- endif -%}

{%- if shop.metafields.multicartapp.multicart_text == blank -%}
{%- assign multicart_text = '' -%}
{%- else -%}
{%- assign multicart_text = shop.metafields.multicartapp.multicart_text -%}
{%- endif -%}

{%- if shop.metafields.multicartapp.categories_limit == blank -%}
{%- assign categories_limit = 10 | minus: 1 -%}
{%- else -%}
{%- assign categories_limit = shop.metafields.multicartapp.categories_limit | minus: 1 -%}
{%- endif -%}

{%- if shop.metafields.multicartapp.products_limit == blank -%}
{%- assign products_limit = 10 -%}
{%- else -%}
{%- assign products_limit = shop.metafields.multicartapp.products_limit -%}
{%- endif -%}

{%- if shop.metafields.multicartapp.show_lbl_val == blank -%}
{%- assign show_lbl_val = "yes" -%}
{%- else -%}
{%- assign show_lbl_val = shop.metafields.multicartapp.show_lbl_val -%}
{%- endif -%}

{%- if shop.metafields.multicartapp.new_label == blank -%}
{%- assign new_label = "New" -%}
{%- else -%}
{%- assign new_label = shop.metafields.multicartapp.new_label -%}
{%- endif -%}

{%- if shop.metafields.multicartapp.new_label_days == blank -%}
{%- assign new_label_days =  24 | times: 3600 | times: 1 -%}
{%- else -%}
{%- assign new_label_days = 24 | times: 3600 | times: shop.metafields.multicartapp.new_label_days -%}
{%- endif -%}

{%- if shop.metafields.multicartapp.sale_label == blank -%}
{%- assign sale_label = "Sale" -%}
{%- else -%}
{%- assign sale_label = shop.metafields.multicartapp.sale_label -%}
{%- endif -%}

{%- if shop.metafields.multicartapp.soldout_label == blank -%}
{%- assign soldout_label = "Soldout" -%}
{%- else -%}
{%- assign soldout_label = shop.metafields.multicartapp.soldout_label -%}
{%- endif -%}

{%- if shop.metafields.multicartapp.loadmoretext == blank -%}
{%- assign loadmoretext = "Load More" -%}
{%- else -%}
{%- assign loadmoretext = shop.metafields.multicartapp.loadmoretext -%}
{%- endif -%}

{%- if shop.metafields.multicartapp.disableflipper == blank -%}
{%- assign disableflipper = "no" -%}
{%- else -%}
{%- assign disableflipper = shop.metafields.multicartapp.disableflipper -%}
{%- endif -%}

{%- if shop.metafields.multicartapp.show_in_stock == blank -%}
{%- assign show_in_stock = "yes" -%}
{%- else -%}
{%- assign show_in_stock = shop.metafields.multicartapp.show_in_stock -%}
{%- endif -%}

{%- if shop.metafields.multicartapp.show_prd_image == blank -%}
{%- assign show_prd_image = "yes" -%}
{%- else -%}
{%- assign show_prd_image = shop.metafields.multicartapp.show_prd_image -%}
{%- endif -%}

{%- if shop.metafields.multicartapp.show_brand_sku == blank -%}
{%- assign show_brand_sku = "yes" -%}
{%- else -%}
{%- assign show_brand_sku = shop.metafields.multicartapp.show_brand_sku -%}
{%- endif -%}

{%- if shop.metafields.multicartapp.show_categories_sidebar == blank -%}
{%- assign show_categories_sidebar = "yes" -%}
{%- else -%}
{%- assign show_categories_sidebar = shop.metafields.multicartapp.show_categories_sidebar -%}
{%- endif -%}

{%- if shop.metafields.multicartapp.show_sort_by == blank -%}
{%- assign show_sort_by = "yes" -%}
{%- else -%}
{%- assign show_sort_by = shop.metafields.multicartapp.show_sort_by -%}
{%- endif -%}

{%- if shop.metafields.multicartapp.show_multicart_bar == blank -%}
{%- assign show_multicart_bar = "yes" -%}
{%- else -%}
{%- assign show_multicart_bar = shop.metafields.multicartapp.show_multicart_bar -%}
{%- endif -%}

{%- if shop.metafields.multicartapp.preset_qty_val == blank -%}
{%- assign preset_qty_val = 0 | plus: 0 -%}
{%- else -%}
{%- assign preset_qty_val = shop.metafields.multicartapp.preset_qty_val | plus: 0 -%}
{%- endif -%}


{%- if shop.metafields.multicartapp.show_checkout_btn == blank -%}
{%- assign show_checkout_btn = "yes" -%}
{%- else -%}
{%- assign show_checkout_btn = shop.metafields.multicartapp.show_checkout_btn -%}
{%- endif -%}

{%- assign curr_coll = collection.handle -%}

{%- comment -%} Error Handling {%- endcomment -%}
{%- capture sort_by_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.sort_by' }}{%- endcapture -%}
{%- capture featured_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.featured' }}{%- endcapture -%}
{%- capture price_low_to_high_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.price_low_to_high' }}{%- endcapture -%}
{%- capture price_high_to_low_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.price_high_to_low' }}{%- endcapture -%}
{%- capture alphabetically_a_to_z_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.alphabetically_a_to_z' }}{%- endcapture -%}
{%- capture alphabetically_z_to_a_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.alphabetically_z_to_a' }}{%- endcapture -%}
{%- capture date_new_to_old_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.date_new_to_old' }}{%- endcapture -%}
{%- capture date_old_to_new_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.date_old_to_new' }}{%- endcapture -%}
{%- capture best_selling_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.best_selling' }}{%- endcapture -%}
{%- capture products_text_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.products_text' }}{%- endcapture -%}
{%- capture product_image_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.product_image' }}{%- endcapture -%}
{%- capture product_name_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.product_name' }}{%- endcapture -%}
{%- capture brand_text_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.brand_text' }}{%- endcapture -%}
{%- capture sku_text_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.sku_text' }}{%- endcapture -%}
{%- capture price_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.price' }}{%- endcapture -%}
{%- capture quantity_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.quantity' }}{%- endcapture -%}
{%- capture show_options_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.show_options' }}{%- endcapture -%}
{%- capture hide_options_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.hide_options' }}{%- endcapture -%}
{%- capture max_stock_alert_text_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.max_stock_alert_text' }}{%- endcapture -%}
{%- capture no_items_added_text_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.no_items_added_text' }}{%- endcapture -%}
{%- capture single_add_to_cart_button_text_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.single_add_to_cart_button_text' }}{%- endcapture -%}
{%- capture add_more_text_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.add_more_text' }}{%- endcapture -%}
{%- capture thank_you_text_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.thank_you_text' }}{%- endcapture -%}
{%- capture cart_popup_title_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.cart_popup_title' }}{%- endcapture -%}
{%- capture add_to_cart_error_message_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.add_to_cart_error_message' }}{%- endcapture -%}
{%- capture close_text_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.close_text' }}{%- endcapture -%}
{%- capture item_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.item_text' }}{%- endcapture -%}
{%- capture items_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.items_text' }}{%- endcapture -%}
{%- assign item_txt = 'cws_bulk_add_to_cart.item_text' | t | replace: item_error, 'Item' | strip_newlines -%}
{%- assign items_txt = 'cws_bulk_add_to_cart.items_text' | t | replace: items_error, 'Items' | strip_newlines -%}
{%- capture all_collection_page_title_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.all_collection_page_title' }}{%- endcapture -%}
{%- capture no_items_text_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.no_items_text' }}{%- endcapture -%}
{%- capture adding_text_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.adding_text' }}{%- endcapture -%}
{%- capture product_options_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.product_options' }}{%- endcapture -%}
{%- capture categories_text_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.categories_text' }}{%- endcapture -%}
{%- capture view_more_categories_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.view_more_categories' }}{%- endcapture -%}
{%- capture view_less_categories_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.view_less_categories' }}{%- endcapture -%}
{%- capture only_n_items_available_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.only_n_items_available' }}{%- endcapture -%}
{%- capture too_many_items_in_cart_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.too_many_items_in_cart' }}{%- endcapture -%}
{%- capture all_items_in_cart_error -%}{{ 'translation missing:' | append:' ' | append: shop.locale | append: '.cws_bulk_add_to_cart.all_items_in_cart' }}{%- endcapture -%}

<script type="text/javascript">
   //Load Javascript Function
   window.bulkCartLoadScript = function(url, callback) {
	var script = document.createElement("script")
	script.type = "text/javascript";

  	if(script.readyState) { //IE
   	script.onreadystatechange = function () {
     	if (script.readyState == "loaded" || script.readyState == "complete" || script.readyState == "loading" || dom.readyState == "uninitialized") {
        	script.onreadystatechange = null;
        	callback();
     	}
    };
  	} else { //Others
    	script.onload = function () {
      	callback();
    	};
  	}

  	script.type = 'text/javascript';
  	script.async = true;
  	script.src = url;
  	var x = document.getElementsByTagName('script')[0];
  	x.parentNode.insertBefore(script, x);
	};
  
   	function getUrlParameter(sParam) {
    var sPageURL = decodeURIComponent(window.location.search.substring(1)),
        sURLVariables = sPageURL.split('&'),
        sParameterName,
        i;
		
    for(i = 0; i < sURLVariables.length; i++) {
        sParameterName = sURLVariables[i].split('=');

        if (sParameterName[0] === sParam) {
        return sParameterName[1] === undefined ? true : sParameterName[1];
        }
    }
	}
  
   //Pass variables
   window.multicart_money_format = "{{ shop.money_format | strip_html }}"; 
   window.multicart_no_image = "{{ 'multicart-noimg.png' | asset_img_url: '90X90' }}";
   window.coll_default_sort_by = "{{ collection.default_sort_by }}";
   window.shopify_ajaxapi = "{{ 'api.jquery.js' | shopify_asset_url }}";
   window.multiple_atc_js = "{{ 'multiple-addtocart.js' | asset_url }}";
   window.coll_prd_count = "{{ collection.products_count }}";
   window.show_prd_image = "{{ show_prd_image }}";
   window.preset_qty_val = "{{ preset_qty_val }}";
  
   //Language Texts Pass - 786
   window.show_options = "{{ 'cws_bulk_add_to_cart.show_options' | t | replace: show_options_error, 'Show Options' | strip_newlines }}";
   window.hide_options = "{{ 'cws_bulk_add_to_cart.hide_options' | t | replace: hide_options_error, 'Hide Options' | strip_newlines }}";
   var stock_max_alert = "{{ 'cws_bulk_add_to_cart.max_stock_alert_text' | t | replace: max_stock_alert_text_error, 'We have maximum %s items in stock.' | strip_newlines }} ";
   window.stock_max_alert = stock_max_alert.toString().split("%s");
   window.stock_max_alert1 = window.stock_max_alert[0];
   window.stock_max_alert2 = window.stock_max_alert[1];
   window.no_items_added_text = "{{ 'cws_bulk_add_to_cart.no_items_added_text' | t | replace: no_items_added_text_error, 'Sorry! no items added to cart, please enter Qty.' | strip_newlines }}";
   window.single_atc_text = "{{ 'cws_bulk_add_to_cart.single_add_to_cart_button_text' | t | replace: single_add_to_cart_button_text_error, 'Add to Cart' | strip_newlines }}";
   window.add_more_text = "{{ 'cws_bulk_add_to_cart.add_more_text' | t | replace: add_more_text_error, 'Add More...' | strip_newlines }}";
   window.thank_you_text = "{{ 'cws_bulk_add_to_cart.thank_you_text' | t | replace: thank_you_text_error, 'Thank You...' | strip_newlines }}";
   var cart_popup_title = "{{ 'cws_bulk_add_to_cart.cart_popup_title' | t | replace: cart_popup_title_error, 'YOUR CART HAS %s ITEMS' | strip_newlines }}";
   window.cart_popup_title = cart_popup_title.toString().split("%s");
   window.cart_popup_title1 = window.cart_popup_title[0];
   window.cart_popup_title2 = window.cart_popup_title[1];
   window.add_to_cart_error_message = "{{ 'cws_bulk_add_to_cart.add_to_cart_error_message' | t | replace: add_to_cart_error_message_error, 'Below items, which were not successfully added to your Shopping Cart due to following errors.' | strip_newlines }}";
  
   //Set lang. specific cart response error messages - 786/ASK
   window.only_n_items_available = "{{ 'cws_bulk_add_to_cart.only_n_items_available' | t | replace: only_n_items_available_error, 'You can only add %count %product_name to the cart.' | strip_newlines }}";
   window.too_many_items_in_cart = "{{ "cws_bulk_add_to_cart.too_many_items_in_cart" | t | replace: too_many_items_in_cart_error, "You can't add more %product_name to the cart." | replace: '&#39;', "'" | strip_newlines }}";
   window.all_items_in_cart = "{{ 'cws_bulk_add_to_cart.all_items_in_cart' | t | replace: all_items_in_cart_error, 'All %count %product_name are in your cart.' | strip_newlines }}";
   var too_many_items_in_cart = window.too_many_items_in_cart.toString().split("%product_name");
   var all_items_in_cart = window.all_items_in_cart.toString().split("%count %product_name");
   var only_n_items_available = window.only_n_items_available.toString().split("%count %product_name");
  
   window.adding_text = "{{ 'cws_bulk_add_to_cart.adding_text' | t | replace: adding_text_error, 'Adding...' | strip_newlines }}";
   window.item_txt = "{{ item_txt }}";
   window.items_txt = "{{ items_txt }}";
   window.cat_limit = "{{ categories_limit }}";
   window.view_more_category_text = "{{ 'cws_bulk_add_to_cart.view_more_categories' | t | replace: view_more_categories_error, 'View More Category' | strip_newlines }}";
   window.view_less_category_text = "{{ 'cws_bulk_add_to_cart.view_less_categories' | t | replace: view_less_categories_error, 'View Less Category' | strip_newlines }}";
   window.prd_img_txt = "{{ 'cws_bulk_add_to_cart.product_image' | t | replace: product_image_error, 'Product Image' | strip_newlines }}";
   window.prd_name_txt = "{{ 'cws_bulk_add_to_cart.product_name' | t | replace: product_name_error, 'Product Name' | strip_newlines }}";
   window.prd_brand_sku_txt = "{{ 'cws_bulk_add_to_cart.brand_text' | t | replace: brand_text_error, 'Brand' | strip_newlines }}/{{ 'cws_bulk_add_to_cart.sku_text' | t | replace: sku_text_error, 'SKU' | strip_newlines }}";
   window.price_txt = "{{ 'cws_bulk_add_to_cart.price' | t | replace: price_error, 'Price' | strip_newlines }}";
   window.qty_txt = "{{ 'cws_bulk_add_to_cart.quantity' | t | replace: quantity_error, 'Quantity' | strip_newlines }}";
   window.prd_options_txt = "{{ 'cws_bulk_add_to_cart.product_options' | t | replace: product_options_error, 'Product Options' | strip_newlines }}";
     
   //Check jquery lib. is already included or not and if version is less then call jquery lib through our App.
   if ((typeof jQuery === "undefined") || (jQuery.fn.jquery.replace(/\.(\d)/g,".0$1").replace(/\.0(\d{2})/g,".$1") < "1.08.01")) {
     window.bulkCartLoadScript("//cdnjs.cloudflare.com/ajax/libs/jquery/1.12.0/jquery.min.js", function (){
   		jQuery.noConflict();
        
        //Append dynamic CSS in header for mobile view.[It's require to add below line after jQuery has been loaded.]
        jQuery('head').append('<style type="text/css">@media only screen and (max-width: 767px) {.multicart-container #multi-addToCart #resp-table-body td.cws_prd_img:before{ content:"'+window.prd_img_txt+'"; }.multicart-container #multi-addToCart #resp-table-body td.cws_prd_name:before{ content:"'+window.prd_name_txt+'"; }.multicart-container #multi-addToCart #resp-table-body td.cws_prd_options:before{ content:"'+window.prd_options_txt+'" }.multicart-container #multi-addToCart #resp-table-body td.cws_brand_sku:before{ content:"'+window.prd_brand_sku_txt+'"; }.multicart-container #multi-addToCart #resp-table-body td.cws_price:before{ content:"'+window.price_txt+'"; }.multicart-container #multi-addToCart #resp-table-body td.cws_prd_qty:before{ content:"'+window.qty_txt+'"; } .multicart-container .cart_table td.cws_prd_img:before { content: "'+window.prd_img_txt+'"; }.multicart-container .cart_table td.cws_prd_name:before { content: "'+window.prd_name_txt+'"; }.multicart-container .cart_table td.cws_prd_qty:before { content: "'+ window.qty_txt +'"; } .multicart-container .cart_table td.cws_price:before { content: "'+window.price_txt+'"; }}</style>');
     
        //Check if shopify AJAX API lib. is not included then only include it.[It's require to add below line after jQuery has been loaded.]
    	var check_exist = jQuery('script[src*="api.jquery"]').length;
    	if(check_exist == 0) {
   		jQuery.getScript(window.shopify_ajaxapi);
    	}
		
		//786/ASK
		window.bulkCartLoadScript(window.multiple_atc_js, function (){
		});
		
     });
   } else {
        //Append dynamic CSS in header for mobile view.[It's require to add below line after jQuery is already added by theme.]
    	jQuery('head').append('<style type="text/css">@media only screen and (max-width: 767px) {.multicart-container #multi-addToCart thead { display:none; }.multicart-container #multi-addToCart #resp-table-body td.cws_prd_img:before{ content:"'+window.prd_img_txt+'"; }.multicart-container #multi-addToCart #resp-table-body td.cws_prd_name:before{ content:"'+window.prd_name_txt+'"; }.multicart-container #multi-addToCart #resp-table-body td.cws_prd_options:before{ content:"'+window.prd_options_txt+'" }.multicart-container #multi-addToCart #resp-table-body td.cws_brand_sku:before{ content:"'+window.prd_brand_sku_txt+'"; }.multicart-container #multi-addToCart #resp-table-body td.cws_price:before{ content:"'+window.price_txt+'"; }.multicart-container #multi-addToCart #resp-table-body td.cws_prd_qty:before{ content:"'+window.qty_txt+'"; } .multicart-container .cart_table thead { display:none; }.multicart-container .cart_table td.cws_prd_img:before { content: "'+window.prd_img_txt+'"; }.multicart-container .cart_table td.cws_prd_name:before { content: "'+window.prd_name_txt+'"; }.multicart-container .cart_table td.cws_prd_qty:before { content: "'+ window.qty_txt +'"; } .multicart-container .cart_table td.cws_price:before { content: "'+window.price_txt+'"; }}</style>');
    	
        //Check if shopify AJAX API lib. is not included then only include it.[It's require to add below line after jQuery is already added by theme.]
    	var check_exist = jQuery('script[src*="api.jquery"]').length;
    	if(check_exist == 0) {
   		jQuery.getScript(window.shopify_ajaxapi);
    	}
		
		//786/ASK
		window.bulkCartLoadScript(window.multiple_atc_js, function (){
		});
   }
</script>

{%- comment -%} Get Multiadd to cart Collections {%- endcomment -%} 
{%- assign coll_count = 0 -%}
{%- capture colls_list -%}

{% paginate collections by 999 %}
{%- for col in collections -%}
{%- if col.template_suffix == "multiple-addtocart" -%}
{%- assign coll_count = coll_count | plus: 1 -%}
{{ col.handle }}
{%- unless forloop.last -%},{%- endunless -%}
{%- endif -%}
{%- endfor -%}
{% endpaginate %}

{%- endcapture -%}


{%- if collection.products_count > 0 -%}
{%- paginate collection.products by products_limit -%}
<div class="multicart-container Container">
   <div class="multicart-loader" style="display: none;"><img src="{{ 'multiaddtocart-loader.gif' | asset_img_url:'200x' }}"></div>
   <div class="page-width" id="collection-multicart">
      {%- comment -%} Categories Sidebar {%- endcomment -%}
      <div class="coll-header page-title" style="margin: 40px 0 20px">
        {%- if collection.handle != "all" -%}
        <center><h2>{{ collection.title }}</h2></center>
        {%- else -%}
        <center><h2>{{ 'cws_bulk_add_to_cart.all_collection_page_title' | t | replace: all_collection_page_title_error, 'Products' | strip_newlines }}</h2></center>
        {%- endif -%}
      </div>
     
     <p style="text-align: center; font-size: 16px"><span style="display: inline-block; padding: 20px; border: 1px solid grey">Polos Shipping time: {% assign days = 42 | times: 86400 %}
{{ "now" | date: "%s" | plus: days | date: "%-e %B" }} or earlier.<br>All other products shipping time: 2 weeks or earlier</span></p>
     
      {%- if collection.image != blank or collection.description != blank -%}
      <div class="image-description">
         {%- if collection.image != blank -%}
         <div class="coll-img" >
            <img src="{{ collection.image | img_url: 'medium' }}" />
         </div>
         {%- endif -%}
         {%- if collection.description != blank -%}
         <div class="coll-desc" >{{ collection.description }}</div>
         {%- endif -%}	
     </div>
     {%- endif -%}	
     
     {%- if coll_count > 1 and show_categories_sidebar == "yes" -%}
     <div class="widget-filter sidebar collection-filters sidebar_left">
         <div class="block multicart-categories">
            <h3 class="sub-title">{{ 'cws_bulk_add_to_cart.categories_text' | t | replace: categories_text_error, 'Categories' | strip_newlines }}</h3>
            <div class="block-content" style="display:none;">
               <ul class="sidebar-collections">
                 {%- for collection in collections -%}
                 {%- if collection.template_suffix == "multiple-addtocart" -%}
                 {%- if colls_list contains collection.handle and collection.handle != curr_coll -%}
                 <li><a href="{{ collection.url }}">{{ collection.title }}</a></li>
                 {%- endif -%}
                 {%- endif -%}
                 {%- endfor -%}
               </ul>
            </div>
         </div>
      </div>
     {%- endif -%}
     
     <div class="{%- if coll_count > 1 and show_categories_sidebar == "yes" -%}collection-multicart-template{%- else -%}collection-multicart-fullwidth{%- endif -%}">
        <div class="multicart_err" style="display:none;">
         <button class="err_close" onclick="this.parentElement.style.display='none';" title="{{ 'cws_bulk_add_to_cart.close_text' | t | replace: close_text_error, 'Close' | strip_newlines }}">&times;</button>
      	</div>
      	<div class="product_list">
         {%- if show_sort_by == "yes" -%}
         <div class="multicart_sort filters-toolbar-wrapper">
           <div class="cart_sort_by">
            <span><strong>{{ 'cws_bulk_add_to_cart.sort_by' | t | replace: sort_by_error, 'Sort By' | strip_newlines }}:</strong></span>
            <div class="sort_by_dropdown">
            <select id="multicart_sort_by">
               <option value="manual">{{ 'cws_bulk_add_to_cart.featured' | t | replace: featured_error, 'Featured' | strip_newlines }}</option>
               <option value="price-ascending">{{ 'cws_bulk_add_to_cart.price_low_to_high' | t | replace: price_low_to_high_error, 'Price: Low to High' | strip_newlines }}</option>
               <option value="price-descending">{{ 'cws_bulk_add_to_cart.price_high_to_low' | t | replace: price_high_to_low_error, 'Price: High to Low' | strip_newlines }}</option>
               <option value="title-ascending">{{ 'cws_bulk_add_to_cart.alphabetically_a_to_z' | t | replace: alphabetically_a_to_z_error, 'Alphabetically, A-Z' | strip_newlines }}</option>
               <option value="title-descending">{{ 'cws_bulk_add_to_cart.alphabetically_z_to_a' | t | replace: alphabetically_z_to_a_error, 'Alphabetically, Z-A' | strip_newlines }}</option>
               <option value="created-ascending">{{ 'cws_bulk_add_to_cart.date_old_to_new' | t | replace: date_old_to_new_error, 'Oldest to Newest' | strip_newlines }}</option>
               <option value="created-descending">{{ 'cws_bulk_add_to_cart.date_new_to_old' | t | replace: date_new_to_old_error, 'Newest to Oldest' | strip_newlines }}</option>
               <option value="best-selling">{{ 'cws_bulk_add_to_cart.best_selling' | t | replace: best_selling_error, 'Best Selling' | strip_newlines }}</option>
            </select>
            </div>
           </div>
         </div>
         {%- endif -%}
         <div class="list-view-items">
            <div id="list-view-items">
               <div id="resp-table-caption">
                  {%- comment -%} Multiple addtocart button is included here {%- endcomment -%}
                  {%- if show_multicart_bar == "yes" and bar_position == 'top_bottom' or bar_position == 'top' -%}
                  <div class="multiaddtocart">
                     <h3 class="multicart-text">{{ multicart_text }}</h3>
                     <button class="submit current_list-add-to-cart">
                     <img src="{{'multicart-icon.png' | asset_url }}" alt="Cart" title="{{ button_text }}" />
                     <span>{{ button_text }}</span>
                     </button>          
                  </div>
                  {%- endif -%}
               </div>
               <div class="multcart-container">
                  <div class="multcart_total_cnt">
                     <p><strong>{{ collection.all_products_count }} {{ 'cws_bulk_add_to_cart.products_text' | t | replace: products_text_error, 'Products' | strip_newlines }}</strong></p>
                  </div>
                  <table id="multi-addToCart" class="collection-multiaddtocart">
                     <thead id="resp-table-header">
                        <tr>
                           {%- if show_prd_image == "yes" -%}<th class="cws_prd_img">{{ 'cws_bulk_add_to_cart.product_image' | t | replace: product_image_error, 'Product Image' | strip_newlines }}</th>{%- endif -%}
                           <th class="cws_prd_name">{{ 'cws_bulk_add_to_cart.product_name' | t | replace: product_name_error, 'Product Name' | strip_newlines }}</th>
                           {%- if show_brand_sku == "yes" -%}<th class="cws_brand_sku">{{ 'cws_bulk_add_to_cart.brand_text' | t | replace: brand_text_error, 'Brand' | strip_newlines }}/{{ 'cws_bulk_add_to_cart.sku_text' | t | replace: sku_text_error, 'SKU' | strip_newlines }}</th>{%- endif -%}
                           <th class="cws_price">{{ 'cws_bulk_add_to_cart.price' | t | replace: price_error, 'Price' | strip_newlines }}</th>
                           <th class="cws_prd_qty">{{ 'cws_bulk_add_to_cart.quantity' | t | replace: quantity_error, 'Quantity' | strip_newlines }}</th>
                           <th></th>
                        </tr>
                     </thead>
                     <tbody id="resp-table-body">
                       
                        {%- for product in collection.products -%}
                        {%- render 'product-card-multiple-addtocart', product: product, show_options_error: show_options_error, button_text: button_text, product_image_error: product_image_error, brand_text_error: brand_text_error, sku_text_error: sku_text_error, price_error: price_error, quantity_error: quantity_error, single_add_to_cart_button_text_error: single_add_to_cart_button_text_error, product_options_error: product_options_error, button_text: button_text, disableflipper: disableflipper, show_lbl_val: show_lbl_val, new_label: new_label, new_label_days: new_label_days, sale_label: sale_label, soldout_label: soldout_label, show_in_stock: show_in_stock, show_prd_image: show_prd_image, show_brand_sku: show_brand_sku, preset_qty_val: preset_qty_val -%}
                        {%- endfor -%}
                       
                     </tbody>
                  </table>
               </div>
            </div>
         </div>
      </div>
      {%- comment -%} Load More {%- endcomment -%}
      {%- if paginate.next -%}
      <div id="product-list-foot" align="center">
         <div class="multcart_more">
            {%- if autoloadmore == "yes" -%}
            &darr; More <a href="{{ paginate.next.url }}" style="display:none;">More</a>
            {%- else -%}
            <a href="{{ paginate.next.url }}" class="btn">{{ loadmoretext }}</a>
            {%- endif -%}
         </div>
      </div>
      {%- endif -%}
      {%- comment -%} Multiple addtocart button is included here {%- endcomment -%}
      {%- if show_multicart_bar == "yes" and bar_position == 'top_bottom' or bar_position == 'top' -%}
      <div class="multiaddtocart">
         <h3 class="multicart-text">{{ multicart_text }}</h3>
         <button class="submit current_list-add-to-cart">
         <img src="{{'multicart-icon.png' | asset_url }}" alt="Cart" title="{{ button_text }}" />
         <span>{{ button_text }}</span>
         </button>       
      </div>
      {%- endif -%}
      <a itle="Shopping Cart" id="flyToCart" class="cart_anchor" href="/cart">
         <div id="multicartDiv" class="multi-items-cart">{{ cart.item_count }} {{ cart.item_count | pluralize: item_txt, items_txt | strip_newlines }}</div>
      </a>
      {%- capture multicartlist_content -%} 
      {%- render 'multiaddtocart-list' -%}{%- endcapture -%}
      {%- unless multicartlist_content contains "Liquid error" -%}	
      {%- render 'multiaddtocart-list', show_checkout_btn: show_checkout_btn, product_image_error: product_image_error, product_name_error: product_name_error, price_error: price_error, quantity_error: quantity_error, close_text_error: close_text_error, show_prd_image: show_prd_image -%}
      {%- endunless -%}	
     </div>
   </div>
</div>
{%- endpaginate -%}
{%- else -%}
{%- comment -%} No Products Found {%- endcomment -%}
<div class="multicart-container">
   <div class="page-width" id="collection-multicart">
      <div class="coll-header page-title" >
        {%- if collection.handle != "all" -%}
        <center><h2>{{ collection.title }}</h2></center>
        {%- else -%}
        <center><h2>{{ 'cws_bulk_add_to_cart.all_collection_page_title' | t | replace: all_collection_page_title_error, 'Products' | strip_newlines }}</h2></center>
        {%- endif -%}
      </div>
     
      {%- if collection.image != blank or collection.description != blank -%}
      <div class="image-description">
         {%- if collection.image != blank -%}
         <div class="coll-img" >
            <img src="{{ collection.image | img_url: 'medium' }}" />
         </div>
         {%- endif -%}
         {%- if collection.description != blank -%}
         <div class="coll-desc" >{{ collection.description }}</div>
         {%- endif -%}	
      </div>
      {%- endif -%}
    
      {%- if coll_count > 1 and show_categories_sidebar == "yes" -%}
      <div class="widget-filter sidebar collection-filters sidebar_left">
         <div class="block multicart-categories">
            <h3 class="sub-title">{{ 'cws_bulk_add_to_cart.categories_text' | t | replace: categories_text_error, 'Categories' | strip_newlines }}</h3>
            <div class="block-content" style="display:none;">
               <ul class="sidebar-collections">
                 {%- for collection in collections -%}
                 {%- if collection.template_suffix == "multiple-addtocart" -%}
                 {%- if colls_list contains collection.handle and collection.handle != curr_coll -%}
                 <li><a href="{{ collection.url }}">{{ collection.title }}</a></li>
                 {%- endif -%}
                 {%- endif -%}
                 {%- endfor -%}
               </ul>
            </div>
         </div>
      </div>
     {%- endif -%}
     
     <div class="{%- if coll_count > 1 and show_categories_sidebar == "yes" -%}collection-multicart-template{%- else -%}collection-multicart-fullwidth{%- endif -%}">
       <div class="grid__item small-text-center">
         <p class="text-center no-products">{{ 'cws_bulk_add_to_cart.no_items_text' | t | replace: no_items_text_error, 'Sorry, There are no Products in this Collection.' | strip_newlines }}</p>
       </div>
     </div>
   </div>
</div>
{%- endif -%}

<style>
  .table-price td {
    display: table-cell;
    padding: 10px;
    font-size: 14px;
    line-height: normal;
    border: 1px solid #ddd;
    text-align: center;
    position: relative;
    vertical-align: middle;
  }
</style>

<div id="naidoc-week-2022-customizable-polos" style="display: none">
  
<h3>Standard Polo</h3>

<table class="table-price">

<tr>
  <td>Volume Bracket</td>
  <td>Price</td>
  <td>Price with Decoration</td>
</tr>

<tr>
  <td>15 - 25</td>
  <td>$49.50</td>
  <td>$49.50</td>
</tr>

<tr>
  <td>26 - 49</td>
  <td>$46.75</td>
  <td>$46.75</td>
</tr>

<tr>
  <td>50 - 99</td>
  <td>$44.00</td>
  <td>$44.00</td>
</tr>

<tr>
  <td>100 - 199</td>
  <td>$42.25</td>
  <td>$42.25</td>
</tr>
  
<tr>
<td>200+</td>
  <td>$38.50</td>
  <td>$38.50</td>
</tr>


</table>
</div>



<div id="naidoc-week-2022-customizable-t-shirts" style="display: none">

<h3>Tees</h3>

<table class="table-price">

<tr>
  <td>Volume Bracket</td>
  <td>Price</td>
  <td>Price with Decoration</td>
</tr>

<tr>
  <td>100 - 199</td>
  <td>$16.90</td>
  <td>$18.85</td>
</tr>
  
<tr>
  <td>200 - 299</td>
  <td>$15.30</td>
  <td>$16.95</td>
</tr>

<tr>
  <td>300 - 399</td>
  <td>$14.75</td>
  <td>$15.85</td>
</tr>

<tr>
  <td>400 - 499</td>
  <td>$14.20</td>
  <td>$15.30</td>
</tr>

<tr>
  <td>500 - 1499</td>
  <td>$12.40</td>
  <td>$13.50</td>
</tr>

<tr>
  <td>1500+</td>
  <td>$10.50</td>
  <td>$11.60</td>
</tr>

</table>

</div>





<div id="naidoc-week-2022-customizable-hoodies" style="display: none">

<h3>Hoodies</h3>

<table class="table-price">

<tr>
  <td>Volume Bracket</td>
  <td>Price</td>
  <td>Price with Decoration</td>
</tr>

<tr>
  <td>19 - 29</td>
  <td>$58.70</td>
  <td>$60.90</td>
</tr>

<tr>
  <td>30 - 49</td>
  <td>$54.45</td>
  <td>$56.65</td>
</tr>

<tr>
  <td>50 - 99</td>
  <td>$51.70</td>
  <td>$53.90</td>
</tr>

<tr>
  <td>100 - 199</td>
  <td>$49.20</td>
  <td>$51.10</td>
</tr>
  
<tr>
  <td>200 - 299</td>
  <td>$46.85</td>
  <td>$48.50</td>
</tr>

<tr>
  <td>300 - 399</td>
  <td>$44.70</td>
  <td>$45.80</td>
</tr>

<tr>
  <td>400 - 499</td>
  <td>$42.65</td>
  <td>$43.75</td>
</tr>

<tr>
  <td>500 - 1499</td>
  <td>$39.05</td>
  <td>$40.15</td>
</tr>

<tr>
  <td>1500+</td>
  <td>$33.95</td>
  <td>$35.05</td>
</tr>

</table>

</div>




<div id="naidoc-week-2022-customizable-water-bottles" style="display: none">

<h3>Bottles</h3>

<table class="table-price">

<tr>
  <td>Volume Bracket</td>
  <td>Price</td>
  <td>Price with Decoration</td>
</tr>

<tr>
  <td>19 - 29</td>
  <td>$26.90</td>
  <td>$29.10</td>
</tr>

<tr>
  <td>30 - 49</td>
  <td>$25.20</td>
  <td>$27.40</td>
</tr>

<tr>
  <td>50 - 99</td>
  <td>$24.20</td>
  <td>$26.40</td>
</tr>

<tr>
  <td>100 - 199</td>
  <td>$23.30</td>
  <td>$25.20</td>
</tr>
  
<tr>
  <td>200 - 299</td>
  <td>$22.40</td>
  <td>$24.05</td>
</tr>

<tr>
  <td>300 - 399</td>
  <td>$21.60</td>
  <td>$22.70</td>
</tr>

<tr>
  <td>400 - 499</td>
  <td>$20.85</td>
  <td>$21.95</td>
</tr>

<tr>
  <td>500 - 1499</td>
  <td>$19.50</td>
  <td>$20.60</td>
</tr>

<tr>
  <td>1500+</td>
  <td>$17.80</td>
  <td>$18.90</td>
</tr>

</table>

</div>




<div id="naidoc-week-2022-customizable-contrast-polos" style="display: none">

<h3>Contrast Polo</h3>

<table class="table-price">

<tr>
  <td>Volume Bracket</td>
  <td>Price</td>
  <td>Price with Decoration</td>
</tr>

<tr>
  <td>15 - 25</td>
  <td>$55.00</td>
  <td>$55.00</td>
</tr>

<tr>
  <td>26 - 49</td>
  <td>$52.25</td>
  <td>$52.25</td>
</tr>

<tr>
  <td>50 - 99</td>
  <td>$49.50</td>
  <td>$49.50</td>
</tr>

<tr>
  <td>100 - 199</td>
  <td>$46.75</td>
  <td>$46.75</td>
</tr>
  
<tr>
  <td>200+</td>
  <td>$44.00</td>
  <td>$44.00</td>
</tr>


</table>

</div>



<div id="naidoc-week-2022-customizable-beach-towels" style="display: none">

<h3>Beach Towel</h3>

<table class="table-price">

<tr>
  <td>Volume Bracket</td>
  <td>Price</td>
  <td>Price with Decoration</td>
</tr>

<tr>
  <td>19 - 29</td>
  <td>$26.40</td>
  <td>$28.05</td>
</tr>

<tr>
  <td>30 - 49</td>
  <td>$25.40</td>
  <td>$26.75</td>
</tr>

<tr>
  <td>50 - 99</td>
  <td>$24.45</td>
  <td>$25.55</td>
</tr>

<tr>
  <td>100 - 199</td>
  <td>$23.55</td>
  <td>$24.65</td>
</tr>
  
<tr>
  <td>200 - 299</td>
  <td>$22.75</td>
  <td>$23.60</td>
</tr>

<tr>
  <td>300 - 399</td>
  <td>$22.00</td>
  <td>$22.85</td>
</tr>

<tr>
  <td>400 - 499</td>
  <td>$21.65</td>
  <td>$22.45</td>
</tr>

<tr>
  <td>500 - 1499</td>
  <td>$20.65</td>
  <td>$21.20</td>
</tr>

<tr>
  <td>1500+</td>
  <td>$19.70</td>
  <td>$20.25</td>
</tr>

</table>

</div>



<div id="naidoc-week-2022-customizable-coffee-mugs" style="display: none">

<h3>Coffee Mugs</h3>

<table class="table-price">

<tr>
  <td>Volume Bracket</td>
  <td>Price</td>
  <td>Price with Decoration</td>
</tr>

<tr>
  <td>19 - 29</td>
  <td>$8.25</td>
  <td>$9.65</td>
</tr>

<tr>
  <td>30 - 49</td>
  <td>$7.05</td>
  <td>$8.45</td>
</tr>

<tr>
  <td>50 - 99</td>
  <td>$6.20</td>
  <td>$7.55</td>
</tr>

<tr>
  <td>100 - 199</td>
  <td>$5.50</td>
  <td>$6.60</td>
</tr>
  
<tr>
  <td>200 - 299</td>
  <td>$4.95</td>
  <td>$6.05</td>
</tr>

<tr>
  <td>300 - 399</td>
  <td>$4.95</td>
  <td>$5.80</td>
</tr>

<tr>
  <td>400 - 499</td>
  <td>$4.95</td>
  <td>$5.80</td>
</tr>

<tr>
  <td>500 - 1499</td>
  <td>$4.95</td>
  <td>$5.50</td>
</tr>

<tr>
  <td>1500+</td>
  <td>$4.95</td>
  <td>$5.50</td>
</tr>

</table>

</div>




<div id="naidoc-week-2022-customizable-stationery" style="display: none">

<h3>Mouse Pads</h3>

<table class="table-price">

<tr>
  <td>Volume Bracket</td>
  <td>Price</td>
  <td>Price with Decoration</td>
</tr>

<tr>
  <td>19 - 29</td>
  <td>$11.00</td>
  <td>$12.65</td>
</tr>

<tr>
  <td>30 - 49</td>
  <td>$10.60</td>
  <td>$11.95</td>
</tr>

<tr>
  <td>50 - 99</td>
  <td>$10.20</td>
  <td>$11.30</td>
</tr>

<tr>
  <td>100 - 199</td>
  <td>$9.80</td>
  <td>$10.90</td>
</tr>
  
<tr>
  <td>200 - 299</td>
  <td>$9.50</td>
  <td>$10.30</td>
</tr>

<tr>
  <td>300 - 399</td>
  <td>$9.15</td>
  <td>$10.00</td>
</tr>

<tr>
  <td>400 - 499</td>
  <td>$9.00</td>
  <td>$9.85</td>
</tr>

<tr>
  <td>500 - 1499</td>
  <td>$8.60</td>
  <td>$9.15</td>
</tr>

<tr>
  <td>1500+</td>
  <td>$8.20</td>
  <td>$8.75</td>
</tr>

</table>

</div>




<div id="naidoc-week-2022-customizable-headwear" style="display: none">

<h3>Caps</h3>

<table class="table-price">

<tr>
  <td>Volume Bracket</td>
  <td>Price</td>
  <td>Price with Decoration</td>
</tr>

<tr>
  <td>19 - 29</td>
  <td>$19.80</td>
  <td>$21.45</td>
</tr>

<tr>
  <td>30 - 49</td>
  <td>$19.05</td>
  <td>$20.40</td>
</tr>

<tr>
  <td>50 - 99</td>
  <td>$18.35</td>
  <td>$19.45</td>
</tr>

<tr>
  <td>100 - 199</td>
  <td>$17.70</td>
  <td>$18.80</td>
</tr>
  
<tr>
  <td>200 - 299</td>
  <td>$17.05</td>
  <td>$17.90</td>
</tr>

<tr>
  <td>300 - 399</td>
  <td>$16.50</td>
  <td>$17.35</td>
</tr>

<tr>
  <td>400 - 499</td>
  <td>$15.80</td>
  <td>$16.60</td>
</tr>

<tr>
  <td>500 - 1499</td>
  <td>$14.60</td>
  <td>$15.15</td>
</tr>

<tr>
  <td>1500+</td>
  <td>$13.15</td>
  <td>$13.70</td>
</tr>

</table>

</div>



<div id="naidoc-week-2022-customizable-bags" style="display: none">

<h3>Tote Bag (Calico)</h3>

<table class="table-price">

<tr>
  <td>Volume Bracket</td>
  <td>Price</td>
  <td>Price with Decoration</td>
</tr>

<tr>
  <td>19 - 29</td>
  <td>$5.70</td>
  <td>$7.40</td>
</tr>

<tr>
  <td>30 - 49</td>
  <td>$5.50</td>
  <td>$6.90</td>
</tr>

<tr>
  <td>50 - 99</td>
  <td>$5.30</td>
  <td>$6.40</td>
</tr>

<tr>
  <td>100 - 199</td>
  <td>$5.10</td>
  <td>$6.20</td>
</tr>
  
<tr>
  <td>200 - 299</td>
  <td>$4.95</td>
  <td>$5.75</td>
</tr>

<tr>
  <td>300 - 399</td>
  <td>$4.75</td>
  <td>$5.60</td>
</tr>

<tr>
  <td>400 - 499</td>
  <td>$4.60</td>
  <td>$5.45</td>
</tr>

<tr>
  <td>500 - 1499</td>
  <td>$4.40</td>
  <td>$4.95</td>
</tr>

<tr>
  <td>1500+</td>
  <td>$4.20</td>
  <td>$4.75</td>
</tr>

</table>

</div>


<div id="naidoc-week-2022-customizable-cotton-bags" style="display: none">

<h3>Tote Bag (Cotton)</h3>

<table class="table-price">

<tr>
  <td>Volume Bracket</td>
  <td>Price</td>
  <td>Price with Decoration</td>
</tr>

<tr>
  <td>19 - 29</td>
  <td>$7.90</td>
  <td>$9.60</td>
</tr>

<tr>
  <td>30 - 49</td>
  <td>$7.60</td>
  <td>$9.00</td>
</tr>

<tr>
  <td>50 - 99</td>
  <td>$7.35</td>
  <td>$8.45</td>
</tr>

<tr>
  <td>100 - 199</td>
  <td>$7.05</td>
  <td>$8.15</td>
</tr>
  
<tr>
  <td>200 - 299</td>
  <td>$6.85</td>
  <td>$7.65</td>
</tr>

<tr>
  <td>300 - 399</td>
  <td>$6.60</td>
  <td>$7.45</td>
</tr>

<tr>
  <td>400 - 499</td>
  <td>$6.50</td>
  <td>$7.30</td>
</tr>

<tr>
  <td>500 - 1499</td>
  <td>$6.20</td>
  <td>$6.75</td>
</tr>

<tr>
  <td>1500+</td>
  <td>$5.90</td>
  <td>$6.45</td>
</tr>

</table>

</div>

<div style="clear: both"></div>

<style>
  .carousel-cell {
    width: 100%;
  }
  
  .carousel-cell img {
    width: 100%;
  }
  
  #dialog-content .maiin-carousel > .carousel-cell img {
    display: none!important;
  }
  
  #dialog-content .maiin-carousel > .carousel-cell:first-child img {
    display: block!important;
  }
</style>


<div id="dialog-content" style="display:none; width: 100%; max-width: 400px">
  
</div>

<script>
document.addEventListener('click', function (event) {
  
  event.preventDefault();

  if (!event.target.closest('.quick-view')) return;

  let dataHandle = event.target.getAttribute("data-handle");

  $('#dialog-content').empty();
  $('#dialog-content').modal();

  $.get('/products/' + dataHandle + '?view=qv',  // url
      function (data, textStatus, jqXHR) { 
          $( "#dialog-content" ).append( data );
    
          setTimeout(function(){
          $('.maiin-carousel').flickity({
            // options
            wrapAround: true,
            prevNextButtons: false
          });
          $('.maiin-carousel').flickity('resize');
          }, 2000);
            
  });

});
</script>

<script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.umd.js"></script>