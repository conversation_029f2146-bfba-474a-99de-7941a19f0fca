<style>
  
  .template-account .Container {
    display: flex;
    flex-wrap: wrap;
  }
  
  .template-account .Container .PageHeader {
    width: 30%;
    margin: 0;
    padding: 50px 20px;
    background-color: #e9f2f3;
  }
  
  .template-account .Container .PageHeader h3 {
    color: #316873;
    font-weight: 600;
    font-size: 26px;
    line-height: 1em;
    margin-bottom: 5px;
  }
  
  .template-account .Container .PageHeader .account-coins {
    margin-bottom: 25px;
    font-size: 18px;
    color: #316873;
  }
  
  .template-account .Container .PageLayout {
    width: 70%;
    margin: 0;
    padding: 50px 20px;
    flex-wrap: wrap;
  }
  
  .template-account .Container .PageLayout .PageLayout__Section--secondary {
    display: none;
  }
  
  ul.account-sidemenu {
    list-style: none;
    margin: 0; padding: 0;
  }
  
  ul.account-sidemenu li {
    text-transform: uppercase;
    margin: 15px 0;
  }
  
  ul.account-sidemenu li.account-active {
    font-weight: bold;
    color: black;
  }
  
  .account-sidemenu li.account-active img {
    opacity: 1;
  }
  
  .account-sidemenu li img {
    width: 18px;
    margin-right: 10px;
    opacity: .6;
  }
  
  @media (max-width: 1024px) {
    .template-account .Container .PageHeader, .template-addresses .Container .AddressList {
      width: 100%;
    }
  }
  
 
  
</style>

<div class="Container Container--narrow">
  
  <header class="PageHeader">
    
    <h3>Hi {{ customer.first_name }}</h3>
    
    <ul class="account-sidemenu">
      
      
      <li class="{% if template == 'customers/account' %}account-active{% endif %}"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/noun_order_-1_2x_c3f7f45d-97e1-4a63-8c03-12a122f3de2f.png?v=**********"><a href="">My Orders</a></li>
      <li class="{% if template == 'customers/addresses' %}account-active{% endif %}"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/noun_account_-1_2x_7d6680d7-28e7-4ee3-9076-ec22d9a662ba.png?v=**********"><a href="/account/addresses">My Details</a></li>
      <li class="" id="rc_link_container">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 58 58" role="img" aria-hidden="true" focusable="false" class="yotpo-widget-campaign-widget-icon" style="color: #848888;"><g fill="none" fill-rule="evenodd"><path fill="currentColor" d="M29.48 51.04c-11.888 0-21.56-9.672-21.56-21.56 0-11.888 9.672-21.56 21.56-21.56 11.888 0 21.56 9.672 21.56 21.56 0 11.888-9.672 21.56-21.56 21.56m0-47.04C15.43 4 4 15.43 4 29.48s11.43 25.48 25.48 25.48 25.48-11.43 25.48-25.48S43.53 4 29.48 4"></path><path fill="currentColor" d="M29.474 19.785a3.875 3.875 0 0 1 3.878 3.864 1.96 1.96 0 1 0 3.92 0c0-3.615-2.487-6.653-5.838-7.526V14.87a1.96 1.96 0 1 0-3.92 0v1.255c-3.345.873-5.824 3.91-5.824 7.525 0 4.292 3.491 7.785 7.784 7.785a3.875 3.875 0 0 1 3.878 3.864 3.883 3.883 0 0 1-3.878 3.878c-2.13 0-3.864-1.74-3.864-3.878a1.96 1.96 0 0 0-3.92 0c0 3.62 2.48 6.663 5.824 7.537v1.256a1.96 1.96 0 0 0 3.92 0v-1.255c3.351-.873 5.838-3.917 5.838-7.538 0-4.292-3.499-7.784-7.798-7.784a3.869 3.869 0 0 1-3.864-3.865 3.869 3.869 0 0 1 3.864-3.864"></path></g></svg>
        <a href="/pages/rewards" style="margin-left: 5px;">My Reward Points</a>
      </li>
      
      {% for tag in customer.tags %}
        {% if tag == 'artist' %}
          <li style="" class="" id="artist_profile"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/noun_account_-1_2x_7d6680d7-28e7-4ee3-9076-ec22d9a662ba.png?v=**********"><a href="/apps/commission">Artist Profile</a></li>
          {% break %}   
        {% endif %}
      {% endfor %}
      <li class=""><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/noun_Log_Out_-1_2x_ec591a0c-1f14-42fc-b5cd-5480c5910ec9.png?v=**********"><a href="/account/logout">Log out</a></li>

    </ul>
    
    {% comment %}
    <a href="/account/logout" class="PageHeader__Back Heading Text--subdued Link Link--primary u-h7">{{ 'customer.account.logout' | t }}</a>
    <h2 class="u-h2" style="">You currently have <a href="/pages/rewards" style="color: #106572; text-decoration: underline"><span data-lion-points></span> Yarn Coins</a></h2>
    
    {% unless customer.metafields.loyaltylion.loyalty_tier == "Yarn Gold" %}
    <h2 class="u-h3" style="padding: 20px; border: 1px solid #e9e9e9; background: #fbfbfb;">Earn double points with Yarn Gold. Sign up for <a style="color: #106572; text-decoration: underline" href="/products/yarn-gold-monthly">Monthly Subscription ($9.95)</a> or <a style="color: #106572; text-decoration: underline" href="/products/yarn-gold">Yearly Subscription ($99)</a> now</h2>
    {% endunless %}
    
    <div class="SectionHeader">
      <h1 class="SectionHeader__Heading Heading u-h1">{{ 'customer.account.title' | t }}</h1>
      <p class="SectionHeader__Description">{{ 'customer.account.welcome' | t: first_name: customer.first_name }}</p>
    </div>
    {% endcomment %}
    
  </header>

  <div class="PageLayout PageLayout--breakLap">
    
      <h3 style="width: 100%; font-size: 26px; margin-bottom: 25px; font-weight: 600; color: black">Order Tracking</h3>
    

    
      <div class="" style="margin-bottom: 40px">
        <form id="trackingForm" action="" method="POST">
            <p><label>Email</label><input type="email" required name="email" class="Form__Input"></p>
            <p><label>Order Number (without # symbol)</label><input type="†ext" required name="order_id" class="Form__Input"></p>
            <button class="Button Button--primary">Submit</button>
          </form>
          <ul id="tracking_urls"></ul>
          <span id="error-message"></span>
      </div>
    
    <h3 style="width: 100%; font-size: 26px; margin-bottom: 25px; font-weight: 600; color: black">Order History</h3>
    {%- comment -%}
    --------------------------------------------------------------------------------------------------------------------
    ORDER HISTORY
    --------------------------------------------------------------------------------------------------------------------
    {%- endcomment -%}

  
     
      
      
      
      
      
      {%- if customer.orders.size == 0 -%}
        <div class="Segment">
          <h2 class="Segment__Title Heading u-h7">{{ 'customer.account.no_orders_title' | t }}</h2>

          <div class="Segment__Content">
            <p>{{ 'customer.account.no_orders_content' | t }}</p>
          </div>
        </div>
      {%- else -%}
        {%- paginate customer.orders by 25 -%}
          <div class="TableWrapper" style="width: 100%">
            <table class="AccountTable Table Table--large">
              <thead class="Text--subdued">
                <tr>
                  <th>{{ 'customer.orders.order_number' | t }}</th>
                  <th>{{ 'customer.orders.date' | t }}</th>
                  <th>{{ 'customer.orders.payment_status' | t }}</th>
                  <th>{{ 'customer.orders.fulfillment_status' | t }}</th>
                  <th class="Text--alignRight">{{ 'customer.orders.total' | t }}</th>
                </tr>
              </thead>

              <tbody class="Heading u-h7">
                {%- for order in customer.orders -%}
                  <tr>
                    <td><a href="{{ order.customer_url }}" class="Link Link--underline">{{ order.name }}</a></td>
                    <td>{{ order.created_at | date: format: 'month_day_year' }}</td>
                    <td>{{ order.financial_status_label }}</td>
                    <td>{{ order.fulfillment_status_label }}</td>
                    <td class="Text--alignRight">{{ order.total_price | money_without_trailing_zeros }}</td>
                  </tr>
                {%- endfor -%}
              </tbody>
            </table>
          </div>

          {%- include 'pagination' -%}
        {%- endpaginate -%}
      {%- endif -%}
    </div>

    {%- comment -%}
    --------------------------------------------------------------------------------------------------------------------
    ADDRESS
    --------------------------------------------------------------------------------------------------------------------
    {%- endcomment -%}

  
    {% comment %}
    <div class="PageLayout__Section PageLayout__Section--secondary">
      <div class="Segment">
       
    	  <!-- End ReCharge code -->
        {%- if customer.addresses_count == 0 -%}
          <h2 class="Segment__Title Heading u-h7">{{ 'customer.account.no_addresses_title' | t }}</h2>

          <div class="Segment__Content">
            <p>{{ 'customer.account.no_addresses_content' | t }}</p>

            <div class="Segment__ButtonWrapper">
              <a href="/account/addresses" class="Button Button--primary">{{ 'customer.account.manage_addresses' | t }}</a>
            </div>
          </div>
        {%- else -%}
          <h2 class="Segment__Title Heading u-h7">{{ 'customer.account.default_address' | t }}</h2>

          <div class="Segment__Content">
            {{ customer.default_address | format_address | replace: '<p>', '<p class="AccountAddress"><span>' | replace_first: '<br>', '</span><br>' }}
            <div class="Segment__ButtonWrapper">
              <a href="/account/addresses" class="Button Button--primary">{{ 'customer.account.edit_addresses' | t }}</a>
            </div>
          </div>
        {%- endif -%}
        
         <h3 class="Segment__Title Heading u-h7" style="margin-top: 30px">Yarn Gold Subscriptions</h3>
         <!-- Begin ReCharge code -->
        
          <p id="rc_link_container">
              <a href="/tools/recurring/login">Manage Gold Subscription</a>
          </p>
    
      </div>
    </div>
   {% endcomment %}
  
  
  
  </div>
</div>

{% comment %}


{{ 'layout.customer.log_out' | t | customer_logout_link }}

        <!-- commission -->
        <div class="commission">
          <style>
            .manage-sub{    
                  display: flex;
                  justify-content: center;
                  margin-bottom: 5%;
                  font-size: 25px;
                  text-decoration: underline;
            }
          </style>
          <br/>
          <a href=/apps/commission class="manage-sub">Artist Profile</a>
        </div>


        

{% endcomment %}


<script type="text/javascript">
  $(document).ready(function() {
      $(document).on("submit", "#trackingForm", function(e) {
          e.preventDefault();
          e.stopPropagation();
        
          $("#tracking_urls").html("");
          $("#error-message").html("searching...");

          $.ajax({
              url: 'https://responsivemedia.com.au/tracking/get_tracking.php',
              type: 'POST',
              data: $(this).serializeArray(),
              dataType: 'json',
              success: function(response) {
                
              $("#tracking_urls,#error-message").html("");
                
                  if(response.status == "success") {
                    if(response.data && response.data.length > 0) {
                      $(response.data).each(function(i, v) {
                        
                        if(v.includes("google")) {
                          $("#error-message").html("Your order contains a face mask. Unfortunately we can't provide explicit tracking for your order, however, <b>your order is on its way.</b>");
                        } else {
                          $("#tracking_urls").append('<li><a href="'+v+'" target="_blank">'+v+'</a></li>');
                        }
                      
                      
                      });
                    } else {
                     	 $("#error-message").html("Your order is still being processed by our warehouse.");
                         console.log(response.data);
                    }
                  } else {
					$("#error-message").html("Oops, looks like your data isn't correct. Please check you entry.");
                    console.log(response.data);
                  }
              },
              error: function() {

              }
          });
      });
  });
</script>