<style>
  
  
  
   .account-sidemenu li.account-active img {
    opacity: 1;
  }
  
  .account-sidemenu li img {
    width: 18px;
    margin-right: 10px;
    opacity: .6;
  }
  
  .template-addresses .Container {
    display: flex;
    flex-wrap: wrap;
  }
  
  .template-addresses .Container .PageHeader {
    width: 30%;
    margin: 0;
    padding: 50px 20px;
    background-color: #e9f2f3;
  }
  
  .template-addresses .Container .PageHeader h3 {
    color: #316873;
    font-weight: 600;
    font-size: 26px;
    line-height: 1em;
    margin-bottom: 5px;
  }
  
  .template-addresses .Container .PageHeader .account-coins {
    margin-bottom: 25px;
    font-size: 18px;
    color: #316873;
  }
  
  .template-addresses .Container .AddressList {
    width: 70%;
    
    padding: 50px 20px;
    flex-wrap: wrap;
  }
  
  .template-addresses .Container .PageLayout .PageLayout__Section--secondary {
    display: none;
  }
  
  ul.account-sidemenu {
    list-style: none;
    margin: 0; padding: 0;
  }
  
  ul.account-sidemenu li {
    text-transform: uppercase;
    margin: 15px 0;
  }
  
  ul.account-sidemenu li.account-active {
    font-weight: bold;
    color: black;
  }
  
  @media (max-width: 1024px) {
    .template-addresses .Container .PageHeader, .template-addresses .Container .AddressList {
      width: 100%;
    }
  }
  
</style>

<div class="Container Container--narrow" data-section-type="addresses" data-section-id="addresses">

    <header class="PageHeader">
    
    <h3>Hi {{ customer.first_name }},</h3>
   
    
    <ul class="account-sidemenu">
      
      <li class="{% if template == 'customers/account' %}account-active{% endif %}"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/noun_order_-1_2x_c3f7f45d-97e1-4a63-8c03-12a122f3de2f.png?v=**********"><a href="/account">My Orders</a></li>
      <li class="{% if template == 'customers/addresses' %}account-active{% endif %}"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/noun_account_-1_2x_7d6680d7-28e7-4ee3-9076-ec22d9a662ba.png?v=**********"><a href="/account/addresses">My Details</a></li>
      <li class="" id="rc_link_container">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 58 58" role="img" aria-hidden="true" focusable="false" class="yotpo-widget-campaign-widget-icon" style="color: #848888;"><g fill="none" fill-rule="evenodd"><path fill="currentColor" d="M29.48 51.04c-11.888 0-21.56-9.672-21.56-21.56 0-11.888 9.672-21.56 21.56-21.56 11.888 0 21.56 9.672 21.56 21.56 0 11.888-9.672 21.56-21.56 21.56m0-47.04C15.43 4 4 15.43 4 29.48s11.43 25.48 25.48 25.48 25.48-11.43 25.48-25.48S43.53 4 29.48 4"></path><path fill="currentColor" d="M29.474 19.785a3.875 3.875 0 0 1 3.878 3.864 1.96 1.96 0 1 0 3.92 0c0-3.615-2.487-6.653-5.838-7.526V14.87a1.96 1.96 0 1 0-3.92 0v1.255c-3.345.873-5.824 3.91-5.824 7.525 0 4.292 3.491 7.785 7.784 7.785a3.875 3.875 0 0 1 3.878 3.864 3.883 3.883 0 0 1-3.878 3.878c-2.13 0-3.864-1.74-3.864-3.878a1.96 1.96 0 0 0-3.92 0c0 3.62 2.48 6.663 5.824 7.537v1.256a1.96 1.96 0 0 0 3.92 0v-1.255c3.351-.873 5.838-3.917 5.838-7.538 0-4.292-3.499-7.784-7.798-7.784a3.869 3.869 0 0 1-3.864-3.865 3.869 3.869 0 0 1 3.864-3.864"></path></g></svg>
        <a href="/pages/rewards" style="margin-left: 5px;">My Reward Points</a>
      </li>
      <li class=""><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/noun_Log_Out_-1_2x_ec591a0c-1f14-42fc-b5cd-5480c5910ec9.png?v=**********"><a href="/account/logout">Log out</a></li>


    </ul>
    
    {% comment %}
    <a href="/account/logout" class="PageHeader__Back Heading Text--subdued Link Link--primary u-h7">{{ 'customer.account.logout' | t }}</a>
    <h2 class="u-h2" style="">You currently have <a href="/pages/rewards" style="color: #106572; text-decoration: underline"><span data-lion-points></span> Yarn Coins</a></h2>
    
    {% unless customer.metafields.loyaltylion.loyalty_tier == "Yarn Gold" %}
    <h2 class="u-h3" style="padding: 20px; border: 1px solid #e9e9e9; background: #fbfbfb;">Earn double points with Yarn Gold. Sign up for <a style="color: #106572; text-decoration: underline" href="/products/yarn-gold-monthly">Monthly Subscription ($9.95)</a> or <a style="color: #106572; text-decoration: underline" href="/products/yarn-gold">Yearly Subscription ($99)</a> now</h2>
    {% endunless %}
    
    <div class="SectionHeader">
      <h1 class="SectionHeader__Heading Heading u-h1">{{ 'customer.account.title' | t }}</h1>
      <p class="SectionHeader__Description">{{ 'customer.account.welcome' | t: first_name: customer.first_name }}</p>
    </div>
    {% endcomment %}
    
  </header>

  {%- if customer.addresses_count > 0 -%}
    {%- paginate customer.addresses by 9 -%}
      <div class="Grid Grid--l AddressList">
        <h3 class="Grid__Cell" style="width: 100%; font-size: 26px; margin-bottom: 25px; font-weight: 600; color: black">My Details</h3>
        {%- for address in customer.addresses -%}
          <div class="Grid__Cell 1/2--tablet 1/3--lap-and-up">
            <div class="Segment">
              {%- if address == customer.default_address -%}
                <h2 class="Segment__Title Heading u-h7">{{ 'customer.addresses.default_address_label' | t }}</h2>
              {%- else -%}
                {%- assign position = paginate.current_page | times: forloop.index -%}
                <h2 class="Segment__Title Heading u-h7">{{ 'customer.addresses.address_label' | t: position: position }}</h2>
              {%- endif -%}

              <div class="Segment__Content">
                {{ address | format_address | replace: '<p>', '<p class="AccountAddress"><span>' | replace_first: '<br>', '</span><br>' }}

                <div class="Segment__ActionList">
                  <button class="Segment__ActionItem Link Link--underline" data-action="open-modal" aria-controls="modal-{{ address.id }}">{{ 'customer.addresses.edit' | t }}</button>
                  <button class="Segment__ActionItem Link Link--underline" onclick="Shopify.CustomerAddress.destroy({{ address.id }}); return false">{{ 'customer.addresses.delete' | t }}</button>
                </div>
              </div>
            </div>
          </div>
        {%- endfor -%}
      </div>

      {%- include 'pagination' -%}
    {%- endpaginate -%}
  {%- endif -%}
</div>

{% comment %}FORM FOR NEW ADDRESS{% endcomment %}

<div id="modal-address-new" class="Modal Modal--address" aria-hidden="true" role="dialog" data-scrollable>
  <button class="Modal__Close Modal__Close--outside" data-action="close-modal">{%- include 'icon' with 'close' -%}</button>

  <header class="Modal__Header">
    <h3 class="Modal__Title Heading u-h2">{{ 'customer.addresses.add_address' | t }}</h3>
    <p class="Modal__Description">{{ 'customer.addresses.form_subtitle' | t }}</p>
  </header>

  <div class="Modal__Content">
    {% form 'customer_address', customer.new_address, class: 'Form Form--spacingTight' %}
      <div class="Form__Item">
        <input type="text" class="Form__Input" name="address[first_name]" value="{{ form.first_name }}" placeholder="{{ 'customer.addresses.first_name' | t }}" aria-label="{{ 'customer.addresses.first_name' | t }}" autofocus>
        <label class="Form__FloatingLabel">{{ 'customer.addresses.first_name' | t }}</label>
      </div>

      <div class="Form__Item">
        <input type="text" class="Form__Input" name="address[last_name]" value="{{ form.last_name }}" placeholder="{{ 'customer.addresses.last_name' | t }}" aria-label="{{ 'customer.addresses.last_name' | t }}">
        <label class="Form__FloatingLabel">{{ 'customer.addresses.last_name' | t }}</label>
      </div>

      <div class="Form__Item">
        <input type="text" class="Form__Input" name="address[company]" value="{{ form.company }}" placeholder="{{ 'customer.addresses.company' | t }}" aria-label="{{ 'customer.addresses.company' | t }}">
        <label class="Form__FloatingLabel">{{ 'customer.addresses.company' | t }}</label>
      </div>

      <div class="Form__Item">
        <input type="text" class="Form__Input" name="address[phone]" value="{{ form.phone }}" placeholder="{{ 'customer.addresses.phone' | t }}" aria-label="{{ 'customer.addresses.phone' | t }}">
        <label class="Form__FloatingLabel">{{ 'customer.addresses.phone' | t }}</label>
      </div>

      <div class="Form__Item">
        <input type="text" class="Form__Input" name="address[address1]" value="{{ form.address1 }}" placeholder="{{ 'customer.addresses.address1' | t }}" aria-label="{{ 'customer.addresses.address1' | t }}">
        <label class="Form__FloatingLabel">{{ 'customer.addresses.address1' | t }}</label>
      </div>

      <div class="Form__Item">
        <input type="text" class="Form__Input" name="address[address2]" value="{{ form.address2 }}" placeholder="{{ 'customer.addresses.address2' | t }}" aria-label="{{ 'customer.addresses.address2' | t }}">
        <label class="Form__FloatingLabel">{{ 'customer.addresses.address2' | t }}</label>
      </div>

      <div class="Form__Item">
        <input type="text" class="Form__Input" name="address[city]" value="{{ form.city }}" placeholder="{{ 'customer.addresses.city' | t }}" aria-label="{{ 'customer.addresses.city' | t }}">
        <label class="Form__FloatingLabel">{{ 'customer.addresses.city' | t }}</label>
      </div>

      <div class="Form__Group">
        <div class="Form__Item">
          <div class="Form__Select Select Select--primary">
            {%- include 'icon' with 'select-arrow' -%}
            <select name="address[country]" title="{{ 'customer.addresses.country' | t }}">{{ all_country_option_tags }}</select>
          </div>
        </div>

        <div class="Form__Item">
          <input type="text" class="Form__Input" name="address[zip]" value="{{ form.zip }}" placeholder="{{ 'customer.addresses.zip' | t }}" aria-label="{{ 'customer.addresses.zip' | t }}">
          <label class="Form__FloatingLabel">{{ 'customer.addresses.zip' | t }}</label>
        </div>
      </div>

      <div class="Form__Item Form__Select Select Select--primary" style="display: none">
        {%- include 'icon' with 'select-arrow' -%}
        <select name="address[province]" title="{{ 'customer.addresses.province' | t }}"></select>
      </div>

      <div class="Form__Item">
        <div class="Form__CheckboxWrapper">
          <input type="checkbox" class="Form__Checkbox" name="address[default]" id="address-new[default]" value="0">
          {% include 'icon' with 'checkmark' %}

          <label for="address-new[default]">{{ 'customer.addresses.set_default' | t }}</label>
        </div>
      </div>

      <button class="Form__Submit Button Button--primary Button--full">{{ 'customer.addresses.add_address' | t }}</button>
    {% endform %}
  </div>
</div>

{% comment %}FORM FOR EXISTING ADDRESSES{% endcomment %}

{%- paginate customer.addresses by 9 -%}
  {%- for address in customer.addresses -%}
    <div id="modal-{{ address.id }}" class="Modal Modal--address" aria-hidden="true" role="dialog" data-scrollable>
      <button class="Modal__Close Modal__Close--outside" data-action="close-modal">{%- include 'icon' with 'close' -%}</button>

      <header class="Modal__Header">
        <h3 class="Modal__Title Heading u-h2">{{ 'customer.addresses.edit_address' | t }}</h3>
        <p class="Modal__Description">{{ 'customer.addresses.form_subtitle' | t }}</p>
      </header>

      <div class="Modal__Content">
        {% form 'customer_address', address, class: 'Form Form--spacingTight' %}
          <div class="Form__Item">
            <input type="text" class="Form__Input" name="address[first_name]" value="{{ form.first_name }}" placeholder="{{ 'customer.addresses.first_name' | t }}" aria-label="{{ 'customer.addresses.first_name' | t }}" autofocus>
            <label class="Form__FloatingLabel">{{ 'customer.addresses.first_name' | t }}</label>
          </div>

          <div class="Form__Item">
            <input type="text" class="Form__Input" name="address[last_name]" value="{{ form.last_name }}" placeholder="{{ 'customer.addresses.last_name' | t }}" aria-label="{{ 'customer.addresses.last_name' | t }}">
            <label class="Form__FloatingLabel">{{ 'customer.addresses.last_name' | t }}</label>
          </div>

          <div class="Form__Item">
            <input type="text" class="Form__Input" name="address[company]" value="{{ form.company }}" placeholder="{{ 'customer.addresses.company' | t }}" aria-label="{{ 'customer.addresses.company' | t }}">
            <label class="Form__FloatingLabel">{{ 'customer.addresses.company' | t }}</label>
          </div>

          <div class="Form__Item">
            <input type="text" class="Form__Input" name="address[phone]" value="{{ form.phone }}" placeholder="{{ 'customer.addresses.phone' | t }}" aria-label="{{ 'customer.addresses.phone' | t }}">
            <label class="Form__FloatingLabel">{{ 'customer.addresses.phone' | t }}</label>
          </div>

          <div class="Form__Item">
            <input type="text" class="Form__Input" name="address[address1]" value="{{ form.address1 }}" placeholder="{{ 'customer.addresses.address1' | t }}" aria-label="{{ 'customer.addresses.address1' | t }}">
            <label class="Form__FloatingLabel">{{ 'customer.addresses.address1' | t }}</label>
          </div>

          <div class="Form__Item">
            <input type="text" class="Form__Input" name="address[address2]" value="{{ form.address2 }}" placeholder="{{ 'customer.addresses.address2' | t }}" aria-label="{{ 'customer.addresses.address2' | t }}">
            <label class="Form__FloatingLabel">{{ 'customer.addresses.address2' | t }}</label>
          </div>

          <div class="Form__Item">
            <input type="text" class="Form__Input" name="address[city]" value="{{ form.city }}" placeholder="{{ 'customer.addresses.city' | t }}" aria-label="{{ 'customer.addresses.city' | t }}">
            <label class="Form__FloatingLabel">{{ 'customer.addresses.city' | t }}</label>
          </div>

          <div class="Form__Group">
            <div class="Form__Item">
              <div class="Form__Select Select Select--primary">
                {%- include 'icon' with 'select-arrow' -%}
                <select name="address[country]" title="{{ 'customer.addresses.country' | t }}" data-default="{{ form.country }}">{{ all_country_option_tags }}</select>
              </div>
            </div>

            <div class="Form__Item">
              <input type="text" class="Form__Input" name="address[zip]" value="{{ form.zip }}" placeholder="{{ 'customer.addresses.zip' | t }}" aria-label="{{ 'customer.addresses.zip' | t }}">
              <label class="Form__FloatingLabel">{{ 'customer.addresses.zip' | t }}</label>
            </div>
          </div>

          <div class="Form__Item Form__Select Select Select--primary" style="display: none">
            {%- include 'icon' with 'select-arrow' -%}
            <select name="address[province]" title="{{ 'customer.addresses.province' | t }}" data-default="{{ form.province }}"></select>
          </div>

          <div class="Form__Item">
            <div class="Form__CheckboxWrapper">
              <input type="checkbox" class="Form__Checkbox" id="address-{{ address.id }}[default]" name="address[default]" {% if address.id == customer.default_address.id %}value="1" checked{% endif %}>
              {% include 'icon' with 'checkmark' %}

              <label for="address-{{ address.id }}[default]">{{ 'customer.addresses.set_default' | t }}</label>
            </div>
          </div>

          <button class="Form__Submit Button Button--primary Button--full">{{ 'customer.addresses.edit_address' | t }}</button>
        {% endform %}
      </div>
    </div>
  {%- endfor -%}
{%- endpaginate -%}