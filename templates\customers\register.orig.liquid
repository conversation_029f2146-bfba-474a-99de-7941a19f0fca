<section data-section-id="reset-password" data-section-type="reset-password">
  <div class="Container">
    <div class="PageContent PageContent--fitScreen PageContent--extraNarrow">
      {%- form 'create_customer', name: 'create_customer', class: 'Form Form--spacingTight', id: 'create_customer' -%}
      <input type="hidden" name="return_to" value="/pages/rewards">
        <header class="Form__Header">
          <h1 class="Form__Title Heading u-h1">Create an account to join Yarn Rewards</h1>
          <p class="Form__Legend">Already have an account? <a href="/account/login" style="text-decoration: underline">Login here</a></p>
        </header>

        {%- if form.errors -%}
          <div class="Form__Alert Alert Alert--error">
            <ul class="Alert__ErrorList">
              {%- for field in form.errors -%}
                {%- if field == 'form' -%}
                  <li class="Alert__ErrorItem">{{ form.errors.messages[field] }}</li>
                {%- else -%}
                  <li class="Alert__ErrorItem"><strong>{{ form.errors.translated_fields[field] }}</strong> {{ form.errors.messages[field] }}</li>
                {%- endif -%}
              {%- endfor -%}
            </ul>
          </div>
        {%- endif -%}

        <div class="Form__Item">
          <input type="text" class="Form__Input" name="customer[first_name]" aria-label="{{ 'customer.register.first_name' | t }}" placeholder="{{ 'customer.register.first_name' | t }}" autofocus>
          
        </div>

        <div class="Form__Item">
          <input type="text" class="Form__Input" name="customer[last_name]" aria-label="{{ 'customer.register.last_name' | t }}" placeholder="{{ 'customer.register.last_name' | t }}">
          
        </div>

        <div class="Form__Item">
          <input type="email" class="Form__Input" name="customer[email]" aria-label="{{ 'customer.register.email' | t }}" placeholder="{{ 'customer.register.email' | t }}" required="required">
          
        </div>

        <div class="Form__Item">
          <input type="password" class="Form__Input" name="customer[password]" aria-label="{{ 'customer.register.password' | t }}" placeholder="{{ 'customer.register.password' | t }}" required="required">
          
        </div>
      
        <input type="hidden" name="contact[tags]" value="newsletter">
      
        <p>*By creating your account, you agree to receive emails regarding sales, deals and discounts from us and can opt out at any time.</p>

        <button type="submit" class="Form__Submit Button Button--primary Button--full">{{ 'customer.register.submit' | t }}</button>
      {%- endform -%}
    </div>
  </div>
</section>