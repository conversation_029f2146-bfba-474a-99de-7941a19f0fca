<section data-section-id="reset-password" data-section-type="reset-password">
  <div class="Container">
    <div class="PageContent PageContent--fitScreen PageContent--extraNarrow">
      {%- form 'reset_customer_password', name: 'activate', class: 'Form Form--spacingTight', id: 'reset_customer_password' -%}
        <header class="Form__Header">
          <h1 class="Form__Title Heading u-h1">{{ 'customer.reset_password.title' | t }}</h1>
          <p class="Form__Legend">{{ 'customer.reset_password.description' | t }}</p>
        </header>

        {%- if form.errors -%}
          <div class="Form__Alert Alert Alert--error">
            <ul class="Alert__ErrorList">
              {%- for field in form.errors -%}
                {%- if field == 'form' -%}
                  <li class="Alert__ErrorItem">{{ form.errors.messages[field] }}</li>
                {%- else -%}
                  <li class="Alert__ErrorItem"><strong>{{ form.errors.translated_fields[field] }}</strong> {{ form.errors.messages[field] }}</li>
                {%- endif -%}
              {%- endfor -%}
            </ul>
          </div>
        {%- endif -%}

        <div class="Form__Item">
          <input type="password" class="Form__Input" name="customer[password]" aria-label="{{ 'customer.reset_password.password' | t }}" placeholder="{{ 'customer.reset_password.password' | t }}" autofocus>
          <label class="Form__FloatingLabel">{{ 'customer.reset_password.password' | t }}</label>
        </div>

        <div class="Form__Item">
          <input type="password" class="Form__Input" name="customer[password_confirmation]" aria-label="{{ 'customer.reset_password.password_confirmation' | t }}" placeholder="{{ 'customer.reset_password.password_confirmation' | t }}">
          <label class="Form__FloatingLabel">{{ 'customer.reset_password.password_confirmation' | t }}</label>
        </div>

        <button type="submit" class="Form__Submit Button Button--primary Button--full">{{ 'customer.reset_password.submit' | t }}</button>
      {%- endform -%}
    </div>
  </div>
</section>