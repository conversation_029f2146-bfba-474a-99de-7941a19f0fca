{% layout none %}{% paginate collections by 1000 %}{% capture output %}[{% for collection in collections %}{% if collection.image %}{% assign thumbURL = collection | img_url: 'small' %}{% else %}{% assign thumbURL = collection.products.first | img_url: 'small' %}{% endif %}{"title":{{ collection.title | json }},"price":"","price2":"","url":{{ collection.url | json }},"thumb":{{ thumbURL | json }},"id":{{collection.id}},"preferred":"0","published_at":"{{collection.published_at}}","created_at":"{{collection.updated_at}}",{% comment %}"vendor":"{% include 'json_cleanup' with collection.all_vendors %}",{% endcomment %}{% comment %}"type":"{% include 'json_cleanup' with collection.all_types %}",{% endcomment %}{% comment %}"tags":"{% include 'json_cleanup' with collection.all_tags %}",{% endcomment %}"object_type":"collection","sku":"","allinfo":""},{% endfor %}{"title":"", "id":"", "price":"", "allinfo":""}]{% endcapture %}{{ output | strip_newlines }}{% endpaginate %}