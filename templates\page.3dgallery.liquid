<style>
[style*="--aspect-ratio"] > :first-child {
  width: 100%;
}
[style*="--aspect-ratio"] > img {  
  height: auto;
} 
@supports (--custom:property) {
  [style*="--aspect-ratio"] {
    position: relative;
  }
  [style*="--aspect-ratio"]::before {
    content: "";
    display: block;
    padding-bottom: calc(100% / (var(--aspect-ratio)));
  }  
  [style*="--aspect-ratio"] > :first-child {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
  }  
}
  
.gallp {
  margin: 20px;
}
  
@media (min-width: 600px) {
  .gallp {
    position: absolute; z-index: 9;
  }
}
  

</style>
<div style="position: relative">
  
<p class="gallp" style=""><a href="/collections/yarn-gallery" style="font-weight: 600">Back to Yarn Gallery Collection</a></p>

  <div style="--aspect-ratio: 16/9;">

    
    {{page.content}}
 

</div>
  </div>