<style>

  #timer {
    font-size: 4em;
    line-height: 1em;
    font-weight: 600;
    color: white;
    text-align: center;
  }

  #timer div {
    display: inline-block;
    min-width: 90px;
    padding: 0 10px;
    text-align: center;
  }

  #timer div:not(:last-child) {
    border-right: 2px solid white;
  }

  #timer div span {
    color: #ffffff;
    display: block;
    font-size: .25em;
    font-weight: 600;
    line-height: 1.2em;
    letter-spacing: 1px;
    margin: 5px 0;
  }
  
  .koa-section {
    padding: 60px 0 60px;
    //background: url(https://cdn.shopify.com/s/files/1/0247/4021/files/j.jpg?v=1665544263) no-repeat top center;
    //background-size: cover;
    color: white;
    position: relative;
    background: black;
  }

  .koa-section:before {
    
    content: '';
    position: absolute;
    top: -50px; left: 0; right: 0; bottom: 0;
    background: url(https://cdn.shopify.com/s/files/1/0247/4021/files/j.jpg?v=1665544263) no-repeat top center;
    background-size: cover;
  }

 

  .koa-section p {
    font-size: 17px;
  }

  .koa-section:after {
    content: '';
    display: table;
    clear: both;
  }

  .koa-section .container {
    width: 1200px;
    max-width: 100%;
    padding: 0 20px;
    margin: 0 auto;
    position: relative;
  }

  .koa-content {
    width: 500px;
    max-width: 100%;
    float: right;
  }

  .cd-pic {
    display: none;
  }

  .koa-brand {
      position: relative;
      left: auto;
      top: auto;
      text-align: center;
      width: 100%;
    }

    .koa-brand img {
    }

  @media (max-width: 1140px) {
    .koa-content {
      width: 450px;
      float: right;
    }

    
  }

   @media (min-width: 1140px) {
    .koa-section { 
      background-size: cover;
    }
  }


  @media (max-width: 940px) {
    .koa-content {
      width: 600px;
      margin: 0 auto;
      float: none;
    }

    .cd-pic {
      display: block;
    }

    .koa-section:before {
      background: url(https://cdn.shopify.com/s/files/1/0247/4021/files/j.jpg?v=1665544263) no-repeat top right;
      opacity: .8;
      background-size: cover;
      top: 0;
    }

    #timer {
      font-size: 3em;
    }

    #timer div {
      min-width: 75px;
      padding: 0 5px;
    }
  }
  
</style>

<section class="koa-section">
  <div class="container">

    <div class="koa-content">
      <p style="text-align: center; margin: 0"><img style="width: 280px" src="https://cdn.shopify.com/s/files/1/0247/4021/files/ogo.png?v=1665544271"></p>
      <div class="koa-brand" style="margin-bottom: 20px"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/Group_3171.png?v=1665544271"></div>
      
      {% comment %}
      <div id="timer" style="margin-bottom: 20px"></div>
      {% endcomment %}
      
      <p class="cd-pic"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/h5.png?v=1665632346"></p>

      <h2 class="u-h1"><img style="width: 200px" src="https://cdn.shopify.com/s/files/1/0247/4021/files/comingsoon.png?v=1665631946"></h2>
      <p>In collaboration with Yorta Yorta artist <span style="color: #E2A060; font-weight: bold">Alkina Edwards</span>, this empowering collection celebrates and acknowledges the significance women have on country as well as their connection to it! This brand new collection features 11 new artworks that tell stories of Country and the inspirational women who live there. It’s time to support and empower the important women in your community! <span style="color: #E2A060; font-weight: bold">Sign up to our newsletter and be the first to know when this collection drops!</span></p>

      <div style="margin: 25px 0"><div class="klaviyo-form-WC3swr"></div></div>
    </div>

  </div>
</section>



<script type="text/javascript">
function updateTimer() {
    future = Date.parse("dec 28, 2022 17:00:00");
    now = new Date();
    diff = future - now;

    days = Math.floor(diff / (1000 * 60 * 60 * 24));
    hours = Math.floor(diff / (1000 * 60 * 60));
    mins = Math.floor(diff / (1000 * 60));
    secs = Math.floor(diff / 1000);

    d = days;
    h = hours - days * 24;
    m = mins - hours * 60;
    s = secs - mins * 60;

    document.getElementById("timer")
        .innerHTML =
        '<div>' + d + '<span>DAYS</span></div>' +
        '<div>' + h + '<span>HOURS</span></div>' +
        '<div>' + m + '<span>MINS</span></div>' +
        '<div>' + s + '<span>SECS</span></div>';
}
setInterval('updateTimer()', 1000);
</script>