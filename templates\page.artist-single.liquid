<style>
  .flickity-prev-next-button {
    top: 50%;
  }
  .flickity-prev-next-button.next {
    right: 0;
  }
  .flickity-prev-next-button.previous {
    left: 0;
  }
</style>

<div class="main-custom-page-width" style="width: 1200px; max-width: 100%; margin: 0 auto">


<div class="PageContentt">
  
    
{% section 'section-new-artist' %}
    
    
</div>
  
  
</div>


<script>
  var paintings =  document.querySelector(".paintings-carousel");
if (typeof(paintings) != 'undefined' && paintings != null)
{
  let flktyy = new Flickity('.paintings-carousel', {
    "wrapAround": false, 
    "pageDots": false,
    "initialIndex": 1
  });
  
   window.addEventListener('load', function(event) {
    flktyy.on( 'settle', function( index ) {
      flktyy.resize();
  });
    
  flktyy.resize();
    
  });
}
  
  
  
 

   
</script>