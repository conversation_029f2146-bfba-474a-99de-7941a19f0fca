<script src="https://cdnjs.cloudflare.com/ajax/libs/sticky-js/1.2.2/sticky.min.js"></script> 

<style>

  .artists-banner {
    background: url(https://cdn.shopify.com/s/files/1/0247/4021/files/artist-banner.jpg?v=1635329021) no-repeat center center;
    background-size: cover;
    height: 300px;
  }
  
  #artists-page .wrapper{
    width: 1200px;
    max-width: 100%;
    padding: 0 20px;
    margin: 0 auto;
  }
  
  .artists-intro {
    text-align: center;
    margin: 40px 0;
  }
  
  .artists-intro h1 {
    font-size: 28px;
    font-weight: 600;
    color: #707070;
    
  }
  
  .artists-filter {
    mmargin: 40px 0 20px;
    text-align: center;
    background: white;
    position: relative;
    z-index: 3;
  }
  
  @media (min-width: 641px) {
    .artists-filter.is-sticky {
        margin-top: 82px;
        
      }
    }


  @media (min-width: 1240px) {
    .artists-filter.is-sticky {
        margin-top: 140px;
      }
    }
  
  .artist-filter .artists-label {
    font-size: 16px;
  }
  
  .artists-filter ul {
    list-style: none;
    text-align: center;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    padding: 15px 0;
  }
  
  .artists-filter li {
    font-weight: 600;
    font-size: 22px;
    color: #707070;
    padding: 0 10px;
    margin: 0;
    line-height: 1.3em;
  }
  
  .artists-label {
    display: inline-block;
    color: #106572;
    background: #B5CFD3;
    padding: 0 10px;
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
  }
  
  .artists-grid {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 50px;
  }
  
  .artists-letter {
    width: 100%;
    margin-top: 30px;
    font-size: 30px;
    font-weight: 600;
    color: #707070;
  }
  
  .artists-profile {
    width: 33.3%;
    margin-top: 30px;
    display: flex;
  }
  
  @media (max-width: 900px) {
    .artists-profile {
      width: 50%;
    }
  }
  
  @media (max-width: 550px) {
    .artists-profile {
      width: 100%;
    }
  }
  
  .artists-profile > div {
    width: 50%;
  }
  
  .artists-profile .artists-label {
    font-size: 10px;
  }
  
  .profile-image {
    padding: 0 15px;
    
  }
  
  @media (max-width: 550px) {
    .artists-profile .profile-image {
      padding: 0 15px 0 0;
      width: 35%;
    }
    
    .artists-profile .profile-content {
      width: 65%;
    }
  }
  
  .profile-image img {
    border-radius: 50%;
  }
  
  .profile-image span {
    border-radius: 50%;
    width: 100%;
    display: block;
    padding-top: 100%;
    position: relatve;
    background-color: #106572;
  }
  
  .artists-name {
    font-weight: 300;
    font-size: 24px;
    margin-bottom: 7px;
    margin-top: 7px;
    line-height: 28px;
  }
  
  .artists-place {
    margin-bottom: 10px;
  }
  
  .artists-link {
    color: #106572;
    font-weight: 600;
    display: inline-block;
    border-bottom: 2px solid #106572;
  }
  
  @media (max-width: 640px) {
    #site-header.hidetop:not(.hide) + #main .artists-filter {
      margin-top: 72px!important;
    }
  }
  
  
  
</style>

<div id="artists-page">
  
<div class="artists-banner">
  
</div>

<div class="artists-intro">
  <div class="wrapper">
    <h1>ARTISTS</h1>
    <p>From humble beginnings, Yarn's mission has always been to support Indigenous art and culture. Our determination to promote and share this amazing culture has provided us the opportunity to work alongside many amazing Indigenous artists from all over Australia. Below is a collection of artists - past and present - who have shared their stories with us.</p>
  </div>
</div>
  
{% section 'section-new-artist' %}

  
</div>

<script>
  var stickyz = new Sticky('.artists-filter');
</script>