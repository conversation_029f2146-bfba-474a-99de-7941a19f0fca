<main id="main" role="main">

    
    {% section 'section-black-friday' %}
    

</main>

<script src="https://cdnjs.cloudflare.com/ajax/libs/sticky-js/1.2.2/sticky.min.js"></script> 

{% comment %}
<script src="https://cdn.jsdelivr.net/gh/cferdinandi/smooth-scroll@15.0.0/dist/smooth-scroll.polyfills.min.js"></script>
{% endcomment %}

<script>
  
  $(document).ready( function() {
 
    
    let headrh = $('#section-header').outerHeight() + 50;
    //console.log(headrh);


    $('.scrollme').on('click', function (el) {
      el.preventDefault();

      $('html, body').animate({
        scrollTop: $($(this).attr('href')).offset().top
      }, 700, 'linear');
    });
    
    
    //stickybits('.naidoc-submenu');
    var stickyz = new Sticky('.naidoc-submenu');
    //var scroll = new SmoothScroll('[data-scroll]');
    
    
    
    $('.Section--bundles .ProductForm__AddToCart').click( function(e) {
        e.preventDefault();
        // .btn animation
      
      console.log(1);

        let bundleItems = [];

        $(this).closest('.ProductList').find('.product-form__variants option:selected').each( function(){
            let variantId = $(this).val();

            bundleItems.push({
                quantity: 1,
                id: variantId
            });
          
            console.log(2);
        });
      
        $(this).closest('.ProductList').find('.bundle-input').each( function(){
            let variantId = $(this).val();

            bundleItems.push({
                quantity: 1,
                id: variantId
            });
          
          console.log(bundleItems);
        });



        jQuery.post('/cart/add.js', {
            items: bundleItems
        }, function( data ) {
            console.log(4);
            window.location.href = '/cart';
        }, "json");


    });
    
    
    
  
  });
  
  
</script>
