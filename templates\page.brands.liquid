<div class="main-custom-page-width">

 
    <div class="SectionHeader SectionHeader" style="display: none">
      <h1 class="SectionHeader__Heading Heading u-h1">{{ page.title }}</h1>
    </div>


  <div class="PageContentt Rte">
    
    {% section 'custom-brands' %}
 
  </div>

</div>

<script>
  function getQueryStringParameters() {
     var parameters = {}, hash;
     var q = document.URL.split('?')[1];
     if(q != undefined) {
         q = q.split('&');
         for(var i = 0; i < q.length; i++) {
             hash = q[i].split('=');
             parameters[hash[0]] = hash[1];
         }
     }
     return parameters;
  }
  
  function isEmpty(obj) {
   for(var key in obj) {
   if(obj.hasOwnProperty(key))
   return false;
   }
   return true;
  }
  
  $(document).ready(function() {
   var parameters = getQueryStringParameters();
   if(isEmpty(parameters) == false) {
   $.each(parameters, function(key, value) {
   
     
     
     if (value == 'clothing') {
       rudrSwitchTab('tb_1', 'content_1');
     } else if(value == 'homeware') {
       rudrSwitchTab('tb_2', 'content_2');
     } else if(value == 'beauty') {
       rudrSwitchTab('tb_3', 'content_3');
     } else if(value == 'jewellery') {
       rudrSwitchTab('tb_4', 'content_4');
     } else if(value == 'food') {
      rudrSwitchTab('tb_5', 'content_5');
     }
     
     
   });
   }
  });
  
</script>