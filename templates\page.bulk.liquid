<main id="main" role="main">

    
    {% section 'bulk-sections' %}
    

</main>

<script src="https://cdnjs.cloudflare.com/ajax/libs/sticky-js/1.2.2/sticky.min.js"></script> 


<script>
  
  $(document).ready( function() {
  
    /*
    $('.carousel-naidoc').flickity({
      // options
      cellAlign: 'left',
      contain: true,
      autoPlay: 3000,
      pageDots: false,
      wrapAround: true,
      pauseAutoPlayOnHover: false
    });
    */
    
    let headrh = $('#section-header').outerHeight() + 50;
    //console.log(headrh);
    
    /*
    $('a[href="/pages/naidoc-2020"] + .DropdownMenu .Link--secondary').each(function( index ) {
      
      $(this).addClass('scrollme');
      let href = $(this).attr('href');
      href = href.split("#").pop();
      $(this).attr("href", "#" + href);
      
    });
    */
    
    $('.ProductItem__Wrapper a').on( 'click', function(e) {
      e.preventDefault();
      
      let hreff = $(this).attr('href');
      window.location.href = hreff + '?view=bulk';
    });
    
    $('.SidebarMenu__Nav a[href*="/collections/bulk"]').click( function( e ) {
        e.preventDefault();
        //do some other stuff here
        let hreff = $(this).attr('href');
        window.location.replace(hreff);
        //window.location.href = hreff;
    });


    
    $('.scrollme').on('click', function (el) {
      el.preventDefault();

      $('html, body').animate({
        scrollTop: $($(this).attr('href')).offset().top
      }, 700, 'linear');
    });
    
    
    //stickybits('.naidoc-submenu');
    var stickyz = new Sticky('.naidoc-submenu');
    //var scroll = new SmoothScroll('[data-scroll]');
    
    
    
    $('.Section--bundles .ProductForm__AddToCart').click( function(e) {
        e.preventDefault();
        // .btn animation
      
      console.log(1);

        let bundleItems = [];

        $(this).closest('.ProductList').find('.product-form__variants option:selected').each( function(){
            let variantId = $(this).val();

            bundleItems.push({
                quantity: 1,
                id: variantId
            });
          
            console.log(2);
        });
      
        $(this).closest('.ProductList').find('.bundle-input').each( function(){
            let variantId = $(this).val();

            bundleItems.push({
                quantity: 1,
                id: variantId
            });
          
          console.log(bundleItems);
        });



        jQuery.post('/cart/add.js', {
            items: bundleItems
        }, function( data ) {
            console.log(4);
            window.location.href = '/cart';
        }, "json");


    });
    
    
    
  
  });
  
  
</script>