<style>
.main-custom-page-width {
  padding: 0;
}
.PageContent--narrow {
    max-width: 100%;
}
.banner {
  background: url(https://cdn.shopify.com/s/files/1/0247/4021/files/DSC08315_2x_61e7954e-fb74-4ff9-a77c-76edd72bff69.jpg?v=1627174496) no-repeat 50% 30%;
  background-size: cover;
  padding: 80px 0;
}
.full-width {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}
.container {
  margin: 0 auto;
  width: 1100px;
  padding: 0 20px;
  max-width: 100%;
}

.banner h1 {
  font: normal normal normal 64px/77px Qualy;
  color: #106572;
  margin: 0;
}

.career-intro {
  margin: 60px 0;
}

.career-intro h2 {
  font: normal normal normal 33px/39px Qualy;
  letter-spacing: 0.66px;
  color: #106572;
  text-align: center;
  margin: 0 0 25px;
}

.career-intro p {
  font: normal normal 400 17px/29px Visby CF;
  letter-spacing: 0px;
  color: #106572;
}

.career-cta {
  text-align: right;
  color: #106572;
  margin: 50px 0;
}

.career-cta h3 {
  font: normal normal normal 55px/66px Qualy;
  margin: 0;
  color: #106572;
}

.career-cta p {
  font: normal normal normal 33px/39px Qualy;
}

.accordion {
  box-sizing: border-box;
  display: flex;
  font-family: 'Visby CF', 'Source Sans Pro', sans-serif;
  overflow: hidden;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.accordion-select {
  cursor: pointer;
  margin: 0;
  opacity: 0;
  z-index: 1;
margin-top: 15px;
}

.accordion.yarn-accordion {
    width: 100%;
    height: 100%;
    display: grid;
}
  
.accordion-title {
  position: relative;
  display: flex;
  align-items: center;
  border-radius: 20px;
  font-weight: 600!important;
}

.accordion-title:not(:nth-last-child(2))::after {
  border: 1px solid transparent;
  bottom: 0;
  content: '';
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}

.accordion-title:before {
  content: '+';
  position: absolute;
  top: 5px;
  right: 15px;
  color: white;
  font-size: 22px;
}

.accordion-select:checked + .accordion-title:before {
  content: '-';
}

.accordion-title span {
  bottom: 0px;
  box-sizing: border-box;
  display: block;
  line-height: 1em;
  width: 100%;
}

.accordion-content {
  box-sizing: border-box;
  overflow: auto;
  position: relative;
  transition: margin 0.3s ease 0.1s;
  text-align: left;
  background-color: white;
  border-radius: 0 0 20px 20px;
}

.accordion-select:checked + .accordion-title {
  border-radius: 20px 20px 0 0;
}

.accordion-select:checked + .accordion-title + .accordion-content {
  margin-bottom: 0;
  margin-right: 0;
  background-color: #cff5f5;
  height: 100%;
  display: inline-table;
}

.accordion.yarn-accordion {
   border-color: #dedede;
   border-radius: 0px;
   border-style: solid;
   border-width: 1px;
   flex-direction: column;
   height: auto;
border: none;
} 

    .yarn-accordion.yarn-accordion .accordion-title,
    .yarn-accordion.yarn-accordion .accordion-select  {
      background-color: #106572;
      color: #ffffff;
      width: 100%;
      min-height: 52px;
      font-size: 16px;
      font-weight: normal;
      text-transform: uppercase;
}

.yarn-accordion.yarn-accordion .accordion-select {
    margin-bottom: -52px;
    margin-right: -52px;
}

    .yarn-accordion.yarn-accordion .accordion-title:not(:nth-last-child(2))::after {
     // border-bottom-color: rgb(219, 219, 219);
     // border-right-color: transparent;
    } 

    .yarn-accordion.yarn-accordion .accordion-select:hover + .yarn-accordion.yarn-accordion .accordion-title,
    .yarn-accordion.yarn-accordion .accordion-select:checked + .yarn-accordion.yarn-accordion .accordion-title {
      background-color: #ffffff;
    } 

     .yarn-accordion.yarn-accordion .accordion-title span  {	
        transform: rotate(0deg);
        -ms-writing-mode: lr-tb;
        filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=0);
        padding-left: 26px;
        padding-right: 26px;
        line-height: 1em;
    } 

    .yarn-accordion.yarn-accordion .accordion-content {
     // background-color: #cff5f5;
      color: #5c5c5c;
      height: 430px;
      margin-bottom: -430px;
      margin-right: 0;
      padding: 30px;
      width: 100%;
      font-size: 16px;
    } 

.yarn-accordion.yarn-accordion .accordion-content h4 {
  margin: 0 0 15px;
  font-weight: bold;
}

.ll-faq {
  margin: 30px 0 0;
}

.accordion-select:not(:checked)  + .accordion-title + .accordion-content {
    -ms-overflow-style: none; /* for Internet Explorer, Edge */
    scrollbar-width: none; /* for Firefox */
    overflow-y: scroll; 
}

.accordion-select:not(:checked)  + .accordion-title + .accordion-content::-webkit-scrollbar {
    display: none; /* for Chrome, Safari, and Opera */
}

.banner-team .is-mobile {
  display: none;
}

div#shopify-section-jobs {
    margin-bottom: 50px;
}

@media (max-width: 600px) {
  .banner h1 {
    font: normal normal normal 44px/57px Qualy;
  }
.career-intro p {
  font: normal normal 400 16px/26px Visby CF;
}
  .career-cta h3 {
    font: normal normal normal 35px/46px Qualy;
  }

.career-cta p {
   font: normal normal bold 23px/29px Visby CF;
  }
  .banner-team .is-mobile {
    display: block;
  }
  .banner-team .is-desk {
    display: none;
  }
}
</style>




{% if page.content contains "split" %}
{% assign my_description = page.content | split: '<!-- split -->'  %}
{{ my_description[0] }}
{% endif %}
<div class="main-custom-page-width">

  <div class="PageContent--narrow Rte {% if page.handle contains "promotional-products"%} enquiry_custom{% endif %}">
    
    
    
    <div class="full-width">
    <div class="container">
      
      <div class="banner full-width">
        <div class="container">
        <h1 class="">careers</h1>
        </div>
      </div>
      
      <div class="career-intro">
        <h2 class="">about us.</h2>
        <p>Regal Sportswear is a multi brand group that specialises in fashion retail and promotional products. Yarn, as our major retail brand, is a community marketplace that features a diverse range of products and brands that showcase authentic Indigenous artwork. We help First Nations owned brands to grow and connect with new customers, through the assistance of our logistic infrastructure and marketing expertise. We showcase one of a kind designs - designs that speak of country, culture, and the Dreamtime. Through featuring these beautiful designs and products Yarn is a platform dedicated to supporting First Nations artists, art centres, community organisations and small businesses.</p>
      </div>
      
      <div class="ll-faq" id="faqs">
      <div class="">
      <div class="faq">
        
        {% section "jobs" %}
  
      </div>
      </div>
      </div>

  
      
    </div>
    </div>
    
    
 
  </div>

</div>