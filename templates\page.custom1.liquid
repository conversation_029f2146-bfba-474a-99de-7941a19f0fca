<div id='woobox-root'></div>
<script>
	(function(d, s, id) {
	var js, fjs = d.getElementsByTagName(s)[0];
	if (d.getElementById(id)) return;
	js = d.createElement(s); js.id = id;
	js.src = "https://woobox.com/js/plugins/woo.js";
	fjs.parentNode.insertBefore(js, fjs);
	}(document, 'script', 'woobox-sdk'));
</script>

<main id="main" role="main">

    {% section 'custom-section' %}
    
</main>

<script src="https://cdnjs.cloudflare.com/ajax/libs/sticky-js/1.2.2/sticky.min.js"></script> 

{% comment %}
<script src="https://cdn.jsdelivr.net/gh/cferdinandi/smooth-scroll@15.0.0/dist/smooth-scroll.polyfills.min.js"></script>
{% endcomment %}

<script>
  
  $(document).ready( function() {
  

    
    let headrh = $('#section-header').outerHeight() + 50;
    //console.log(headrh);
    
    $('a[href="/pages/naidoc-2020"] + .DropdownMenu .Link--secondary').each(function( index ) {
      
      $(this).addClass('scrollme');
      let href = $(this).attr('href');
      href = href.split("#").pop();
      $(this).attr("href", "#" + href);
      
      
    });
    
    $('.SidebarMenu__Nav a[href*="/pages/naidoc-2020"]').click( function( e ) {
        e.preventDefault();
        //do some other stuff here
        $('.Drawer__Close').trigger('click');
        let hreff = $(this).attr('href');
        window.location.replace(hreff);
        //window.location.href = hreff;
    });


    
    $('.scrollme').on('click', function (el) {
      el.preventDefault();

      $('html, body').animate({
        scrollTop: $($(this).attr('href')).offset().top
      }, 700, 'linear');
      
     // $('.naidoc-submenu-list.n-mobile ul').css( "display", "none" );
     
    });
    
    
    //stickybits('.naidoc-submenu');
    var stickyz = new Sticky('.naidoc-submenu');
    //var scroll = new SmoothScroll('[data-scroll]');

    
    
  
  });
  
  
</script>
