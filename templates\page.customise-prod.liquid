{% if page.content contains "split" %}
{% assign my_description = page.content | split: '<!-- split -->'  %}
{{ my_description[0] }}
{% endif %}
<div class="main-custom-page-width">
 
    <div class="page-width">
      <div class="grid page-customise">
          <div class="loader"></div>
      </div>
    </div>
    <style>
      .page-customise{
          min-height: 500px;
          position: relative;
        }
      .loader {
        border: 10px solid #f3f3f3;
        border-radius: 50%;
        border-top: 10px solid black;
        width: 100px;
        height: 100px;
      -webkit-animation: spin 2s linear infinite; /* Safari */
      animation: spin 2s linear infinite;
        position: absolute;
        top: 25%;
        left: 46%;
        transform: translate(-50%, -50%);
    }

    /* Safari */
    @-webkit-keyframes spin {
      0% { -webkit-transform: rotate(0deg); }
      100% { -webkit-transform: rotate(360deg); }
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
      </style>
    <script>
        let propertyV = Object.fromEntries(new URLSearchParams(location.search));
        console.log(propertyV);

        function addToCart(varId, properties, quantity) {
            fetch('/cart/add.js', 
                {
                    method: 'POST',
                    credentials: 'same-origin',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        "quantity": quantity,
                        "id": varId,
                        "properties": properties
                    }),
                }
            ).then(response => response.json()).then(data => {
                    console.log(data);
                    window.location.href = "/cart";
             });
        }


        fetch('https://shopifyapp.muzammalrajpoot.com/api/v1/product/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    "property": propertyV
                }),
            }
        ).then(response => response.json()).then(data => {
                console.log(data);
                if (data.status) {
                    data = data.data;
                    let varId = data.varient[0].id;
                    let properties = data.properties;
                    let selectedOption = data.customiser_option;
                    let selectedOption_1 = data.customiser_option_json;
                   
                  let display_option = [];
                  for(let selection of selectedOption_1){

                    let v_info = '';
                    if(selection.color){
                       v_info = v_info + " " + selection.color + ",";
                    }
                    
                    if(selection.size){
                       v_info = v_info + " " + selection.size;
                    }
                    
                    if(selection.qty){
                       v_info = v_info + " x " + selection.qty;
                    }
                    
                    v_info = v_info + " | "
                    
                    display_option.push(v_info);
                    
                  
                  }
                    
                  
                    let productProperties = {
                      ...properties,
                      option: display_option
					}
                    
                    let quantity = data.qty;
//                     debugger;
                    addToCart(varId,productProperties, quantity );
                } else {


                }


                //         window.location.href = "/cart";
         });


    </script>
</div>