<style>

.accordion {
  box-sizing: border-box;
  display: flex;
  font-family: 'Visby CF', 'Source Sans Pro', sans-serif;
  overflow: hidden;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.accordion-select {
  cursor: pointer;
  margin: 0;
  opacity: 0;
  z-index: 1;
margin-top: 5px;
}

.accordion-title {
  position: relative;
  display: flex;
  align-items: center;
  border-radius: 20px;
  font-weight: 600!important;
}

.accordion-title:not(:nth-last-child(2))::after {
  border: 1px solid transparent;
  bottom: 0;
  content: '';
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}

.accordion-title:before {
  content: '+';
  position: absolute;
  top: 5px;
  right: 15px;
  color: white;
  font-size: 22px;
}

.accordion-select:checked + .accordion-title:before {
  content: '-';
}

.accordion-title span {
  bottom: 0px;
  box-sizing: border-box;
  display: block;
  line-height: 1em;
  width: 100%;
}

.accordion-content {
  box-sizing: border-box;
  overflow: auto;
  position: relative;
  transition: margin 0.3s ease 0.1s;
  text-align: left;
  background-color: white;
  border-radius: 0 0 20px 20px;
  max-height: 0;
  transition: max-height 0.3s ease-out;
}

.accordion-select:checked + .accordion-title {
  border-radius: 20px 20px 0 0;
}

.accordion-select:checked + .accordion-title + .accordion-content {
  margin-bottom: 0;
  margin-right: 0;
  background-color: #f4f4f4;
  
}

.accordion.yarn-accordion {
   border-color: transparent;
   border-radius: 0px;
   border-style: solid;
   border-width: 1px;
   flex-direction: column;
   height: auto;
  border: none;
  margin-bottom: 10px;
} 

    .yarn-accordion.yarn-accordion .accordion-title,
    .yarn-accordion.yarn-accordion .accordion-select  {
      background-color: #106572;
      color: #ffffff;
      width: 100%;
      min-height: 52px;
      font-size: 16px;
      font-weight: normal;
      text-transform: uppercase;
}

.yarn-accordion.yarn-accordion .accordion-select {
    margin-bottom: -52px;
    margin-right: -52px;
}

    .yarn-accordion.yarn-accordion .accordion-title:not(:nth-last-child(2))::after {
     border-bottom-color: rgb(219, 219, 219);
     border-right-color: transparent;
    } 

    .yarn-accordion.yarn-accordion .accordion-select:hover + .yarn-accordion.yarn-accordion .accordion-title,
    .yarn-accordion.yarn-accordion .accordion-select:checked + .yarn-accordion.yarn-accordion .accordion-title {
      background-color: #ffffff;
    } 

     .yarn-accordion.yarn-accordion .accordion-title span  {	
        transform: rotate(0deg);
        -ms-writing-mode: lr-tb;
        filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=0);
        padding-left: 26px;
        padding-right: 26px;
        line-height: 1em;
    } 

    .yarn-accordion.yarn-accordion .accordion-content {
      background-color: #f4f4f4;
      color: #5c5c5c;
      height: 300px;
      margin-bottom: -300px;
      margin-right: 0;
      padding: 30px;
      width: 100%;
      font-size: 16px;
    } 

.yarn-accordion.yarn-accordion .accordion-content h4 {
  margin: 0 0 15px;
  font-weight: bold;
}

.ll-faq {
  margin: 30px 0;
}

.accordion-select:not(:checked)  + .accordion-title + .accordion-content {
    -ms-overflow-style: none; /* for Internet Explorer, Edge */
    scrollbar-width: none; /* for Firefox */
    overflow-y: scroll; 
    max-height: 500px;
}

.accordion-select:not(:checked)  + .accordion-title + .accordion-content::-webkit-scrollbar {
    display: none; /* for Chrome, Safari, and Opera */
}

.accordion-select:checked + .accordion-title + .accordion-content {
   max-height: 500px; 
}
  
.bannertitle .containr {
  width: 1000px;
  max-width: 100%;
  margin: 0 auto;
  display: block;
  margin-bottom: 30px;
  font-size: 50px; 
  font-family: 'Qualy';
  color: white;
}

.bannertitle  {
  position: absolute;
  bottom: 0;
  left: 0; right: 0;
}
  
.custm_mian {
  position: relative;
}
  
.cust-mob {
  display: none!important;
}
  
@media (max-width: 600px) {
  .bannertitle .containr {
    margin-bottom: 20px;
    font-size: 30px; 
    padding-left: 20px;
  }
  
  .cust-web {
    display: none!important;
  }
  
  .cust-mob {
    display: block!important;
  }
}

</style>

<div class="custm_mian">
  <img class="cust-web" src="https://cdn.shopify.com/s/files/1/0247/4021/files/FAQHeaders_WEB.jpg?v=1642139314" />
  <img class="cust-mob" src="https://cdn.shopify.com/s/files/1/0247/4021/files/FAQHeaders_MOBILE.jpg?v=1642139314" />
  <div class="bannertitle"><span class="containr">FAQs</span></div>
</div>

<div class="">

  <div class="PageContent Rte">
    
    {% section 'page-faq' %}
 
  </div>

</div>