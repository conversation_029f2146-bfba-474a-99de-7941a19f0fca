{% comment %}
	GEMPAGE BUILDER (https://apps.shopify.com/gempage)

	You SHOULD NOT modify source code in this page because
	It is automatically generated from GEMPAGE BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->

<link data-instant-track rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.6.3/css/font-awesome.min.css" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-page-83270434950.css' | asset_url }}" class="gf_page_style">
<link data-instant-track rel="stylesheet" type="text/css" href= "https://d1um8515vdn9kb.cloudfront.net/libs/css/gfaccordion.css" class="gf_libs">
<link data-instant-track rel="stylesheet" type="text/css" href= "https://d1um8515vdn9kb.cloudfront.net/libs/css/owl.carousel.min.css" class="gf_libs">
<!--GEM_HEADER_END-->
<!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor">
<div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779524" class="gf_row gf_row-fluid gf_row-no-padding gf_equal-height gf_row-gap-0" data-icon="gpicon-row" data-id="1665046779524" data-layout-lg="4+8" data-extraclass="" data-layout-md="4+8" data-layout-sm="4+8" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible;" data-row-gap="0px"><div class="gf_column gf_col-xs-12 gf_col-lg-5 gf_col-md-5 gf_col-sm-12" id="c-1664822429953" data-id="1664822429953" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779582" class="gf_row" data-icon="gpicon-row" data-id="1665046779582" style="min-height: auto;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664822469854" data-id="1664822469854"><div data-label="Countdown Timer" data-key="count-down" data-atomgroup="module" id="m-1665046779623" class="module-wrap" data-icon="gpicon-countdown" data-ver="1.0" data-id="1665046779623"><div class="module count-down gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-end="2022/10/12 8:12:59" data-week="0" data-weeklabel="Weeks" data-day="1" data-daylabel="D" data-hour="1" data-hourlabel="H" data-minute="1" data-minutelabel="M" data-second="1" data-secondlabel="S" data-type="evergreen" data-daily="10h, 30m, 10s" data-evergreen="2d, 3h, 0m, 0s" data-redirect="#" data-cookietime="1664845991588" data-evergreenloop="1" data-timezone="UTC+7" data-auto-hide="0"><div class="count-down-wrap"><div class="count-down-inner"><div class="day-left time-left"><span class="num gf_gs-text-paragraph-1">00</span><span class="count-label gf_gs-text-paragraph-1">D</span></div><div class="hour-left time-left"><span class="num gf_gs-text-paragraph-1">00</span><span class="count-label gf_gs-text-paragraph-1">H</span></div><div class="minute-left time-left"><span class="num gf_gs-text-paragraph-1">00</span><span class="count-label gf_gs-text-paragraph-1">M</span></div><div class="second-left time-left"><span class="num gf_gs-text-paragraph-1">00</span><span class="count-label gf_gs-text-paragraph-1">S</span></div></div></div></div></div><div data-label="Separator" data-key="separator" data-atomgroup="element" id="e-1665046779613" class="element-wrap" data-icon="gpicon-separator" data-ver="1.0" data-id="1665046779613"><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-align="left" data-exc=""><hr class="gf_separator"></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1665046779664" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1665046779664"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">CYBER MONDAY</h1></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1665046779547" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1665046779547"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-xs gf-elm-center-sm" data-gemlang="en" data-exc=""><h2 class="gf_gs-text-heading-2">SALE</h2></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1665046779628" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1665046779628"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">UPTO 45% OFF</h1></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1665046779586" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1665046779586"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-xs gf-elm-center-sm gf-elm-center-lg" data-stretch-lg="0" data-stretch-xs="0" data-stretch-sm="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#m-1665046779530" target="" data-scroll-speed="2000" data-exc="" data-scroll-speed-xs="2000" data-scroll-speed-sm="2000"><span>Get It Now</span></a></div></div></div></div></div><div class="gf_column gf_col-xs-12 gf_col-lg-7 gf_col-md-7 gf_col-sm-12" id="c-1664822432040" data-id="1664822432040" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1665046779584" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1665046779584" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/155bc4b1-9c14-4aa1-92cc-75b6e04260fd/-/format/auto/-/preview/3000x3000/-/quality/lighter/bn1.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" natural-width="1401" natural-height="1050" width="1401" height="1050"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779663" class="gf_row" data-icon="gpicon-row" data-id="1665046779663" data-layout-lg="3+3+3+3" data-extraclass="" data-layout-md="3+3+3+3" data-layout-sm="6+6+6+6" data-layout-xs="12+12+12+12"><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1664823431182" data-id="1664823431182"><div data-label="Icon List" data-key="icon-list" data-atomgroup="module" id="m-1665046779579" class="module-wrap" data-icon="gpicon-iconlist" data-ver="1.0" data-id="1665046779579"><div class="module gf_module-left gf_module-left-lg gf_module-left-md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li style="margin-bottom: 0px"><span class="gf-il-icon item-content" data-index="1" data-key="content" style="width: 85px"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1665046779559" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1665046779559"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/8670073b-5e13-4fbc-8a02-671527558584/i1.svg" alt="" class="gf_image" data-gemlang="en" data-width="40px" data-height="auto" title="" natural-width="42" natural-height="42" width="42" height="42"></div></div></span><div class="gf-il-content item-content" data-index="1" data-key="content1" style="padding-left: 85px"><div data-label="Text Block" id="e-1665046779579-6" class="element-wrap" data-id="1665046779579-6"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc="">Free Delivery</div></div><div data-label="Text Block" id="e-1665046779651" class="element-wrap" data-id="1665046779651"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc="">from $250</div></div></div></li></ul></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1664823433985" data-id="1664823433985"><div data-label="Icon List" data-key="icon-list" data-atomgroup="module" id="m-1665046779670" class="module-wrap" data-icon="gpicon-iconlist" data-ver="1.0" data-id="1665046779670"><div class="module gf_module-left gf_module-left-lg gf_module-left-md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li style="margin-bottom: 0px"><span class="gf-il-icon item-content" data-index="1" data-key="content" style="width: 85px"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1665046779662" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1665046779662"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/c18706fb-461a-4c4a-b5c0-87acfbd87a0b/i2.svg" alt="" class="gf_image" data-gemlang="en" data-width="40px" data-height="auto" title="" natural-width="40" natural-height="40" width="40" height="40"></div></div></span><div class="gf-il-content item-content" data-index="1" data-key="content1" style="padding-left: 85px"><div data-label="Text Block" id="e-1665046779670-6" class="element-wrap" data-id="1665046779670-6"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc="">Full Refund</div></div><div data-label="Text Block" id="e-1665046779572" class="element-wrap" data-id="1665046779572"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc="">For free return<br></div></div></div></li></ul></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1664823433921" data-id="1664823433921"><div data-label="Icon List" data-key="icon-list" data-atomgroup="module" id="m-1665046779659" class="module-wrap" data-icon="gpicon-iconlist" data-ver="1.0" data-id="1665046779659"><div class="module gf_module-left gf_module-left-lg gf_module-left-md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li style="margin-bottom: 0px"><span class="gf-il-icon item-content" data-index="1" data-key="content" style="width: 85px"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1665046779585" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1665046779585"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/01d2c2ef-3a9a-42ed-b6e3-7d53474d5531/i3.svg" alt="" class="gf_image" data-gemlang="en" data-width="40px" data-height="auto" title="" natural-width="42" natural-height="42" width="42" height="42"></div></div></span><div class="gf-il-content item-content" data-index="1" data-key="content1" style="padding-left: 85px"><div data-label="Text Block" id="e-1665046779659-6" class="element-wrap" data-id="1665046779659-6"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc="">Online Payment</div></div><div data-label="Text Block" id="e-1665046779606" class="element-wrap" data-id="1665046779606"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc="">Secure Sytem</div></div></div></li></ul></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1664823434037" data-id="1664823434037"><div data-label="Icon List" data-key="icon-list" data-atomgroup="module" id="m-1665046779555" class="module-wrap" data-icon="gpicon-iconlist" data-ver="1.0" data-id="1665046779555"><div class="module gf_module-left gf_module-left-lg gf_module-left-md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li style="margin-bottom: 0px"><span class="gf-il-icon item-content" data-index="1" data-key="content" style="width: 85px"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1665046779548" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1665046779548"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/ff2ae39e-43c5-4717-93bb-a48da69dc752/i4.svg" alt="" class="gf_image" data-gemlang="en" data-width="40px" data-height="auto" title="" natural-width="40" natural-height="40" width="40" height="40"></div></div></span><div class="gf-il-content item-content" data-index="1" data-key="content1" style="padding-left: 85px"><div data-label="Text Block" id="e-1665046779555-6" class="element-wrap" data-id="1665046779555-6"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc="">Online Support</div></div><div data-label="Text Block" id="e-1665046779540" class="element-wrap" data-id="1665046779540"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc="">24/7 Free Support</div></div></div></li></ul></div></div></div></div><!--gfsplit--><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1665046779632" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1665046779632" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="39716378476678">{% assign product = all_products['mawurji-dreaming-quilt-cover-set'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779652" class="gf_row" data-icon="gpicon-row" data-id="1665046779652" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="12+12" data-layout-xs="12+12"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1664824534996" data-id="1664824534996"><div data-label="(P) Image List" data-key="p-image-list" data-atomgroup="child-product" id="m-1665046779647" class="module-wrap" data-icon="gpicon-product-image" data-ver="1" data-id="1665046779647"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs  style-default {% if 0 == 1 %}gallery-icon-1{% endif %}" data-pid="{{product.id}}" data-style="default" data-spacing="5px" data-collg="5" data-colmd="4" data-colsm="3" data-colxs="3" data-dotslg="0" data-dotsmd="0" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="{{navspeed}}" data-loop="0" data-mlg="5px" data-mmd="5px" data-msm="5px" data-mxs="5px" data-gallery="0" data-galleryicon="1" data-borderactive="0" data-video-inline="0" data-sync-il="{{sync-il}}" data-3d-model="0" data-hide-one-media="0" data-sync-il-xs="{{sync-il-xs}}">{% assign current_variant = product.selected_or_first_available_variant %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if 'default' == 'slider' %}{% assign sliderClass = 'gf_product-slider owl-carousel owl-theme' %}<div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div>{% else %}{% assign sliderClass = '' %}{% endif %}<div class="gf_product-images-list {{sliderClass}}">{% assign inlineVideo = 0 %}{% assign inline3DImage = 0 %}{% assign hideOneMedia = 0 %}{% if 0 == 1 %}{% assign inlineVideo = 0 %}{% endif %}{% if hideOneMedia == 1 %}{% assign hideOneMedia = true %}{% else %}{% assign hideOneMedia = false %}{% endif %}{% assign mediaLength = product.media | size %}{% unless hideOneMedia and mediaLength <= 1 %}{% for media in product.media %}{% assign media_type = media.media_type %}{% assign altTag = media.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% assign gfIMLItem = "" %}{% if media_type == 'image' %}{% capture gfIMLItem %}<div class="gf_product-image-thumb" data-id="{{media.id}}" data-index="{{forloop.index}}" data-image="{{ media.src | img_url: '1024x1024' }}" data-zoom="{{ media.src | img_url: '2048x2048' }}"><img src="{{ media.src | img_url: '2048x2048' }}" width="{{media.width}}" height="{{media.height}}" alt="{{ altTag }}"></div>{% endcapture %}{% elsif media_type == 'external_video' %}{% capture gfIMLItem %}{% if inlineVideo == 1 %}<div id="gf_product-video-thumb-{{forloop.index}}" data-id="{{media.id}}" class="gf_product-image-thumb gf_product-video-thumb" data-video="" data-video-host="{{ media.host }}" data-video-id="{{ media.external_id }}"></div>{% else %}<div class="gf_product-image-thumb gf_product-video-thumb" data-id="{{media.id}}" data-index="{{forloop.index}}" data-video="" data-video-host="{{ media.host }}" data-video-id="{{ media.external_id }}" data-type="external_video"><img src="{{ media | img_url: '2048x2048' }}" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}"></div>{% endif %}{% endcapture %}{% elsif media_type == 'video' %}{% capture gfIMLItem %}{% if inlineVideo == 1 %}<video controls="" class="gf_product-image-thumb" data-id="{{media.id}}" poster="{{ media | img_url: '2048x2048' }}">{% for source in media.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video>{% else %}<div class="gf_product-image-thumb gf_product-video-thumb" data-id="{{media.id}}" data-index="{{forloop.index}}" data-video="" data-type="video"><img src="{{ media | img_url: '2048x2048' }}" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% for source in media.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</div>{% endif %}{% endcapture %}{% elsif media_type == "model" %}{% capture gfIMLItem %}{% assign media3DSource = media.sources | where: "format", "glb" | first  %}{% if media3DSource.format %}{% if inline3DImage == 1 %}<div class="gf_product-image-thumb gf_product-model-thumb" data-id="{{media.id}}" data-index="{{forloop.index}}" data-url="{{ media3DSource.url }}" data-image="{{ media.preview_image.src | img_url: '1024x1024' }}"><model-viewer poster="{{ media.preview_image.src | img_url: '2048x2048' }}" class="gf_product-3D-image" src="{{ media3DSource.url }}" alt="{{ altTag }}" auto-rotate="" camera-controls="" ar-status="not-presenting"></model-viewer><img style="opacity: 0" src="{{ media.preview_image.src | img_url: '2048x2048' }}" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}"></div>{% else %}<div class="gf_product-image-thumb gf_product-model-thumb" data-id="{{media.id}}" data-index="{{forloop.index}}" data-url="{{ media3DSource.url }}" data-image="{{ media.preview_image.src | img_url: '1024x1024' }}"><img src="{{ media.preview_image.src | img_url: '2048x2048' }}" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}"></div>{% endif %}{% endif %}{% endcapture %}{% endif %}{% if gfIMLItem != "" %}<a class="item" href="javascript:void(0)">{{ gfIMLItem }}</a>{% endif %}{% endfor %}{% endunless %}</div></div></div><div data-label="(P) Image List" data-key="p-image-list" data-atomgroup="child-product" id="m-1665046779602" class="module-wrap" data-icon="gpicon-product-image" data-ver="1" data-id="1665046779602"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs  style-slider {% if 0 == 1 %}gallery-icon-1{% endif %}" data-pid="{{product.id}}" data-style="slider" data-spacing="5px" data-collg="5" data-colmd="4" data-colsm="2" data-colxs="1" data-dotslg="0" data-dotsmd="0" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="{{navspeed}}" data-loop="0" data-mlg="5px" data-mmd="5px" data-msm="5px" data-mxs="5px" data-gallery="0" data-galleryicon="1" data-borderactive="0" data-video-inline="0" data-sync-il="{{sync-il}}" data-3d-model="0" data-hide-one-media="0" data-sync-il-xs="{{sync-il-xs}}">{% assign current_variant = product.selected_or_first_available_variant %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if 'slider' == 'slider' %}{% assign sliderClass = 'gf_product-slider owl-carousel owl-theme' %}<div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div>{% else %}{% assign sliderClass = '' %}{% endif %}<div class="gf_product-images-list {{sliderClass}}">{% assign inlineVideo = 0 %}{% assign inline3DImage = 0 %}{% assign hideOneMedia = 0 %}{% if 0 == 1 %}{% assign inlineVideo = 0 %}{% endif %}{% if hideOneMedia == 1 %}{% assign hideOneMedia = true %}{% else %}{% assign hideOneMedia = false %}{% endif %}{% assign mediaLength = product.media | size %}{% unless hideOneMedia and mediaLength <= 1 %}{% for media in product.media %}{% assign media_type = media.media_type %}{% assign altTag = media.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% assign gfIMLItem = "" %}{% if media_type == 'image' %}{% capture gfIMLItem %}<div class="gf_product-image-thumb" data-id="{{media.id}}" data-index="{{forloop.index}}" data-image="{{ media.src | img_url: '1024x1024' }}" data-zoom="{{ media.src | img_url: '2048x2048' }}"><img src="{{ media.src | img_url: '600x600' }}" width="{{media.width}}" height="{{media.height}}" alt="{{ altTag }}"></div>{% endcapture %}{% elsif media_type == 'external_video' %}{% capture gfIMLItem %}{% if inlineVideo == 1 %}<div id="gf_product-video-thumb-{{forloop.index}}" data-id="{{media.id}}" class="gf_product-image-thumb gf_product-video-thumb" data-video="" data-video-host="{{ media.host }}" data-video-id="{{ media.external_id }}"></div>{% else %}<div class="gf_product-image-thumb gf_product-video-thumb" data-id="{{media.id}}" data-index="{{forloop.index}}" data-video="" data-video-host="{{ media.host }}" data-video-id="{{ media.external_id }}" data-type="external_video"><img src="{{ media | img_url: '600x600' }}" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}"></div>{% endif %}{% endcapture %}{% elsif media_type == 'video' %}{% capture gfIMLItem %}{% if inlineVideo == 1 %}<video controls="" class="gf_product-image-thumb" data-id="{{media.id}}" poster="{{ media | img_url: '600x600' }}">{% for source in media.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video>{% else %}<div class="gf_product-image-thumb gf_product-video-thumb" data-id="{{media.id}}" data-index="{{forloop.index}}" data-video="" data-type="video"><img src="{{ media | img_url: '600x600' }}" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% for source in media.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</div>{% endif %}{% endcapture %}{% elsif media_type == "model" %}{% capture gfIMLItem %}{% assign media3DSource = media.sources | where: "format", "glb" | first  %}{% if media3DSource.format %}{% if inline3DImage == 1 %}<div class="gf_product-image-thumb gf_product-model-thumb" data-id="{{media.id}}" data-index="{{forloop.index}}" data-url="{{ media3DSource.url }}" data-image="{{ media.preview_image.src | img_url: '1024x1024' }}"><model-viewer poster="{{ media.preview_image.src | img_url: '600x600' }}" class="gf_product-3D-image" src="{{ media3DSource.url }}" alt="{{ altTag }}" auto-rotate="" camera-controls="" ar-status="not-presenting"></model-viewer><img style="opacity: 0" src="{{ media.preview_image.src | img_url: '600x600' }}" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}"></div>{% else %}<div class="gf_product-image-thumb gf_product-model-thumb" data-id="{{media.id}}" data-index="{{forloop.index}}" data-url="{{ media3DSource.url }}" data-image="{{ media.preview_image.src | img_url: '1024x1024' }}"><img src="{{ media.preview_image.src | img_url: '600x600' }}" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}"></div>{% endif %}{% endif %}{% endcapture %}{% endif %}{% if gfIMLItem != "" %}<a class="item" href="javascript:void(0)">{{ gfIMLItem }}</a>{% endif %}{% endfor %}{% endunless %}</div></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1664824536572" data-id="1664824536572"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779599" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1665046779599" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664824624633" data-id="1664824624633"><div class="module-wrap" id="m-1665046779632-1" data-id="1665046779632-1" data-label="(P) Title" data-icon="gpicon-product-title" data-ver="1.0"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div data-label="Icon List Hoz" data-key="icon-list-hoz" data-atomgroup="module" id="m-1665046779592" class="module-wrap" data-icon="gpicon-iconlist2" data-ver="1.0" data-id="1665046779592"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li class="item" style="width: 17px;"><div data-index="1" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779592-1" class="element-wrap" data-icon="eicon-post" data-id="1665046779592-1"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 17px;"><div data-index="2" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779611" class="element-wrap" data-icon="eicon-post" data-id="1665046779611"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 17px;"><div data-index="3" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779608" class="element-wrap" data-icon="eicon-post" data-id="1665046779608"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 17px;"><div data-index="4" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779502" class="element-wrap" data-icon="eicon-post" data-id="1665046779502"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 17px;"><div data-index="5" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779560" class="element-wrap" data-icon="eicon-post" data-id="1665046779560"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li></ul></div></div><div class="module-wrap" id="m-1665046779632-2" data-id="1665046779632-2" data-label="(P) Price" data-icon="gpicon-product-price" data-ver="1.4"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '1' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span>-</span><span class="gf_pq-percent">{{ diff }}%</span><span></span></span></span>{% endif %}</div></div></div><div data-label="(P) Swatches" data-key="p-swatches" data-atomgroup="child-product" id="m-1665046779587" class="module-wrap" data-icon="gpicon-product-swatches" data-ver="1.0" data-id="1665046779587"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-group="{{group}}" data-swatch-text="1" data-pid="{{product.id}}" data-swatcher-hide="All" data-swatcher-hideother="" data-none-option="{{noneOption}}" data-soldout="1" data-soldout-style="default" data-soldout-color="rgba(224, 224, 224, 1)" data-soldout-logic="1" data-background="e30=">{% unless product.variants.size == 1 and product.variants[0].title == 'Default Title' %}{% assign columnClass = 'gf_column gf_col_no_tools gf_col-md-12' %}<div class="gf_swatches gf_row-no-padding gf_row gf_row_no_tools" data-type="{{group}}">{% for option in product.options_with_values %}<div class="{{columnClass}} gf_swatches-selector gf_swatches-option{{forloop.index}}" data-name="{{option.name}}">{% if '1' == '1' %}<label>{{option.name}}</label>{% endif %}{% for value in option.values %}{% if forloop.index == 1 %}{% assign selectedClass = 'gf_selected' %}{% else %}{% assign selectedClass = '' %}{% endif %}<span class="gf_swatch {{selectedClass}}" data-group="{{group}}" data-price="0" style="margin-bottom: 15px;margin-right: 24px" data-value="{{value | escape}}"><span>{{value}}</span></span>{% endfor %}</div>{% endfor %}</div>{% endunless %}<code class="gf_swatches-data" style="display: none!important;">{{swatcher}}</code></div></div><div data-label="Accordion" data-key="accordion" data-atomgroup="module" id="m-1665046779549" class="module-wrap" data-icon="gpicon-accordion" data-ver="2" data-id="1665046779549"><div data-accordion-group="" class="module " data-opendefault="1" data-opentab="1" data-single="1" data-bordercolor="rgb(238, 238, 238)" data-bordersize="1px"><div data-accordion="" style="padding-bottom: "><div data-control="" data-index="1" data-key="content" class="chevron iCon-right text- item-content"><div data-label="Text Block" id="e-1665046779549-1" class="element-wrap" data-icon="eicon-animation-text" data-id="1665046779549-1"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-xs gf-elm-left-sm" data-exc=""><p>Includes</p></div></div><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:rgba(180, 180, 180, 1)"></span></button></div><div data-content="" style="margin-top: ; background-color: #FFFFFF"><article data-index="1" data-key="content1" class="item-content"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779554" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779554"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><ul><li>1pc Comforter (90” x 90”)</li><li>2pc Shams (20” x 26” + 2”)</li><li>2pc Breakfast Cushions (12” x 18”)</li><li>1pc Decorative Pillow (18” x 18”)</li><li>1pc Fitted Sheet (60” x 80” x 12”)</li><li>1pc Flat Sheet (90” x 108” + 3”)</li><li>2pc Pillowcases (21” x 27” + 3”)</li><li>1pc Blanket (90” x 90”)<br></li></ul></div></div></article></div></div><div data-accordion="" style="padding-bottom: "><div data-control="" data-index="2" data-key="content" class="chevron iCon-right text- item-content"><div data-label="Text Block" id="e-1665046779634" class="element-wrap" data-icon="eicon-animation-text" data-id="1665046779634"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><p>Fabric material</p></div></div><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:rgba(180, 180, 180, 1)"></span></button></div><div data-content="" style="margin-top: ; background-color: #FFFFFF"><article data-index="2" data-key="content1" class="item-content"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779618" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779618"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><span style="color: rgb(36, 36, 36);"><strong>Organic cotton:</strong></span> Clean, crisp organic cotton is tightly woven into a substantial, 300-thread count percale, creating a smooth and breathable classic for the bed that wears exceptionally well with time. Contrary to the sheen of sateen, percale features a cooler feel that keeps warm sleepers comfortable in any climate, creating better air flow for those who are looking to avoid the discomfort of hot, humid nights.</div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779639" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779639"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-xs gf-elm-left-sm" data-gemlang="en" data-exc=""><strong>What are the benefits of using cotton? </strong><br></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779607" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779607"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><ul><li>Organic cotton is ideal for sensitive skin, or even people with atopic skin.</li><li>100% organic cotton is grown and woven in India</li><li>100% organic and toxic-free fabric</li><li>Cotton is breathable. It keeps skin dry by absorbing moisture and expelling body heat.</li><li>It does not generate static electricity unlike synthetic fibres.</li><li>Another of its advantages is that its maintenance is simple.</li><li>organic cotton is that it is respectful with the</li><li>environment, by reducing water consumption in agriculture.</li></ul></div></div></article></div></div><div data-accordion="" style="padding-bottom: "><div data-control="" data-index="3" data-key="content" class="chevron iCon-right text- item-content"><div data-label="Text Block" id="e-1665046779590" class="element-wrap" data-icon="eicon-animation-text" data-id="1665046779590"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><p>Tip and Trick</p></div></div><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:rgba(180, 180, 180, 1)"></span></button></div><div data-content="" style="margin-top: ; background-color: #FFFFFF"><article data-index="3" data-key="content1" class="item-content"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779649" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779649"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><ul><li>Washing bed linen before use is a personal choice. We recommend that you do: it refreshes your linens after manufacture and enhances the feel of the cloth</li></ul></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779671" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779671"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><ul><li>Our fitted sheets and valances are unlikely to be suitable for mattresses that vary greatly from these measurements, though our flat sheets are generously sized so may offer a useful alternative</li></ul></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779650" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779650"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1" data-gemlang="en"><div style="overflow-x:scroll"><table><thead><tr><td></td><th>Queen</th><th>King</th><th>Superking</th><th>Super Single</th></tr></thead><tbody><tr><td>Comforter</td><td data-label="Queen">90”x90”</td><td data-label="King">90”x90”</td><td data-label="Superking">90”x90”</td><td data-label="Super Single">90”x90”</td></tr><tr><td>Shams</td><td data-label="Queen">20”x26”+2”</td><td data-label="King">20”x26”+2”</td><td data-label="Superking">20”x26”+2”</td><td data-label="Super Single">20”x26”+2”</td></tr><tr><td>Breakfast Cushions</td><td data-label="Queen">12”x18”</td><td data-label="King">12”x18”</td><td data-label="Superking">14”x20”</td><td data-label="Super Single">16”x22”</td></tr><tr><td>Decorative Pillow</td><td data-label="Queen">18”x18”</td><td data-label="King">18”x18”</td><td data-label="Superking">20”x20”</td><td data-label="Super Single">24”x24”</td></tr><tr><td>Fitted Sheet</td><td data-label="Queen">60”x80”x12”</td><td data-label="King">78”x80”x14”</td><td data-label="Superking">80”x82”x12”</td><td data-label="Super Single">80”x90”x12”</td></tr><tr><td>Flat Sheet</td><td data-label="Queen">90”x108”+3”</td><td data-label="King">90”x108”+3”</td><td data-label="Superking">90”x108”+3”</td><td data-label="Super Single">90”x108”+3”</td></tr><tr><td>Pillowcases</td><td data-label="Queen">21”x27”+3”</td><td data-label="King">20”x40”</td><td data-label="Superking">20”x40”</td><td data-label="Super Single">21”x27”+3”</td></tr></tbody></table></div></div></div></article></div></div><div data-accordion="" style="padding-bottom: "><div data-control="" data-index="4" data-key="content" class="chevron iCon-right text- item-content"><div data-label="Text Block" id="e-1665046779609" class="element-wrap" data-icon="eicon-animation-text" data-id="1665046779609"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><p>Size guide</p></div></div><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:rgba(180, 180, 180, 1)"></span></button></div><div data-content="" style="margin-top: ; background-color: #FFFFFF"><article data-index="4" data-key="content1" class="item-content"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779600" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779600"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><div style="overflow-x:scroll"><table><thead><tr><td><br></td><th>Queen</th><th>King</th><th>Superking</th><th>Super Single</th></tr></thead><tbody><tr><th>Duvet Cover</th><td data-label="Queen">82”x82”</td><td data-label="King">96”x82”</td><td data-label="Superking">103”x89”</td><td data-label="Super Single">90”x90”</td></tr><tr><th>Fitted Sheet</th><td data-label="Queen">60”x80”+2”</td><td data-label="King">78”x80”</td><td data-label="Superking">90”x98”</td><td data-label="Super Single">48”x84”</td></tr><tr><th>Flat Sheet</th><td data-label="Queen">90”x102”</td><td data-label="King">108”x102”</td><td data-label="Superking">110”x105”</td><td data-label="Super Single">55”x96”</td></tr><tr><th>Pillowslips</th><td data-label="Queen">20”x30”</td><td data-label="King">20”x40”</td><td data-label="Superking">20”x20”</td><td data-label="Super Single">20”x30”</td></tr></tbody></table></div></div></div></article></div></div><div data-accordion="" style="padding-bottom: "><div data-control="" data-index="5" data-key="content" class="chevron iCon-right text- item-content"><div data-label="Text Block" id="e-1665046779568" class="element-wrap" data-icon="eicon-animation-text" data-id="1665046779568"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><p>Shipping & Returns</p></div></div><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:rgba(180, 180, 180, 1)"></span></button></div><div data-content="" style="margin-top: ; background-color: #FFFFFF"><article data-index="5" data-key="content1" class="item-content"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779627" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779627"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><div><span style="font-weight: bold;"><span style="color: rgb(36, 36, 36);">Shipping</span></span><br></div><div>UPS can deliver most in-stock items within 3-5 business days.</div>Next Day Delivery and Processing<br><div>We can arrange for next day delivery to most destinations within the contiguous 48 states, for an extra charge of $26.00 per address. If we receive your order by 4pm PT Monday through Friday, you'll receive your order the next business day; orders placed on Friday after 4pm PT through Sunday will arrive on Tuesday. Note: Next Day Delivery service is not available for some oversized items, White Glove Delivery, out of stock items, personalized items or for items shipped directly from the manufacturer.</div><div><br><span style="font-weight: bold;"><span style="color: rgb(36, 36, 36);">Easy Returns</span></span><br></div><div>You can return eligible item(s) within 30 days of receiving an order or 7 days for Quick Ship upholstery items for a refund of the merchandise value. An original receipt or gift receipt is required. Monogrammed items and Made to Order furniture or rugs are not eligible for returns. Review the full list of ineligible items and our refund policy below before starting the process.</div><div><br></div><span style="font-weight: bold;"><span style="color: rgb(36, 36, 36);">Easy Ways to Return:</span></span><br></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779620" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779620"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><ul><li>Stores - You can return most non-furniture items to your local Gem store for free. A gift receipt or original receipt is required for all returns and exchanges.</li><li>Bring to UPS Location (under 70 lbs) – See our full Easy Returns Policy to start the process.</li></ul></div></div></article></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779601" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1665046779601" data-row-gap="0px" data-extraclass="" data-layout-lg="4+8" data-layout-md="4+8" data-layout-sm="4+8" data-layout-xs="12+12"><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-4 gf_col-xs-12" id="c-1664825760110" data-id="1664825760110"><div data-label="(P) Quantity" data-key="p-quantity" data-atomgroup="child-product" id="m-1665046779570" class="module-wrap" data-icon="gpicon-product-quantity" data-ver="1" data-id="1665046779570"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-style="default" data-updateprice="0">{% if 'default' == 'inline' %}<div class="gf_align-module gf_align-i-center">{% endif %}{% if '0' == '1' %}<label>Quantity</label>{% endif %}{% if '1' == '1' %}{% assign sClass = 'gf_pq-stretch' %}{% else %}{% assign sClass = '' %}{% endif %}<div class="gf_product-quantity {{sClass}}"><a class="gf_product-quantity-minus" style="width: 48px"><span class="gf_product-icon-minus"><svg fill="#585858" height="12px" viewBox="0 0 12 12"><path d="M11.5,7h-11C0.2,7,0,6.8,0,6.5v-1C0,5.2,0.2,5,0.5,5h11C11.8,5,12,5.2,12,5.5v1C12,6.8,11.8,7,11.5,7z"></path></svg></span></a><input type="{% if 'default' == 'simple' %}number{% else %}text{% endif %}" name="quantity" value="1" class="gf_pq_qty"><a class="gf_product-quantity-plus" style="width: 48px"><span class="gf_product-icon-plus"><svg fill="#585858" height="12px" viewBox="0 0 12 12"><path d="M12,5.5v1C12,6.8,11.8,7,11.5,7H7v4.5C7,11.8,6.8,12,6.5,12h-1C5.2,12,5,11.8,5,11.5V7H0.5C0.2,7,0,6.8,0,6.5 v-1C0,5.2,0.2,5,0.5,5H5V0.5C5,0.2,5.2,0,5.5,0h1C6.8,0,7,0.2,7,0.5V5h4.5C11.8,5,12,5.2,12,5.5z"></path></svg></span></a></div>{% if 'default' == 'inline' %}</div>{% endif %}</div></div></div><div class="gf_column gf_col-lg-8 gf_col-md-8 gf_col-sm-8 gf_col-xs-12" id="c-1664825769517" data-id="1664825769517"><div class="module-wrap" id="m-1665046779632-3" data-id="1665046779632-3" data-label="(P) Cart Button" data-icon="gpicon-product-cartbutton" data-ver="1.1"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="1" data-cbto="checkout" data-editlink="" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="1" data-stretch-md="1" data-stretch-sm="1" data-stretch-xs="1">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div></div></div></div></div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779644" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1665046779644" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664934330679" data-id="1664934330679"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779537" class="gf_row gf_equal-height" data-icon="gpicon-row" data-id="1665046779537" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="12+12" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1664826189647" data-id="1664826189647" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1665046779594" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1665046779594" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/92fa4c71-796a-40ec-a5a0-03343e6f567c/-/format/auto/-/preview/3000x3000/-/quality/lighter/img1.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" natural-width="856" natural-height="684" width="856" height="684"></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1664826192356" data-id="1664826192356" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779610" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1665046779610" data-row-gap="0px" data-extraclass="" style="min-height: auto;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664826253689" data-id="1664826253689"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1665046779583" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1665046779583"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Organically Grown</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779589" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779589"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc="">Our cotton is certified to the Global Organic Textile Standard (GOTS) because it is the highest standard in the world. Every step of the supply chain must be independently certified, from sowing through sewing, and all the way to your door. Our GOTS certified cotton products cut toxic chemicals out of the equation, keeping workers safe and bringing out the natural comfort of every yarn, weave, and finish we select.</div></div></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779545" class="gf_row gf_equal-height" data-icon="gpicon-row" data-id="1665046779545" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="12+12" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1664826189647" data-id="1664826189647" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1665046779588" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1665046779588" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/dbcef15b-a2b8-468b-b664-309363d80bbc/-/format/auto/-/preview/3000x3000/-/quality/lighter/img2.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" natural-width="856" natural-height="684" width="856" height="684"></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1664826192356" data-id="1664826192356" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779574" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1665046779574" data-row-gap="0px" data-extraclass="" style="min-height: auto;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664826253689" data-id="1664826253689"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1665046779532" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1665046779532"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-xs gf-elm-left-sm" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Buyback & Resell</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779646" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779646"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc="">Every year, millions of pieces of secondhand bedding go to waste. That’s why we’re buying back your used GEM bedding, to give blankets and pillows as many lives as possible.<br><br>With Buyback & Resell, you sell us used GEM bedding you don’t need in a safe, streamlined way year-round. You’ll get GEM in-store credit to refresh your bed. We will recycle, and renew your old bedding, and sell it for a low price. It’s one little action that can have a big impact on your wallet and the planet.<br><br>What your used bedding is worth will vary, but you’ll get more in-store credit for pieces that are in better condition or like-new.</div></div></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779542" class="gf_row gf_equal-height" data-icon="gpicon-row" data-id="1665046779542" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="12+12" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1664826189647" data-id="1664826189647" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1665046779518" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1665046779518" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/5cbf4595-10bc-49a5-af47-f115ec3fa7aa/-/format/auto/-/preview/3000x3000/-/quality/lighter/img3.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" natural-width="856" natural-height="684" width="856" height="684"></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1664826192356" data-id="1664826192356" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779626" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1665046779626" data-row-gap="0px" data-extraclass="" style="min-height: auto;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664826253689" data-id="1664826253689"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1665046779571" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1665046779571"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-xs gf-elm-left-sm" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Bedding & Linen CLeaning</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779500" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779500"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-xs gf-elm-left-sm" data-gemlang="en" data-exc="">Our machines get hotter than at-home models, which helps kill viruses or germs living in your bedsheets and blankets. And, don’t forget to send us your comforters and feather pillows, which can harbor germs, dust, and bacteria. To clean your feather pillow, we’ll cut open the ticking and clean and sterilize all the feathers in a special machine. Upon your request, we’ll add down, and finally blow it all into a 100% new cotton ticking for you. Send your bedding to Dependable Cleaners, and sleep easy.<br><br><span style="color: rgb(87, 87, 87); font-weight: bold;">Note:</span> Free laundry only for products purchased in the store (free period within 1 year from date of purchase on invoice)</div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779612" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1665046779612" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664871923926" data-id="1664871923926"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779578" class="gf_row" data-icon="gpicon-row" data-id="1665046779578"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664871930444" data-id="1664871930444"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1665046779617" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1665046779617"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-xs gf-elm-center-sm" data-gemlang="en" data-exc=""><h2 class="gf_gs-text-heading-2">Ingredients In Bedding</h2></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779581" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779581"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc="">Don’t miss the chance for massive savings</div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779630" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1665046779630" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664871938509" data-id="1664871938509"><div data-label="Product List" data-key="product-list" data-atomgroup="product-list" id="m-1665046779631" class="module-wrap" data-icon="gpicon-product-list" data-ver="1.0" data-id="1665046779631" style="min-height: auto;"><div class="module " data-cid="auto" data-chandle="latest-products" data-limit="8" data-collg="4" data-colmd="4" data-colsm="2" data-colxs="2"><div class="item-content item-content-no-placeholder"><div class="gf_row gf_row-no-width gf_row_no_tools gf_row-no-padding">{% assign colXsClass = ' gf_col-xs-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colXsClass = colXsClass | append:cols %}{% assign colSmClass = ' gf_col-sm-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colSmClass = colSmClass | append:cols %}{% assign colMdClass = ' gf_col-md-' %}{% assign cols = 12 | divided_by: 4 %}{% assign colMdClass = colMdClass | append:cols %}{% assign colLgClass = ' gf_col-lg-' %}{% assign cols = 12 | divided_by: 4 %}{% assign colLgClass = colLgClass | append:cols %}{% assign colClass = 'gf_column gf_col_no_tools' %}{% assign colClass = colClass | append: colXsClass %}{% assign colClass = colClass | append: colSmClass %}{% assign colClass = colClass | append: colMdClass %}{% assign colClass = colClass | append: colLgClass %}{% assign gPLCount = 1 %}{% paginate collections.all.products by 8 %}{% for product in collections.all.products %}<div class="{{colClass}}" style="padding: 15px !important"><div data-label="Product" data-key="product" id="m-1665046779631-child{{forloop.index}}" data-ver="3.1" class="module-wrap" data-id="1665046779631-child{{forloop.index}}" data-index="1" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="39584884097158" style="">{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779631-child{{forloop.index}}-8" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1665046779631-child{{forloop.index}}-8" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1665046779631-child{{forloop.index}}-7" data-id="1665046779631-child{{forloop.index}}-7"><div data-label="(P) Image" data-key="p-image" data-atomgroup="child-product" id="m-1665046779631-child{{forloop.index}}-15" class="module-wrap" data-icon="gpicon-product-image" data-ver="1.1" data-id="1665046779631-child{{forloop.index}}-15"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="1">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '1' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '1'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;" width="{{ featured_image.width }}" height="{{ featured_image.height }}">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" alt="{{ altTag }}" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto" width="{{ featured_image.width }}" height="{{ featured_image.height }}">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto" width="{{ featured_image.width }}" height="{{ featured_image.height }}">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>- </span>{%if '1'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto" width="{{ featured_image.width }}" height="{{ featured_image.height }}">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1665046779631-child{{forloop.index}}-10" data-id="1665046779631-child{{forloop.index}}-10" data-label="(P) Title"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1665046779631-child{{forloop.index}}-11" data-id="1665046779631-child{{forloop.index}}-11" data-label="(P) Price"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div data-label="(P) Swatches" data-key="p-swatches" data-atomgroup="child-product" id="m-1665046779631-child{{forloop.index}}-17" class="module-wrap" data-icon="gpicon-product-swatches" data-ver="1.0" data-id="1665046779631-child{{forloop.index}}-17"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-group="{{group}}" data-swatch-text="0" data-pid="{{product.id}}" data-swatcher-hide="All" data-swatcher-hideother="" data-none-option="{{noneOption}}" data-soldout="1" data-soldout-style="default" data-soldout-color="#000000" data-soldout-logic="1" data-background="eyJDb2xvciI6eyJ0eXBlIjoibWFudWFsIiwidmFyaWFudHMiOnsiQ2hhdGhhbXMgQmx1ZSI6eyJ0eXBlIjoiY29sb3IiLCJ2YWx1ZSI6InJnYmEoMTcsIDc4LCAxMjIsIDEpIn0sIkluZGlnbyI6eyJ0eXBlIjoiY29sb3IiLCJ2YWx1ZSI6InJnYmEoNzQsIDExMCwgMjA0LCAxKSJ9LCJMaW1lZCBPYWsiOnsidHlwZSI6ImNvbG9yIiwidmFsdWUiOiJyZ2JhKDE2NCwgMTQ2LCA4MiwgMSkifX19fQ==">{% unless product.variants.size == 1 and product.variants[0].title == 'Default Title' %}{% assign columnClass = 'gf_column gf_col_no_tools gf_col-md-12' %}<div class="gf_swatches gf_row-no-padding gf_row gf_row_no_tools" data-type="{{group}}">{% for option in product.options_with_values %}<div class="{{columnClass}} gf_swatches-selector gf_swatches-option{{forloop.index}}" data-name="{{option.name}}">{% if '0' == '1' %}<label>{{option.name}}</label>{% endif %}{% for value in option.values %}{% if forloop.index == 1 %}{% assign selectedClass = 'gf_selected' %}{% else %}{% assign selectedClass = '' %}{% endif %}<span class="gf_swatch {{selectedClass}}" data-group="{{group}}" data-price="0" style="margin-bottom: 15px;margin-right: 4px" data-value="{{value | escape}}"><span>{{value}}</span></span>{% endfor %}</div>{% endfor %}</div>{% endunless %}<code class="gf_swatches-data" style="display: none!important;">{{swatcher}}</code></div></div><div data-label="(P) Variants" data-key="p-variants" data-atomgroup="child-product" id="m-1665046779631-child{{forloop.index}}-13" class="module-wrap" data-icon="gpicon-product-swatches" data-ver="1.0" data-id="1665046779631-child{{forloop.index}}-13"><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-group="separately" data-style="default" data-inlinespacing="100px" data-pid="{{product.id}}" data-blankoption="0" data-blankoptiontext="Please select an item in the list">{% unless product.options_with_values.size == 1 and product.variants[0].title == 'Default Title' %}{% assign cols_lg = 12 | divided_by: 1 %}{% assign cols_md = 12 | divided_by: 1 %}{% assign cols_sm = 12 | divided_by: 1 %}{% assign cols_xs = 12 | divided_by: 1 %}{% assign rechargeClass = 'single-option-selector single-option-selector-product' %}{% assign rechargeId = 'SingleOptionSelector-product-' %}{% unless rechargeCount %}{% assign rechargeCount = 0 %}{% endunless %}{% if 'separately' == 'together' %}<div class="gf_variants-wrapper"><select id="{{rechargeId}}{{rechargeCount}}" class="gf_variants {{rechargeClass}}" data-type="separately" style="margin-bottom: 10px;">{% assign current_variant = product.selected_or_first_available_variant %}{% for variant in product.variants %}{% if variant.available %}{% if variant.id == current_variant.id %}<option selected="true" data-sku="{{variant.sku}}" value="{{variant.id}}">{{ variant.title }} - {{ variant.price | money_with_currency }}</option>{% else %}<option data-sku="{{variant.sku}}" value="{{variant.id}}">{{ variant.title }} - {{ variant.price | money_with_currency }}</option>{% endif %}{% else %}<option disabled="disabled">{{ variant.title }} - (Sold Out)</option>{% endif %}{% endfor %}</select></div>{% else %}<div class="gf_variants-wrapper gf_row gf_row_no_tools gf_row-no-padding">{% for option in product.options_with_values %}<div class="gf_column gf_col_no_tools gf_col-lg-{{cols_lg}} gf_col-md-{{cols_md}} gf_col-sm-{{cols_sm}} gf_col-xs-{{cols_xs}} gf_variants-option{{forloop.index}}">{% if 'default' == 'inline' %}<div class="gf_align-module gf_align-i-center">{% endif %}{% if '0' == '1' %}<label>{{option.name}}</label>{% endif %}<select id="{{rechargeId}}{{rechargeCount}}" class="gf_variants {{rechargeClass}}" data-type="separately" style="margin-bottom: 10px;">{% for value in option.values %}<option value="{{ value | escape }}">{{value}}</option>{% endfor %}</select>{% if 'default' == 'inline' %}</div>{% endif %}</div>{% assign rechargeCount = rechargeCount | plus: 1 %}{% endfor %}</div>{% endif %}{% endunless %}</div></div><div class="module-wrap" id="m-1665046779631-child{{forloop.index}}-14" data-id="1665046779631-child{{forloop.index}}-14" data-label="(P) Cart Button"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="1" data-stretch-md="1" data-stretch-sm="1" data-stretch-xs="1">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div>{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-xs"></div>{% endif %}{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-sm"></div>{% endif %}{% assign modulo = gPLCount | modulo: 4 %}{% if modulo == 0 %}<div class="gf_clearfix visible-md"></div>{% endif %}{% assign modulo = gPLCount | modulo: 4 %}{% if modulo == 0 %}<div class="gf_clearfix visible-lg"></div>{% endif %}{% assign gPLCount = gPLCount | plus: 1 %}{% endfor %}{% endpaginate %}</div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779598" class="gf_row" data-icon="gpicon-row" data-id="1665046779598"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664874359777" data-id="1664874359777"><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1665046779575" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1665046779575"><div class="module main-slider owl-carousel owl-theme " data-collg="1" data-colmd="1" data-colsm="1" data-colxs="1" data-marginlg="5px" data-marginmd="5px" data-marginsm="5px" data-marginxs="5px" data-dotslg="0" data-dotsmd="0" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="1" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="1"><div class="item"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779616" class="gf_row gf_equal-height gf_row-gap-0" data-icon="gpicon-row" data-id="1665046779616" data-row-gap="0px" data-extraclass="" data-layout-lg="6+6" data-layout-md="6+6" data-layout-sm="12+12" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1664874405394" data-id="1664874405394" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1665046779621" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1665046779621" data-resolution="768x768"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/2c0a3346-a2dd-4c06-90c9-d3e45d78ac64/-/format/auto/-/preview/768x768/-/quality/lighter/t1.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" natural-width="585" natural-height="585" width="585" height="585"></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1664874416148" data-id="1664874416148" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779551" class="gf_row" data-icon="gpicon-row" data-id="1665046779551" style="min-height: auto;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664874681026" data-id="1664874681026"><div data-label="Icon List Hoz" data-key="icon-list-hoz" data-atomgroup="module" id="m-1665046779538" class="module-wrap" data-icon="gpicon-iconlist2" data-ver="1.0" data-id="1665046779538"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li class="item" style="width: 23px;"><div data-index="1" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779538-1" class="element-wrap" data-icon="eicon-post" data-id="1665046779538-1"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 23px;"><div data-index="2" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779673" class="element-wrap" data-icon="eicon-post" data-id="1665046779673"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 23px;"><div data-index="3" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779605" class="element-wrap" data-icon="eicon-post" data-id="1665046779605"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 23px;"><div data-index="4" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779669" class="element-wrap" data-icon="eicon-post" data-id="1665046779669"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 23px;"><div data-index="5" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779577" class="element-wrap" data-icon="eicon-post" data-id="1665046779577"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star-half-empty"></i></div></div></div></div></li></ul></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1665046779641" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1665046779641"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Great quality of bedsheets!</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779654" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779654"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc="">“I bought the both bamboo and linen sheets. They feel so cool and comfortable and the color was so natural. Nadia was very professional and kind. And delivery is so fast!!<br>Thank you for your help.”</div></div><div data-label="Icon List" data-key="icon-list" data-atomgroup="module" id="m-1665046779678" class="module-wrap" data-icon="gpicon-iconlist" data-ver="1.0" data-id="1665046779678"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li style="margin-bottom: 15px"><span class="gf-il-icon item-content" data-index="1" data-key="content" style="width: 51px"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1665046779684" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1665046779684" data-resolution="240x240"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><img src="https://ucarecdn.com/0602ab1b-7fb3-45ef-9dbe-37fdf8fb3edd/-/format/auto/-/preview/240x240/-/quality/lighter/yi.png" alt="" class="gf_image" data-gemlang="en" data-width="40px" data-height="auto" title="" natural-width="81" natural-height="82" width="81" height="82"></div></div></span><div class="gf-il-content item-content" data-index="1" data-key="content1" style="padding-left: 51px"><div data-label="Text Block" id="e-1665046779678-2" class="element-wrap" data-id="1665046779678-2"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left gf-elm-left-md gf-elm-left-lg gf-elm-left-xs gf-elm-left-sm" data-exc=""><span style="font-weight: bold;"><span style="color: rgb(36, 36, 36);">Yi H.</span></span> / Design Director</div></div></div></li></ul></div></div></div></div></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779635" class="gf_row gf_equal-height gf_row-gap-0" data-icon="gpicon-row" data-id="1665046779635" data-row-gap="0px" data-extraclass="" data-layout-lg="6+6" data-layout-md="6+6" data-layout-sm="12+12" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1664874405394" data-id="1664874405394" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1665046779636" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1665046779636" data-resolution="768x768"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/e4c05a89-c2de-4623-8cac-cfc34dfa024c/-/format/auto/-/preview/768x768/-/quality/lighter/t2.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" natural-width="585" natural-height="585" width="585" height="585"></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1664874416148" data-id="1664874416148" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779550" class="gf_row" data-icon="gpicon-row" data-id="1665046779550" style="min-height: auto;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664874681026" data-id="1664874681026"><div data-label="Icon List Hoz" data-key="icon-list-hoz" data-atomgroup="module" id="m-1665046779565" class="module-wrap" data-icon="gpicon-iconlist2" data-ver="1.0" data-id="1665046779565"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li class="item" style="width: 23px;"><div data-index="1" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779565-1" class="element-wrap" data-icon="eicon-post" data-id="1665046779565-1"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 23px;"><div data-index="2" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779653" class="element-wrap" data-icon="eicon-post" data-id="1665046779653"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 23px;"><div data-index="3" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779544" class="element-wrap" data-icon="eicon-post" data-id="1665046779544"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 23px;"><div data-index="4" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779531" class="element-wrap" data-icon="eicon-post" data-id="1665046779531"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 23px;"><div data-index="5" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779642" class="element-wrap" data-icon="eicon-post" data-id="1665046779642"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star-half-empty"></i></div></div></div></div></li></ul></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1665046779674" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1665046779674"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">&nbsp;Very nice pattern and texture!</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779645" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779645"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc="">“I’m not usually a fan of this type of material. However, I really like the quality of this duvet cover. It’s able to be washed and dried quickly. Beautiful design. Love the type of enclosure! Highly recommend.”</div></div><div data-label="Icon List" data-key="icon-list" data-atomgroup="module" id="m-1665046779638" class="module-wrap" data-icon="gpicon-iconlist" data-ver="1.0" data-id="1665046779638"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li style="margin-bottom: 15px"><span class="gf-il-icon item-content" data-index="1" data-key="content" style="width: 51px"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1665046779655" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1665046779655" data-resolution="240x240"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf-elm-left-lg" data-exc=""><img src="https://ucarecdn.com/1e251a2d-36d5-4300-afb6-05b59990f48f/-/format/auto/-/preview/240x240/-/quality/lighter/leanda.png" alt="" class="gf_image" data-gemlang="en" data-width="40px" data-height="auto" title="" natural-width="80" natural-height="82" width="80" height="82"></div></div></span><div class="gf-il-content item-content" data-index="1" data-key="content1" style="padding-left: 51px"><div data-label="Text Block" id="e-1665046779638-2" class="element-wrap" data-id="1665046779638-2"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf-elm-left-lg" data-exc=""><span style="font-weight: bold;"><span style="color: rgb(36, 36, 36);">LEANDA R.</span></span> / Design Director</div></div></div></li></ul></div></div></div></div></div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779567" class="gf_row gf_equal-height gf_row-gap-0" data-icon="gpicon-row" data-id="1665046779567" data-row-gap="0px" data-extraclass="" data-layout-lg="6+6" data-layout-md="6+6" data-layout-sm="12+12" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1664874405394" data-id="1664874405394" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1665046779546" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1665046779546" data-resolution="768x768"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/7d2e3cc0-f14c-4f14-8132-0437440eff9c/-/format/auto/-/preview/768x768/-/quality/lighter/t3.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" natural-width="768" natural-height="768" width="768" height="768"></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1664874416148" data-id="1664874416148" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779507" class="gf_row" data-icon="gpicon-row" data-id="1665046779507" style="min-height: auto;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664874681026" data-id="1664874681026"><div data-label="Icon List Hoz" data-key="icon-list-hoz" data-atomgroup="module" id="m-1665046779556" class="module-wrap" data-icon="gpicon-iconlist2" data-ver="1.0" data-id="1665046779556"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li class="item" style="width: 23px;"><div data-index="1" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779556-1" class="element-wrap" data-icon="eicon-post" data-id="1665046779556-1"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 23px;"><div data-index="2" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779656" class="element-wrap" data-icon="eicon-post" data-id="1665046779656"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 23px;"><div data-index="3" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779604" class="element-wrap" data-icon="eicon-post" data-id="1665046779604"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 23px;"><div data-index="4" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779624" class="element-wrap" data-icon="eicon-post" data-id="1665046779624"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 23px;"><div data-index="5" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779640" class="element-wrap" data-icon="eicon-post" data-id="1665046779640"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star-half-empty"></i></div></div></div></div></li></ul></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1665046779648" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1665046779648"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">This duvet cover is awesome</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779533" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779533"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc="">“This duvet cover is awesome. It's so soft. It might have some wrinkles but honestly could careless. The feel and look are 5 star. Would buy again. My kitty loves it too!”</div></div><div data-label="Icon List" data-key="icon-list" data-atomgroup="module" id="m-1665046779506" class="module-wrap" data-icon="gpicon-iconlist" data-ver="1.0" data-id="1665046779506"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li style="margin-bottom: 15px"><span class="gf-il-icon item-content" data-index="1" data-key="content" style="width: 51px"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1665046779541" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1665046779541" data-resolution="240x240"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><img src="https://ucarecdn.com/1e251a2d-36d5-4300-afb6-05b59990f48f/-/format/auto/-/preview/240x240/-/quality/lighter/leanda.png" alt="" class="gf_image" data-gemlang="en" data-width="40px" data-height="auto" title="" natural-width="80" natural-height="82" width="80" height="82"></div></div></span><div class="gf-il-content item-content" data-index="1" data-key="content1" style="padding-left: 51px"><div data-label="Text Block" id="e-1665046779506-2" class="element-wrap" data-id="1665046779506-2"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf-elm-left-lg" data-exc=""><span style="font-weight: bold;"><span style="color: rgb(36, 36, 36);">HARRY Q.</span></span> / Design Director</div></div></div></li></ul></div></div></div></div></div></div></div></div><div class="item"><div data-index="4" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779561" class="gf_row gf_equal-height gf_row-gap-0" data-icon="gpicon-row" data-id="1665046779561" data-row-gap="0px" data-extraclass="" data-layout-lg="6+6" data-layout-md="6+6" data-layout-sm="12+12" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1664874405394" data-id="1664874405394" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1665046779525" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1665046779525" data-resolution="768x768"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/d380f33d-4dc8-41f5-be71-8ee4e5e8bf34/-/format/auto/-/preview/768x768/-/quality/lighter/t4.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" natural-width="768" natural-height="768" width="768" height="768"></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1664874416148" data-id="1664874416148" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779591" class="gf_row" data-icon="gpicon-row" data-id="1665046779591" style="min-height: auto;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664874681026" data-id="1664874681026"><div data-label="Icon List Hoz" data-key="icon-list-hoz" data-atomgroup="module" id="m-1665046779576" class="module-wrap" data-icon="gpicon-iconlist2" data-ver="1.0" data-id="1665046779576"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li class="item" style="width: 23px;"><div data-index="1" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779576-1" class="element-wrap" data-icon="eicon-post" data-id="1665046779576-1"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 23px;"><div data-index="2" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779508" class="element-wrap" data-icon="eicon-post" data-id="1665046779508"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 23px;"><div data-index="3" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779619" class="element-wrap" data-icon="eicon-post" data-id="1665046779619"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 23px;"><div data-index="4" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779672" class="element-wrap" data-icon="eicon-post" data-id="1665046779672"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 23px;"><div data-index="5" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779514" class="element-wrap" data-icon="eicon-post" data-id="1665046779514"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star-half-empty"></i></div></div></div></div></li></ul></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1665046779539" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1665046779539"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Love it makes my room look pretty</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779517" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779517"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-xs gf-elm-center-sm" data-gemlang="en" data-exc="">“Was easy to put on,now I can change my room simply by putting this on  I bought the open bag or slightly used and it wasn’t used at all just returned because someone didn’t like it very happy with ita comforter or taking it off.I guess the only small dislike would be pattern is more Old than I thought.”</div></div><div data-label="Icon List" data-key="icon-list" data-atomgroup="module" id="m-1665046779566" class="module-wrap" data-icon="gpicon-iconlist" data-ver="1.0" data-id="1665046779566"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li style="margin-bottom: 15px"><span class="gf-il-icon item-content" data-index="1" data-key="content" style="width: 51px"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1665046779633" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1665046779633" data-resolution="240x240"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><img src="https://ucarecdn.com/b97fa5e3-1486-4769-98d4-b37e48409c76/-/format/auto/-/preview/240x240/-/quality/lighter/jerry.png" alt="" class="gf_image" data-gemlang="en" data-width="40px" data-height="auto" title="" natural-width="80" natural-height="82" width="80" height="82"></div></div></span><div class="gf-il-content item-content" data-index="1" data-key="content1" style="padding-left: 51px"><div data-label="Text Block" id="e-1665046779566-2" class="element-wrap" data-id="1665046779566-2"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf-elm-left-lg" data-exc=""><span style="font-weight: bold;"><span style="color: rgb(36, 36, 36);">JERRY N.</span></span> / Design Director</div></div></div></li></ul></div></div></div></div></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779535" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1665046779535" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664876822018" data-id="1664876822018"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779558" class="gf_row" data-icon="gpicon-row" data-id="1665046779558"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664871930444" data-id="1664871930444"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1665046779553" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1665046779553"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h2 class="gf_gs-text-heading-2">Frequently asked questions</h2></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779593" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779593"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-xs gf-elm-center-sm" data-gemlang="en" data-exc="">Have questions? We’re hereto help</div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779661" class="gf_row" data-icon="gpicon-row" data-id="1665046779661" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="12+12" data-layout-xs="12+12"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1664876849462" data-id="1664876849462"><div data-label="Accordion" data-key="accordion" data-atomgroup="module" id="m-1665046779529" class="module-wrap" data-icon="gpicon-accordion" data-ver="2" data-id="1665046779529"><div data-accordion-group="" class="module " data-opendefault="0" data-opentab="1" data-single="1" data-bordercolor="#cecece" data-bordersize="1px"><div data-accordion="" style="padding-bottom: "><div data-control="" data-index="1" data-key="content" class="chevron iCon-right text- item-content"><div data-label="Text Block" id="e-1665046779519" class="element-wrap" data-icon="eicon-animation-text" data-id="1665046779519"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-xs gf-elm-left-sm" data-exc=""><p>What is the difference between a duvet cover and a bed sheet?</p></div></div><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:rgba(180, 180, 180, 1)"></span></button></div><div data-content="" style="margin-top: ; background-color: #FFFFFF"><article data-index="1" data-key="content1" class="item-content"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779660" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779660"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc="">A duvet cover is placed over your duvet and is buttoned or zipped closed to protect it, while sheets are placed over your mattress. We offer a wide selection of sheets, including fitted, flat, valance, and platform valance.</div></div></article></div></div><div data-accordion="" style="padding-bottom: "><div data-control="" data-index="2" data-key="content" class="chevron iCon-right text- item-content"><div data-label="Text Block" id="e-1665046779622" class="element-wrap" data-icon="eicon-animation-text" data-id="1665046779622"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><p>How often should you change your bed sheets?</p></div></div><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:rgba(180, 180, 180, 1)"></span></button></div><div data-content="" style="margin-top: ; background-color: #FFFFFF"><article data-index="2" data-key="content1" class="item-content"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779680" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779680"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc="">It is recommended that your sheets and pillowcases be changed once a week, with duvet covers once every two weeks at most.</div></div></article></div></div><div data-accordion="" style="padding-bottom: "><div data-control="" data-index="3" data-key="content" class="chevron iCon-right text- item-content"><div data-label="Text Block" id="e-1665046779563" class="element-wrap" data-icon="eicon-animation-text" data-id="1665046779563"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><p>When can a duvet be used, year-round or during winter only?</p></div></div><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:rgba(180, 180, 180, 1)"></span></button></div><div data-content="" style="margin-top: ; background-color: #FFFFFF"><article data-index="3" data-key="content1" class="item-content"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779521" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779521"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc="">We provide a selection of duvets with different togs to suit the seasons. The higher the tog, the warmer the duvet. 7.5 tog would be for the summer months, 10.5 is a standard all-year-round duvet, while 13.5 gives that extra thickness for colder months.</div></div></article></div></div><div data-accordion="" style="padding-bottom: "><div data-control="" data-index="4" data-key="content" class="chevron iCon-right text- item-content"><div data-label="Text Block" id="e-1665046779522" class="element-wrap" data-icon="eicon-animation-text" data-id="1665046779522"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc="">How to put on a duvet cover? <br></div></div><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:rgba(180, 180, 180, 1)"></span></button></div><div data-content="" style="margin-top: ; background-color: #FFFFFF"><article data-index="4" data-key="content1" class="item-content"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779685" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779685"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc="">Follow our easy step-by-step guide and never have trouble changing your duvet covers again!</div></div></article></div></div></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1664876851879" data-id="1664876851879"><div data-label="Accordion" data-key="accordion" data-atomgroup="module" id="m-1665046779677" class="module-wrap" data-icon="gpicon-accordion" data-ver="2" data-id="1665046779677"><div data-accordion-group="" class="module " data-opendefault="0" data-opentab="1" data-single="1" data-bordercolor="#cecece" data-bordersize="1px"><div data-accordion="" style="padding-bottom: "><div data-control="" data-index="1" data-key="content" class="chevron iCon-right text- item-content"><div data-label="Text Block" id="e-1665046779637" class="element-wrap" data-icon="eicon-animation-text" data-id="1665046779637"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><p>What are all the things to consider when buying bedding?</p></div></div><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:rgba(180, 180, 180, 1)"></span></button></div><div data-content="" style="margin-top: ; background-color: #FFFFFF"><article data-index="1" data-key="content1" class="item-content"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779681" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779681"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc="">Our bedding range provides a variety of materials, styles, patterns, and sizes. Please use our Buying Guide to help you choose your perfect bed linen.</div></div></article></div></div><div data-accordion="" style="padding-bottom: "><div data-control="" data-index="2" data-key="content" class="chevron iCon-right text- item-content"><div data-label="Text Block" id="e-1665046779667" class="element-wrap" data-icon="eicon-animation-text" data-id="1665046779667"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><p>What are the advantages of duvet covers?</p></div></div><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:rgba(180, 180, 180, 1)"></span></button></div><div data-content="" style="margin-top: ; background-color: #FFFFFF"><article data-index="2" data-key="content1" class="item-content"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779526" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779526"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc="">Duvet covers not only keep your duvet clean but also allow you to add decoration and style to your bedroom. They provide extra comfort and help with insulation to keep you warm. <br></div></div></article></div></div><div data-accordion="" style="padding-bottom: "><div data-control="" data-index="3" data-key="content" class="chevron iCon-right text- item-content"><div data-label="Text Block" id="e-1665046779503" class="element-wrap" data-icon="eicon-animation-text" data-id="1665046779503"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc="">What do you need for a bed set?</div></div><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:rgba(180, 180, 180, 1)"></span></button></div><div data-content="" style="margin-top: ; background-color: #FFFFFF"><article data-index="3" data-key="content1" class="item-content"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779665" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779665"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc="">At Home Store + More, we offer everything you need to complete a full bed set; this includes duvet covers, pillowcases, bedspreads, sheets, cushions, and pillowshams.</div></div></article></div></div><div data-accordion="" style="padding-bottom: "><div data-control="" data-index="4" data-key="content" class="chevron iCon-right text- item-content"><div data-label="Text Block" id="e-1665046779511" class="element-wrap" data-icon="eicon-animation-text" data-id="1665046779511"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc="">Can I wash my feather duvet in the washing machine? <br></div></div><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:#000000"></span></button><button type="button" class="wrapp-icon"><span class="acc-icon" style="background-color:rgba(180, 180, 180, 1)"></span></button></div><div data-content="" style="margin-top: ; background-color: #FFFFFF"><article data-index="4" data-key="content1" class="item-content"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1665046779683" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1665046779683"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc="">Usually, yes, you can, but always read the instructions on the label. Before washing, double-check for worn stitching and minor rips, and repair to avoid losing any down stuffing or feathers. Use a delicate setting on your machine and mild/non-bio detergent. <br></div></div></article></div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1665046779530" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1665046779530" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="39976528183430" style="">{% assign product = all_products['the-time-is-now-naidoc-week-2022-rpet-fold-up-bag-free'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665129413152" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1665129413152" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1665129413160" data-id="1665129413160"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779691" class="gf_row gf_equal-height gf_row-gap-0" data-icon="gpicon-row" data-id="1665046779691" data-layout-lg="4+8" data-extraclass="" data-layout-md="12+12" data-layout-sm="12+12" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible;" data-row-gap="0px"><div class="gf_column gf_col-lg-4 gf_col-xs-12 gf_col-sm-12 gf_col-md-12" id="c-1664935126489" data-id="1664935126489" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div class="module-wrap" id="m-1665046779530-1" data-id="1665046779530-1" data-label="(P) Title" data-icon="gpicon-product-title" data-ver="1.0"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div data-label="Icon List Hoz" data-key="icon-list-hoz" data-atomgroup="module" id="m-1665046779676" class="module-wrap" data-icon="gpicon-iconlist2" data-ver="1.0" data-id="1665046779676"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs "><ul class="gf_icon-list"><li class="item" style="width: 17px;"><div data-index="1" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779676-1" class="element-wrap" data-icon="eicon-post" data-id="1665046779676-1"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 17px;"><div data-index="2" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779523" class="element-wrap" data-icon="eicon-post" data-id="1665046779523"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 17px;"><div data-index="3" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779528" class="element-wrap" data-icon="eicon-post" data-id="1665046779528"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 17px;"><div data-index="4" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779509" class="element-wrap" data-icon="eicon-post" data-id="1665046779509"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li><li class="item" style="width: 17px;"><div data-index="5" data-key="content" class="item-content"><div data-label="Icon" id="e-1665046779499" class="element-wrap" data-icon="eicon-post" data-id="1665046779499"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><div class="gf_icon-wrap"><i class="gf_icon fa fa-star"></i></div></div></div></div></li></ul></div></div><div class="module-wrap" id="m-1665046779530-2" data-id="1665046779530-2" data-label="(P) Price" data-icon="gpicon-product-price" data-ver="1.4"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '1' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span>-</span><span class="gf_pq-percent">{{ diff }}%</span><span></span></span></span>{% endif %}</div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1665046779675" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1665046779675" data-extraclass="" data-layout-lg="6+6" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="6+6" data-row-gap="0px" style="min-height: auto;"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-6" id="c-1664935198219" data-id="1664935198219"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1665046779668" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1665046779668" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-right-lg gf-elm-right-md gf-elm-right-sm gf-elm-right-xs" data-exc=""><img src="https://ucarecdn.com/0cf90a89-d7cb-446e-b29e-142337b1181d/-/format/auto/-/preview/3000x3000/-/quality/lighter/i1.png" alt="" class="gf_image" data-gemlang="en" data-width="60px" data-height="auto" title="" natural-width="120" natural-height="120" width="120" height="120"></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-6" id="c-1664935209249" data-id="1664935209249"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1665046779687" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1665046779687" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><img src="https://ucarecdn.com/27b86ca8-69f4-40a5-baa0-cf120b0571fe/-/format/auto/-/preview/3000x3000/-/quality/lighter/i2.png" alt="" class="gf_image" data-gemlang="en" data-width="60px" data-height="auto" title="" natural-width="120" natural-height="120" width="120" height="120"></div></div></div></div><div class="module-wrap" id="m-1665046779530-3" data-id="1665046779530-3" data-label="(P) Cart Button" data-icon="gpicon-product-cartbutton" data-ver="1.1"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Buy Now" data-soldouttext="Sold Out" data-ajaxcart="1" data-cbto="checkout" data-editlink="" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Buy Now</span></button>{% endif %}</div></div></div><div class="gf_column gf_col-lg-8 gf_col-xs-12 gf_col-sm-12 gf_col-md-12" id="c-1664935131520" data-id="1664935131520" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div class="module-wrap" id="m-1665046779530-0" data-id="1665046779530-0" data-label="(P) Image" data-icon="gpicon-product-image" data-ver="1.1"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;" width="{{ featured_image.width }}" height="{{ featured_image.height }}">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" alt="{{ altTag }}" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto" width="{{ featured_image.width }}" height="{{ featured_image.height }}">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto" width="{{ featured_image.width }}" height="{{ featured_image.height }}">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto" width="{{ featured_image.width }}" height="{{ featured_image.height }}">{% endif %}{{elementImgHolderClose}}</div></div></div></div></div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div><!--gfsplit--><script>window.__gemStoreData = {};window.__gemStoreData.gemKeyValid = {};window.__gemStoreData.gemKeyValid.bestWayAddToCart = 1;</script>
</div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		"https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv1countdown.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv3product.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfaccordion.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/owl.carousel.min.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv4productimagelist.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv4lightbox.js",
		 "https://www.youtube.com/player_api",
		'{{ 'gem-page-83270434950.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->