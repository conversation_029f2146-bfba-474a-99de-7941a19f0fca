{% comment %}
	GEMPAGE BUILDER (https://apps.shopify.com/gempage)

	You SHOULD NOT modify source code in this page because
	It is automatically generated from GEMPAGE BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->

<link data-instant-track rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.6.3/css/font-awesome.min.css" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-page-83308544134.css' | asset_url }}" class="gf_page_style">
<link data-instant-track class="gf_fonts" data-fonts="Gochi Hand" href="//fonts.googleapis.com/css2?family=Gochi Hand:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant Garamond" href="//fonts.googleapis.com/css2?family=Cormorant Garamond:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant" href="//fonts.googleapis.com/css2?family=Cormorant:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track rel="stylesheet" type="text/css" href= "https://d1um8515vdn9kb.cloudfront.net/libs/css/owl.carousel.min.css" class="gf_libs">
<!--GEM_HEADER_END-->
<!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor">
<div data-label="Liquid" data-key="liquid" data-atomgroup="module" id="m-1666334624508" class="module-wrap" data-icon="gpicon-liquid" data-ver="1.1" data-id="1666334624508" data-name="Right click on this, and choose Edit Code."><div class="module gf_module- gf_module--lg gf_module--md gf_module--sm gf_module--xs {{extraClass}}"><marquee class="html-marquee" direction="left" behavior="scroll" scrollamount="12"><p> ON SALE NOW </p></marquee></div></div><!--gfsplit--><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1666680384264" class="module-wrap gf_hero-fixed-mode" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1666680384264" style=""><div class="module " data-image="https://ucarecdn.com/81e4b47a-2e92-45a3-9619-e8a9ac9166a1/-/format/auto/-/preview/3000x3000/-/quality/lighter/R53A0892.png" data-image-lg="https://ucarecdn.com/81e4b47a-2e92-45a3-9619-e8a9ac9166a1/-/format/auto/-/preview/3000x3000/-/quality/lighter/r53a0892.png" data-image-md="https://ucarecdn.com/81e4b47a-2e92-45a3-9619-e8a9ac9166a1/-/format/auto/-/preview/3000x3000/-/quality/lighter/r53a0892.png" data-image-sm="https://ucarecdn.com/81e4b47a-2e92-45a3-9619-e8a9ac9166a1/-/format/auto/-/preview/3000x3000/-/quality/lighter/r53a0892.png" data-image-xs="https://ucarecdn.com/81e4b47a-2e92-45a3-9619-e8a9ac9166a1/-/format/auto/-/preview/3000x3000/-/quality/lighter/r53a0892.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="1"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666680553814" class="gf_row gf_equal-height" data-icon="gpicon-row" data-id="1666680553814" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1666680553755" data-id="1666680553755" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1666680604503" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1666680604503" style=""><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">THE HOME of GIFTS</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1666680612668" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666680612668" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>From trimming the tree to hanging the wreath, prepare your home for a wonderous festive season.</p></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666680670773" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1666680670773" style="min-height: auto;" data-layout-lg="3+3+3+3" data-extraclass="" data-layout-md="3+3+3+3" data-layout-sm="6+6+6+6" data-layout-xs="12+12+12+12" data-row-gap="0px"><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1666680670785" data-id="1666680670785" style="min-height: 1px; display: block; flex-direction: unset; justify-content: unset;"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666680676001" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666680676001" style=""><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>Homeware</span></a></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1666680672128" data-id="1666680672128" style="min-height: 1px; display: block; flex-direction: unset; justify-content: unset;"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666680681305" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666680681305" style=""><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>Women's Fashion</span></a></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1666680672151" data-id="1666680672151" style="min-height: 1px; display: block; flex-direction: unset; justify-content: unset;"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666680680232" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666680680232" style=""><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>Men's Fashion</span></a></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1666680672148" data-id="1666680672148" style="min-height: 1px; display: block; flex-direction: unset; justify-content: unset;"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666680678252" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666680678252" style=""><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>Accesories</span></a></div></div></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1666680558010" data-id="1666680558010" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666680904581" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666680904581" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/0d3c3489-a481-4c9b-a453-10780c87303f/-/format/auto/-/preview/3000x3000/-/quality/lighter/DADAAA.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1034" height="663" natural-width="1034" natural-height="663"></div></div></div></div></span></div><picture class="gf_hero-bg-wrap"><source media="(min-width: 1200px)" srcset="https://ucarecdn.com/81e4b47a-2e92-45a3-9619-e8a9ac9166a1/-/format/auto/-/preview/3000x3000/-/quality/lighter/r53a0892.png"><source media="(min-width: 992px)" srcset="https://ucarecdn.com/81e4b47a-2e92-45a3-9619-e8a9ac9166a1/-/format/auto/-/preview/3000x3000/-/quality/lighter/r53a0892.png"><source media="(min-width: 768px)" srcset="https://ucarecdn.com/81e4b47a-2e92-45a3-9619-e8a9ac9166a1/-/format/auto/-/preview/3000x3000/-/quality/lighter/r53a0892.png"><img src="https://ucarecdn.com/81e4b47a-2e92-45a3-9619-e8a9ac9166a1/-/format/auto/-/preview/3000x3000/-/quality/lighter/r53a0892.png" alt=""></picture><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666680950628" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1666680950628" data-extraclass="" style="display: block; flex-wrap: unset; visibility: visible;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1666680950517" data-id="1666680950517" style="display: block; flex-direction: unset; justify-content: unset; min-height: auto;"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666680961718" class="gf_row gf_row-gap-15" data-icon="gpicon-row" data-id="1666680961718" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="12+12" data-row-gap="15px"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1666680961707" data-id="1666680961707"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666680966438" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666680966438" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/0a9afe25-f741-4984-834d-95b1dd98b595/-/format/auto/-/preview/3000x3000/-/quality/lighter/DDD.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1334" height="1600" natural-width="1334" natural-height="1600"></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1666680962902" data-id="1666680962902"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666680984946" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666680984946" style="" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/aa70e75a-0ab4-43c2-9e0a-c1ce95472952/-/format/auto/-/preview/3000x3000/-/quality/lighter/ASFFFFFFF.JPG" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="624" height="447" natural-width="624" natural-height="447"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666681032946" class="gf_row" data-icon="gpicon-row" data-id="1666681032946"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1666681032995" data-id="1666681032995"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1666681024352" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1666681024352" style=""><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Gift The Halls</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1666681043004" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666681043004"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p><span style="color: rgb(16, 17, 22); font-family: freight-display-pro, serif; font-size: 20px; letter-spacing: 0.14px;">From festive Decor to intricate ornaments and magnificent trees, deck your halls with delight. Our Christmas shop is now&nbsp;open.</span><br></p></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666681372515" class="gf_row" data-icon="gpicon-row" data-id="1666681372515" data-layout-lg="3+3+3+3" data-extraclass="" data-layout-md="3+3+3+3" data-layout-sm="6+6+6+6" data-layout-xs="12+12+12+12"><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1666681372460" data-id="1666681372460"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666681146256" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666681146256" style=""><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>Homewares</span></a></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1666681373911" data-id="1666681373911"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666681149985" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666681149985" style=""><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>Outdoors</span></a></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1666681373918" data-id="1666681373918"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666681153241" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666681153241" style=""><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>Polos</span></a></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1666681373871" data-id="1666681373871"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666681153523" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666681153523" style=""><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>T-Shirts</span></a></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666681140039" class="gf_row" data-icon="gpicon-row" data-id="1666681140039" data-layout-lg="4+4+4" data-extraclass="" data-layout-md="4+4+4" data-layout-sm="12+12+12" data-layout-xs="12+12+12"><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-12 gf_col-xs-12" id="c-1666681140046" data-id="1666681140046"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666681238498" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666681238498" style=""><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>For Him</span></a></div></div></div><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-12 gf_col-xs-12" id="c-1666681141477" data-id="1666681141477"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666681166693" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666681166693" style=""><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>For Them</span></a></div></div></div><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-12 gf_col-xs-12" id="c-1666681141518" data-id="1666681141518"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666681178622" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666681178622" style=""><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>For Her</span></a></div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1666681834675" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1666681834675" style=""><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">'Tis The Season</h1></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666676900429" class="gf_row gf_equal-height gf_row-fluid gf_row-no-padding gf_row-gap-10" data-icon="gpicon-row" data-id="1666676900429" data-layout-lg="3+3+3+3" data-extraclass="" data-layout-md="3+3+3+3" data-layout-sm="6+6+6+6" data-layout-xs="12+12+12+12" data-row-gap="10px" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-12 gf_col-xs-12" id="c-1666676900418" data-id="1666676900418" style="display: flex; flex-direction: column; justify-content: flex-start; min-height: auto;"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1666676992438" class="module-wrap gf_hero-fixed-mode" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1666676992438" style=""><div class="module " data-image="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/DSC09587.png" data-image-lg="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-image-md="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-image-sm="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-image-xs="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="1"><span data-index="1" class="item-content align-bottom"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666677008286" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666677008286" style=""><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>Call To Action</span></a></div></div></span></div><picture class="gf_hero-bg-wrap"><source media="(min-width: 1200px)" srcset="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png"><source media="(min-width: 992px)" srcset="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png"><source media="(min-width: 768px)" srcset="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png"><img src="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" alt=""></picture><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-12 gf_col-xs-12" id="c-1666676909228" data-id="1666676909228" style="display: flex; flex-direction: column; justify-content: flex-start; min-height: auto;"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1666676964717" class="module-wrap gf_hero-fixed-mode" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1666676964717" style=""><div class="module " data-image="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/DSC09587.png" data-image-lg="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-image-md="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-image-sm="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-image-xs="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="1"><span data-index="1" class="item-content align-bottom"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666677010326" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666677010326" style=""><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>Call To Action</span></a></div></div></span></div><picture class="gf_hero-bg-wrap"><source media="(min-width: 1200px)" srcset="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png"><source media="(min-width: 992px)" srcset="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png"><source media="(min-width: 768px)" srcset="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png"><img src="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" alt=""></picture><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-12 gf_col-xs-12" id="c-1666676909180" data-id="1666676909180" style="display: flex; flex-direction: column; justify-content: flex-start; min-height: auto;"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1666676964225" class="module-wrap gf_hero-fixed-mode" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1666676964225" style=""><div class="module " data-image="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/DSC09587.png" data-image-lg="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-image-md="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-image-sm="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-image-xs="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="1"><span data-index="1" class="item-content align-bottom"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666677009569" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666677009569" style=""><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>Call To Action</span></a></div></div></span></div><picture class="gf_hero-bg-wrap"><source media="(min-width: 1200px)" srcset="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png"><source media="(min-width: 992px)" srcset="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png"><source media="(min-width: 768px)" srcset="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png"><img src="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" alt=""></picture><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666680403058" class="gf_row gf_row-gap-0 gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1666680403058" data-extraclass="" data-row-gap="0px" style="display: block; flex-wrap: unset; visibility: visible;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1666680403141" data-id="1666680403141" style="display: block; flex-direction: unset; justify-content: unset; min-height: auto;"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1666680538548" class="module-wrap gf_hero-fixed-mode" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1666680538548" style=""><div class="module " data-image="https://ucarecdn.com/81e4b47a-2e92-45a3-9619-e8a9ac9166a1/-/format/auto/-/preview/3000x3000/-/quality/lighter/R53A0892.png" data-image-lg="https://ucarecdn.com/81e4b47a-2e92-45a3-9619-e8a9ac9166a1/-/format/auto/-/preview/3000x3000/-/quality/lighter/r53a0892.png" data-image-md="https://ucarecdn.com/81e4b47a-2e92-45a3-9619-e8a9ac9166a1/-/format/auto/-/preview/3000x3000/-/quality/lighter/r53a0892.png" data-image-sm="https://ucarecdn.com/81e4b47a-2e92-45a3-9619-e8a9ac9166a1/-/format/auto/-/preview/3000x3000/-/quality/lighter/r53a0892.png" data-image-xs="https://ucarecdn.com/81e4b47a-2e92-45a3-9619-e8a9ac9166a1/-/format/auto/-/preview/3000x3000/-/quality/lighter/r53a0892.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="1"><span data-index="1" class="item-content align-bottom"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666680538566" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666680538566"><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>Present Polos</span></a></div></div></span></div><picture class="gf_hero-bg-wrap"><source media="(min-width: 1200px)" srcset="https://ucarecdn.com/81e4b47a-2e92-45a3-9619-e8a9ac9166a1/-/format/auto/-/preview/3000x3000/-/quality/lighter/r53a0892.png"><source media="(min-width: 992px)" srcset="https://ucarecdn.com/81e4b47a-2e92-45a3-9619-e8a9ac9166a1/-/format/auto/-/preview/3000x3000/-/quality/lighter/r53a0892.png"><source media="(min-width: 768px)" srcset="https://ucarecdn.com/81e4b47a-2e92-45a3-9619-e8a9ac9166a1/-/format/auto/-/preview/3000x3000/-/quality/lighter/r53a0892.png"><img src="https://ucarecdn.com/81e4b47a-2e92-45a3-9619-e8a9ac9166a1/-/format/auto/-/preview/3000x3000/-/quality/lighter/r53a0892.png" alt=""></picture><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666853389920" class="gf_row gf_equal-height gf_row-gap-10 gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1666853389920" data-layout-lg="3+3+3+3" data-extraclass="" data-layout-md="3+3+3+3" data-layout-sm="6+6+6+6" data-layout-xs="12+12+12+12" data-row-gap="10px" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1666676900418" data-id="1666676900418" style="display: flex; flex-direction: column; justify-content: flex-start; min-height: auto;"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1666853389915" class="module-wrap gf_hero-fixed-mode" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1666853389915" style=""><div class="module " data-image="https://ucarecdn.com/9696d9d2-0c02-4850-80d6-10c0e125e061/-/format/auto/-/preview/3000x3000/-/quality/lighter/Artboard%20%E2%80%93%201.png" data-image-lg="https://ucarecdn.com/9696d9d2-0c02-4850-80d6-10c0e125e061/-/format/auto/-/preview/3000x3000/-/quality/lighter/artboard%20%e2%80%93%201.png" data-image-md="https://ucarecdn.com/9696d9d2-0c02-4850-80d6-10c0e125e061/-/format/auto/-/preview/3000x3000/-/quality/lighter/artboard%20%e2%80%93%201.png" data-image-sm="https://ucarecdn.com/9696d9d2-0c02-4850-80d6-10c0e125e061/-/format/auto/-/preview/3000x3000/-/quality/lighter/artboard%20%e2%80%93%201.png" data-image-xs="https://ucarecdn.com/9696d9d2-0c02-4850-80d6-10c0e125e061/-/format/auto/-/preview/3000x3000/-/quality/lighter/artboard%20%e2%80%93%201.png" data-height="" data-effect="none" data-transition="0.5" data-fixedmode="1"><span data-index="1" class="item-content align-bottom"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666853389879" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666853389879" style=""><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>Call To Action</span></a></div></div></span></div><picture class="gf_hero-bg-wrap"><source media="(min-width: 1200px)" srcset="https://ucarecdn.com/9696d9d2-0c02-4850-80d6-10c0e125e061/-/format/auto/-/preview/3000x3000/-/quality/lighter/artboard%20%e2%80%93%201.png"><source media="(min-width: 992px)" srcset="https://ucarecdn.com/9696d9d2-0c02-4850-80d6-10c0e125e061/-/format/auto/-/preview/3000x3000/-/quality/lighter/artboard%20%e2%80%93%201.png"><source media="(min-width: 768px)" srcset="https://ucarecdn.com/9696d9d2-0c02-4850-80d6-10c0e125e061/-/format/auto/-/preview/3000x3000/-/quality/lighter/artboard%20%e2%80%93%201.png"><img src="https://ucarecdn.com/9696d9d2-0c02-4850-80d6-10c0e125e061/-/format/auto/-/preview/3000x3000/-/quality/lighter/artboard%20%e2%80%93%201.png" alt=""></picture><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1666676909228" data-id="1666676909228" style="display: flex; flex-direction: column; justify-content: flex-start; min-height: auto;"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1666853389868" class="module-wrap gf_hero-fixed-mode" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1666853389868" style=""><div class="module " data-image="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/DSC09587.png" data-image-lg="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-image-md="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-image-sm="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-image-xs="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="1"><span data-index="1" class="item-content align-bottom"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666853389886" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666853389886" style=""><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>Call To Action</span></a></div></div></span></div><picture class="gf_hero-bg-wrap"><source media="(min-width: 1200px)" srcset="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png"><source media="(min-width: 992px)" srcset="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png"><source media="(min-width: 768px)" srcset="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png"><img src="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" alt=""></picture><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1666676909180" data-id="1666676909180" style="display: flex; flex-direction: column; justify-content: flex-start; min-height: auto;"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1666853389885" class="module-wrap gf_hero-fixed-mode" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1666853389885" style=""><div class="module " data-image="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/DSC09587.png" data-image-lg="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-image-md="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-image-sm="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-image-xs="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="1"><span data-index="1" class="item-content align-bottom"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666853389841" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666853389841" style=""><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>Call To Action</span></a></div></div></span></div><picture class="gf_hero-bg-wrap"><source media="(min-width: 1200px)" srcset="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png"><source media="(min-width: 992px)" srcset="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png"><source media="(min-width: 768px)" srcset="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png"><img src="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" alt=""></picture><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1666676909214" data-id="1666676909214" style="display: flex; flex-direction: column; justify-content: flex-start; min-height: auto;"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1666853389842" class="module-wrap gf_hero-fixed-mode" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1666853389842" style=""><div class="module " data-image="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/DSC09587.png" data-image-lg="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-image-md="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-image-sm="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-image-xs="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="1"><span data-index="1" class="item-content align-bottom"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666853389826" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666853389826" style=""><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>Call To Action</span></a></div></div></span></div><picture class="gf_hero-bg-wrap"><source media="(min-width: 1200px)" srcset="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png"><source media="(min-width: 992px)" srcset="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png"><source media="(min-width: 768px)" srcset="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png"><img src="https://ucarecdn.com/acae3763-ca46-4a0f-b0ee-e02eca2666f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsc09587.png" alt=""></picture><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666853681971" class="gf_row gf_equal-height gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1666853681971" data-layout-lg="4+4+4" data-extraclass="" data-layout-md="4+4+4" data-layout-sm="12+12+12" data-layout-xs="12+12+12" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-12 gf_col-xs-12" id="c-1666853681985" data-id="1666853681985" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1666853686286" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1666853686286"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">GIFTING 101</h1></div></div></div><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-12 gf_col-xs-12" id="c-1666853683515" data-id="1666853683515" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1666853689810" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666853689810"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1" data-gemlang="en"><p>Read our blog on our top picks for gifting so you can get it right every-time!</p></div></div></div><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-12 gf_col-xs-12" id="c-1666853683450" data-id="1666853683450" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666853693319" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666853693319"><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>Call To Action</span></a></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666677916411" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1666677916411" data-extraclass="" data-row-gap="0px" style="display: block;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1652082236525" data-id="1652082236525"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666677916392" class="gf_row" data-icon="gpicon-row" data-id="1666677916392" data-layout-lg="12" data-extraclass="" data-layout-md="12" data-layout-sm="12" data-layout-xs="12"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1652082241226" data-id="1652082241226"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1666677916301" class="element-wrap" data-icon="gpicon-heading" data-ver="1" data-id="1666677916301" style="display: block;"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-lg gf-elm-center-sm gf-elm-center-xs gf_gs-text-heading-2" data-gemlang="en" data-exc="">Best Selling&nbsp;</div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1666677916288" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666677916288"><div class="elm text-edit gf-elm-left gf-elm-center-lg gf-elm-center-md gf-elm-center-xs gf-elm-center-sm gf_gs-text-paragraph-1" data-gemlang="en" data-exc="">Made using clean, non-toxic ingredients, our products are designed for everyone.</div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666677916382" class="gf_row gf_row-gap-20 gf_equal-height" data-icon="gpicon-row" data-id="1666677916382" data-extraclass="" data-row-gap="20px" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1652082247768" data-id="1652082247768" style="display: flex; flex-direction: column; justify-content: flex-start; min-height: auto;"><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1666677916313" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1666677916313" style=""><div class="module main-slider owl-carousel owl-theme " data-collg="5" data-colmd="4" data-colsm="2" data-colxs="2" data-marginlg="30px" data-marginmd="30px" data-marginsm="24px" data-marginxs="24px" data-dotslg="0" data-dotsmd="0" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="0" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="0"><div class="item"><div data-index="1" class="item-content"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1666677916381" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1666677916381" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="***********" style="">{% assign product = all_products['aboriginal-clothes-womens-polo-shirts-australia-upf-50-kangaroo-dance'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666677916309" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1666677916309" data-row-gap="0px" data-extraclass="" style="display: block; flex-wrap: unset; visibility: visible;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664435722756-child5-5" data-id="1664435722756-child5-5" style="display: block; flex-direction: unset; justify-content: unset; min-height: auto;"><div class="module-wrap" id="m-1666677916372" data-id="1666677916372" data-label="(P) Price" style=""><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1666677916332" data-id="1666677916332" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="1">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '1' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '1'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>- </span>{%if '1'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 0px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div data-label="(P) Variants" data-key="p-variants" data-atomgroup="child-product" id="m-1666678017374" class="module-wrap" data-icon="gpicon-product-swatches" data-ver="1.0" data-id="1666678017374" style=""><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-group="separately" data-style="inline" data-inlinespacing="100px" data-pid="{{product.id}}" data-blankoption="0" data-blankoptiontext="Please select an item in the list">{% unless product.options_with_values.size == 1 and product.variants[0].title == 'Default Title' %}{% assign cols_lg = 12 | divided_by: 1 %}{% assign cols_md = 12 | divided_by: 1 %}{% assign cols_sm = 12 | divided_by: 1 %}{% assign cols_xs = 12 | divided_by: 1 %}{% assign rechargeClass = 'single-option-selector single-option-selector-product' %}{% assign rechargeId = 'SingleOptionSelector-product-' %}{% unless rechargeCount %}{% assign rechargeCount = 0 %}{% endunless %}{% if 'separately' == 'together' %}<div class="gf_variants-wrapper"><select id="{{rechargeId}}{{rechargeCount}}" class="gf_variants {{rechargeClass}}" data-type="separately" style="margin-bottom: 0px;">{% assign current_variant = product.selected_or_first_available_variant %}{% for variant in product.variants %}{% if variant.available %}{% if variant.id == current_variant.id %}<option selected="true" data-sku="{{variant.sku}}" value="{{variant.id}}">{{ variant.title }} - {{ variant.price | money_with_currency }}</option>{% else %}<option data-sku="{{variant.sku}}" value="{{variant.id}}">{{ variant.title }} - {{ variant.price | money_with_currency }}</option>{% endif %}{% else %}<option disabled="disabled">{{ variant.title }} - (Sold Out)</option>{% endif %}{% endfor %}</select></div>{% else %}<div class="gf_variants-wrapper gf_row gf_row_no_tools gf_row-no-padding">{% for option in product.options_with_values %}<div class="gf_column gf_col_no_tools gf_col-lg-{{cols_lg}} gf_col-md-{{cols_md}} gf_col-sm-{{cols_sm}} gf_col-xs-{{cols_xs}} gf_variants-option{{forloop.index}}">{% if 'inline' == 'inline' %}<div class="gf_align-module gf_align-i-center">{% endif %}{% if '1' == '1' %}<label>{{option.name}}</label>{% endif %}<select id="{{rechargeId}}{{rechargeCount}}" class="gf_variants {{rechargeClass}}" data-type="separately" style="margin-bottom: 0px;">{% for value in option.values %}<option value="{{ value | escape }}">{{value}}</option>{% endfor %}</select>{% if 'inline' == 'inline' %}</div>{% endif %}</div>{% assign rechargeCount = rechargeCount | plus: 1 %}{% endfor %}</div>{% endif %}{% endunless %}</div></div><div class="module-wrap" id="m-1666677916290" data-id="1666677916290" data-label="(P) Title" style=""><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div></div></div><div class="module-wrap" id="m-1666677916330" data-id="1666677916330" data-label="(P) Cart Button" style=""><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="1" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1666678135591" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1666678135591" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="***********">{% assign product = all_products['aboriginal-clothes-womens-polo-shirts-australia-upf-50-yalingbila-balgany'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666678135527" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1666678135527" data-row-gap="0px" data-extraclass="" style="display: block; flex-wrap: unset; visibility: visible;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664435722756-child5-5" data-id="1664435722756-child5-5" style="display: block; flex-direction: unset; justify-content: unset; min-height: auto;"><div class="module-wrap" id="m-1666678135651" data-id="1666678135651" data-label="(P) Price" style=""><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1666678135655" data-id="1666678135655" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '1'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>- </span>{%if '1'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 0px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div data-label="(P) Variants" data-key="p-variants" data-atomgroup="child-product" id="m-1666678135603" class="module-wrap" data-icon="gpicon-product-swatches" data-ver="1.0" data-id="1666678135603" style=""><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-group="separately" data-style="inline" data-inlinespacing="100px" data-pid="{{product.id}}" data-blankoption="0" data-blankoptiontext="Please select an item in the list">{% unless product.options_with_values.size == 1 and product.variants[0].title == 'Default Title' %}{% assign cols_lg = 12 | divided_by: 1 %}{% assign cols_md = 12 | divided_by: 1 %}{% assign cols_sm = 12 | divided_by: 1 %}{% assign cols_xs = 12 | divided_by: 1 %}{% assign rechargeClass = 'single-option-selector single-option-selector-product' %}{% assign rechargeId = 'SingleOptionSelector-product-' %}{% unless rechargeCount %}{% assign rechargeCount = 0 %}{% endunless %}{% if 'separately' == 'together' %}<div class="gf_variants-wrapper"><select id="{{rechargeId}}{{rechargeCount}}" class="gf_variants {{rechargeClass}}" data-type="separately" style="margin-bottom: 0px;">{% assign current_variant = product.selected_or_first_available_variant %}{% for variant in product.variants %}{% if variant.available %}{% if variant.id == current_variant.id %}<option selected="true" data-sku="{{variant.sku}}" value="{{variant.id}}">{{ variant.title }} - {{ variant.price | money_with_currency }}</option>{% else %}<option data-sku="{{variant.sku}}" value="{{variant.id}}">{{ variant.title }} - {{ variant.price | money_with_currency }}</option>{% endif %}{% else %}<option disabled="disabled">{{ variant.title }} - (Sold Out)</option>{% endif %}{% endfor %}</select></div>{% else %}<div class="gf_variants-wrapper gf_row gf_row_no_tools gf_row-no-padding">{% for option in product.options_with_values %}<div class="gf_column gf_col_no_tools gf_col-lg-{{cols_lg}} gf_col-md-{{cols_md}} gf_col-sm-{{cols_sm}} gf_col-xs-{{cols_xs}} gf_variants-option{{forloop.index}}">{% if 'inline' == 'inline' %}<div class="gf_align-module gf_align-i-center">{% endif %}{% if '1' == '1' %}<label>{{option.name}}</label>{% endif %}<select id="{{rechargeId}}{{rechargeCount}}" class="gf_variants {{rechargeClass}}" data-type="separately" style="margin-bottom: 0px;">{% for value in option.values %}<option value="{{ value | escape }}">{{value}}</option>{% endfor %}</select>{% if 'inline' == 'inline' %}</div>{% endif %}</div>{% assign rechargeCount = rechargeCount | plus: 1 %}{% endfor %}</div>{% endif %}{% endunless %}</div></div><div class="module-wrap" id="m-1666678135629" data-id="1666678135629" data-label="(P) Title" style=""><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div></div></div><div class="module-wrap" id="m-1666678135584" data-id="1666678135584" data-label="(P) Cart Button" style=""><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="1" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1666678141942" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1666678141942" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="39886777122950" style="">{% assign product = all_products['mother-rpet-recycled-upf50-womens-fitted-polo-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666678141979" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1666678141979" data-row-gap="0px" data-extraclass="" style="display: block; flex-wrap: unset; visibility: visible;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664435722756-child5-5" data-id="1664435722756-child5-5" style="display: block; flex-direction: unset; justify-content: unset; min-height: auto;"><div class="module-wrap" id="m-1666678141901" data-id="1666678141901" data-label="(P) Price" style=""><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1666678141982" data-id="1666678141982" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '1'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>- </span>{%if '1'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 0px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div data-label="(P) Variants" data-key="p-variants" data-atomgroup="child-product" id="m-1666678141909" class="module-wrap" data-icon="gpicon-product-swatches" data-ver="1.0" data-id="1666678141909" style=""><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-group="separately" data-style="inline" data-inlinespacing="100px" data-pid="{{product.id}}" data-blankoption="0" data-blankoptiontext="Please select an item in the list">{% unless product.options_with_values.size == 1 and product.variants[0].title == 'Default Title' %}{% assign cols_lg = 12 | divided_by: 1 %}{% assign cols_md = 12 | divided_by: 1 %}{% assign cols_sm = 12 | divided_by: 1 %}{% assign cols_xs = 12 | divided_by: 1 %}{% assign rechargeClass = 'single-option-selector single-option-selector-product' %}{% assign rechargeId = 'SingleOptionSelector-product-' %}{% unless rechargeCount %}{% assign rechargeCount = 0 %}{% endunless %}{% if 'separately' == 'together' %}<div class="gf_variants-wrapper"><select id="{{rechargeId}}{{rechargeCount}}" class="gf_variants {{rechargeClass}}" data-type="separately" style="margin-bottom: 0px;">{% assign current_variant = product.selected_or_first_available_variant %}{% for variant in product.variants %}{% if variant.available %}{% if variant.id == current_variant.id %}<option selected="true" data-sku="{{variant.sku}}" value="{{variant.id}}">{{ variant.title }} - {{ variant.price | money_with_currency }}</option>{% else %}<option data-sku="{{variant.sku}}" value="{{variant.id}}">{{ variant.title }} - {{ variant.price | money_with_currency }}</option>{% endif %}{% else %}<option disabled="disabled">{{ variant.title }} - (Sold Out)</option>{% endif %}{% endfor %}</select></div>{% else %}<div class="gf_variants-wrapper gf_row gf_row_no_tools gf_row-no-padding">{% for option in product.options_with_values %}<div class="gf_column gf_col_no_tools gf_col-lg-{{cols_lg}} gf_col-md-{{cols_md}} gf_col-sm-{{cols_sm}} gf_col-xs-{{cols_xs}} gf_variants-option{{forloop.index}}">{% if 'inline' == 'inline' %}<div class="gf_align-module gf_align-i-center">{% endif %}{% if '1' == '1' %}<label>{{option.name}}</label>{% endif %}<select id="{{rechargeId}}{{rechargeCount}}" class="gf_variants {{rechargeClass}}" data-type="separately" style="margin-bottom: 0px;">{% for value in option.values %}<option value="{{ value | escape }}">{{value}}</option>{% endfor %}</select>{% if 'inline' == 'inline' %}</div>{% endif %}</div>{% assign rechargeCount = rechargeCount | plus: 1 %}{% endfor %}</div>{% endif %}{% endunless %}</div></div><div class="module-wrap" id="m-1666678142010" data-id="1666678142010" data-label="(P) Title" style=""><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div></div></div><div class="module-wrap" id="m-1666678141991" data-id="1666678141991" data-label="(P) Cart Button" style=""><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="1" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div></div><div class="item"><div data-index="4" class="item-content"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1666678148636" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1666678148636" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="39820550766726" style="">{% assign product = all_products['two-worlds-recycled-upf50-unisex-polo-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666678148641" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1666678148641" data-row-gap="0px" data-extraclass="" style="display: block; flex-wrap: unset; visibility: visible;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664435722756-child5-5" data-id="1664435722756-child5-5" style="display: block; flex-direction: unset; justify-content: unset; min-height: auto;"><div class="module-wrap" id="m-1666678148612" data-id="1666678148612" data-label="(P) Price" style=""><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1666678148699" data-id="1666678148699" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '1'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>- </span>{%if '1'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 0px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div data-label="(P) Variants" data-key="p-variants" data-atomgroup="child-product" id="m-1666678148653" class="module-wrap" data-icon="gpicon-product-swatches" data-ver="1.0" data-id="1666678148653" style=""><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-group="separately" data-style="inline" data-inlinespacing="100px" data-pid="{{product.id}}" data-blankoption="0" data-blankoptiontext="Please select an item in the list">{% unless product.options_with_values.size == 1 and product.variants[0].title == 'Default Title' %}{% assign cols_lg = 12 | divided_by: 1 %}{% assign cols_md = 12 | divided_by: 1 %}{% assign cols_sm = 12 | divided_by: 1 %}{% assign cols_xs = 12 | divided_by: 1 %}{% assign rechargeClass = 'single-option-selector single-option-selector-product' %}{% assign rechargeId = 'SingleOptionSelector-product-' %}{% unless rechargeCount %}{% assign rechargeCount = 0 %}{% endunless %}{% if 'separately' == 'together' %}<div class="gf_variants-wrapper"><select id="{{rechargeId}}{{rechargeCount}}" class="gf_variants {{rechargeClass}}" data-type="separately" style="margin-bottom: 0px;">{% assign current_variant = product.selected_or_first_available_variant %}{% for variant in product.variants %}{% if variant.available %}{% if variant.id == current_variant.id %}<option selected="true" data-sku="{{variant.sku}}" value="{{variant.id}}">{{ variant.title }} - {{ variant.price | money_with_currency }}</option>{% else %}<option data-sku="{{variant.sku}}" value="{{variant.id}}">{{ variant.title }} - {{ variant.price | money_with_currency }}</option>{% endif %}{% else %}<option disabled="disabled">{{ variant.title }} - (Sold Out)</option>{% endif %}{% endfor %}</select></div>{% else %}<div class="gf_variants-wrapper gf_row gf_row_no_tools gf_row-no-padding">{% for option in product.options_with_values %}<div class="gf_column gf_col_no_tools gf_col-lg-{{cols_lg}} gf_col-md-{{cols_md}} gf_col-sm-{{cols_sm}} gf_col-xs-{{cols_xs}} gf_variants-option{{forloop.index}}">{% if 'inline' == 'inline' %}<div class="gf_align-module gf_align-i-center">{% endif %}{% if '1' == '1' %}<label>{{option.name}}</label>{% endif %}<select id="{{rechargeId}}{{rechargeCount}}" class="gf_variants {{rechargeClass}}" data-type="separately" style="margin-bottom: 0px;">{% for value in option.values %}<option value="{{ value | escape }}">{{value}}</option>{% endfor %}</select>{% if 'inline' == 'inline' %}</div>{% endif %}</div>{% assign rechargeCount = rechargeCount | plus: 1 %}{% endfor %}</div>{% endif %}{% endunless %}</div></div><div class="module-wrap" id="m-1666678148592" data-id="1666678148592" data-label="(P) Title" style=""><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div></div></div><div class="module-wrap" id="m-1666678148637" data-id="1666678148637" data-label="(P) Cart Button" style=""><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="1" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div></div><div class="item"><div data-index="5" class="item-content"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1666678500335" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1666678500335" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="39886776533126" style="">{% assign product = all_products['healing-spirits-rpet-recycled-upf50-womens-fitted-polo-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666678500461" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1666678500461" data-row-gap="0px" data-extraclass="" style="display: block; flex-wrap: unset; visibility: visible;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664435722756-child5-5" data-id="1664435722756-child5-5" style="display: block; flex-direction: unset; justify-content: unset; min-height: auto;"><div class="module-wrap" id="m-1666678500431" data-id="1666678500431" data-label="(P) Price" style=""><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1666678500336" data-id="1666678500336" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '1'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>- </span>{%if '1'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 0px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div data-label="(P) Variants" data-key="p-variants" data-atomgroup="child-product" id="m-1666678500444" class="module-wrap" data-icon="gpicon-product-swatches" data-ver="1.0" data-id="1666678500444" style=""><div class="module gf_module-left gf_module-left-lg gf_module--md gf_module--sm gf_module--xs " data-group="separately" data-style="inline" data-inlinespacing="100px" data-pid="{{product.id}}" data-blankoption="0" data-blankoptiontext="Please select an item in the list">{% unless product.options_with_values.size == 1 and product.variants[0].title == 'Default Title' %}{% assign cols_lg = 12 | divided_by: 1 %}{% assign cols_md = 12 | divided_by: 1 %}{% assign cols_sm = 12 | divided_by: 1 %}{% assign cols_xs = 12 | divided_by: 1 %}{% assign rechargeClass = 'single-option-selector single-option-selector-product' %}{% assign rechargeId = 'SingleOptionSelector-product-' %}{% unless rechargeCount %}{% assign rechargeCount = 0 %}{% endunless %}{% if 'separately' == 'together' %}<div class="gf_variants-wrapper"><select id="{{rechargeId}}{{rechargeCount}}" class="gf_variants {{rechargeClass}}" data-type="separately" style="margin-bottom: 0px;">{% assign current_variant = product.selected_or_first_available_variant %}{% for variant in product.variants %}{% if variant.available %}{% if variant.id == current_variant.id %}<option selected="true" data-sku="{{variant.sku}}" value="{{variant.id}}">{{ variant.title }} - {{ variant.price | money_with_currency }}</option>{% else %}<option data-sku="{{variant.sku}}" value="{{variant.id}}">{{ variant.title }} - {{ variant.price | money_with_currency }}</option>{% endif %}{% else %}<option disabled="disabled">{{ variant.title }} - (Sold Out)</option>{% endif %}{% endfor %}</select></div>{% else %}<div class="gf_variants-wrapper gf_row gf_row_no_tools gf_row-no-padding">{% for option in product.options_with_values %}<div class="gf_column gf_col_no_tools gf_col-lg-{{cols_lg}} gf_col-md-{{cols_md}} gf_col-sm-{{cols_sm}} gf_col-xs-{{cols_xs}} gf_variants-option{{forloop.index}}">{% if 'inline' == 'inline' %}<div class="gf_align-module gf_align-i-center">{% endif %}{% if '1' == '1' %}<label>{{option.name}}</label>{% endif %}<select id="{{rechargeId}}{{rechargeCount}}" class="gf_variants {{rechargeClass}}" data-type="separately" style="margin-bottom: 0px;">{% for value in option.values %}<option value="{{ value | escape }}">{{value}}</option>{% endfor %}</select>{% if 'inline' == 'inline' %}</div>{% endif %}</div>{% assign rechargeCount = rechargeCount | plus: 1 %}{% endfor %}</div>{% endif %}{% endunless %}</div></div><div class="module-wrap" id="m-1666678500435" data-id="1666678500435" data-label="(P) Title" style=""><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div></div></div><div class="module-wrap" id="m-1666678500449" data-id="1666678500449" data-label="(P) Cart Button" style=""><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="1" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div></div></div></div></div><!--gfsplit--><script>window.__gemStoreData = {};window.__gemStoreData.gemKeyValid = {};window.__gemStoreData.gemKeyValid.bestWayAddToCart = 1;</script>
</div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		"https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv2herobanner.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/owl.carousel.min.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv3product.js",
		 "https://www.youtube.com/player_api",
		'{{ 'gem-page-83308544134.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->