{% comment %}
	GEMPAGES BUILDER (https://apps.shopify.com/gempages)

	You SHOULD NOT modify source code in this file because
	It is automatically generated from GEMPAGES BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->


<link rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/libs/css/fontawesome-4.6.3.1.min.css" class="gf-style">
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-page-83339182214.css' | asset_url }}" class="gf_page_style">
<link data-instant-track class="gf_fonts" data-fonts="Gochi Hand" href="//fonts.googleapis.com/css2?family=Gochi Hand:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant Garamond" href="//fonts.googleapis.com/css2?family=Cormorant Garamond:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant" href="//fonts.googleapis.com/css2?family=Cormorant:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Bebas Neue" href="//fonts.googleapis.com/css2?family=Bebas Neue:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Anton" href="//fonts.googleapis.com/css2?family=Anton:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Aladin" href="//fonts.googleapis.com/css2?family=Aladin:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Acme" href="//fonts.googleapis.com/css2?family=Acme:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Alfa Slab One" href="//fonts.googleapis.com/css2?family=Alfa Slab One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Allan" href="//fonts.googleapis.com/css2?family=Allan:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Boogaloo" href="//fonts.googleapis.com/css2?family=Boogaloo:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Dancing Script" href="//fonts.googleapis.com/css2?family=Dancing Script:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Albert Sans" href="//fonts.googleapis.com/css2?family=Albert Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track rel="stylesheet" type="text/css" href= "https://d1um8515vdn9kb.cloudfront.net/libs/css/owl.carousel.min.css" class="gf_libs">
<link data-instant-track rel="stylesheet" type="text/css" href= "https://d1um8515vdn9kb.cloudfront.net/files/gfv1animate.min.css" class="gf_libs">
<!--GEM_HEADER_END-->
<!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666067204775" class="gf_row gf_row-fluid gf_row-no-padding gf_equal-height gf_row-gap-15" data-icon="gpicon-row" data-id="1666067204775" data-row-gap="15px" data-extraclass="" style="display: flex; flex-wrap: wrap; visibility: visible; transform: none; z-index: 50;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1651715993972" data-id="1651715993972" style="display: flex; flex-direction: column; justify-content: flex-start; min-height: auto; transform: none; z-index: 50;"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1666067204747" class="module-wrap gf_hero-fixed-mode" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1666067204747" style="display: block;"><div class="module " data-image="https://ucarecdn.com/8ab2284a-133b-4734-a31d-c0d9ccb13ecf/-/format/auto/-/preview/3000x3000/-/quality/lighter/Sheri-5.png" data-image-lg="https://ucarecdn.com/4f1c6b3c-d36b-4536-a7e7-f354034feef9/-/format/auto/-/preview/3000x3000/-/quality/lighter/artboard%20%e2%80%93%201.png" data-image-md="https://ucarecdn.com/a3c286cc-9b07-4203-bafe-75a2d1fdc44a/-/format/auto/-/preview/3000x3000/-/quality/lighter/artboard%20%e2%80%93%201.png" data-image-sm="https://ucarecdn.com/8ab2284a-133b-4734-a31d-c0d9ccb13ecf/-/format/auto/-/preview/3000x3000/-/quality/lighter/sheri-5.png" data-image-xs="https://ucarecdn.com/979d8c8b-9d38-45f1-8c33-205162347ebb/-/format/auto/-/preview/3000x3000/-/quality/lighter/sheri-5.png" data-height="" data-effect="none" data-transition="0.5" data-fixedmode="1"><span data-index="1" class="item-content align-bottom"></span></div><picture class="gf_hero-bg-wrap"><source media="(min-width: 1200px)" srcset="https://ucarecdn.com/4f1c6b3c-d36b-4536-a7e7-f354034feef9/-/format/auto/-/preview/3000x3000/-/quality/lighter/artboard%20%e2%80%93%201.png"><source media="(min-width: 992px)" srcset="https://ucarecdn.com/a3c286cc-9b07-4203-bafe-75a2d1fdc44a/-/format/auto/-/preview/3000x3000/-/quality/lighter/artboard%20%e2%80%93%201.png"><source media="(min-width: 768px)" srcset="https://ucarecdn.com/8ab2284a-133b-4734-a31d-c0d9ccb13ecf/-/format/auto/-/preview/3000x3000/-/quality/lighter/sheri-5.png"><img src="https://ucarecdn.com/979d8c8b-9d38-45f1-8c33-205162347ebb/-/format/auto/-/preview/3000x3000/-/quality/lighter/sheri-5.png" alt=""></picture><div class="gf_hero-overlay" style="background:transparent;opacity:0"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666067204704" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1666067204704" data-extraclass="" style="display: block; transform: none; z-index: 50;" data-row-gap="0px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1651717077587" data-id="1651717077587" style="transform: none; z-index: 50;"><div data-label="Row" id="r-1666076333839" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1666076333839" data-row-gap="0px" data-extraclass=""><div class="gf_col-md-12 gf_column" id="c-1542355586871" data-id="1542355586871" style="min-height: auto;"><div data-label="Row" id="r-1666076333888" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1666076333888" data-row-gap="0px" data-extraclass=""><div class="gf_col-md-12 gf_column" id="c-1542250371092" data-id="1542250371092" style="min-height: auto;"><div data-label="Text Block" id="e-1666076333878" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666076333878"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-xs gf-elm-center-sm" data-gemlang="en" data-exc=""><p>“I began painting as a way to connect with my culture and quickly realised that it was also an incredibly meditative and spiritual experience for me. “</p></div></div></div></div></div></div><div data-label="Row" id="r-1666233937744" class="gf_row gf_equal-height gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1666233937744" data-vivaldi-spatnav-clickable="1" data-extraclass="" data-row-gap="0px" style="display: flex; flex-wrap: wrap; visibility: visible;" data-layout-xs="12+12" data-layout-lg="6+6" data-layout-md="6+6" data-layout-sm="6+6"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1505709938000" data-id="1505709938000" style="min-height: auto; display: flex; flex-direction: column; justify-content: center;" data-extraclass=""><div data-label="Row" id="r-1666233937834" class="gf_row" data-icon="gpicon-row" data-id="1666233937834" data-vivaldi-spatnav-clickable="1" style="min-height: auto;" data-extraclass=""><div class="gf_col-md-12 gf_column" id="c-1505709938077" data-id="1505709938077" data-extraclass="" style="min-height: 1px; display: block; flex-direction: unset; justify-content: unset;"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1666678897090" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666678897090"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><p>ABOUT THE ARTIST</p></div></div><div data-label="Heading" id="e-1666233937784" class="element-wrap" data-icon="gpicon-heading" data-id="1666233937784" data-vivaldi-spatnav-clickable="1"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><h1 class="gf_gs-text-heading-2">SHERI SKELE</h1></div></div><div data-label="Text Block" id="e-1666233937841" class="element-wrap" data-icon="gpicon-textblock" data-id="1666233937841" data-vivaldi-spatnav-clickable="1"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><p style="text-align: inherit!important;">"I'm a proud Bidjara woman and a contemporary Aboriginal artist sharing my culture, experiences and hopes for healing our history through my knowledge and story works. I began painting as a way to connect with my culture and quickly realised that it was also an incredibly meditative and spiritual experience for me.</p><p style="text-align: inherit!important;"><br></p><p style="text-align: inherit!important;">I call my artworks bigi nagala, which means 'I am dreaming' in Bidjara. I feel a deep connection to my Aboriginal heritage, the earth, country and spirit, and feel&nbsp;<span style="font-size: inherit; font-weight: inherit; text-align: inherit; letter-spacing: 0px;">privileged to be able to share my stories and culture with you through my artwork. I hope to make my mob proud of the artworks that I create and knowledge that I share."</span></p><p style="text-align: inherit!important;"></p></div></div></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1666246553845" data-id="1666246553845" style="min-height: auto; display: flex; flex-direction: column; justify-content: center;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666678873214" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666678873214" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/0582ce27-54ff-401d-a76f-ce71ee4b4a73/-/format/auto/-/preview/3000x3000/-/quality/lighter/Mask%20Group%201.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="734" height="641" natural-width="734" natural-height="641"></div></div></div></div><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1666681386166" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1666681386166" style="display: block;"><div class="module main-slider owl-carousel owl-theme " data-collg="1" data-colmd="1" data-colsm="1" data-colxs="1" data-marginlg="0px" data-marginmd="5px" data-marginsm="5px" data-marginxs="5px" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="0" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="1"><div class="item"><div data-index="1" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666681386235" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666681386235" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-sm gf-elm-center-xs gf-elm-center-md gf-elm-center-lg" data-exc=""><a href="https://www.yarn.com.au/collections/stationary?sort=best-selling" target=""><img src="https://ucarecdn.com/6285a857-881b-4c95-b826-a998e1fc854e/-/format/auto/-/preview/3000x3000/-/quality/lighter/Yarn--10.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1650" height="555" natural-width="1650" natural-height="555"></a></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666681386199" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666681386199" data-resolution="3000x3000" style=""><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><a href="https://www.yarn.com.au/collections/stationary?sort=best-selling" target=""><img src="https://ucarecdn.com/c6ac170c-b461-4e89-9e3a-c0c2fbb0b69e/-/format/auto/-/preview/3000x3000/-/quality/lighter/Yarn--2.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1650" height="555" natural-width="1650" natural-height="555"></a></div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666681386269" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666681386269" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><a href="https://www.yarn.com.au/collections/stationary?sort=best-selling" target=""><img src="https://ucarecdn.com/13db2420-f249-42ce-82cd-22f05502a44a/-/format/auto/-/preview/3000x3000/-/quality/lighter/Yarn-01522.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1650" height="555" natural-width="1650" natural-height="555"></a></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div><div data-label="Row" data-key="row" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" id="r-1666244651274" data-icon="gpicon-row" data-id="1666244651274" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1666231698167" data-id="1666231698167"><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1666742971218" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1666742971218"><div class="module main-slider owl-carousel owl-theme " data-collg="3" data-colmd="3" data-colsm="1" data-colxs="1" data-marginlg="5px" data-marginmd="5px" data-marginsm="0px" data-marginxs="5px" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="0" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="0"><div class="item"><div data-index="1" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666742988664" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666742988664" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-sm gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/collections/stationary?sort=best-selling" target=""><img src="https://ucarecdn.com/ea3a085f-ca4a-4a70-bd5b-d956d9b69994/-/format/auto/-/preview/3000x3000/-/quality/lighter/2.png" alt="" class="gf_image" data-gemlang="en" width="750" height="672" data-width="auto" data-height="auto" title="" natural-width="750" natural-height="672"></a></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666743041769" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666743041769" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/3537251e-7dd6-4308-9cab-ed73e5c26fac/-/format/auto/-/preview/3000x3000/-/quality/lighter/Mask%20Group%20151.png" alt="" class="gf_image" data-gemlang="en" data-width="auto" data-height="auto" title="" width="750" height="672" natural-width="750" natural-height="672"></div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666743048138" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666743048138" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-sm gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/collections/stationary?sort=best-selling" target=""><img src="https://ucarecdn.com/c15313ba-948a-4362-9092-4200172c14f0/-/format/auto/-/preview/3000x3000/-/quality/lighter/11.png" alt="" class="gf_image" data-gemlang="en" data-width="auto" data-height="auto" title="" width="750" height="672" natural-width="750" natural-height="672"></a></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1666244651316" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1666244651316"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Shop New Arrivals</h1></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666317351571" class="gf_row" data-icon="gpicon-row" data-id="1666317351571" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1666317351598" data-id="1666317351598"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1666317354353" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666317354353"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><div>We have a range of beautiful stationery and homewares for you to explore.&nbsp;</div></div></div></div></div><div data-label="Product List" data-key="product-list" data-atomgroup="product-list" id="m-1666679443187" class="module-wrap" data-icon="gpicon-product-list" data-ver="1.0" data-id="1666679443187" style="min-height: auto;"><div class="module " data-cid="264615329926" data-chandle="sheri-skele-collection" data-limit="12" data-collg="4" data-colmd="3" data-colsm="3" data-colxs="2"><div class="item-content item-content-no-placeholder"><div class="gf_row gf_row-no-width gf_row_no_tools gf_row-no-padding">{% assign colXsClass = ' gf_col-xs-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colXsClass = colXsClass | append:cols %}{% assign colSmClass = ' gf_col-sm-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colSmClass = colSmClass | append:cols %}{% assign colMdClass = ' gf_col-md-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colMdClass = colMdClass | append:cols %}{% assign colLgClass = ' gf_col-lg-' %}{% assign cols = 12 | divided_by: 4 %}{% assign colLgClass = colLgClass | append:cols %}{% assign colClass = 'gf_column gf_col_no_tools' %}{% assign colClass = colClass | append: colXsClass %}{% assign colClass = colClass | append: colSmClass %}{% assign colClass = colClass | append: colMdClass %}{% assign colClass = colClass | append: colLgClass %}{% assign gPLCount = 1 %}{% paginate collections["sheri-skele-collection"].products by 12 %}{% for product in collections["sheri-skele-collection"].products %}<div class="{{colClass}}" style="padding: 10px !important"><div data-label="Product" data-key="product" id="m-1666679443187-child{{forloop.index}}" data-ver="3.1" class="module-wrap" data-id="1666679443187-child{{forloop.index}}" data-index="1" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="39773803151494" style="">{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1666679443187-child{{forloop.index}}-0" data-id="1666679443187-child{{forloop.index}}-0" data-label="(P) Image" style="display: block;"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="zoom" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'zoom' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1666679443187-child{{forloop.index}}-1" data-id="1666679443187-child{{forloop.index}}-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1666679443187-child{{forloop.index}}-2" data-id="1666679443187-child{{forloop.index}}-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1666679443187-child{{forloop.index}}-3" data-id="1666679443187-child{{forloop.index}}-3" data-label="(P) Cart Button"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div>{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-xs"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-sm"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-md"></div>{% endif %}{% assign modulo = gPLCount | modulo: 4 %}{% if modulo == 0 %}<div class="gf_clearfix visible-lg"></div>{% endif %}{% assign gPLCount = gPLCount | plus: 1 %}{% endfor %}{% endpaginate %}</div></div></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666317413844" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666317413844" style="opacity: 0;"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/artist-sheri-skele" target="" data-scroll-speed="2000" data-exc="" style=""><span>Shop Collection</span></a></div></div><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1666743167742" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1666743167742"><div class="module main-slider owl-carousel owl-theme " data-collg="3" data-colmd="3" data-colsm="1" data-colxs="1" data-marginlg="5px" data-marginmd="5px" data-marginsm="0px" data-marginxs="5px" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="0" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="0"><div class="item"><div data-index="1" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666743167799" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666743167799" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-sm gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/products/adventure-quilt-cover-set?variant=39716379295878" target=""><img src="https://ucarecdn.com/eb0e6440-c49b-4a4b-8197-81ea82520583/-/format/auto/-/preview/3000x3000/-/quality/lighter/BED.png" alt="" class="gf_image" data-gemlang="en" width="750" height="672" data-width="auto" data-height="auto" title="" natural-width="750" natural-height="672"></a></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666743167764" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666743167764" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-sm gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/products/shallows-quilt-cover-set?view=bag-swatches&variant=39************" target=""><img src="https://ucarecdn.com/c9fc53e4-2f6d-4531-8e4d-88c84e87e1ac/-/format/auto/-/preview/3000x3000/-/quality/lighter/BED2.png" alt="" class="gf_image" data-gemlang="en" data-width="auto" data-height="auto" title="" width="750" height="672" natural-width="750" natural-height="672"></a></div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666743167800" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666743167800" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-sm gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/products/fresh-water-quilt-cover-set?variant=39716379459718" target=""><img src="https://ucarecdn.com/e629b578-75ca-420e-a089-8e7659eb57f2/-/format/auto/-/preview/3000x3000/-/quality/lighter/BED3.png" alt="" class="gf_image" data-gemlang="en" data-width="auto" data-height="auto" title="" width="750" height="672" natural-width="750" natural-height="672"></a></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div></div></div><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1666743284615" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1666743284615" style="display: block;"><div class="module main-slider owl-carousel owl-theme " data-collg="1" data-colmd="1" data-colsm="1" data-colxs="1" data-marginlg="0px" data-marginmd="5px" data-marginsm="5px" data-marginxs="5px" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="0" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="1"><div class="item"><div data-index="1" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666743284683" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666743284683" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-sm gf-elm-center-xs gf-elm-center-md gf-elm-center-lg" data-exc=""><a href="https://www.yarn.com.au/products/adventure-quilt-cover-set?variant=39716379295878" target=""><img src="https://ucarecdn.com/0ca73c8c-d0b1-4654-8d43-a663938631af/-/format/auto/-/preview/3000x3000/-/quality/lighter/DSC_9438.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1650" height="555" natural-width="1650" natural-height="555"></a></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666743284618" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666743284618" data-resolution="3000x3000" style=""><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><a href="https://www.yarn.com.au/products/fresh-water-quilt-cover-set?variant=39716379459718" target=""><img src="https://ucarecdn.com/f86e5437-6754-409b-823a-257dd51558c2/-/format/auto/-/preview/3000x3000/-/quality/lighter/DSC_9533.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1650" height="555" natural-width="1650" natural-height="555"></a></div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666743284641" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666743284641" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><a href="https://www.yarn.com.au/products/shallows-quilt-cover-set?view=bag-swatches&variant=39************" target=""><img src="https://ucarecdn.com/e3d5e85a-d382-46ab-8c04-9b544c801fc3/-/format/auto/-/preview/3000x3000/-/quality/lighter/DSC_4003.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1650" height="555" natural-width="1650" natural-height="555"></a></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div><div data-label="Row" data-key="row" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" id="r-1666156880480" data-icon="gpicon-row" data-id="1666156880480" data-row-gap="0px" data-extraclass="" style="display: block;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1666156880546" data-id="1666156880546"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666159372035" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1666159372035" data-extraclass="" data-row-gap="0px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1666159372115" data-id="1666159372115"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1666156894440" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666156894440"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>ON THE BLOG</p></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1666156889842" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1666156889842"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-lg gf-elm-center-xs" data-gemlang="en" data-exc=""><h2 class="gf_gs-text-heading-2">We Talk to Bidjara Contemporary Artist Sheri Skele about her Artistic Journey and NAIDOC collaboration with Yarn</h2><div><br></div></div></div><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666679754397" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666679754397" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/7bfb1fd6-8521-4f5a-8eb5-a216334d7664/-/format/auto/-/preview/3000x3000/-/quality/lighter/Mask%20Group%20145.png" alt="" class="gf_image" data-gemlang="en" width="734" height="641" data-width="80%" data-height="auto" title="" natural-width="734" natural-height="641"></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666157030067" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666157030067" style="opacity: 0;"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-sm="0" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/blogs/yarn-in-the-community/we-talk-to-bidjara-contemporary-artist-sheri-skele-about-her-artistic-journey-and-naidoc-collaboration-with-yarn?_pos=1&_sid=c81247804&_ss=r" target="" data-scroll-speed-sm="2000" data-exc="" data-scroll-speed="2000" style=""><span>READ MORE</span></a></div></div></div></div></div></div><div data-label="Row" data-key="row" class="gf_row gf_row-fluid gf_row-no-padding" id="r-1666746878127" data-icon="gpicon-row" data-id="1666746878127" data-extraclass=""><div class="gf_column gf_col-md-12 gf_col-sm-12 gf_col-xs-12 gf_col-lg-12" id="c-1666746878143" data-id="1666746878143"><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1666744819011" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1666744819011"><div class="module main-slider owl-carousel owl-theme " data-collg="1" data-colmd="1" data-colsm="1" data-colxs="1" data-marginlg="5px" data-marginmd="5px" data-marginsm="5px" data-marginxs="5px" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="0" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="0"><div class="item"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666744843198" class="gf_row gf_equal-height" data-icon="gpicon-row" data-id="1666744843198" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1666744843318" data-id="1666744843318" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666744867760" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666744867760" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/1b6d1955-d6c7-429b-8023-fbaa6ecb8187/-/format/auto/-/preview/3000x3000/-/quality/lighter/Mask%20Group%20107.png" alt="" class="gf_image" data-gemlang="en" width="819" height="788" data-width="100%" data-height="auto" title="" natural-width="819" natural-height="788"></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1666744845679" data-id="1666744845679" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Row" id="r-1666744889561" class="gf_row" data-icon="gpicon-row" data-id="1666744889561" data-vivaldi-spatnav-clickable="1" style="min-height: auto;" data-extraclass=""><div class="gf_col-md-12 gf_column" id="c-1505709938077" data-id="1505709938077" data-extraclass="" style="min-height: 1px; display: block; flex-direction: unset; justify-content: unset;"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1666744889666" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666744889666"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>ABOUT THE ARTWORK</p></div></div><div data-label="Heading" id="e-1666744889603" class="element-wrap" data-icon="gpicon-heading" data-id="1666744889603" data-vivaldi-spatnav-clickable="1"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-exc=""><h1 class="gf_gs-text-heading-2">Pink Sunset</h1></div></div><div data-label="Text Block" id="e-1666744889620" class="element-wrap" data-icon="gpicon-textblock" data-id="1666744889620" data-vivaldi-spatnav-clickable="1"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-exc=""><p style="text-align: inherit!important;">This painting is inspired by the beautiful Queensland coastline and breath-taking pink autumn sunsets. Filled with stars, dreamy palms, campsites and yarning circles, pink sunset will add warmth and personality to any space in your home. - Sheri Skele</p><p style="text-align: inherit!important;"></p></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666746607916" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666746607916"><div class="elm gf-elm-center gf-elm-left-md gf-elm-left-sm gf-elm-center-xs gf-elm-left-lg" data-stretch-lg="0" data-stretch-xs="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/artist-sheri-skele/__tab1:story-pink-sunset" target="" data-scroll-speed="2000" data-exc="" data-scroll-speed-xs="2000"><span>SHOP PRODUCTS USING ARTWORK</span></a></div></div></div></div></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666746309280" class="gf_row gf_equal-height" data-icon="gpicon-row" data-id="1666746309280" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1666744843318" data-id="1666744843318" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666746309253" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666746309253" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/7350b132-3ca9-4246-bd32-33d83a62e85d/-/format/auto/-/preview/3000x3000/-/quality/lighter/adventure.png" alt="" class="gf_image" data-gemlang="en" width="809" height="737" data-width="100%" data-height="auto" title="" natural-width="809" natural-height="737"></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1666744845679" data-id="1666744845679" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Row" id="r-1666746309262" class="gf_row" data-icon="gpicon-row" data-id="1666746309262" data-vivaldi-spatnav-clickable="1" style="min-height: auto;" data-extraclass=""><div class="gf_col-md-12 gf_column" id="c-1505709938077" data-id="1505709938077" data-extraclass="" style="min-height: 1px; display: block; flex-direction: unset; justify-content: unset;"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1666746309282" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666746309282"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>ABOUT THE ARTWORK</p></div></div><div data-label="Heading" id="e-1666746309338" class="element-wrap" data-icon="gpicon-heading" data-id="1666746309338" data-vivaldi-spatnav-clickable="1"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-exc=""><h1 class="gf_gs-text-heading-2">Adventure</h1></div></div><div data-label="Text Block" id="e-1666746309267" class="element-wrap" data-icon="gpicon-textblock" data-id="1666746309267" data-vivaldi-spatnav-clickable="1"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-exc=""><p style="text-align: inherit!important;">Living in Nhulunbuy was such an adventure. We would jump in the ute and head to one of the many waterholes nearby. Along the way we would pass lots of families in the bush often stopping to offer them some freshly caught fish if we had any. The country was so beautiful, full of colour, trees as far as the eye could see, and flies....yep there were a lot of flies. - Sheri Skele</p><p style="text-align: inherit!important;"></p></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666746688602" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666746688602"><div class="elm gf-elm-center gf-elm-left-md gf-elm-left-sm gf-elm-center-xs gf-elm-left-lg" data-stretch-lg="0" data-stretch-xs="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/artist-sheri-skele/__tab1:story-adventure" target="" data-scroll-speed="2000" data-exc="" data-scroll-speed-xs="2000"><span>SHOP PRODUCTS USING ARTWORK</span></a></div></div></div></div></div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666746317532" class="gf_row gf_equal-height" data-icon="gpicon-row" data-id="1666746317532" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1666744843318" data-id="1666744843318" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666746317528" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666746317528" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/bee52743-bac7-4283-8b4f-8c5185596e53/-/format/auto/-/preview/3000x3000/-/quality/lighter/dreamy.png" alt="" class="gf_image" data-gemlang="en" width="809" height="737" data-width="100%" data-height="auto" title="" natural-width="809" natural-height="737"></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1666744845679" data-id="1666744845679" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Row" id="r-1666746317499" class="gf_row" data-icon="gpicon-row" data-id="1666746317499" data-vivaldi-spatnav-clickable="1" style="min-height: auto;" data-extraclass=""><div class="gf_col-md-12 gf_column" id="c-1505709938077" data-id="1505709938077" data-extraclass="" style="min-height: 1px; display: block; flex-direction: unset; justify-content: unset;"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1666746317521" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666746317521"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>ABOUT THE ARTWORK</p></div></div><div data-label="Heading" id="e-1666746317551" class="element-wrap" data-icon="gpicon-heading" data-id="1666746317551" data-vivaldi-spatnav-clickable="1"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-exc=""><h1 class="gf_gs-text-heading-2">Fresh water</h1></div></div><div data-label="Text Block" id="e-1666746317503" class="element-wrap" data-icon="gpicon-textblock" data-id="1666746317503" data-vivaldi-spatnav-clickable="1"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-exc=""><p style="text-align: inherit!important;">This piece represents the fresh water in the wet season. We would often drive for hours before reaching a fresh water hole to take a refreshing dip in. The water is so crystal clear you can see reeds growing on the bottom and fish hiding in them. - Sheri Skele</p><p style="text-align: inherit!important;"></p></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666746748966" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666746748966"><div class="elm gf-elm-center gf-elm-left-md gf-elm-left-sm gf-elm-center-xs gf-elm-left-lg" data-stretch-lg="0" data-stretch-xs="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/artist-sheri-skele/__tab1:story-fresh-water" target="" data-scroll-speed="2000" data-exc="" data-scroll-speed-xs="2000"><span>SHOP PRODUCTS USING ARTWORK</span></a></div></div></div></div></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" class="gf_row gf_row-fluid gf_row-no-padding" id="r-1666681920592" data-icon="gpicon-row" data-id="1666681920592" data-extraclass="" data-layout-lg="9+3" data-layout-md="9+3" data-layout-sm="9+3" data-layout-xs="12+12"><div class="gf_column gf_col-lg-9 gf_col-md-9 gf_col-sm-9 gf_col-xs-12" id="c-1666681920547" data-id="1666681920547"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1666743719805" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666743719805"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p><span style="color: rgb(255, 255, 255);">Shop Sheri Skele Collection</span></p></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-3 gf_col-xs-12" id="c-1666743633982" data-id="1666743633982"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666743644114" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666743644114"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/artist-sheri-skele" target="" data-scroll-speed="2000" data-exc=""><span>Shop now</span></a></div></div></div></div><!--gfsplit--><script>window.__gemStoreData = {};window.__gemStoreData.gemKeyValid = {};window.__gemStoreData.gemKeyValid.bestWayAddToCart = 1;</script></div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		"https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv2herobanner.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/owl.carousel.min.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv3product.js",
		 "https://www.youtube.com/player_api",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv1animate.js",
		'{{ 'gem-page-83339182214.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->