{% comment %}
	GEMPAGE BUILDER (https://apps.shopify.com/gempage)

	You SHOULD NOT modify source code in this page because
	It is automatically generated from GEMPAGE BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->

<link data-instant-track rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.6.3/css/font-awesome.min.css" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-page-83370606726.css' | asset_url }}" class="gf_page_style">
<link data-instant-track class="gf_fonts" data-fonts="Gochi Hand" href="//fonts.googleapis.com/css2?family=Gochi Hand:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant Garamond" href="//fonts.googleapis.com/css2?family=Cormorant Garamond:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant" href="//fonts.googleapis.com/css2?family=Cormorant:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<!--GEM_HEADER_END-->
<!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor">
<div data-label="Row" data-key="row" id="r-1666919135369" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1666919135369" data-extraclass=""><div class="gf_col-lg-12 gf_column" id="c-1606712924501" data-id="1606712924501"><div data-label="Hero Banner" data-key="hero-banner" id="m-1666919135345" class="module-wrap" data-icon="gpicon-herobanner" data-ver="1.0" data-id="1666919135345" style="height: inherit;"><div class="module " data-image="https://ucarecdn.com/5d6ab6ac-0079-4993-a240-6f23c6e146d7/-/format/auto/-/preview/3000x3000/-/quality/lighter/" data-height="inheritpx" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" id="r-1666919135318" class="gf_row" data-icon="gpicon-row" data-id="1666919135318"><div class="gf_col-lg-12 gf_column" id="c-1606702819953" data-id="1606702819953"><div data-label="Text Block" data-key="text-block" id="e-1666919135308" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666919135308"><div class="elm text-edit gf-elm-left gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>Black Friday Early Access</p></div></div><div data-label="Heading" data-key="heading" id="e-1666919135229" class="element-wrap" data-icon="gpicon-heading" data-ver="1" data-id="1666919135229"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h2 class="gf_gs-text-heading-2"><b>All Items up to <font color="#f5cf6d">70% off</font></b></h2><h2 class="gf_gs-text-heading-2"><b>coming soon.</b></h2></div></div><div data-label="Text Block" data-key="text-block" id="e-1666919135241" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666919135241"><div class="elm text-edit gf-elm-left gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg gf_gs-text-paragraph-1" data-gemlang="en" data-exc=""><p>Will be released on December 10, 2020</p></div></div><div data-label="Count Down" data-key="count-down" id="m-1666919135272" class="module-wrap" data-icon="gpicon-countdown" data-ver="1.0" data-id="1666919135272"><div class="module count-down gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-end="2020/12/8 10:53:44" data-week="0" data-weeklabel="Weeks" data-day="1" data-daylabel="Days" data-hour="1" data-hourlabel="Hours" data-minute="1" data-minutelabel="Mins" data-second="1" data-secondlabel="Secs" data-type="standard" data-daily="10h, 30m, 10s" data-evergreen="3h" data-redirect="#" data-cookietime="0" data-evergreenloop="1" data-timezone="UTC+7" data-auto-hide="0"><div class="count-down-wrap"><div class="count-down-inner"><div class="day-left time-left"><span class="num gf_gs-text-paragraph-1">00</span><span class="count-label gf_gs-text-paragraph-1">Days</span></div><div class="hour-left time-left"><span class="num gf_gs-text-paragraph-1">00</span><span class="count-label gf_gs-text-paragraph-1">Hours</span></div><div class="minute-left time-left"><span class="num gf_gs-text-paragraph-1">00</span><span class="count-label gf_gs-text-paragraph-1">Mins</span></div><div class="second-left time-left"><span class="num gf_gs-text-paragraph-1">00</span><span class="count-label gf_gs-text-paragraph-1">Secs</span></div></div></div></div></div><div data-label="Newsletter" data-key="newsletter-form" id="m-1666919135311" class="module-wrap" data-icon="gpicon-newsletter" data-ver="1" data-id="1666919135311"><div class="module" data-success-msg="Thanks for contacting us. We'll get back to you as soon as possible." data-success-color="#81d742" data-error-msg="Can't send email. Please try again later." data-error-color="#dd3333" data-editlink="" data-target="">{% form 'customer' %}{% if form.posted_successfully? %}<p class="note form-success" style="border-color: #81d742; color: #81d742;">Thanks for contacting us. We'll get back to you as soon as possible.</p>{% endif %}{% if form.errors %}<p class="note form-error" style="border-color: #dd3333; color: #dd3333">Can't send email. Please try again later.</p>{% endif %}<div data-index="1" class="item-content"><div id="e-1666919135311-31" class="element-wrap" data-id="1666919135311-31" data-label="Text Block" data-icon="gpicon-textblock" data-ver="1"><div class="elm text-edit gf-elm-left gf-elm-center-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-lg gf_gs-text-paragraph-1" data-exc=""><p>Sign up to receive information on our discounts</p></div></div><div data-label="Klaviyo" data-key="klaviyo-signup-form" data-atomgroup="module" id="m-1666919190374" class="module-wrap" data-icon="gpicon-klaviyo" data-ver="1" data-id="1666919190374" data-compile="false"><div class="module gf_module-left-lg gf_module-left-md gf_module-left-sm gf_module-left-xs "><div class="klaviyo-form-"></div></div></div></div>{% endform %}</div></div></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><script>window.__gemStoreData = {};window.__gemStoreData.gemKeyValid = {};window.__gemStoreData.gemKeyValid.bestWayAddToCart = 1;</script>
</div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		"https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv1countdown.js",
		'{{ 'gem-page-83370606726.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->