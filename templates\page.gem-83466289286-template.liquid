{% comment %}
	GEMPAGES BUILDER (https://apps.shopify.com/gempages)

	You SHOULD NOT modify source code in this file because
	It is automatically generated from GEMPAGES BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->


<link rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/libs/css/fontawesome-4.6.3.1.min.css" class="gf-style">
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-page-83466289286.css' | asset_url }}" class="gf_page_style">
<link data-instant-track class="gf_fonts" data-fonts="Gochi Hand" href="//fonts.googleapis.com/css2?family=Gochi Hand:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant Garamond" href="//fonts.googleapis.com/css2?family=Cormorant Garamond:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant" href="//fonts.googleapis.com/css2?family=Cormorant:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Bebas Neue" href="//fonts.googleapis.com/css2?family=Bebas Neue:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Anton" href="//fonts.googleapis.com/css2?family=Anton:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Allan" href="//fonts.googleapis.com/css2?family=Allan:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Aladin" href="//fonts.googleapis.com/css2?family=Aladin:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Acme" href="//fonts.googleapis.com/css2?family=Acme:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Alfa Slab One" href="//fonts.googleapis.com/css2?family=Alfa Slab One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Boogaloo" href="//fonts.googleapis.com/css2?family=Boogaloo:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track rel="stylesheet" type="text/css" href= "https://d1um8515vdn9kb.cloudfront.net/libs/css/owl.carousel.min.css" class="gf_libs">
<link data-instant-track rel="stylesheet" type="text/css" href= "https://d1um8515vdn9kb.cloudfront.net/libs/js/jquery.magnific-popup/magnific-popup.css" class="gf_libs">
<link data-instant-track rel="stylesheet" type="text/css" href= "https://d1um8515vdn9kb.cloudfront.net/files/gfv1animate.min.css" class="gf_libs">
<!--GEM_HEADER_END-->
<!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666067204775" class="gf_row gf_row-fluid gf_row-no-padding gf_equal-height gf_row-gap-15" data-icon="gpicon-row" data-id="1666067204775" data-row-gap="15px" data-extraclass="" style="display: flex; flex-wrap: wrap; visibility: visible; transform: none; z-index: 50;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1651715993972" data-id="1651715993972" style="display: flex; flex-direction: column; justify-content: flex-start; min-height: auto; transform: none; z-index: 50;"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1666067204747" class="module-wrap gf_hero-fixed-mode" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1666067204747" style="display: block;"><div class="module " data-image="https://ucarecdn.com/fcce3eb3-b203-4074-92c0-8bde6abcb5f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/Group%203234.png" data-image-lg="https://ucarecdn.com/fcce3eb3-b203-4074-92c0-8bde6abcb5f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/group%203234.png" data-image-md="https://ucarecdn.com/a3c286cc-9b07-4203-bafe-75a2d1fdc44a/-/format/auto/-/preview/3000x3000/-/quality/lighter/artboard%20%e2%80%93%201.png" data-image-sm="https://ucarecdn.com/8ab2284a-133b-4734-a31d-c0d9ccb13ecf/-/format/auto/-/preview/3000x3000/-/quality/lighter/sheri-5.png" data-image-xs="https://ucarecdn.com/979d8c8b-9d38-45f1-8c33-205162347ebb/-/format/auto/-/preview/3000x3000/-/quality/lighter/sheri-5.png" data-height="" data-effect="none" data-transition="0.5" data-fixedmode="1"><span data-index="1" class="item-content align-bottom"></span></div><picture class="gf_hero-bg-wrap"><source media="(min-width: 1200px)" srcset="https://ucarecdn.com/fcce3eb3-b203-4074-92c0-8bde6abcb5f8/-/format/auto/-/preview/3000x3000/-/quality/lighter/group%203234.png"><source media="(min-width: 992px)" srcset="https://ucarecdn.com/a3c286cc-9b07-4203-bafe-75a2d1fdc44a/-/format/auto/-/preview/3000x3000/-/quality/lighter/artboard%20%e2%80%93%201.png"><source media="(min-width: 768px)" srcset="https://ucarecdn.com/8ab2284a-133b-4734-a31d-c0d9ccb13ecf/-/format/auto/-/preview/3000x3000/-/quality/lighter/sheri-5.png"><img src="https://ucarecdn.com/979d8c8b-9d38-45f1-8c33-205162347ebb/-/format/auto/-/preview/3000x3000/-/quality/lighter/sheri-5.png" alt=""></picture><div class="gf_hero-overlay" style="background:transparent;opacity:0"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666067204704" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1666067204704" data-extraclass="" style="display: block; transform: none; z-index: 50;" data-row-gap="0px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1651717077587" data-id="1651717077587" style="transform: none; z-index: 50;"><div data-label="Row" id="r-1666076333839" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1666076333839" data-row-gap="0px" data-extraclass=""><div class="gf_col-md-12 gf_column" id="c-1542355586871" data-id="1542355586871" style="min-height: auto;"><div data-label="Row" id="r-1666076333888" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1666076333888" data-row-gap="0px" data-extraclass=""><div class="gf_col-md-12 gf_column" id="c-1542250371092" data-id="1542250371092" style="min-height: auto;"><div data-label="Text Block" id="e-1666076333878" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666076333878"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-xs gf-elm-center-sm" data-gemlang="en" data-exc=""><p>"I would find a lot of inspiration from my Father & Great Uncles who are all established artists & have painted giant murals, for me as a young boy witnessing it first hand was special & a lot of my style I owe to them."</p></div></div></div></div></div></div><div data-label="Row" id="r-1666233937744" class="gf_row gf_equal-height gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1666233937744" data-vivaldi-spatnav-clickable="1" data-extraclass="" data-row-gap="0px" style="display: flex; flex-wrap: wrap; visibility: visible;" data-layout-xs="12+12" data-layout-lg="6+6" data-layout-md="6+6" data-layout-sm="6+6"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1505709938000" data-id="1505709938000" style="min-height: auto; display: flex; flex-direction: column; justify-content: center;" data-extraclass=""><div data-label="Row" id="r-1666233937834" class="gf_row" data-icon="gpicon-row" data-id="1666233937834" data-vivaldi-spatnav-clickable="1" style="min-height: auto;" data-extraclass=""><div class="gf_col-md-12 gf_column" id="c-1505709938077" data-id="1505709938077" data-extraclass="" style="min-height: 1px; display: block; flex-direction: unset; justify-content: unset;"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1666678897090" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666678897090"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><p>ABOUT THE ARTIST</p></div></div><div data-label="Heading" id="e-1666233937784" class="element-wrap" data-icon="gpicon-heading" data-id="1666233937784" data-vivaldi-spatnav-clickable="1"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><h1 class="gf_gs-text-heading-2">NAT CHAPMAN</h1></div></div><div data-label="Text Block" id="e-1666233937841" class="element-wrap" data-icon="gpicon-textblock" data-id="1666233937841" data-vivaldi-spatnav-clickable="1"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><p style="text-align: inherit!important;">Proud Goenpul/Yuggera Man of North Stradbroke Island / Brisbane City. Also hailing from the ‘Wambia’ Tribe on the outskirts of the Northern Territory, the 'Brunette/Chapman families' is where my artistic flair descends from.</p><p style="text-align: inherit!important;"><br></p><p style="text-align: inherit!important;">As a young Indigenous man I have had the privilege to travel to multiple countries devotedly expressing my cultural heritage through song & dance. My skin name is ‘BILLEN’ which means the Parrot & I now have the privilege to express myself through my Art.</p><p style="text-align: inherit!important;"></p><p style="text-align: inherit!important;"></p></div></div></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1666246553845" data-id="1666246553845" style="min-height: auto; display: flex; flex-direction: column; justify-content: center;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666678873214" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666678873214" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/201e3120-75b4-4195-ae2e-d068269c4133/-/format/auto/-/preview/3000x3000/-/quality/lighter/Mask%20Group%20116.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1210" height="1057" natural-width="1210" natural-height="1057"></div></div></div></div><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1666681386166" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1666681386166" style="display: block;"><div class="module main-slider owl-carousel owl-theme " data-collg="1" data-colmd="1" data-colsm="1" data-colxs="1" data-marginlg="0px" data-marginmd="5px" data-marginsm="5px" data-marginxs="5px" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="1" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="1"><div class="item"><div data-index="1" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666681386235" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666681386235" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-sm gf-elm-center-xs gf-elm-center-md gf-elm-center-lg" data-exc=""><a href="https://www.yarn.com.au/collections/stationary?sort=best-selling" target=""><img src="https://ucarecdn.com/ad0c85f1-7626-4339-97a5-69e57ecd52d3/-/format/auto/-/preview/3000x3000/-/quality/lighter/Custodian_Streetwear-234.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1650" height="803" natural-width="1650" natural-height="803"></a></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666681386199" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666681386199" data-resolution="3000x3000" style=""><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><a href="https://www.yarn.com.au/collections/stationary?sort=best-selling" target=""><img src="https://ucarecdn.com/f1bb489a-bc5b-4556-86dd-f886ae60589b/-/format/auto/-/preview/3000x3000/-/quality/lighter/DSC04986.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1650" height="771" natural-width="1650" natural-height="771"></a></div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666681386269" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666681386269" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/collections/stationary?sort=best-selling" target=""><img src="https://ucarecdn.com/cfbec366-3d63-4b08-8480-4e203db7d43d/-/format/auto/-/preview/3000x3000/-/quality/lighter/DSC08267-2.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1650" height="803" natural-width="1650" natural-height="803"></a></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div><div data-label="Row" data-key="row" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" id="r-1666244651274" data-icon="gpicon-row" data-id="1666244651274" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1666231698167" data-id="1666231698167"><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1666742971218" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1666742971218"><div class="module main-slider owl-carousel owl-theme " data-collg="3" data-colmd="3" data-colsm="1" data-colxs="1" data-marginlg="5px" data-marginmd="5px" data-marginsm="0px" data-marginxs="5px" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="0" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="0"><div class="item"><div data-index="1" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666742988664" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666742988664" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-sm gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/collections/stationary?sort=best-selling" target=""><img src="https://ucarecdn.com/ea3a085f-ca4a-4a70-bd5b-d956d9b69994/-/format/auto/-/preview/3000x3000/-/quality/lighter/2.png" alt="" class="gf_image" data-gemlang="en" width="750" height="672" data-width="auto" data-height="auto" title="" natural-width="750" natural-height="672"></a></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666743041769" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666743041769" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/3537251e-7dd6-4308-9cab-ed73e5c26fac/-/format/auto/-/preview/3000x3000/-/quality/lighter/Mask%20Group%20151.png" alt="" class="gf_image" data-gemlang="en" data-width="auto" data-height="auto" title="" width="750" height="672" natural-width="750" natural-height="672"></div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666743048138" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666743048138" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-sm gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/collections/stationary?sort=best-selling" target=""><img src="https://ucarecdn.com/c15313ba-948a-4362-9092-4200172c14f0/-/format/auto/-/preview/3000x3000/-/quality/lighter/11.png" alt="" class="gf_image" data-gemlang="en" data-width="auto" data-height="auto" title="" width="750" height="672" natural-width="750" natural-height="672"></a></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1666244651316" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1666244651316"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Shop New Arrivals</h1></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666317351571" class="gf_row" data-icon="gpicon-row" data-id="1666317351571" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1666317351598" data-id="1666317351598"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1666317354353" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666317354353"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><div>We have a range of beautiful stationery and homewares for you to explore.&nbsp;</div></div></div></div></div><div data-label="Product List" data-key="product-list" data-atomgroup="product-list" id="m-1666679443187" class="module-wrap" data-icon="gpicon-product-list" data-ver="1.0" data-id="1666679443187" style="min-height: auto;"><div class="module " data-cid="262528270470" data-chandle="artist-nathaniel-chapman" data-limit="12" data-collg="4" data-colmd="3" data-colsm="3" data-colxs="2"><div class="item-content item-content-no-placeholder"><div class="gf_row gf_row-no-width gf_row_no_tools gf_row-no-padding">{% assign colXsClass = ' gf_col-xs-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colXsClass = colXsClass | append:cols %}{% assign colSmClass = ' gf_col-sm-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colSmClass = colSmClass | append:cols %}{% assign colMdClass = ' gf_col-md-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colMdClass = colMdClass | append:cols %}{% assign colLgClass = ' gf_col-lg-' %}{% assign cols = 12 | divided_by: 4 %}{% assign colLgClass = colLgClass | append:cols %}{% assign colClass = 'gf_column gf_col_no_tools' %}{% assign colClass = colClass | append: colXsClass %}{% assign colClass = colClass | append: colSmClass %}{% assign colClass = colClass | append: colMdClass %}{% assign colClass = colClass | append: colLgClass %}{% assign gPLCount = 1 %}{% paginate collections["artist-nathaniel-chapman"].products by 12 %}{% for product in collections["artist-nathaniel-chapman"].products %}<div class="{{colClass}}" style="padding: 10px !important"><div data-label="Product" data-key="product" id="m-1666679443187-child{{forloop.index}}" data-ver="3.1" class="module-wrap" data-id="1666679443187-child{{forloop.index}}" data-index="1" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="40000427655302" style="">{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1666679443187-child{{forloop.index}}-0" data-id="1666679443187-child{{forloop.index}}-0" data-label="(P) Image" style="display: block;"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="zoom" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'zoom' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div data-label="(P) Swatches" data-key="p-swatches" data-atomgroup="child-product" id="m-1666679443187-child{{forloop.index}}-4" class="module-wrap" data-icon="gpicon-product-swatches" data-ver="1.0" data-id="1666679443187-child{{forloop.index}}-4"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-group="{{group}}" data-swatch-text="1" data-pid="{{product.id}}" data-swatcher-hide="All" data-swatcher-hideother="" data-none-option="{{noneOption}}" data-soldout="1" data-soldout-style="opacity" data-soldout-color="#000000" data-soldout-logic="1" data-background="e30=">{% unless product.variants.size == 1 and product.variants[0].title == 'Default Title' %}{% assign columnClass = 'gf_column gf_col_no_tools gf_col-md-12' %}<div class="gf_swatches gf_row-no-padding gf_row gf_row_no_tools" data-type="{{group}}">{% for option in product.options_with_values %}<div class="{{columnClass}} gf_swatches-selector gf_swatches-option{{forloop.index}}" data-name="{{option.name}}">{% if '1' == '1' %}<label>{{option.name}}</label>{% endif %}{% for value in option.values %}{% if forloop.index == 1 %}{% assign selectedClass = 'gf_selected' %}{% else %}{% assign selectedClass = '' %}{% endif %}<span class="gf_swatch {{selectedClass}}" data-group="{{group}}" data-price="0" style="margin-bottom: 10px;margin-right: 10px" data-value="{{value | escape}}"><span>{{value}}</span></span>{% endfor %}</div>{% endfor %}</div>{% endunless %}<code class="gf_swatches-data" style="display: none!important;">{{swatcher}}</code></div></div><div class="module-wrap" id="m-1666679443187-child{{forloop.index}}-1" data-id="1666679443187-child{{forloop.index}}-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1666679443187-child{{forloop.index}}-2" data-id="1666679443187-child{{forloop.index}}-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1666679443187-child{{forloop.index}}-3" data-id="1666679443187-child{{forloop.index}}-3" data-label="(P) Cart Button"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="1" data-cbto="custom" data-editlink="" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div>{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-xs"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-sm"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-md"></div>{% endif %}{% assign modulo = gPLCount | modulo: 4 %}{% if modulo == 0 %}<div class="gf_clearfix visible-lg"></div>{% endif %}{% assign gPLCount = gPLCount | plus: 1 %}{% endfor %}{% endpaginate %}</div></div></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666317413844" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666317413844" style="opacity: 0;"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/artist-nathaniel-chapman" target="" data-scroll-speed="2000" data-exc="" style=""><span>Shop Nat's Collection's</span></a></div></div><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1666743167742" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1666743167742"><div class="module main-slider owl-carousel owl-theme " data-collg="3" data-colmd="3" data-colsm="1" data-colxs="1" data-marginlg="5px" data-marginmd="5px" data-marginsm="0px" data-marginxs="5px" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="0" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="0"><div class="item"><div data-index="1" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666743167799" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666743167799" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-sm gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/products/adventure-quilt-cover-set?variant=39716379295878" target=""><img src="https://ucarecdn.com/eb0e6440-c49b-4a4b-8197-81ea82520583/-/format/auto/-/preview/3000x3000/-/quality/lighter/BED.png" alt="" class="gf_image" data-gemlang="en" width="750" height="672" data-width="auto" data-height="auto" title="" natural-width="750" natural-height="672"></a></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666743167764" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666743167764" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-sm gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/products/shallows-quilt-cover-set?view=bag-swatches&variant=39716379132038" target=""><img src="https://ucarecdn.com/c9fc53e4-2f6d-4531-8e4d-88c84e87e1ac/-/format/auto/-/preview/3000x3000/-/quality/lighter/BED2.png" alt="" class="gf_image" data-gemlang="en" data-width="auto" data-height="auto" title="" width="750" height="672" natural-width="750" natural-height="672"></a></div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666743167800" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666743167800" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-sm gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/products/fresh-water-quilt-cover-set?variant=39716379459718" target=""><img src="https://ucarecdn.com/e629b578-75ca-420e-a089-8e7659eb57f2/-/format/auto/-/preview/3000x3000/-/quality/lighter/BED3.png" alt="" class="gf_image" data-gemlang="en" data-width="auto" data-height="auto" title="" width="750" height="672" natural-width="750" natural-height="672"></a></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div></div></div><div data-label="Row" data-key="row" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" id="r-1666156880480" data-icon="gpicon-row" data-id="1666156880480" data-row-gap="0px" data-extraclass="" style="display: block;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1666156880546" data-id="1666156880546"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666159372035" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1666159372035" data-extraclass="" data-row-gap="0px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1666159372115" data-id="1666159372115"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1666156894440" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666156894440"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-gemlang="en" data-exc=""><p>ON THE BLOG</p></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1666156889842" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1666156889842"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-gemlang="en" data-exc=""><h2 class="gf_gs-text-heading-2">An Interview with Nathaniel Chapman</h2><div><br></div><div></div></div></div><div data-label="Video Popup" data-key="video-popup" data-atomgroup="module" id="m-1668125913676" class="module-wrap" data-icon="gpicon-videopopup" data-ver="1" data-id="1668125913676"><div class="module gf_module-center"><a class="video-popup " href="https://youtu.be/BAm6y3n_08Q" target="_blank"><img src="https://ucarecdn.com/c9feee21-2117-4615-9d1c-886b9639ab50/-/format/auto/-/preview/3000x3000/-/quality/lighter/fa221e.jpg" style="width: 70%; height: auto;" alt="Video"></a></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666157030067" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666157030067" style="opacity: 0;"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-sm="0" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/blogs/yarn-in-the-community/we-talk-to-bidjara-contemporary-artist-sheri-skele-about-her-artistic-journey-and-naidoc-collaboration-with-yarn?_pos=1&_sid=c81247804&_ss=r" target="" data-scroll-speed-sm="2000" data-exc="" data-scroll-speed="2000" style=""><span>READ MORE AT OUR BLOG</span></a></div></div></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1668127254236" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1668127254236" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1668127254262" data-id="1668127254262"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1668127188706" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1668127188706"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">NAT HAS OVER 20+ ARTWORKS</h1></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1668127241708" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1668127241708"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-lg="0" data-stretch-xs="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/pages/artist-nathaniel-chapman" target="" data-scroll-speed="2000" data-exc="" data-scroll-speed-xs="2000"><span>EXPLORE ALL HIS ARTWORKS here</span></a></div></div></div></div><div data-label="Row" data-key="row" class="gf_row gf_row-fluid gf_row-no-padding" id="r-1666746878127" data-icon="gpicon-row" data-id="1666746878127" data-extraclass="" style="display: block;"><div class="gf_column gf_col-md-12 gf_col-sm-12 gf_col-xs-12 gf_col-lg-12" id="c-1666746878143" data-id="1666746878143"><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1666744819011" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1666744819011"><div class="module main-slider owl-carousel owl-theme " data-collg="1" data-colmd="1" data-colsm="1" data-colxs="1" data-marginlg="5px" data-marginmd="5px" data-marginsm="5px" data-marginxs="5px" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="0" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="0"><div class="item"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666744843198" class="gf_row gf_equal-height" data-icon="gpicon-row" data-id="1666744843198" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1666744843318" data-id="1666744843318" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1666744867760" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1666744867760" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/18f51af7-b1bc-4de4-86fb-24e1d3b92236/-/format/auto/-/preview/3000x3000/-/quality/lighter/Sea%20Eagle-Nat.png" alt="" class="gf_image" data-gemlang="en" width="1977" height="2520" data-width="60%" data-height="auto" title="" natural-width="1977" natural-height="2520"></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1666744845679" data-id="1666744845679" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Row" id="r-1666744889561" class="gf_row" data-icon="gpicon-row" data-id="1666744889561" data-vivaldi-spatnav-clickable="1" style="min-height: auto;" data-extraclass=""><div class="gf_col-md-12 gf_column" id="c-1505709938077" data-id="1505709938077" data-extraclass="" style="min-height: 1px; display: block; flex-direction: unset; justify-content: unset;"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1666744889666" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666744889666"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>ABOUT THE ARTWORK</p></div></div><div data-label="Heading" id="e-1666744889603" class="element-wrap" data-icon="gpicon-heading" data-id="1666744889603" data-vivaldi-spatnav-clickable="1"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-exc=""><h1 class="gf_gs-text-heading-2">MIBBIN - SEA EAGLE</h1></div></div><div data-label="Text Block" id="e-1666744889620" class="element-wrap" data-icon="gpicon-textblock" data-id="1666744889620" data-vivaldi-spatnav-clickable="1"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-exc=""><p style="text-align: inherit!important;">The Sea Eagle soaring high above Amity Point tells a thousand year old story, helping visitors learn about the ancestral connections between Quandamooka people and the land.&nbsp;</p><p style="text-align: inherit!important;"></p></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666746607916" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666746607916"><div class="elm gf-elm-center gf-elm-left-md gf-elm-left-sm gf-elm-center-xs gf-elm-left-lg" data-stretch-lg="0" data-stretch-xs="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/products/aboriginal-clothes-crew-neck-mens-cotton-black-printed-t-shirts-oc?variant=34959458173062" target="" data-scroll-speed="2000" data-exc="" data-scroll-speed-xs="2000"><span>SHOP PRODUCTS USING ARTWORK</span></a></div></div></div></div></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1668126954353" class="gf_row gf_equal-height" data-icon="gpicon-row" data-id="1668126954353" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1666744843318" data-id="1666744843318" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1668126954478" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1668126954478" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/1cffb29d-d147-4738-b347-db585dc7d8bc/-/format/auto/-/preview/3000x3000/-/quality/lighter/jimboi-04.png" alt="" class="gf_image" data-gemlang="en" width="225" height="311" data-width="60%" data-height="auto" title="" natural-width="225" natural-height="311"></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1666744845679" data-id="1666744845679" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Row" id="r-1668126954381" class="gf_row" data-icon="gpicon-row" data-id="1668126954381" data-vivaldi-spatnav-clickable="1" style="min-height: auto;" data-extraclass=""><div class="gf_col-md-12 gf_column" id="c-1505709938077" data-id="1505709938077" data-extraclass="" style="min-height: 1px; display: block; flex-direction: unset; justify-content: unset;"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1668126954416" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1668126954416"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>ABOUT THE ARTWORK</p></div></div><div data-label="Heading" id="e-1668126954400" class="element-wrap" data-icon="gpicon-heading" data-id="1668126954400" data-vivaldi-spatnav-clickable="1"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-exc=""><h1 class="gf_gs-text-heading-2">TROPIC EAGLE</h1></div></div><div data-label="Text Block" id="e-1668126954429" class="element-wrap" data-icon="gpicon-textblock" data-id="1668126954429" data-vivaldi-spatnav-clickable="1"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-exc=""><p style="text-align: inherit!important;">The Wedge Tail Eagle hunts a long the Coast line telling us when the shoals of fish are migrating into the bay.</p><p style="text-align: inherit!important;"><br></p><p style="text-align: inherit!important;">This Artwork is has two other unique artworks of the same style check them out today!</p><p style="text-align: inherit!important;"></p></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1668126954463" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1668126954463"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-stretch-lg="0" data-stretch-xs="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/pages/homelands" target="" data-scroll-speed="2000" data-exc="" data-scroll-speed-xs="2000"><span>SHOP THE HOMELANDS RANGE</span></a></div></div></div></div></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" class="gf_row gf_row-fluid gf_row-no-padding" id="r-1666681920592" data-icon="gpicon-row" data-id="1666681920592" data-extraclass="" data-layout-lg="9+3" data-layout-md="9+3" data-layout-sm="9+3" data-layout-xs="12+12"><div class="gf_column gf_col-lg-9 gf_col-md-9 gf_col-sm-9 gf_col-xs-12" id="c-1666681920547" data-id="1666681920547"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1668127140221" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1668127140221"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p><span style="color: rgb(255, 255, 255);">Shop Nat's Collection Today!</span></p></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-3 gf_col-xs-12" id="c-1666743633982" data-id="1666743633982"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666743644114" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666743644114"><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/artist-nathaniel-chapman" target="" data-scroll-speed="2000" data-exc=""><span>Shop now</span></a></div></div></div></div><!--gfsplit--><script>window.__gemStoreData = {};window.__gemStoreData.gemKeyValid = {};window.__gemStoreData.gemKeyValid.bestWayAddToCart = 1;</script></div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		"https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv2herobanner.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/owl.carousel.min.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv3product.js",
		 "https://www.youtube.com/player_api",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/jquery.magnific-popup/jquery.magnific-popup.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv1animate.js",
		'{{ 'gem-page-83466289286.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->