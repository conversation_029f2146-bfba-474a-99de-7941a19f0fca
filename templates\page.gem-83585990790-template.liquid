{% comment %}
	GEMPAGES BUILDER (https://apps.shopify.com/gempages)

	You SHOULD NOT modify source code in this file because
	It is automatically generated from GEMPAGES BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->


<link rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/libs/css/fontawesome-4.6.3.1.min.css" class="gf-style">
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-page-83585990790.css' | asset_url }}" class="gf_page_style">
<link data-instant-track class="gf_fonts" data-fonts="Anton" href="//fonts.googleapis.com/css2?family=Anton:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Aladin" href="//fonts.googleapis.com/css2?family=Aladin:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Acme" href="//fonts.googleapis.com/css2?family=Acme:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Alfa Slab One" href="//fonts.googleapis.com/css2?family=Alfa Slab One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Allan" href="//fonts.googleapis.com/css2?family=Allan:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Boogaloo" href="//fonts.googleapis.com/css2?family=Boogaloo:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Dancing Script" href="//fonts.googleapis.com/css2?family=Dancing Script:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track rel="stylesheet" type="text/css" href= "https://d1um8515vdn9kb.cloudfront.net/libs/css/owl.carousel.min.css" class="gf_libs">
<link data-instant-track rel="stylesheet" type="text/css" href= "https://d1um8515vdn9kb.cloudfront.net/files/gfv1animate.min.css" class="gf_libs">
<!--GEM_HEADER_END-->
<!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666067204775" class="gf_row gf_row-fluid gf_row-no-padding gf_equal-height gf_row-gap-15" data-icon="gpicon-row" data-id="1666067204775" data-row-gap="15px" data-extraclass="" style="display: flex; flex-wrap: wrap; visibility: visible; transform: none; z-index: 50;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1651715993972" data-id="1651715993972" style="display: flex; flex-direction: column; justify-content: flex-start; min-height: auto; transform: none; z-index: 50;"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1666067204747" class="module-wrap gf_hero-fixed-mode" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1666067204747"><div class="module " data-image="https://ucarecdn.com/4fb859b5-f94f-4978-91c0-12a6e71074e9/-/format/auto/-/preview/3000x3000/-/quality/lighter/laptp.jpg" data-image-lg="https://ucarecdn.com/4fb859b5-f94f-4978-91c0-12a6e71074e9/-/format/auto/-/preview/3000x3000/-/quality/lighter/laptp.jpg" data-image-md="https://ucarecdn.com/ec5894ea-0231-4e75-86e7-476d98ac3d67/-/format/auto/-/preview/3000x3000/-/quality/lighter/laptp.jpg" data-image-sm="https://ucarecdn.com/cb06e254-f6a2-4c6f-a0e3-9efe2c30adbc/-/format/auto/-/preview/3000x3000/-/quality/lighter/tablet.jpg" data-image-xs="https://ucarecdn.com/0207d17c-ee0b-483c-a9e2-15abeabcc027/-/format/auto/-/preview/3000x3000/-/quality/lighter/untitled-4.jpg" data-height="" data-effect="none" data-transition="0.5" data-fixedmode="1"><span data-index="1" class="item-content align-bottom"></span></div><picture class="gf_hero-bg-wrap"><source media="(min-width: 1200px)" srcset="https://ucarecdn.com/4fb859b5-f94f-4978-91c0-12a6e71074e9/-/format/auto/-/preview/3000x3000/-/quality/lighter/laptp.jpg"><source media="(min-width: 992px)" srcset="https://ucarecdn.com/ec5894ea-0231-4e75-86e7-476d98ac3d67/-/format/auto/-/preview/3000x3000/-/quality/lighter/laptp.jpg"><source media="(min-width: 768px)" srcset="https://ucarecdn.com/cb06e254-f6a2-4c6f-a0e3-9efe2c30adbc/-/format/auto/-/preview/3000x3000/-/quality/lighter/tablet.jpg"><img src="https://ucarecdn.com/0207d17c-ee0b-483c-a9e2-15abeabcc027/-/format/auto/-/preview/3000x3000/-/quality/lighter/untitled-4.jpg" alt=""></picture><div class="gf_hero-overlay" style="background:transparent;opacity:0"></div><a class="hero-link" href="https://www.yarn.com.au/collections/reel-warriors-range" target="">&nbsp;</a></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666067204704" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1666067204704" data-extraclass="" style="display: block; transform: none; z-index: 50;" data-row-gap="0px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1651717077587" data-id="1651717077587" style="transform: none; z-index: 50;"><div data-label="Row" id="r-1666076333839" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1666076333839" data-row-gap="0px" data-extraclass=""><div class="gf_col-md-12 gf_column" id="c-1542355586871" data-id="1542355586871" style="min-height: auto;"><div data-label="Row" id="r-1666076333888" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1666076333888" data-row-gap="0px" data-extraclass=""><div class="gf_col-md-12 gf_column" id="c-1542250371092" data-id="1542250371092" style="min-height: auto;"><div data-label="Text Block" id="e-1666076333878" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666076333878"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-sm gf-elm-center-lg gf-elm-center-md gf-elm-center-xs" data-gemlang="en" data-exc=""><p><span style="font-family: inherit; font-size: inherit; font-weight: inherit; text-align: inherit; letter-spacing: 0px;">Following the summer, the tide rises and brings us Nat Chapman’s latest release in collab with Yarn: Reel Warriors! Reel Warriors is inspired by Australian First Nations people from ocean Country and their cultural fishing practices. Each Reel Warrior polo offers a unique colour and design, representing the fierce fishermen who came before.&nbsp;</span><br></p></div></div></div></div></div></div><div data-label="Row" id="r-1666233937744" class="gf_row gf_row-gap-0 gf_equal-height" data-icon="gpicon-row" data-id="1666233937744" data-vivaldi-spatnav-clickable="1" data-extraclass="" data-row-gap="0px" style="display: flex; flex-wrap: wrap; visibility: visible;" data-layout-xs="12+12" data-layout-lg="6+6" data-layout-md="6+6" data-layout-sm="6+6"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1505709938000" data-id="1505709938000" style="min-height: auto; display: flex; flex-direction: column; justify-content: center;" data-extraclass=""><div data-label="Row" id="r-1666233937834" class="gf_row" data-icon="gpicon-row" data-id="1666233937834" data-vivaldi-spatnav-clickable="1" style="min-height: auto;" data-extraclass=""><div class="gf_col-md-12 gf_column" id="c-1505709938077" data-id="1505709938077" data-extraclass="" style="min-height: 1px; display: block; flex-direction: unset; justify-content: unset;"><div data-label="Heading" id="e-1666233937784" class="element-wrap" data-icon="gpicon-heading" data-id="1666233937784" data-vivaldi-spatnav-clickable="1"><div class="elm text-edit gf-elm-center gf-elm-center-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm" data-exc=""><h1 class="gf_gs-text-heading-2">ABOUT THE ARTIST&nbsp;</h1><h1 class="gf_gs-text-heading-2"><span style="color: rgb(246, 179, 4);">NATHANIEL CHAPMAN</span></h1></div></div><div data-label="Text Block" id="e-1666233937841" class="element-wrap" data-icon="gpicon-textblock" data-id="1666233937841" data-vivaldi-spatnav-clickable="1"><div class="elm text-edit gf_gs-text-paragraph-1 gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><p style="text-align: inherit!important;">" I am a Proud Goenpul/Yuggera Man of North Stradbroke Island / Brisbane City. Also hailing from the ‘Wambia’ Tribe on the outskirts of the Northern Territory, the 'Brunette/Chapman families' is where my artistic flair descends from.&nbsp;<span style="font-size: inherit; font-weight: inherit; text-align: inherit; letter-spacing: 0px; background-color: transparent;">As a young Indigenous man I have had the privilege to travel to multiple countries devotedly expressing my cultural heritage through song & dance. My skin name is ‘BILLEN’ which means the Parrot & I now have the privilege to express myself through my Art.</span></p><p style="text-align: inherit!important;"><span style="font-size: inherit; font-weight: inherit; text-align: inherit; letter-spacing: 0px; background-color: transparent;"><br></span></p><p style="text-align: inherit!important;"><span style="font-size: inherit; font-weight: inherit; text-align: inherit; letter-spacing: 0px; background-color: transparent;">All 4 of my designs depict the First Nation connection with Australia’s salt water environments and its marine life. The way we love & respect our country goes hand in hand with our coast lines & oceans. We have been sustained by these environments & their marine life for thousands of years.&nbsp;</span></p><p style="text-align: inherit!important;"></p><p style="text-align: inherit!important;"></p></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666233937793" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666233937793" style="opacity: 0;"><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/reel-warriors-range" target="" data-scroll-speed="2000" data-exc="" style=""><span>EXPLORE COLLECTION</span></a></div></div></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1666246553845" data-id="1666246553845" style="min-height: auto; display: flex; flex-direction: column; justify-content: center;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1669776507470" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1669776507470" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/a176714d-070e-4a99-b4fa-ab81ed9710a5/-/format/auto/-/preview/3000x3000/-/quality/lighter/Untitled-6.png" alt="" class="gf_image" data-gemlang="en" width="1080" height="1080" data-width="100%" data-height="auto" title="" natural-width="1080" natural-height="1080"></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666067204873" class="gf_row gf_row-fluid gf_row-no-padding gf_equal-height gf_row-gap-0" data-icon="gpicon-row" data-id="1666067204873" data-extraclass="" style="display: flex; flex-wrap: wrap; visibility: visible;" data-row-gap="0px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1651732980241" data-id="1651732980241" style="display: flex; flex-direction: column; justify-content: flex-start; min-height: auto;"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1669788024212" class="gf_row" data-icon="gpicon-row" data-id="1669788024212" data-extraclass="" style="display: block; flex-wrap: unset; visibility: visible; min-height: 4197.11px;" data-layout-lg="6+6" data-layout-md="12" data-layout-sm="12" data-layout-xs="12"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1669788024246" data-id="1669788024246" style="display: block; flex-direction: unset; justify-content: unset; min-height: 1px;"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1669787520310" class="gf_row gf_row-gap-9" data-icon="gpicon-row" data-id="1669787520310" data-layout-lg="3+3+3+3" data-extraclass="" data-layout-md="3+3+3+3" data-layout-sm="3+3+3+3" data-layout-xs="12+12+12+12" data-row-gap="9px" style="min-height: auto;"><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-3 gf_col-xs-12" id="c-1664420807825" data-id="1664420807825" style="min-height: 1px; display: block; flex-direction: unset; justify-content: unset;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1669787520378" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1669787520378" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/8e783303-bea1-45a2-8515-d0e652f8c64d/-/format/auto/-/preview/3000x3000/-/quality/lighter/imgpsh_fullsize_anim%20_1_.jpg" alt="Want The Whole Place Visible?" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="687" height="969" natural-width="687" natural-height="969"></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1669787520337" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1669787520337"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h2 class="gf_gs-text-heading-2">Lightning Jack</h2></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-3 gf_col-xs-12" id="c-1664420892900" data-id="1664420892900" style="min-height: 1px; display: block; flex-direction: unset; justify-content: unset;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1669787520375" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1669787520375" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/ee5150f8-a3d6-4b7e-a2eb-a6f1108cb26d/-/format/auto/-/preview/3000x3000/-/quality/lighter/imgpsh_fullsize_anim%20_3_.jpg" alt="Secure Your Home At Any Time" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="686" height="979" natural-width="686" natural-height="979"></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1669787520409" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1669787520409"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-lg gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h2 class="gf_gs-text-heading-2">RED DUSK BARRA</h2></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-3 gf_col-xs-12" id="c-1664420892940" data-id="1664420892940" style="min-height: 1px; display: block; flex-direction: unset; justify-content: unset;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1669787520303" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1669787520303" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/5761ac6d-2927-4229-9c2d-4ff388be1abc/-/format/auto/-/preview/3000x3000/-/quality/lighter/imgpsh_fullsize_anim%20_4_.png" alt="Catch them in the act" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="689" height="977" natural-width="689" natural-height="977"></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1669787520315" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1669787520315"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-lg gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h2 class="gf_gs-text-heading-2">GT POPPING</h2></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-3 gf_col-xs-12" id="c-1669787800757" data-id="1669787800757" style="min-height: 1px; display: block; flex-direction: unset; justify-content: unset;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1669787914199" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1669787914199" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/62a0a3ed-b5e4-42c8-8e94-cc369da83b72/-/format/auto/-/preview/3000x3000/-/quality/lighter/dsfcs.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="682" height="977" natural-width="682" natural-height="977"></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1669788405426" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1669788405426"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-lg gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h2 class="gf_gs-text-heading-2">Reef king</h2></div></div></div></div></div></div></div></div><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1669781323010" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1669781323010" style=""><div class="module main-slider owl-carousel owl-theme " data-collg="1" data-colmd="1" data-colsm="1" data-colxs="1" data-marginlg="5px" data-marginmd="5px" data-marginsm="5px" data-marginxs="5px" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="0" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="0"><div class="item"><div data-index="1" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1669782336066" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1669782336066" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><a href="https://www.yarn.com.au/collections/reel-warriors-range" target=""><img src="https://ucarecdn.com/07685ce6-246b-4a12-8254-cd7d740208bf/-/format/auto/-/preview/3000x3000/-/quality/lighter/Artboard%201.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1920" height="800" natural-width="1920" natural-height="800"></a></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1669782867632" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1669782867632" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><a href="https://www.yarn.com.au/collections/reel-warriors-range" target=""><img src="https://ucarecdn.com/f5277ce7-7e72-4b35-aa12-e14e5bc8e1af/-/format/auto/-/preview/3000x3000/-/quality/lighter/Artboard%201.3.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1920" height="800" natural-width="1920" natural-height="800"></a></div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1669782882413" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1669782882413" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><a href="https://www.yarn.com.au/collections/reel-warriors-range" target=""><img src="https://ucarecdn.com/7f077159-e336-4315-a9d0-a5f0d07e17a2/-/format/auto/-/preview/3000x3000/-/quality/lighter/Artboard%201.3.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1920" height="800" natural-width="1920" natural-height="800"></a></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1669782800409" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1669782800409"><div class="module main-slider owl-carousel owl-theme " data-collg="3" data-colmd="3" data-colsm="3" data-colxs="1" data-marginlg="5px" data-marginmd="5px" data-marginsm="5px" data-marginxs="5px" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="0" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="0"><div class="item"><div data-index="1" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1669782804510" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1669782804510" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/1859dbe9-8020-4e0c-900a-0bc9456f4deb/-/format/auto/-/preview/3000x3000/-/quality/lighter/Artboard%202.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="375" height="200" natural-width="375" natural-height="200"></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1669783000581" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1669783000581" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/6501be72-1ca5-49c1-8cda-dc5957124532/-/format/auto/-/preview/3000x3000/-/quality/lighter/Artboard%202.3.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="375" height="200" natural-width="375" natural-height="200"></div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1669783016829" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1669783016829" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/aa7476e0-0f0c-4379-a1aa-85d658682704/-/format/auto/-/preview/3000x3000/-/quality/lighter/Artboard%202.3.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="375" height="200" natural-width="375" natural-height="200"></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div><div data-label="Row" data-key="row" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" id="r-1666244651274" data-icon="gpicon-row" data-id="1666244651274" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1666231698167" data-id="1666231698167"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1666244651316" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1666244651316"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">UPF50+ FISHING POLOS</h1></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666317351571" class="gf_row" data-icon="gpicon-row" data-id="1666317351571" data-layout-lg="3+6+3" data-extraclass="" data-layout-md="3+6+3" data-layout-sm="12+12+12" data-layout-xs="12+12+12"><div class="gf_column gf_col-sm-12 gf_col-xs-12 gf_col-lg-3 gf_col-md-3" id="c-1666317351598" data-id="1666317351598"></div><div class="gf_column gf_col-sm-12 gf_col-xs-12 gf_col-lg-6 gf_col-md-6" id="c-1669774839646" data-id="1669774839646"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1666317354353" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666317354353"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><div>These polos pay homage to the vibrant aquatic life and cultural fishing that happens on Country. No matter the season or weather…our polos give every fisherman the freedom and confidence to fish anywhere with a unique outfit that offers UPF 50+ sun protection.</div></div></div></div><div class="gf_column gf_col-sm-12 gf_col-xs-12 gf_col-lg-3 gf_col-md-3" id="c-1669774839644" data-id="1669774839644"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1669775173606" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1669775173606" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="12+12" data-layout-xs="12+12" data-row-gap="0px"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1655126558935" data-id="1655126558935"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1669775173639" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1669775173639" data-row-gap="0px" data-extraclass="" data-layout-lg="6+6" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="6+6"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-6" id="c-1655190894177" data-id="1655190894177"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1669775173651" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1669775173651" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="39947790614662" style="">{% assign product = all_products['barramundi-upf-50-unisex-long-sleeve-polo-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1669775173551" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1669775173551" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-25" data-id="1655126608882-child2-25"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1669775173597" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1669775173597" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-32" data-id="1655126608882-child2-32"><div class="module-wrap" id="m-1669775173602" data-id="1669775173602" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '1'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>- </span>{%if '1'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 0px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div></div></div><div data-label="(P) Title" data-key="p-title" data-atomgroup="child-product" id="m-1669775173609" class="module-wrap" data-icon="gpicon-product-title" data-ver="1.0" data-id="1669775173609"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1669775173577" data-id="1669775173577" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div data-label="Liquid" data-key="liquid" data-atomgroup="module" id="m-1669775173662" class="module-wrap" data-icon="gpicon-liquid" data-ver="1.1" data-id="1669775173662" data-name="GP Custom badge"><div class="module gf_module- gf_module--lg gf_module--md gf_module--sm gf_module--xs {{extraClass}}"></div></div></div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-6" id="c-1655190924326" data-id="1655190924326"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1669775173621" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1669775173621" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="39947557339270" style="">{% assign product = all_products['giant-trevally-upf-50-unisex-long-sleeve-polo-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1669775173546" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1669775173546" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-25" data-id="1655126608882-child2-25"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1669775173614" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1669775173614" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-32" data-id="1655126608882-child2-32"><div class="module-wrap" id="m-1669775173561" data-id="1669775173561" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '1'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>- </span>{%if '1'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 0px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div></div></div><div data-label="(P) Title" data-key="p-title" data-atomgroup="child-product" id="m-1669775173656" class="module-wrap" data-icon="gpicon-product-title" data-ver="1.0" data-id="1669775173656"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1669775173552" data-id="1669775173552" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div data-label="Liquid" data-key="liquid" data-atomgroup="module" id="m-1669775173593" class="module-wrap" data-icon="gpicon-liquid" data-ver="1.1" data-id="1669775173593" data-name="GP Custom badge"><div class="module gf_module- gf_module--lg gf_module--md gf_module--sm gf_module--xs {{extraClass}}"></div></div></div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1655126561730" data-id="1655126561730"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1669775173557" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1669775173557" data-row-gap="0px" data-extraclass="" data-layout-lg="6+6" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="6+6"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-6" id="c-1655190894177" data-id="1655190894177"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1669775173628" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1669775173628" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="39947556814982">{% assign product = all_products['coral-trout-upf-50-unisex-long-sleeve-polo-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1669775173640" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1669775173640" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-25" data-id="1655126608882-child2-25"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1669775173603" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1669775173603" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-32" data-id="1655126608882-child2-32"><div class="module-wrap" id="m-1669775173661" data-id="1669775173661" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '1'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>- </span>{%if '1'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 0px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div></div></div><div data-label="(P) Title" data-key="p-title" data-atomgroup="child-product" id="m-1669775173655" class="module-wrap" data-icon="gpicon-product-title" data-ver="1.0" data-id="1669775173655"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1669775173588" data-id="1669775173588" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div data-label="Liquid" data-key="liquid" data-atomgroup="module" id="m-1669775173604" class="module-wrap" data-icon="gpicon-liquid" data-ver="1.1" data-id="1669775173604" data-name="GP Custom badge"><div class="module gf_module- gf_module--lg gf_module--md gf_module--sm gf_module--xs {{extraClass}}"></div></div></div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-6" id="c-1655190929830" data-id="1655190929830"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1669775173612" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1669775173612" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="39947558518918">{% assign product = all_products['mangove-jack-upf-50-unisex-long-sleeve-polo-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1669775173685" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1669775173685" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-25" data-id="1655126608882-child2-25"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1669775173569" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1669775173569" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-32" data-id="1655126608882-child2-32"><div class="module-wrap" id="m-1669775173571" data-id="1669775173571" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '1'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>- </span>{%if '1'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 0px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div></div></div><div data-label="(P) Title" data-key="p-title" data-atomgroup="child-product" id="m-1669775173544" class="module-wrap" data-icon="gpicon-product-title" data-ver="1.0" data-id="1669775173544"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1669775173559" data-id="1669775173559" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div data-label="Liquid" data-key="liquid" data-atomgroup="module" id="m-1669775173556" class="module-wrap" data-icon="gpicon-liquid" data-ver="1.1" data-id="1669775173556" data-name="GP Custom badge"><div class="module gf_module- gf_module--lg gf_module--md gf_module--sm gf_module--xs {{extraClass}}"></div></div></div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div></div></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1669790463619" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1669790463619"><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/reel-warriors-range" target="" data-scroll-speed="2000" data-exc=""><span>SHOP COLLECTION</span></a></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" class="gf_row gf_row-gap-0 gf_row-fluid gf_row-no-padding" id="r-1666237205323" data-icon="gpicon-row" data-id="1666237205323" data-row-gap="0px" data-extraclass="" style="display: block;"><div class="gf_column gf_col-md-12 gf_col-sm-12 gf_col-xs-12 gf_col-lg-12" id="c-1666237205327" data-id="1666237205327"><div data-label="Row" id="r-1666162653229" class="gf_row gf_equal-height gf_row-gap-9 gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1666162653229" data-extraclass="" data-row-gap="9px" data-layout-lg="3+3+3+3" data-layout-md="3+3+3+3" data-layout-sm="6+6+6+6" data-layout-xs="12+12+12+12" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-4 gf_col-xs-12" id="c-1542252636495" data-id="1542252636495" style="min-height: auto; display: flex; flex-direction: column; justify-content: center;"><div data-label="Hero Banner" id="m-1666223691353" class="module-wrap effect-zoom" data-icon="gpicon-herobanner" data-ver="1" data-id="1666223691353"><div class="module " data-image="" data-height="auto" data-effect="effect-zoom" data-transition="1" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1669783095678" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1669783095678" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/collections/reel-warriors-range?pf_t_style=TYPE_Polos+%28Long+Sleeve+Unisex%29" target=""><img src="https://ucarecdn.com/db795ad9-5c53-43e9-9ac9-72c9d5112be4/-/format/auto/-/preview/3000x3000/-/quality/lighter/Artboard%201.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="750" height="944" natural-width="750" natural-height="944"></a></div></div></span></div><a class="hero-link" href="https://www.yarn.com.au/collections/reel-warriors-range?pf_t_style=TYPE_Polos+%28Long+Sleeve+Unisex%29" target="">&nbsp;</a><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-4 gf_col-xs-12" id="c-1666223536059" data-id="1666223536059" style="min-height: auto;"><div data-label="Hero Banner" id="m-1666162653334" class="module-wrap effect-zoom" data-icon="gpicon-herobanner" data-ver="1" data-id="1666162653334"><div class="module " data-image="" data-height="auto" data-effect="effect-zoom" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1669783110849" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1669783110849" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/collections/reel-warriors-range" target=""><img src="https://ucarecdn.com/4b680b5f-8266-4c84-8f62-c24478b0ff6d/-/format/auto/-/preview/3000x3000/-/quality/lighter/Artboard%202.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="750" height="944" natural-width="750" natural-height="944"></a></div></div></span></div><a class="hero-link" href="https://www.yarn.com.au/collections/reel-warriors-range" target="">&nbsp;</a><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:rgba(255, 255, 255, 1);opacity:0.2"></div></div></div><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-4 gf_col-xs-12" id="c-1666223536135" data-id="1666223536135" style="min-height: auto;"><div data-label="Hero Banner" id="m-1666223187913" class="module-wrap effect-zoom" data-icon="gpicon-herobanner" data-ver="1" data-id="1666223187913" style="display: block;"><div class="module " data-image="" data-height="auto" data-effect="effect-zoom" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1669783128370" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1669783128370" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/collections/reel-warriors-range" target=""><img src="https://ucarecdn.com/e574de48-16bf-423f-975e-5a19a198e52f/-/format/auto/-/preview/3000x3000/-/quality/lighter/Artboard%203.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="750" height="944" natural-width="750" natural-height="944"></a></div></div></span></div><a class="hero-link" href="https://www.yarn.com.au/collections/reel-warriors-range?pf_t_style=TYPE_Towels" target="">&nbsp;</a><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:rgba(255, 255, 255, 1);opacity:1"></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1669787520342" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1669787520342" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664420773532" data-id="1664420773532"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666067204884" class="gf_row gf_equal-height" data-icon="gpicon-row" data-id="1666067204884" data-layout-lg="9+3" data-extraclass="" data-layout-md="9+3" data-layout-sm="9+3" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible; min-height: auto;"><div class="gf_column gf_col-lg-9 gf_col-md-9 gf_col-sm-9 gf_col-xs-12" id="c-1651733004243" data-id="1651733004243" style="display: block; flex-direction: unset; justify-content: unset; min-height: auto;"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1666067204877" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666067204877"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-center-xs gf-elm-left-sm" data-gemlang="en" data-exc="">Find Your Favourite&nbsp;</div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1666067204734" class="element-wrap" data-icon="gpicon-heading" data-ver="1" data-id="1666067204734"><div class="elm text-edit gf-elm-center gf_gs-text-heading-2 gf-elm-left-md gf-elm-left-sm gf-elm-center-xs gf-elm-left-lg" data-gemlang="en" data-exc="">EXPLORE THE COLLECTION TODAY</div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-3 gf_col-xs-12" id="c-1651733005755" data-id="1651733005755" style="display: block; flex-direction: unset; justify-content: unset; min-height: auto;"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666070670907" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666070670907" style="opacity: 0;"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-lg="0" data-stretch-xs="0" data-stretch-md="0" data-stretch-sm="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/reel-warriors-range" target="" data-scroll-speed="2000" data-exc="" style="" data-scroll-speed-xs="2000" data-scroll-speed-md="2000" data-scroll-speed-sm="2000"><span>SHOP NOW</span></a></div></div></div></div></div></div></div></div><!--gfsplit--><script>window.__gemStoreData = {};window.__gemStoreData.gemKeyValid = {};window.__gemStoreData.gemKeyValid.bestWayAddToCart = 1;</script></div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		"https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv2herobanner.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv3product.js",
		 "https://www.youtube.com/player_api",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/owl.carousel.min.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv1animate.js",
		'{{ 'gem-page-83585990790.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->