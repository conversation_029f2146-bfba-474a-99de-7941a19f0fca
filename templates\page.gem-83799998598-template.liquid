{% comment %}
	GEMPAGES BUILDER (https://apps.shopify.com/gempages)

	You SHOULD NOT modify source code in this file because
	It is automatically generated from GEMPAGES BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->


<link rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/libs/css/fontawesome-4.6.3.1.min.css" class="gf-style">
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-page-83799998598.css' | asset_url }}" class="gf_page_style">
<link data-instant-track rel="stylesheet" type="text/css" href= "https://d1um8515vdn9kb.cloudfront.net/libs/css/owl.carousel.min.css" class="gf_libs">
<link data-instant-track rel="stylesheet" type="text/css" href= "https://d1um8515vdn9kb.cloudfront.net/libs/js/jquery.magnific-popup/magnific-popup.css" class="gf_libs">
<link data-instant-track rel="stylesheet" type="text/css" href= "https://d1um8515vdn9kb.cloudfront.net/files/gfv1animate.min.css" class="gf_libs">
<!--GEM_HEADER_END-->
<!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1673912568185" class="module-wrap" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1673912568185" style=""><div class="module " data-image="https://ucarecdn.com/711361aa-f3d0-49e8-ae33-8b43ceb4f05b/-/format/auto/-/preview/3000x3000/-/quality/lighter/nature-mob.jpg" data-image-lg="https://ucarecdn.com/1172fbb5-0655-4ca1-b18f-54c9d9e953d3/-/format/auto/-/preview/3000x3000/-/quality/lighter/web.png" data-image-md="https://ucarecdn.com/725c803d-09ef-4d92-912b-09d749480b93/-/format/auto/-/preview/3000x3000/-/quality/lighter/web.png" data-image-sm="https://ucarecdn.com/790babe5-01cb-43e5-88b6-3e0a10506889/-/format/auto/-/preview/3000x3000/-/quality/lighter/nature-tab.jpg" data-image-xs="https://ucarecdn.com/711361aa-f3d0-49e8-ae33-8b43ceb4f05b/-/format/auto/-/preview/3000x3000/-/quality/lighter/nature-mob.jpg" data-height="" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673912568178" class="gf_row" data-icon="gpicon-row" data-id="1673912568178" data-layout-lg="6+6" data-extraclass="" data-layout-md="7+5" data-layout-sm="8+4" data-layout-xs="12+12"><div class="gf_column gf_col-lg-6 gf_col-xs-12 gf_col-md-7 gf_col-sm-8" id="c-1667793962824" data-id="1667793962824"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1673912568162" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1673912568162" style=""><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">It’s time to think about</h1><h1 class="gf_gs-text-heading-2">nature maintenance</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1673912568264" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1673912568264" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>A New Collection By Luke Mallie</p></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1673912615064" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1673912615064" style="opacity: 0;"><div class="elm gf-elm-center gf-elm-left-md gf-elm-left-sm gf-elm-center-xs gf-elm-left-lg" data-stretch-lg="0" data-stretch-xs="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="/collections/nature-maintenance-range" target="" data-scroll-speed="2000" data-exc="" style="" data-scroll-speed-xs="2000"><span>EXPLORE COLLECTION</span></a></div></div></div><div class="gf_column gf_col-lg-6 gf_col-xs-12 gf_col-md-5 gf_col-sm-4" id="c-1667793964487" data-id="1667793964487"></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div><!--gfsplit--><div data-label="Row" data-key="row" class="gf_row gf_row-gap-0" id="r-1673920609350" data-icon="gpicon-row" data-id="1673920609350" data-row-gap="0px" data-extraclass="" data-layout-lg="6+6" data-layout-md="12" data-layout-sm="12" data-layout-xs="12"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1673914500393" data-id="1673914500393"><div data-label="Row" data-key="row" class="gf_row gf_row-gap-0" id="r-1673920609369" data-icon="gpicon-row" data-id="1673920609369" data-row-gap="0px" data-extraclass="" style=""><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1673911853771" data-id="1673911853771"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1673920609459" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1673920609459" style="" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/26e65bb1-1f6b-4262-9255-502682ae2441/-/format/auto/-/preview/3000x3000/-/quality/lighter/logo.png" alt="" class="gf_image" data-gemlang="en" data-width="35%" data-height="auto" title="" width="621" height="589" natural-width="621" natural-height="589"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673920609372" class="gf_row gf_row-gap-10" data-icon="gpicon-row" data-id="1673920609372" data-extraclass="" data-row-gap="10px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1673912215582" data-id="1673912215582"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1673920609420" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1673920609420"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">HELP US MAINTAIN OUR&nbsp;</h1><h1 class="gf_gs-text-heading-2">NATURAL ENVIRONMENT</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1673920609341" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1673920609341" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>Introducing the Nature Maintenance range, designed in collaboration with Kuku Yalanji and Kubin Village Country man Luke Mallie. This collection is a celebration of the natural landscape of the Currumbin Valley and the importance of conservation and the protection of the environment.</p></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1673920609389" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1673920609389" style="display: block; opacity: 0;"><div class="elm gf-elm-center gf-elm-left-md gf-elm-left-sm gf-elm-center-xs gf-elm-left-lg" data-stretch-lg="0" data-stretch-xs="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="/collections/nature-maintenance-range" target="" data-scroll-speed="2000" data-exc="" style="" data-scroll-speed-xs="2000"><span>EXPLORE COLLECTION</span></a></div></div></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1673911853879" data-id="1673911853879"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1673920609440" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1673920609440" data-resolution="3000x3000" style=""><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/963fedb5-d0c2-47fa-a114-06acc03dc843/-/format/auto/-/preview/3000x3000/-/quality/lighter/square.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="829" height="692" natural-width="829" natural-height="692"></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" id="r-1673914589815" data-icon="gpicon-row" data-id="1673914589815" data-row-gap="0px" data-extraclass="" style="display: block;"><div class="gf_column gf_col-md-12 gf_col-sm-12 gf_col-xs-12 gf_col-lg-12" id="c-1673914589767" data-id="1673914589767"><div data-label="Row" data-key="row" class="gf_row gf_row-gap-0" id="r-1666244651274" data-icon="gpicon-row" data-id="1666244651274" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1666231698167" data-id="1666231698167"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1666244651316" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1666244651316" style=""><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Inspired by the beautiful&nbsp;</h1><h1 class="gf_gs-text-heading-2">currumbin valley</h1></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666317351571" class="gf_row" data-icon="gpicon-row" data-id="1666317351571" data-layout-lg="3+6+3" data-extraclass="" data-layout-md="3+6+3" data-layout-sm="12+12+12" data-layout-xs="12+12+12" style=""><div class="gf_column gf_col-sm-12 gf_col-xs-12 gf_col-lg-3 gf_col-md-3" id="c-1666317351598" data-id="1666317351598"></div><div class="gf_column gf_col-sm-12 gf_col-xs-12 gf_col-lg-6 gf_col-md-6" id="c-1669774839646" data-id="1669774839646"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1666317354353" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666317354353"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><div>Help us to protect Country and the beautiful natural landscapes around us by practicing nature maintenance. This range features polos made from recycled plastic as we do our part to keep Country clean.</div><div><br></div></div></div></div><div class="gf_column gf_col-sm-12 gf_col-xs-12 gf_col-lg-3 gf_col-md-3" id="c-1669774839644" data-id="1669774839644"></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" class="gf_row gf_row-gap-0" id="r-1673914500476" data-icon="gpicon-row" data-id="1673914500476" data-row-gap="0px" data-extraclass="" data-layout-lg="6+6" data-layout-md="12" data-layout-sm="12" data-layout-xs="12"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1673914500393" data-id="1673914500393"><div data-label="Row" data-key="row" class="gf_row gf_row-gap-0" id="r-1673911853847" data-icon="gpicon-row" data-id="1673911853847" data-row-gap="0px" data-extraclass="" style=""><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1673911853771" data-id="1673911853771"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1673911884242" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1673911884242" data-resolution="3000x3000" style=""><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/65b73caa-2c08-4246-ac7d-3b0fae9aba89/-/format/auto/-/preview/3000x3000/-/quality/lighter/a.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="829" height="594" natural-width="829" natural-height="594"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673912215460" class="gf_row" data-icon="gpicon-row" data-id="1673912215460" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1673912215582" data-id="1673912215582"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1673912356541" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1673912356541"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Cockatoo Firebird</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1673912220885" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1673912220885" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>This artwork features a colourful cockatoo, a large and intelligent bird that can be found all over Australia. The use of colour and shapes is suggestive of flame and smoke. Some of Australia’s species of cockatoo are endangered and this bold design reminds us of the beauty inherent in this large bird and how important it is to protect and maintain the environments that they call home.</p><p><br></p></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1673913146486" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1673913146486" style="display: block; opacity: 0;"><div class="elm gf-elm-center gf-elm-left-md gf-elm-left-sm gf-elm-center-xs gf-elm-left-lg" data-stretch-lg="0" data-stretch-xs="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="/collections/nature-maintenance-range" target="" data-scroll-speed="2000" data-exc="" style="" data-scroll-speed-xs="2000"><span>EXPLORE COLLECTION</span></a></div></div></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1673911853879" data-id="1673911853879"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1673911877248" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1673911877248" style="" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/2c600581-ccb5-451d-b767-e855f673a0cc/-/format/auto/-/preview/3000x3000/-/quality/lighter/c.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="829" height="594" natural-width="829" natural-height="594"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673912259438" class="gf_row" data-icon="gpicon-row" data-id="1673912259438" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1673912259479" data-id="1673912259479"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1673912429491" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1673912429491"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Currumbin Valley</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1673912263588" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1673912263588" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>Currumbin sunset depicts the luscious landscape surrounding the Currumbin area and the vibrant colours of a setting sun. This artwork shows the natural beauty of the Currumbin Valley and the diversity of the plants and wildlife. This gorgeous area is home to so much life and it is important to preserve that as much as we can so that future generations will be able to experience the same beauty as we do.&nbsp;</p></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1673913166528" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1673913166528" style="opacity: 0;"><div class="elm gf-elm-center gf-elm-left-md gf-elm-left-sm gf-elm-center-xs gf-elm-left-lg" data-stretch-lg="0" data-stretch-xs="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="/collections/nature-maintenance-range" target="" data-scroll-speed="2000" data-exc="" style="" data-scroll-speed-xs="2000"><span>EXPLORE COLLECTION</span></a></div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666067204704" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1666067204704" data-extraclass="" style="display: block; transform: none; z-index: 50;" data-row-gap="0px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1651717077587" data-id="1651717077587" style="transform: none; z-index: 50;"><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1669781323010" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1669781323010" style=""><div class="module main-slider owl-carousel owl-theme " data-collg="1" data-colmd="1" data-colsm="1" data-colxs="1" data-marginlg="0px" data-marginmd="5px" data-marginsm="5px" data-marginxs="5px" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="0" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="0"><div class="item"><div data-index="1" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1669782867632" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1669782867632" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><a href="/collections/nature-maintenance-range" target=""><img src="https://ucarecdn.com/14c4d064-1c47-4d83-ab9b-a472cab56b2c/-/format/auto/-/preview/3000x3000/-/quality/lighter/newsletter.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1920" height="800" natural-width="1920" natural-height="800"></a></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1669782336066" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1669782336066" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/collections/tsi-range" target=""><img src="https://ucarecdn.com/776317a3-1174-48af-9a6d-fec89b91cf62/-/format/auto/-/preview/3000x3000/-/quality/lighter/1.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1920" height="800" natural-width="1920" natural-height="800"></a></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div><div data-label="Row" data-key="row" class="gf_row gf_row-gap-0" id="r-1673914304252" data-icon="gpicon-row" data-id="1673914304252" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1666231698167" data-id="1666231698167"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1673914304259" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1673914304259"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">SHOP our latest collection</h1></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304282" class="gf_row" data-icon="gpicon-row" data-id="1673914304282" data-layout-lg="3+6+3" data-extraclass="" data-layout-md="3+6+3" data-layout-sm="12+12+12" data-layout-xs="12+12+12"><div class="gf_column gf_col-sm-12 gf_col-xs-12 gf_col-lg-3 gf_col-md-3" id="c-1666317351598" data-id="1666317351598"></div><div class="gf_column gf_col-sm-12 gf_col-xs-12 gf_col-lg-6 gf_col-md-6" id="c-1669774839646" data-id="1669774839646"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1673914304246" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1673914304246"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc="">Enjoy our latest range and start conversations about nature maintenance.<div><br></div></div></div></div><div class="gf_column gf_col-sm-12 gf_col-xs-12 gf_col-lg-3 gf_col-md-3" id="c-1669774839644" data-id="1669774839644"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304321" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304321" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="12+12" data-layout-xs="12+12" data-row-gap="0px"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1655126558935" data-id="1655126558935"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304265" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304265" data-row-gap="0px" data-extraclass="" data-layout-lg="6+6" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="6+6"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-6" id="c-1655190894177" data-id="1655190894177"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1673914304200" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1673914304200" style="min-height: auto;" data-assigned-ver="4"><div class="module" data-variant="auto" data-current-variant="39986969444486" style="">{% assign product = all_products['cockatoo-firebird-black-cotton-crew-neck-unisex-t-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304203" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304203" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-25" data-id="1655126608882-child2-25"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304202" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304202" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-32" data-id="1655126608882-child2-32"><div class="module-wrap" id="m-1673914304161" data-id="1673914304161" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '1'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>- </span>{%if '1'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 0px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div></div></div><div data-label="(P) Title" data-key="p-title" data-atomgroup="child-product" id="m-1673914304195" class="module-wrap" data-icon="gpicon-product-title" data-ver="1.0" data-id="1673914304195"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1673914304199" data-id="1673914304199" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div data-label="Liquid" data-key="liquid" data-atomgroup="module" id="m-1673914304188" class="module-wrap" data-icon="gpicon-liquid" data-ver="1.1" data-id="1673914304188" data-name="GP Custom badge"><div class="module gf_module- gf_module--lg gf_module--md gf_module--sm gf_module--xs {{extraClass}}"></div></div></div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-6" id="c-1655190924326" data-id="1655190924326"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1673914304221" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1673914304221" style="min-height: auto;" data-assigned-ver="4"><div class="module" data-variant="auto" data-current-variant="39986968428678" style="">{% assign product = all_products['currumbin-sunset-recycled-upf50-unisex-fitted-polo-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304157" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304157" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-25" data-id="1655126608882-child2-25"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304214" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304214" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-32" data-id="1655126608882-child2-32"><div class="module-wrap" id="m-1673914304167" data-id="1673914304167" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '1'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>- </span>{%if '1'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 0px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div></div></div><div data-label="(P) Title" data-key="p-title" data-atomgroup="child-product" id="m-1673914304229" class="module-wrap" data-icon="gpicon-product-title" data-ver="1.0" data-id="1673914304229"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1673914304250" data-id="1673914304250" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div data-label="Liquid" data-key="liquid" data-atomgroup="module" id="m-1673914304280" class="module-wrap" data-icon="gpicon-liquid" data-ver="1.1" data-id="1673914304280" data-name="GP Custom badge"><div class="module gf_module- gf_module--lg gf_module--md gf_module--sm gf_module--xs {{extraClass}}"></div></div></div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1655126561730" data-id="1655126561730"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304149" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304149" data-row-gap="0px" data-extraclass="" data-layout-lg="6+6" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="6+6"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-6" id="c-1655190894177" data-id="1655190894177"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1673914304289" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1673914304289" style="min-height: auto;" data-assigned-ver="4"><div class="module" data-variant="auto" data-current-variant="39986966757510" style="">{% assign product = all_products['cockatoo-firebird-rectangle-chiffon-scarf'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304238" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304238" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-25" data-id="1655126608882-child2-25"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304293" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304293" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-32" data-id="1655126608882-child2-32"><div class="module-wrap" id="m-1673914304227" data-id="1673914304227" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '1'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>- </span>{%if '1'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 0px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div></div></div><div data-label="(P) Title" data-key="p-title" data-atomgroup="child-product" id="m-1673914304279" class="module-wrap" data-icon="gpicon-product-title" data-ver="1.0" data-id="1673914304279"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1673914304270" data-id="1673914304270" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div data-label="Liquid" data-key="liquid" data-atomgroup="module" id="m-1673914304235" class="module-wrap" data-icon="gpicon-liquid" data-ver="1.1" data-id="1673914304235" data-name="GP Custom badge"><div class="module gf_module- gf_module--lg gf_module--md gf_module--sm gf_module--xs {{extraClass}}"></div></div></div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-6" id="c-1655190929830" data-id="1655190929830"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1673914304215" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1673914304215" style="min-height: auto;" data-assigned-ver="4"><div class="module" data-variant="auto" data-current-variant="39986966233222" style="">{% assign product = all_products['cockatoo-firebird-womens-fashion-top'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304268" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304268" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-25" data-id="1655126608882-child2-25"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304175" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304175" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-32" data-id="1655126608882-child2-32"><div class="module-wrap" id="m-1673914304241" data-id="1673914304241" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '1'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>- </span>{%if '1'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 0px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div></div></div><div data-label="(P) Title" data-key="p-title" data-atomgroup="child-product" id="m-1673914304276" class="module-wrap" data-icon="gpicon-product-title" data-ver="1.0" data-id="1673914304276"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1673914304281" data-id="1673914304281" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div data-label="Liquid" data-key="liquid" data-atomgroup="module" id="m-1673914304307" class="module-wrap" data-icon="gpicon-liquid" data-ver="1.1" data-id="1673914304307" data-name="GP Custom badge"><div class="module gf_module- gf_module--lg gf_module--md gf_module--sm gf_module--xs {{extraClass}}"></div></div></div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304263" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304263" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="12+12" data-layout-xs="12+12" data-row-gap="0px"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1655126558935" data-id="1655126558935"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304189" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304189" data-row-gap="0px" data-extraclass="" data-layout-lg="6+6" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="6+6"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-6" id="c-1655190894177" data-id="1655190894177"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1673914304211" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1673914304211" style="min-height: auto;" data-assigned-ver="4"><div class="module" data-variant="auto" data-current-variant="39986966823046" style="">{% assign product = all_products['cockatoo-firebird-chiffon-shawl'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304187" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304187" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-25" data-id="1655126608882-child2-25"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304186" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304186" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-32" data-id="1655126608882-child2-32"><div class="module-wrap" id="m-1673914304212" data-id="1673914304212" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '1'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>- </span>{%if '1'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 0px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div></div></div><div data-label="(P) Title" data-key="p-title" data-atomgroup="child-product" id="m-1673914304236" class="module-wrap" data-icon="gpicon-product-title" data-ver="1.0" data-id="1673914304236"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1673914304201" data-id="1673914304201" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div data-label="Liquid" data-key="liquid" data-atomgroup="module" id="m-1673914304208" class="module-wrap" data-icon="gpicon-liquid" data-ver="1.1" data-id="1673914304208" data-name="GP Custom badge"><div class="module gf_module- gf_module--lg gf_module--md gf_module--sm gf_module--xs {{extraClass}}"></div></div></div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-6" id="c-1655190924326" data-id="1655190924326"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1673914304220" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1673914304220" style="min-height: auto;" data-assigned-ver="4"><div class="module" data-variant="auto" data-current-variant="39986969182342" style="">{% assign product = all_products['currumbin-sunset-black-cotton-crew-neck-unisex-t-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304261" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304261" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-25" data-id="1655126608882-child2-25"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304219" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304219" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-32" data-id="1655126608882-child2-32"><div class="module-wrap" id="m-1673914304222" data-id="1673914304222" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '1'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>- </span>{%if '1'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 0px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div></div></div><div data-label="(P) Title" data-key="p-title" data-atomgroup="child-product" id="m-1673914304299" class="module-wrap" data-icon="gpicon-product-title" data-ver="1.0" data-id="1673914304299"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1673914304213" data-id="1673914304213" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div data-label="Liquid" data-key="liquid" data-atomgroup="module" id="m-1673914304234" class="module-wrap" data-icon="gpicon-liquid" data-ver="1.1" data-id="1673914304234" data-name="GP Custom badge"><div class="module gf_module- gf_module--lg gf_module--md gf_module--sm gf_module--xs {{extraClass}}"></div></div></div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-xs-12 gf_col-sm-12" id="c-1655126561730" data-id="1655126561730"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304226" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304226" data-row-gap="0px" data-extraclass="" data-layout-lg="6+6" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="6+6"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-6" id="c-1655190894177" data-id="1655190894177"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1673914304272" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1673914304272" style="min-height: auto;" data-assigned-ver="4"><div class="module" data-variant="auto" data-current-variant="39986967773318" style="">{% assign product = all_products['currumbin-sunset-recycled-upf50-womens-fitted-polo-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304267" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304267" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-25" data-id="1655126608882-child2-25"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304308" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304308" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-32" data-id="1655126608882-child2-32"><div class="module-wrap" id="m-1673914304154" data-id="1673914304154" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '1'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>- </span>{%if '1'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 0px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div></div></div><div data-label="(P) Title" data-key="p-title" data-atomgroup="child-product" id="m-1673914304216" class="module-wrap" data-icon="gpicon-product-title" data-ver="1.0" data-id="1673914304216"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1673914304218" data-id="1673914304218" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div data-label="Liquid" data-key="liquid" data-atomgroup="module" id="m-1673914304133" class="module-wrap" data-icon="gpicon-liquid" data-ver="1.1" data-id="1673914304133" data-name="GP Custom badge"><div class="module gf_module- gf_module--lg gf_module--md gf_module--sm gf_module--xs {{extraClass}}"></div></div></div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-6" id="c-1655190929830" data-id="1655190929830"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1673914304209" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1673914304209" style="min-height: auto;" data-assigned-ver="4"><div class="module" data-variant="auto" data-current-variant="39986968068230" style="">{% assign product = all_products['cockatoo-firebird-recycled-upf50-unisex-polo-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304191" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304191" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-25" data-id="1655126608882-child2-25"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673914304254" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1673914304254" data-row-gap="0px" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1655126608882-child2-32" data-id="1655126608882-child2-32"><div class="module-wrap" id="m-1673914304184" data-id="1673914304184" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '1'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-left {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_none gf_gs-text-paragraph-2"><span>- </span>{%if '1'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 0px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div></div></div><div data-label="(P) Title" data-key="p-title" data-atomgroup="child-product" id="m-1673914304244" class="module-wrap" data-icon="gpicon-product-title" data-ver="1.0" data-id="1673914304244"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1673914304311" data-id="1673914304311" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '0' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '0' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 8px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div data-label="Liquid" data-key="liquid" data-atomgroup="module" id="m-1673914304240" class="module-wrap" data-icon="gpicon-liquid" data-ver="1.1" data-id="1673914304240" data-name="GP Custom badge"><div class="module gf_module- gf_module--lg gf_module--md gf_module--sm gf_module--xs {{extraClass}}"></div></div></div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div></div></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1673914304245" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1673914304245" style=""><div class="elm gf-elm-center gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg gf-elm-center-md" data-stretch-lg="0" data-stretch-md="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="/collections/nature-maintenance-range" target="" data-scroll-speed="2000" data-exc="" data-scroll-speed-md="2000"><span>SHOP COLLECTION</span></a></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" class="gf_row gf_row-gap-0" id="r-1673921855436" data-icon="gpicon-row" data-id="1673921855436" data-row-gap="0px" data-extraclass="" data-layout-lg="6+6" data-layout-md="12" data-layout-sm="12" data-layout-xs="12"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1673914500393" data-id="1673914500393"><div data-label="Row" data-key="row" class="gf_row gf_row-gap-0" id="r-1673921855315" data-icon="gpicon-row" data-id="1673921855315" data-row-gap="0px" data-extraclass="" style=""><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1673911853771" data-id="1673911853771"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673921855295" class="gf_row gf_row-gap-10" data-icon="gpicon-row" data-id="1673921855295" data-extraclass="" data-row-gap="10px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1673912215582" data-id="1673912215582"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1673921855317" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1673921855317" style=""><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2"><span style="letter-spacing: 0px;">Behind the scenes with</span></h1><h1 class="gf_gs-text-heading-2"><span style="letter-spacing: 0px;">artist Luke Mallie</span><br></h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1673921855299" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1673921855299" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>While I was born and bred in Brisbane, my heritage is from North Queensland. My mother is from the Kuku Yalanji people in the Daintree/Mossman area where our totem is the crocodile. My father comes from Kubin Village on Moa Island in the Torres Strait Islands where our totem is the sea eagle.&nbsp;</p><p></p><p></p><p></p></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1673921855407" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1673921855407" style="display: block; opacity: 0;"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-sm gf-elm-center-xs gf-elm-left-md" data-stretch-lg="0" data-stretch-xs="0" data-stretch-md="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/blogs/yarn-in-the-community/get-to-know-kuku-yalanji-and-kubin-village-country-artist-luke-mallie" target="" data-scroll-speed="2000" data-exc="" style="" data-scroll-speed-xs="2000" data-scroll-speed-md="2000"><span>LEARN MORE</span></a></div></div></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1673911853879" data-id="1673911853879"><div data-label="Video Popup" data-key="video-popup" data-atomgroup="module" id="m-1673927914207" class="module-wrap" data-icon="gpicon-videopopup" data-ver="1" data-id="1673927914207"><div class="module gf_module-center"><a class="video-popup " href="https://www.youtube.com/watch?v=IUJsH133vXA&t=34s" target="_blank"><img src="https://ucarecdn.com/69d67854-e6fd-41b1-a7e9-76e71309f0ee/-/format/auto/-/preview/3000x3000/-/quality/lighter/luke.jpg" style="width: auto; height: auto;" alt="Video"></a></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" class="gf_row gf_row-gap-0" id="r-1673926064796" data-icon="gpicon-row" data-id="1673926064796" data-row-gap="0px" data-extraclass="" data-layout-lg="6+6" data-layout-md="12" data-layout-sm="12" data-layout-xs="12"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1673914500393" data-id="1673914500393"><div data-label="Row" data-key="row" class="gf_row gf_row-gap-0" id="r-1673926064783" data-icon="gpicon-row" data-id="1673926064783" data-row-gap="0px" data-extraclass="" style=""><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1673911853771" data-id="1673911853771"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1673926064816" class="gf_row gf_row-gap-10" data-icon="gpicon-row" data-id="1673926064816" data-extraclass="" data-row-gap="10px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1673912215582" data-id="1673912215582"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1673926064696" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1673926064696" style=""><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Behind the scenes with</h1><h1 class="gf_gs-text-heading-2">artist Luke Mallie</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1673926064808" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1673926064808" style=""><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>"While I was born and bred in Brisbane, my heritage is from North Queensland. My mother is from the Kuku Yalanji people in the Daintree/Mossman area where our totem is the crocodile. My father comes from Kubin Village on Moa Island in the Torres Strait Islands where our totem is the sea eagle.&nbsp;<span style="color: inherit; font-family: inherit; font-size: inherit; font-weight: inherit; text-align: inherit; letter-spacing: 0px;">&nbsp;I consider myself as a visual storyteller, it’s a way I can share my culture with others who may not know much about Indigenous culture so my artwork might spark an interest in them to learn more about it. I connect to country and culture by making art to tell stories through the colours, textures and visual representations of home."</span></p><p></p><p></p><p></p></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1673926064712" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1673926064712" style="display: block; opacity: 0;"><div class="elm gf-elm-center gf-elm-left-md gf-elm-left-sm gf-elm-center-xs gf-elm-left-lg" data-stretch-lg="0" data-stretch-xs="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/blogs/yarn-in-the-community/get-to-know-kuku-yalanji-and-kubin-village-country-artist-luke-mallie" target="" data-scroll-speed="2000" data-exc="" style="" data-scroll-speed-xs="2000"><span>LEARN MORE</span></a></div></div></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1673911853879" data-id="1673911853879"><div data-label="Video Popup" data-key="video-popup" data-atomgroup="module" id="m-1673927472077" class="module-wrap" data-icon="gpicon-videopopup" data-ver="1" data-id="1673927472077"><div class="module gf_module-center"><a class="video-popup " href="https://www.youtube.com/watch?v=IUJsH133vXA&t=34s" target="_blank"><img src="https://ucarecdn.com/9b4a94af-d96a-45da-92bc-342658e0d857/-/format/auto/-/preview/3000x3000/-/quality/lighter/luke.jpg" style="width: auto; height: auto;" alt="Video"></a></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" id="r-1673916387299" data-icon="gpicon-row" data-id="1673916387299" data-extraclass="" data-row-gap="0px"><div class="gf_column gf_col-lg-3 gf_col-sm-6 gf_col-md-6 gf_col-xs-6" id="c-1673916387421" data-id="1673916387421"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1673916414468" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1673916414468" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/collections/nature-maintenance-range?pf_t_style=TYPE_Polos+%28Unisex%29&pf_t_style=TYPE_Polos+%28Kids%29&pf_t_style=TYPE_Polos+%28Fitted%29" target=""><img src="https://ucarecdn.com/a3dc798d-dbe3-4cf2-b743-133082e58dec/-/format/auto/-/preview/3000x3000/-/quality/lighter/polos.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1000" height="1000" natural-width="1000" natural-height="1000"></a></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1673916807473" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1673916807473"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">POLOS</h1></div></div></div><div class="gf_column gf_col-lg-3 gf_col-sm-6 gf_col-md-6 gf_col-xs-6" id="c-1673916387365" data-id="1673916387365"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1673916419726" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1673916419726" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/collections/nature-maintenance-range" target=""><img src="https://ucarecdn.com/d5dbffda-5446-4c7d-8c37-2a023f1594f7/-/format/auto/-/preview/3000x3000/-/quality/lighter/tee.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1000" height="1000" natural-width="1000" natural-height="1000"></a></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1673916864882" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1673916864882" style=""><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">T-SHIRTS</h1></div></div></div><div class="gf_column gf_col-lg-3 gf_col-sm-6 gf_col-md-6 gf_col-xs-6" id="c-1673916387352" data-id="1673916387352"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1673916426555" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1673916426555" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/collections/nature-maintenance-range?pf_t_style=TYPE_Shawls+%28Chiffon%29" target=""><img src="https://ucarecdn.com/5815e839-ff9a-4b40-b6bb-7aa1cbc3fb1d/-/format/auto/-/preview/3000x3000/-/quality/lighter/shawls.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1000" height="1000" natural-width="1000" natural-height="1000"></a></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1673916891165" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1673916891165" style=""><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">SHAWLS</h1></div></div></div><div class="gf_column gf_col-lg-3 gf_col-sm-6 gf_col-md-6 gf_col-xs-6" id="c-1673916387381" data-id="1673916387381"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1673916429925" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1673916429925" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/collections/nature-maintenance-range?pf_t_style=TYPE_Towels&pf_t_style=TYPE_Puzzles&pf_t_style=TYPE_Infuser&pf_t_style=TYPE_Coffee+Mugs+%28Ceramic%29&pf_t_style=TYPE_Coasters&pf_t_style=TYPE_Bags" target=""><img src="https://ucarecdn.com/3cd4addb-fe18-47fa-9b8d-f476ceeba49f/-/format/auto/-/preview/3000x3000/-/quality/lighter/towel.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1000" height="1000" natural-width="1000" natural-height="1000"></a></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1673916902573" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1673916902573" style=""><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">HOMEWARES</h1></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" id="r-1673922284823" data-icon="gpicon-row" data-id="1673922284823" data-extraclass="" data-row-gap="0px"><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1673916387421" data-id="1673916387421"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1673922284819" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1673922284819" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><a href="https://www.yarn.com.au/collections/nature-maintenance-range?pf_t_style=TYPE_Polos+%28Unisex%29&pf_t_style=TYPE_Polos+%28Kids%29&pf_t_style=TYPE_Polos+%28Fitted%29" target=""><img src="https://ucarecdn.com/a3dc798d-dbe3-4cf2-b743-133082e58dec/-/format/auto/-/preview/3000x3000/-/quality/lighter/polos.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1000" height="1000" natural-width="1000" natural-height="1000"></a></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1673922284775" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1673922284775"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">POLOS</h1></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1673916387365" data-id="1673916387365"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1673922284782" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1673922284782" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><a href="https://www.yarn.com.au/collections/nature-maintenance-range" target=""><img src="https://ucarecdn.com/d5dbffda-5446-4c7d-8c37-2a023f1594f7/-/format/auto/-/preview/3000x3000/-/quality/lighter/tee.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1000" height="1000" natural-width="1000" natural-height="1000"></a></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1673922284791" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1673922284791" style=""><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">T-SHIRTS</h1></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1673916387352" data-id="1673916387352"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1673922284748" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1673922284748" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><a href="https://www.yarn.com.au/collections/nature-maintenance-range?pf_t_style=TYPE_Shawls+%28Chiffon%29" target=""><img src="https://ucarecdn.com/5815e839-ff9a-4b40-b6bb-7aa1cbc3fb1d/-/format/auto/-/preview/3000x3000/-/quality/lighter/shawls.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1000" height="1000" natural-width="1000" natural-height="1000"></a></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1673922284831" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1673922284831" style=""><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">SHAWLS</h1></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-6 gf_col-xs-12" id="c-1673916387381" data-id="1673916387381"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1673922284881" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1673922284881" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><a href="https://www.yarn.com.au/collections/nature-maintenance-range?pf_t_style=TYPE_Towels&pf_t_style=TYPE_Puzzles&pf_t_style=TYPE_Infuser&pf_t_style=TYPE_Coffee+Mugs+%28Ceramic%29&pf_t_style=TYPE_Coasters&pf_t_style=TYPE_Bags" target=""><img src="https://ucarecdn.com/3cd4addb-fe18-47fa-9b8d-f476ceeba49f/-/format/auto/-/preview/3000x3000/-/quality/lighter/towel.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1000" height="1000" natural-width="1000" natural-height="1000"></a></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1673922284771" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1673922284771" style=""><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">HOMEWARES</h1></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" id="r-1666237205323" data-icon="gpicon-row" data-id="1666237205323" data-row-gap="0px" data-extraclass="" style="display: block;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1666237205327" data-id="1666237205327"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1669787520342" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1669787520342" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1664420773532" data-id="1664420773532"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1666067204884" class="gf_row gf_equal-height" data-icon="gpicon-row" data-id="1666067204884" data-layout-lg="9+3" data-extraclass="" data-layout-md="9+3" data-layout-sm="9+3" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible; min-height: auto;"><div class="gf_column gf_col-lg-9 gf_col-md-9 gf_col-sm-9 gf_col-xs-12" id="c-1651733004243" data-id="1651733004243" style="display: block; flex-direction: unset; justify-content: unset; min-height: auto;"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1666067204877" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1666067204877" style="display: block;"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-center-xs gf-elm-left-sm" data-gemlang="en" data-exc="">Find Your Favourite&nbsp;</div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1666067204734" class="element-wrap" data-icon="gpicon-heading" data-ver="1" data-id="1666067204734"><div class="elm text-edit gf-elm-center gf_gs-text-heading-2 gf-elm-left-md gf-elm-left-sm gf-elm-center-xs gf-elm-left-lg" data-gemlang="en" data-exc="">EXPLORE THE COLLECTION TODAY</div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-3 gf_col-xs-12" id="c-1651733005755" data-id="1651733005755" style="display: block; flex-direction: unset; justify-content: unset; min-height: auto;"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1666070670907" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1666070670907" style="opacity: 0;"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-lg="0" data-stretch-xs="0" data-stretch-md="0" data-stretch-sm="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="/collections/nature-maintenance-range" target="" data-scroll-speed="2000" data-exc="" style="" data-scroll-speed-xs="2000" data-scroll-speed-md="2000" data-scroll-speed-sm="2000"><span>SHOP NOW</span></a></div></div></div></div></div></div></div></div><!--gfsplit--><script>window.__gemStoreData = {};window.__gemStoreData.gemKeyValid = {};window.__gemStoreData.gemKeyValid.bestWayAddToCart = 1;</script></div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		"https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv2herobanner.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv3product.js",
		 "https://www.youtube.com/player_api",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/owl.carousel.min.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/jquery.magnific-popup/jquery.magnific-popup.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv1animate.js",
		'{{ 'gem-page-83799998598.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->