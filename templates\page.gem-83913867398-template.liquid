{% comment %}
	GEMPAGE BUILDER (https://apps.shopify.com/gempage)

	You SHOULD NOT modify source code in this page because
	It is automatically generated from GEMPAGE BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->


<link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.6.3/css/font-awesome.min.css" class="gf-style">
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-page-83913867398.css' | asset_url }}" class="gf_page_style">
<!--GEM_HEADER_END-->
<div class="gryffeditor">
  <div data-label="Text Block" class="element-wrap" data-description data-key="text-block"> <div class="elm text-edit">Currumbin sunset depicts the luscious landscape surrounding the Currumbin area and the vibrant colours of a setting sun. This artwork shows the natural beauty of the Currumbin Valley and the diversity of the plants and wildlife. This gorgeous area is home to so much life and it is important to preserve that as much as we can so that future generations will be able to experience the same beauty as we do.<img src="https://cdn.shopify.com/s/files/1/0247/4021/files/Luke-Mallie-Currimbin-Sunset-Low-Res_480x480.png?v=1675744523" alt=""></div></div>
</div><div id="divContentBk"></div>
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		'{{ 'gem-page-83913867398.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->