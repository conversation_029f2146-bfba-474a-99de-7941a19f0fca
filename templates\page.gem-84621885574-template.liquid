{% comment %}
	GEMPAGES BUILDER (https://apps.shopify.com/gempages)

	You SHOULD NOT modify source code in this file because
	It is automatically generated from GEMPAGES BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->


<link rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/libs/css/fontawesome-4.6.3.1.min.css" class="gf-style">
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-page-84621885574.css' | asset_url }}" class="gf_page_style">
<link data-instant-track class="gf_fonts" data-fonts="Gochi Hand" href="//fonts.googleapis.com/css2?family=Gochi Hand:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant Garamond" href="//fonts.googleapis.com/css2?family=Cormorant Garamond:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant" href="//fonts.googleapis.com/css2?family=Cormorant:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Bebas Neue" href="//fonts.googleapis.com/css2?family=Bebas Neue:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Anton" href="//fonts.googleapis.com/css2?family=Anton:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Aladin" href="//fonts.googleapis.com/css2?family=Aladin:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Acme" href="//fonts.googleapis.com/css2?family=Acme:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Alfa Slab One" href="//fonts.googleapis.com/css2?family=Alfa Slab One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Allan" href="//fonts.googleapis.com/css2?family=Allan:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Boogaloo" href="//fonts.googleapis.com/css2?family=Boogaloo:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Dancing Script" href="//fonts.googleapis.com/css2?family=Dancing Script:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Albert Sans" href="//fonts.googleapis.com/css2?family=Albert Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Dela Gothic One" href="//fonts.googleapis.com/css2?family=Dela Gothic One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Gasoek One" href="//fonts.googleapis.com/css2?family=Gasoek One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<!--GEM_HEADER_END-->
<!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1687932265004" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1687932265004" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/7b63915a-8c6c-495b-a631-d71f100f0519/-/format/auto/-/preview/3000x3000/-/quality/lighter/Header.jpg" alt="Yarn Marketplace image--23" class="gf_image" data-gemlang="en" width="3000" height="1250" data-width="100%" data-height="auto" title="" natural-width="3000" natural-height="1250" loading="lazy"></div></div><!--gfsplit--><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1688014468158" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1688014468158" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/03397515-8445-469d-bb9b-aed409ed392e/-/format/auto/-/preview/3000x3000/-/quality/lighter/Mobile-Header.jpg" alt="Yarn Marketplace image--24" class="gf_image" data-gemlang="en" width="938" height="1645" data-width="100%" data-height="auto" title="" natural-width="938" natural-height="1645" loading="lazy"></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1687932282483" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1687932282483" data-extraclass="" data-row-gap="0px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1687932282566" data-id="1687932282566"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1688014934339" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1688014934339" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/2a2222af-7518-457d-91f6-85a3b339fbd9/-/format/auto/-/preview/3000x3000/-/quality/lighter/Title-1.1.png" alt="Yarn Marketplace image--25" class="gf_image" data-gemlang="en" width="585" height="138" data-width="80%" data-height="auto" title="" natural-width="585" natural-height="138" loading="lazy"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1687932311846" class="gf_row" data-icon="gpicon-row" data-id="1687932311846" data-layout-lg="8+4" data-extraclass="" data-layout-md="8+4" data-layout-sm="8+4" data-layout-xs="12+12"><div class="gf_column gf_col-lg-8 gf_col-md-8 gf_col-sm-8 gf_col-xs-12" id="c-1687932311811" data-id="1687932311811"><div data-label="Youtube" data-key="youtube" data-atomgroup="module" id="m-1688539581377" class="module-wrap" data-icon="gpicon-youtube" data-ver="1" data-id="1688539581377"><div class="module gf_module- " data-url="https://www.youtube.com/watch?v=Kxpa3NBZ_Go" data-width="" data-height="" data-responsive="1" data-sound="0" data-autoplay="0" data-loop="0" data-controls="1" data-showinfo="" data-modestbranding="0" data-fs="1" data-rel="" data-hd="" data-start="" data-end=""></div><div class="gf_youtube-overlay"></div></div></div><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-4 gf_col-xs-12" id="c-1687932402975" data-id="1687932402975"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1687933426284" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1687933426284" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/b2f5ee38-0456-4214-bd43-2640d8a9dd7c/-/format/auto/-/preview/3000x3000/-/quality/lighter/Title-1.png" alt="Yarn Marketplace image--24" class="gf_image" data-gemlang="en" width="585" height="138" data-width="" data-height="auto" title="" natural-width="585" natural-height="138" loading="lazy"></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1687932431069" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1687932431069"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>In celebration of Aboriginal and Torres Strait Islander Children's Day 2022 Yarn visited Dalaigur, a pre-school in NSW that Gunawirra supports. We brought paints, paper and plain t-shirts and the kids sat around together to explore. We encouraged them to paint the future they dreamed of, have a play, paint each other, make a mess and have fun.</p><p>Using their artworks we created a unique collection that speaks to the power, freedom and joy of being raised with strong cultural connections. <strong>$2 per product sold from this collection goes directly to Gunawirra and their programs</strong>.&nbsp;</p><p><br></p></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1688340627427" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1688340627427" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1688340627471" data-id="1688340627471"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1687932465145" class="gf_row" data-icon="gpicon-row" data-id="1687932465145" style="display: block;" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="12+12"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1687932465127" data-id="1687932465127"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1687933658537" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1687933658537" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/9e7d13e0-50d1-49d8-ab64-69c046e99c32/-/format/auto/-/preview/3000x3000/-/quality/lighter/Title-2.png" alt="Yarn Marketplace image--25" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="516" height="70" natural-width="516" natural-height="70" loading="lazy"></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1687932650849" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1687932650849"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>Gunawirra is a community-led organisation that works closely with Aboriginal and Torres Strait Islander people, families, children and communities to reach their full potential. Our focus is operating programs that build capacity and confidence towards healing, resilience and self-reliance.</p><p><br></p><p>Our commitment is operating programs that are initiated and informed by the community we serve. These practical grassroots programs advocate healing through culture, and improvements in health and educational outcomes.</p><p></p></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1687932735189" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1687932735189"><div class="elm gf-elm-center gf-elm-left-md gf-elm-left-sm gf-elm-center-xs gf-elm-left-lg" data-stretch-lg="0" data-stretch-xs="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://gunawirra.org.au/" target="" data-scroll-speed="2000" data-exc="" aria-label="Go to https://gunawirra.org.au/" role="button" data-scroll-speed-xs="2000"><span>FIND OUT MORE</span></a></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1687932631037" data-id="1687932631037"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1687934374563" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1687934374563" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/df1f09ce-a519-4ccb-b4b5-ffee14883de5/-/format/auto/-/preview/3000x3000/-/quality/lighter/image.jpg" alt="Yarn Marketplace image--26" class="gf_image" data-gemlang="en" width="1623" height="1084" data-width="100%" data-height="auto" title="" natural-width="1623" natural-height="1084" loading="lazy"></div></div></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1687997313810" class="gf_row" data-icon="gpicon-row" data-id="1687997313810" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1687997313834" data-id="1687997313834"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1688016309895" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1688016309895" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/3a190547-c407-441a-969f-ec0325ecb291/-/format/auto/-/preview/3000x3000/-/quality/lighter/Title-6.png" alt="Yarn Marketplace image--29" class="gf_image" data-gemlang="en" width="741" height="147" data-width="60%" data-height="auto" title="" natural-width="741" natural-height="147" loading="lazy"></div></div><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1687997318749" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1687997318749" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-xs gf-elm-center-lg gf-elm-center-sm" data-exc=""><img src="https://ucarecdn.com/3472f1f5-5edb-4a45-8554-17bd97274fad/-/format/auto/-/preview/3000x3000/-/quality/lighter/Title-5.2.png" alt="Yarn Marketplace image--27" class="gf_image" data-gemlang="en" data-width="80%" data-height="auto" title="" width="661" height="141" natural-width="661" natural-height="141" loading="lazy"></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1687996745062" class="gf_row" data-icon="gpicon-row" data-id="1687996745062" data-layout-lg="4+4+4" data-extraclass="" data-layout-md="4+4+4" data-layout-sm="12+12+12" data-layout-xs="12+12+12"><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-12 gf_col-xs-12" id="c-1687996745085" data-id="1687996745085"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1687996756823" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1687996756823" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/a562bfe6-4027-44b8-820c-fc72586460e1/-/format/auto/-/preview/3000x3000/-/quality/lighter/tees.png" alt="Yarn Marketplace image--28" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1591" height="2385" natural-width="1591" natural-height="2385" loading="lazy"></div></div></div><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-12 gf_col-xs-12" id="c-1687996750656" data-id="1687996750656"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1687996905653" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1687996905653" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/6454cb39-e2e8-40e2-a36f-0bc128a23030/-/format/auto/-/preview/3000x3000/-/quality/lighter/tees-2.png" alt="Yarn Marketplace image--29" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1591" height="2385" natural-width="1591" natural-height="2385" loading="lazy"></div></div></div><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-12 gf_col-xs-12" id="c-1687996750731" data-id="1687996750731"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1687996913952" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1687996913952" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/116a3486-40b5-475d-8db8-75cbf5bbe3f6/-/format/auto/-/preview/3000x3000/-/quality/lighter/tees-3.png" alt="Yarn Marketplace image--30" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1591" height="2385" natural-width="1591" natural-height="2385" loading="lazy"></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1687933894414" class="gf_row" data-icon="gpicon-row" data-id="1687933894414"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1687933894353" data-id="1687933894353"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1687933900714" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1687933900714" data-resolution="3000x3000" style="display: block;"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/3e7ddd92-1f74-468c-9048-af79f1fb51c1/-/format/auto/-/preview/3000x3000/-/quality/lighter/Title-7.png" alt="Yarn Marketplace image--27" class="gf_image" data-gemlang="en" width="463" height="75" data-width="40%" data-height="auto" title="" natural-width="463" natural-height="75" loading="lazy"></div></div><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1688015117554" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1688015117554" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/1b91376b-a4ef-4081-80be-cf4837c661cb/-/format/auto/-/preview/3000x3000/-/quality/lighter/Title-5.2.png" alt="Yarn Marketplace image--34" class="gf_image" data-gemlang="en" data-width="auto" data-height="auto" title="" width="661" height="141" natural-width="661" natural-height="141" loading="lazy"></div></div><div data-label="Product List" data-key="product-list" data-atomgroup="product-list" id="m-1687933935029" class="module-wrap" data-icon="gpicon-product-list" data-ver="1.0" data-id="1687933935029" style="min-height: auto;"><div class="module " data-cid="265233956998" data-chandle="gunawirra" data-limit="6" data-collg="3" data-colmd="3" data-colsm="2" data-colxs="1"><div class="item-content item-content-no-placeholder"><div class="gf_row gf_row-no-width gf_row_no_tools gf_row-no-padding">{% assign colXsClass = ' gf_col-xs-' %}{% assign cols = 12 | divided_by: 1 %}{% assign colXsClass = colXsClass | append:cols %}{% assign colSmClass = ' gf_col-sm-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colSmClass = colSmClass | append:cols %}{% assign colMdClass = ' gf_col-md-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colMdClass = colMdClass | append:cols %}{% assign colLgClass = ' gf_col-lg-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colLgClass = colLgClass | append:cols %}{% assign colClass = 'gf_column gf_col_no_tools' %}{% assign colClass = colClass | append: colXsClass %}{% assign colClass = colClass | append: colSmClass %}{% assign colClass = colClass | append: colMdClass %}{% assign colClass = colClass | append: colLgClass %}{% assign gPLCount = 1 %}{% paginate collections["gunawirra"].products by 6 %}{% for product in collections["gunawirra"].products %}<div class="{{colClass}}" style="padding: 20px !important"><div data-label="Product" data-key="product" id="m-1687933935029-child{{forloop.index}}" data-ver="3.1" class="module-wrap" data-id="1687933935029-child{{forloop.index}}" data-index="1" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="39904365248646">{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1687933935029-child{{forloop.index}}-0" data-id="1687933935029-child{{forloop.index}}-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="/products/{{product.handle}}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="60%" data-height="auto" style="width: 60%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1687933935029-child{{forloop.index}}-1" data-id="1687933935029-child{{forloop.index}}-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="/products/{{ product.handle }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1687933935029-child{{forloop.index}}-2" data-id="1687933935029-child{{forloop.index}}-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1687933935029-child{{forloop.index}}-3" data-id="1687933935029-child{{forloop.index}}-3" data-label="(P) Cart Button"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div>{% assign modulo = gPLCount | modulo: 1 %}{% if modulo == 0 %}<div class="gf_clearfix visible-xs"></div>{% endif %}{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-sm"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-md"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-lg"></div>{% endif %}{% assign gPLCount = gPLCount | plus: 1 %}{% endfor %}{% endpaginate %}</div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1687994096992" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1687994096992" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1687994097040" data-id="1687994097040"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1687994300388" class="gf_row" data-icon="gpicon-row" data-id="1687994300388" data-layout-lg="8+4" data-extraclass="" data-layout-md="8+4" data-layout-sm="8+4" data-layout-xs="12+12"><div class="gf_column gf_col-lg-8 gf_col-md-8 gf_col-sm-8 gf_col-xs-12" id="c-1687994300338" data-id="1687994300338"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1687994314429" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1687994314429"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>Visit Yarn Raise to Find More</p></div></div></div><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-4 gf_col-xs-12" id="c-1687994302572" data-id="1687994302572"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1687994329391" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1687994329391"><div class="elm gf-elm-center gf-elm-right-md gf-elm-right-sm gf-elm-center-xs gf-elm-right-lg" data-stretch-lg="0" data-stretch-xs="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc="" aria-label="button" role="button" data-scroll-speed-xs="2000"><span>YARN RAISE</span></a></div></div></div></div></div></div><!--gfsplit--><script>window.__gemStoreData = {};window.__gemStoreData.gemKeyValid = {};window.__gemStoreData.gemKeyValid.bestWayAddToCart = 1;</script></div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		"https://d1um8515vdn9kb.cloudfront.net/libs/js/gfyoutube.js",
		 "https://www.youtube.com/player_api",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv3product.js",
		'{{ 'gem-page-84621885574.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->