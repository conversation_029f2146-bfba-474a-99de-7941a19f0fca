{% comment %}
	GEMPAGES BUILDER (https://apps.shopify.com/gempages)

	You SHOULD NOT modify source code in this file because
	It is automatically generated from GEMPAGES BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->


<link rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/libs/css/fontawesome-4.6.3.1.min.css" class="gf-style">
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-page-***********.css' | asset_url }}" class="gf_page_style">
<link data-instant-track class="gf_fonts" data-fonts="Gochi Hand" href="//fonts.googleapis.com/css2?family=Gochi Hand:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant Garamond" href="//fonts.googleapis.com/css2?family=Cormorant Garamond:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant" href="//fonts.googleapis.com/css2?family=Cormorant:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Bebas Neue" href="//fonts.googleapis.com/css2?family=Bebas Neue:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Anton" href="//fonts.googleapis.com/css2?family=Anton:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Aladin" href="//fonts.googleapis.com/css2?family=Aladin:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Acme" href="//fonts.googleapis.com/css2?family=Acme:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Alfa Slab One" href="//fonts.googleapis.com/css2?family=Alfa Slab One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Allan" href="//fonts.googleapis.com/css2?family=Allan:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Boogaloo" href="//fonts.googleapis.com/css2?family=Boogaloo:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Dancing Script" href="//fonts.googleapis.com/css2?family=Dancing Script:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Albert Sans" href="//fonts.googleapis.com/css2?family=Albert Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Dela Gothic One" href="//fonts.googleapis.com/css2?family=Dela Gothic One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Gasoek One" href="//fonts.googleapis.com/css2?family=Gasoek One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track rel="stylesheet" type="text/css" href= "https://d1um8515vdn9kb.cloudfront.net/libs/css/owl.carousel.min.css" class="gf_libs">
<!--GEM_HEADER_END-->
<!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1691646568239" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1691646568239" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1691646568258" data-id="1691646568258"><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1691646627971" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1691646627971"><div class="module main-slider owl-carousel owl-theme " data-collg="1" data-colmd="3" data-colsm="3" data-colxs="1" data-marginlg="5px" data-marginmd="5px" data-marginsm="5px" data-marginxs="5px" data-dotslg="0" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="0" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="0" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="1"><div class="item"><div data-index="1" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1691646669121" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1691646669121" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/4d7edcc5-09bd-4d87-8125-d95076bb7947/-/format/auto/-/preview/3000x3000/-/quality/lighter/Father_s%20day%20gift%20page-01.png" alt="" class="gf_image" data-gemlang="en" data-width="auto" data-height="auto" title="" width="1920" height="999" natural-width="1920" natural-height="999"></div></div></div></div><div class="item"><div data-index="2" class="item-content"></div></div><div class="item"><div data-index="3" class="item-content"></div></div><div class="item"><div data-index="4" class="item-content"></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1691646750880" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1691646750880" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1691646750849" data-id="1691646750849"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1691646761022" class="gf_row" data-icon="gpicon-row" data-id="1691646761022"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1691646761010" data-id="1691646761010"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1691646766111" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1691646766111"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h2 class="gf_gs-text-heading-2">Father's Day Bundles</h2></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1691646841867" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1691646841867"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>Our curated collection of Father's Day gifts are perfect for showing dad how much you appreciate him.</p></div></div><div data-label="Product List" data-key="product-list" data-atomgroup="product-list" id="m-1691647014814" class="module-wrap" data-icon="gpicon-product-list" data-ver="1.0" data-id="1691647014814" style="min-height: auto;"><div class="module " data-cid="269902086278" data-chandle="fathers-day-bundles" data-limit="8" data-collg="4" data-colmd="3" data-colsm="3" data-colxs="2"><div class="item-content item-content-no-placeholder"><div class="gf_row gf_row-no-width gf_row_no_tools gf_row-no-padding">{% assign colXsClass = ' gf_col-xs-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colXsClass = colXsClass | append:cols %}{% assign colSmClass = ' gf_col-sm-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colSmClass = colSmClass | append:cols %}{% assign colMdClass = ' gf_col-md-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colMdClass = colMdClass | append:cols %}{% assign colLgClass = ' gf_col-lg-' %}{% assign cols = 12 | divided_by: 4 %}{% assign colLgClass = colLgClass | append:cols %}{% assign colClass = 'gf_column gf_col_no_tools' %}{% assign colClass = colClass | append: colXsClass %}{% assign colClass = colClass | append: colSmClass %}{% assign colClass = colClass | append: colMdClass %}{% assign colClass = colClass | append: colLgClass %}{% assign gPLCount = 1 %}{% paginate collections["fathers-day-bundles"].products by 8 %}{% for product in collections["fathers-day-bundles"].products %}<div class="{{colClass}}" style="padding: 20px !important"><div data-label="Product" data-key="product" id="m-1691647014814-child{{forloop.index}}" data-ver="3.1" class="module-wrap" data-id="1691647014814-child{{forloop.index}}" data-index="1" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="40208302211206">{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1691647014814-child{{forloop.index}}-0" data-id="1691647014814-child{{forloop.index}}-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1691647014814-child{{forloop.index}}-1" data-id="1691647014814-child{{forloop.index}}-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1691647014814-child{{forloop.index}}-2" data-id="1691647014814-child{{forloop.index}}-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1691647014814-child{{forloop.index}}-3" data-id="1691647014814-child{{forloop.index}}-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div>{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-xs"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-sm"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-md"></div>{% endif %}{% assign modulo = gPLCount | modulo: 4 %}{% if modulo == 0 %}<div class="gf_clearfix visible-lg"></div>{% endif %}{% assign gPLCount = gPLCount | plus: 1 %}{% endfor %}{% endpaginate %}</div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692233924770" class="gf_row" data-icon="gpicon-row" data-id="1692233924770"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692233924802" data-id="1692233924802"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1692233929858" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1692233929858"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/gift-bundles?sort=extra-sort2-descending" target="" data-scroll-speed="2000" data-exc="" aria-label="Go to https://www.yarn.com.au/collections/gift-bundles?page=2&sort=extra-sort2-descending"><span>SHOP ALL</span></a></div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692059401377" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692059401377" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058717436" data-id="1692058717436"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1692059401414" class="module-wrap" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1692059401414"><div class="module " data-image="https://ucarecdn.com/7650421f-d5f9-47e7-98c3-fa52420c2831/-/format/auto/-/preview/3000x3000/-/quality/lighter/Father_s%20day%20gift%20page-17.png" data-image-lg="https://ucarecdn.com/bd3fd050-8ebd-4c98-b0a2-c4e4226236a2/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-16.png" data-image-md="https://ucarecdn.com/bd3fd050-8ebd-4c98-b0a2-c4e4226236a2/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-16.png" data-image-sm="https://ucarecdn.com/bd3fd050-8ebd-4c98-b0a2-c4e4226236a2/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-16.png" data-image-xs="https://ucarecdn.com/7650421f-d5f9-47e7-98c3-fa52420c2831/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-17.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692059401328" class="gf_row" data-icon="gpicon-row" data-id="1692059401328"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058951094" data-id="1692058951094"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692059401433" class="gf_row" data-icon="gpicon-row" data-id="1692059401433"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058973857" data-id="1692058973857"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1692059401380" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1692059401380"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Standard Polos</h1></div></div></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692575611297" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692575611297" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058717436" data-id="1692058717436"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1692575611277" class="module-wrap" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1692575611277"><div class="module " data-image="https://ucarecdn.com/bd3fd050-8ebd-4c98-b0a2-c4e4226236a2/-/format/auto/-/preview/3000x3000/-/quality/lighter/Father_s%20day%20gift%20page-16.png" data-image-lg="https://ucarecdn.com/bd3fd050-8ebd-4c98-b0a2-c4e4226236a2/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-16.png" data-image-md="https://ucarecdn.com/bd3fd050-8ebd-4c98-b0a2-c4e4226236a2/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-16.png" data-image-sm="https://ucarecdn.com/bd3fd050-8ebd-4c98-b0a2-c4e4226236a2/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-16.png" data-image-xs="https://ucarecdn.com/bd3fd050-8ebd-4c98-b0a2-c4e4226236a2/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-16.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692575611171" class="gf_row" data-icon="gpicon-row" data-id="1692575611171"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058951094" data-id="1692058951094"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692575611182" class="gf_row" data-icon="gpicon-row" data-id="1692575611182"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058973857" data-id="1692058973857"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1692575611201" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1692575611201"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Standard Polos</h1></div></div></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692059058836" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692059058836" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1691646750849" data-id="1691646750849"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692059058809" class="gf_row" data-icon="gpicon-row" data-id="1692059058809"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1691646761010" data-id="1691646761010"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1692059058781" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1692059058781"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>We have a massive range of vibrant First Nations polos for men. Ideal from work to weekend.</p></div></div><div data-label="Product List" data-key="product-list" data-atomgroup="product-list" id="m-1692059058870" class="module-wrap" data-icon="gpicon-product-list" data-ver="1.0" data-id="1692059058870" style="min-height: auto;"><div class="module " data-cid="269901987974" data-chandle="fathers-day-standard-polos" data-limit="8" data-collg="4" data-colmd="3" data-colsm="3" data-colxs="2"><div class="item-content item-content-no-placeholder"><div class="gf_row gf_row-no-width gf_row_no_tools gf_row-no-padding">{% assign colXsClass = ' gf_col-xs-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colXsClass = colXsClass | append:cols %}{% assign colSmClass = ' gf_col-sm-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colSmClass = colSmClass | append:cols %}{% assign colMdClass = ' gf_col-md-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colMdClass = colMdClass | append:cols %}{% assign colLgClass = ' gf_col-lg-' %}{% assign cols = 12 | divided_by: 4 %}{% assign colLgClass = colLgClass | append:cols %}{% assign colClass = 'gf_column gf_col_no_tools' %}{% assign colClass = colClass | append: colXsClass %}{% assign colClass = colClass | append: colSmClass %}{% assign colClass = colClass | append: colMdClass %}{% assign colClass = colClass | append: colLgClass %}{% assign gPLCount = 1 %}{% paginate collections["fathers-day-standard-polos"].products by 8 %}{% for product in collections["fathers-day-standard-polos"].products %}<div class="{{colClass}}" style="padding: 20px !important"><div data-label="Product" data-key="product" id="m-1692059058870-child{{forloop.index}}" data-ver="3.1" class="module-wrap" data-id="1692059058870-child{{forloop.index}}" data-index="1" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="39907224617094">{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1692059058870-child{{forloop.index}}-0" data-id="1692059058870-child{{forloop.index}}-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1692059058870-child{{forloop.index}}-1" data-id="1692059058870-child{{forloop.index}}-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1692059058870-child{{forloop.index}}-2" data-id="1692059058870-child{{forloop.index}}-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1692059058870-child{{forloop.index}}-3" data-id="1692059058870-child{{forloop.index}}-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div>{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-xs"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-sm"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-md"></div>{% endif %}{% assign modulo = gPLCount | modulo: 4 %}{% if modulo == 0 %}<div class="gf_clearfix visible-lg"></div>{% endif %}{% assign gPLCount = gPLCount | plus: 1 %}{% endfor %}{% endpaginate %}</div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692234259901" class="gf_row" data-icon="gpicon-row" data-id="1692234259901"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692233924802" data-id="1692233924802"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1692234259843" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1692234259843"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/all-mens-polo-shirts-australia-aboriginal-art-clothes" target="" data-scroll-speed="2000" data-exc="" aria-label="Go to https://www.yarn.com.au/collections/all-mens-polo-shirts-australia-aboriginal-art-clothes"><span>SHOP ALL</span></a></div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692059148487" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692059148487" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058717436" data-id="1692058717436"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1692059148480" class="module-wrap" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1692059148480"><div class="module " data-image="https://ucarecdn.com/abc0c53e-ed32-443b-9486-90e81890a278/-/format/auto/-/preview/3000x3000/-/quality/lighter/Father_s%20day%20gift%20page-13.png" data-image-lg="https://ucarecdn.com/abc0c53e-ed32-443b-9486-90e81890a278/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-13.png" data-image-md="https://ucarecdn.com/abc0c53e-ed32-443b-9486-90e81890a278/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-13.png" data-image-sm="https://ucarecdn.com/abc0c53e-ed32-443b-9486-90e81890a278/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-13.png" data-image-xs="https://ucarecdn.com/abc0c53e-ed32-443b-9486-90e81890a278/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-13.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692059148483" class="gf_row" data-icon="gpicon-row" data-id="1692059148483"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058951094" data-id="1692058951094"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692059148531" class="gf_row" data-icon="gpicon-row" data-id="1692059148531"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058973857" data-id="1692058973857"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1692059148399" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1692059148399"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Corporate Polos</h1></div></div></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692575667581" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692575667581" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058717436" data-id="1692058717436"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1692575667648" class="module-wrap" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1692575667648"><div class="module " data-image="https://ucarecdn.com/041b5a0e-b592-4827-bb4f-92c6d4056814/-/format/auto/-/preview/3000x3000/-/quality/lighter/Father_s%20day%20gift%20page-18.png" data-image-lg="https://ucarecdn.com/abc0c53e-ed32-443b-9486-90e81890a278/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-13.png" data-image-md="https://ucarecdn.com/abc0c53e-ed32-443b-9486-90e81890a278/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-13.png" data-image-sm="https://ucarecdn.com/abc0c53e-ed32-443b-9486-90e81890a278/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-13.png" data-image-xs="https://ucarecdn.com/041b5a0e-b592-4827-bb4f-92c6d4056814/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-18.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692575667629" class="gf_row" data-icon="gpicon-row" data-id="1692575667629"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058951094" data-id="1692058951094"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692575667514" class="gf_row" data-icon="gpicon-row" data-id="1692575667514"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058973857" data-id="1692058973857"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1692575667625" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1692575667625"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Corporate Polos</h1></div></div></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692059253763" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692059253763" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1691646750849" data-id="1691646750849"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692059253783" class="gf_row" data-icon="gpicon-row" data-id="1692059253783"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1691646761010" data-id="1691646761010"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1692059253831" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1692059253831"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>Colourful details on a classic base make our contrast polos perfect for the office.</p></div></div><div data-label="Product List" data-key="product-list" data-atomgroup="product-list" id="m-1692059253837" class="module-wrap" data-icon="gpicon-product-list" data-ver="1.0" data-id="1692059253837" style="min-height: auto;"><div class="module " data-cid="269902020742" data-chandle="fathers-day-corporate-polos" data-limit="8" data-collg="4" data-colmd="3" data-colsm="3" data-colxs="2"><div class="item-content item-content-no-placeholder"><div class="gf_row gf_row-no-width gf_row_no_tools gf_row-no-padding">{% assign colXsClass = ' gf_col-xs-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colXsClass = colXsClass | append:cols %}{% assign colSmClass = ' gf_col-sm-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colSmClass = colSmClass | append:cols %}{% assign colMdClass = ' gf_col-md-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colMdClass = colMdClass | append:cols %}{% assign colLgClass = ' gf_col-lg-' %}{% assign cols = 12 | divided_by: 4 %}{% assign colLgClass = colLgClass | append:cols %}{% assign colClass = 'gf_column gf_col_no_tools' %}{% assign colClass = colClass | append: colXsClass %}{% assign colClass = colClass | append: colSmClass %}{% assign colClass = colClass | append: colMdClass %}{% assign colClass = colClass | append: colLgClass %}{% assign gPLCount = 1 %}{% paginate collections["fathers-day-corporate-polos"].products by 8 %}{% for product in collections["fathers-day-corporate-polos"].products %}<div class="{{colClass}}" style="padding: 20px !important"><div data-label="Product" data-key="product" id="m-1692059253837-child{{forloop.index}}" data-ver="3.1" class="module-wrap" data-id="1692059253837-child{{forloop.index}}" data-index="1" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="40066925461638">{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1692059253837-child{{forloop.index}}-0" data-id="1692059253837-child{{forloop.index}}-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1692059253837-child{{forloop.index}}-1" data-id="1692059253837-child{{forloop.index}}-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1692059253837-child{{forloop.index}}-2" data-id="1692059253837-child{{forloop.index}}-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1692059253837-child{{forloop.index}}-3" data-id="1692059253837-child{{forloop.index}}-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div>{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-xs"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-sm"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-md"></div>{% endif %}{% assign modulo = gPLCount | modulo: 4 %}{% if modulo == 0 %}<div class="gf_clearfix visible-lg"></div>{% endif %}{% assign gPLCount = gPLCount | plus: 1 %}{% endfor %}{% endpaginate %}</div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692234381058" class="gf_row" data-icon="gpicon-row" data-id="1692234381058"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692233924802" data-id="1692233924802"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1692234381061" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1692234381061"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/corporate-polos?pf_t_style=TYPE_Polos+%28Corporate+Unisex%29" target="" data-scroll-speed="2000" data-exc="" aria-label="Go to https://www.yarn.com.au/collections/corporate-polos?pf_t_style=TYPE_Polos+%28Corporate+Unisex%29"><span>SHOP ALL</span></a></div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692059568130" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692059568130" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058717436" data-id="1692058717436"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1692059568123" class="module-wrap" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1692059568123"><div class="module " data-image="https://ucarecdn.com/8ee55028-7a00-411b-b749-417c9d40549b/-/format/auto/-/preview/3000x3000/-/quality/lighter/Father_s%20day%20gift%20page-19.png" data-image-lg="https://ucarecdn.com/70d961ec-213b-45c6-a67c-4fbc91773085/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-15.png" data-image-md="https://ucarecdn.com/70d961ec-213b-45c6-a67c-4fbc91773085/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-15.png" data-image-sm="https://ucarecdn.com/70d961ec-213b-45c6-a67c-4fbc91773085/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-15.png" data-image-xs="https://ucarecdn.com/8ee55028-7a00-411b-b749-417c9d40549b/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-19.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692059568118" class="gf_row" data-icon="gpicon-row" data-id="1692059568118" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058951094" data-id="1692058951094"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692059568111" class="gf_row" data-icon="gpicon-row" data-id="1692059568111" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058973857" data-id="1692058973857"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1692059568054" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1692059568054"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Outdoor Polos</h1></div></div></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692748948377" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692748948377" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058717436" data-id="1692058717436"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1692748948353" class="module-wrap" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1692748948353"><div class="module " data-image="https://ucarecdn.com/6630fb4a-b13b-41fe-b171-20e5de9aa3dc/-/format/auto/-/preview/3000x3000/-/quality/lighter/Father_s%20day%20gift%20page_2-10.png" data-image-lg="https://ucarecdn.com/6630fb4a-b13b-41fe-b171-20e5de9aa3dc/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page_2-10.png" data-image-md="https://ucarecdn.com/6630fb4a-b13b-41fe-b171-20e5de9aa3dc/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page_2-10.png" data-image-sm="https://ucarecdn.com/6630fb4a-b13b-41fe-b171-20e5de9aa3dc/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page_2-10.png" data-image-xs="https://ucarecdn.com/6630fb4a-b13b-41fe-b171-20e5de9aa3dc/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page_2-10.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692748948372" class="gf_row" data-icon="gpicon-row" data-id="1692748948372"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058951094" data-id="1692058951094"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692748948366" class="gf_row" data-icon="gpicon-row" data-id="1692748948366"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058973857" data-id="1692058973857"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1692748948391" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1692748948391"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Outdoor Polos</h1></div></div></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692059736428" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692059736428" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1691646750849" data-id="1691646750849"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692059736514" class="gf_row" data-icon="gpicon-row" data-id="1692059736514"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1691646761010" data-id="1691646761010"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1692059736444" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1692059736444"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>Stay safe in the sun, wind and rain with our outdoor range.&nbsp;</p></div></div><div data-label="Product List" data-key="product-list" data-atomgroup="product-list" id="m-1692059736502" class="module-wrap" data-icon="gpicon-product-list" data-ver="1.0" data-id="1692059736502" style="min-height: auto;"><div class="module " data-cid="269902119046" data-chandle="fathers-day-outdoor-polos" data-limit="8" data-collg="4" data-colmd="3" data-colsm="3" data-colxs="2"><div class="item-content item-content-no-placeholder"><div class="gf_row gf_row-no-width gf_row_no_tools gf_row-no-padding">{% assign colXsClass = ' gf_col-xs-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colXsClass = colXsClass | append:cols %}{% assign colSmClass = ' gf_col-sm-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colSmClass = colSmClass | append:cols %}{% assign colMdClass = ' gf_col-md-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colMdClass = colMdClass | append:cols %}{% assign colLgClass = ' gf_col-lg-' %}{% assign cols = 12 | divided_by: 4 %}{% assign colLgClass = colLgClass | append:cols %}{% assign colClass = 'gf_column gf_col_no_tools' %}{% assign colClass = colClass | append: colXsClass %}{% assign colClass = colClass | append: colSmClass %}{% assign colClass = colClass | append: colMdClass %}{% assign colClass = colClass | append: colLgClass %}{% assign gPLCount = 1 %}{% paginate collections["fathers-day-outdoor-polos"].products by 8 %}{% for product in collections["fathers-day-outdoor-polos"].products %}<div class="{{colClass}}" style="padding: 20px !important"><div data-label="Product" data-key="product" id="m-1692059736502-child{{forloop.index}}" data-ver="3.1" class="module-wrap" data-id="1692059736502-child{{forloop.index}}" data-index="1" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="39947791728774">{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1692059736502-child{{forloop.index}}-0" data-id="1692059736502-child{{forloop.index}}-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1692059736502-child{{forloop.index}}-1" data-id="1692059736502-child{{forloop.index}}-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1692059736502-child{{forloop.index}}-2" data-id="1692059736502-child{{forloop.index}}-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1692059736502-child{{forloop.index}}-3" data-id="1692059736502-child{{forloop.index}}-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div>{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-xs"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-sm"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-md"></div>{% endif %}{% assign modulo = gPLCount | modulo: 4 %}{% if modulo == 0 %}<div class="gf_clearfix visible-lg"></div>{% endif %}{% assign gPLCount = gPLCount | plus: 1 %}{% endfor %}{% endpaginate %}</div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692234522657" class="gf_row" data-icon="gpicon-row" data-id="1692234522657"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692233924802" data-id="1692233924802"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1692234522582" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1692234522582"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/polo-shirts-australia-aboriginal-art-clothes?pf_t_style=TYPE_Polos+%28Long+Sleeve+Unisex%29" target="" data-scroll-speed="2000" data-exc="" aria-label="Go to https://www.yarn.com.au/collections/polo-shirts-australia-aboriginal-art-clothes?pf_t_style=TYPE_Polos+%28Long+Sleeve+Unisex%29"><span>SHOP ALL</span></a></div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692060281218" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692060281218" data-extraclass="" style="display: block;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058717436" data-id="1692058717436"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1692060281244" class="module-wrap" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1692060281244"><div class="module " data-image="https://ucarecdn.com/293eaffc-9b26-4278-88bb-87414517b037/-/format/auto/-/preview/3000x3000/-/quality/lighter/Father_s%20day%20gift%20page-12.png" data-image-lg="https://ucarecdn.com/293eaffc-9b26-4278-88bb-87414517b037/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-12.png" data-image-md="https://ucarecdn.com/293eaffc-9b26-4278-88bb-87414517b037/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-12.png" data-image-sm="https://ucarecdn.com/293eaffc-9b26-4278-88bb-87414517b037/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-12.png" data-image-xs="https://ucarecdn.com/293eaffc-9b26-4278-88bb-87414517b037/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-12.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692060281262" class="gf_row" data-icon="gpicon-row" data-id="1692060281262"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058951094" data-id="1692058951094"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692060281302" class="gf_row" data-icon="gpicon-row" data-id="1692060281302"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058973857" data-id="1692058973857"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1692060281227" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1692060281227"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Mugs</h1></div></div></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692576660557" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692576660557" data-extraclass="" style="display: block;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058717436" data-id="1692058717436"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1692576660659" class="module-wrap" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1692576660659"><div class="module " data-image="https://ucarecdn.com/6f470860-6479-458a-87f4-81924ba762b0/-/format/auto/-/preview/3000x3000/-/quality/lighter/Father_s%20day%20gift%20page-20.png" data-image-lg="https://ucarecdn.com/293eaffc-9b26-4278-88bb-87414517b037/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-12.png" data-image-md="https://ucarecdn.com/293eaffc-9b26-4278-88bb-87414517b037/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-12.png" data-image-sm="https://ucarecdn.com/293eaffc-9b26-4278-88bb-87414517b037/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-12.png" data-image-xs="https://ucarecdn.com/6f470860-6479-458a-87f4-81924ba762b0/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-20.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692576660630" class="gf_row" data-icon="gpicon-row" data-id="1692576660630"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058951094" data-id="1692058951094"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692576660599" class="gf_row" data-icon="gpicon-row" data-id="1692576660599"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058973857" data-id="1692058973857"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1692576660635" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1692576660635"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Mugs</h1></div></div></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692060083927" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692060083927" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1691646750849" data-id="1691646750849"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692060083934" class="gf_row" data-icon="gpicon-row" data-id="1692060083934"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1691646761010" data-id="1691646761010"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1692060083920" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1692060083920"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>Ceramic mugs locally printed in our Meanjin (Brisbane) Studio.</p></div></div><div data-label="Product List" data-key="product-list" data-atomgroup="product-list" id="m-1692060083989" class="module-wrap" data-icon="gpicon-product-list" data-ver="1.0" data-id="1692060083989" style="min-height: auto;"><div class="module " data-cid="269902053510" data-chandle="fathers-day-mugs" data-limit="8" data-collg="4" data-colmd="3" data-colsm="3" data-colxs="2"><div class="item-content item-content-no-placeholder"><div class="gf_row gf_row-no-width gf_row_no_tools gf_row-no-padding">{% assign colXsClass = ' gf_col-xs-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colXsClass = colXsClass | append:cols %}{% assign colSmClass = ' gf_col-sm-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colSmClass = colSmClass | append:cols %}{% assign colMdClass = ' gf_col-md-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colMdClass = colMdClass | append:cols %}{% assign colLgClass = ' gf_col-lg-' %}{% assign cols = 12 | divided_by: 4 %}{% assign colLgClass = colLgClass | append:cols %}{% assign colClass = 'gf_column gf_col_no_tools' %}{% assign colClass = colClass | append: colXsClass %}{% assign colClass = colClass | append: colSmClass %}{% assign colClass = colClass | append: colMdClass %}{% assign colClass = colClass | append: colLgClass %}{% assign gPLCount = 1 %}{% paginate collections["fathers-day-mugs"].products by 8 %}{% for product in collections["fathers-day-mugs"].products %}<div class="{{colClass}}" style="padding: 20px !important"><div data-label="Product" data-key="product" id="m-1692060083989-child{{forloop.index}}" data-ver="3.1" class="module-wrap" data-id="1692060083989-child{{forloop.index}}" data-index="1" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="39669991440518">{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1692060083989-child{{forloop.index}}-0" data-id="1692060083989-child{{forloop.index}}-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1692060083989-child{{forloop.index}}-1" data-id="1692060083989-child{{forloop.index}}-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1692060083989-child{{forloop.index}}-2" data-id="1692060083989-child{{forloop.index}}-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1692060083989-child{{forloop.index}}-3" data-id="1692060083989-child{{forloop.index}}-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div>{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-xs"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-sm"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-md"></div>{% endif %}{% assign modulo = gPLCount | modulo: 4 %}{% if modulo == 0 %}<div class="gf_clearfix visible-lg"></div>{% endif %}{% assign gPLCount = gPLCount | plus: 1 %}{% endfor %}{% endpaginate %}</div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692234646460" class="gf_row" data-icon="gpicon-row" data-id="1692234646460"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692233924802" data-id="1692233924802"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1692234646532" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1692234646532"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/drinkware?pf_t_style=TYPE_Coffee+Mugs+%28Ceramic%29" target="" data-scroll-speed="2000" data-exc="" aria-label="Go to https://www.yarn.com.au/collections/drinkware?pf_t_style=TYPE_Coffee+Mugs+%28Ceramic%29"><span>SHOP ALL</span></a></div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692059909093" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692059909093" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058717436" data-id="1692058717436"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1692059909149" class="module-wrap" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1692059909149"><div class="module " data-image="https://ucarecdn.com/7b562165-f920-4687-85e1-c1a31b57a074/-/format/auto/-/preview/3000x3000/-/quality/lighter/Father_s%20day%20gift%20page-15.png" data-image-lg="https://ucarecdn.com/7b562165-f920-4687-85e1-c1a31b57a074/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-15.png" data-image-md="https://ucarecdn.com/7b562165-f920-4687-85e1-c1a31b57a074/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-15.png" data-image-sm="https://ucarecdn.com/7b562165-f920-4687-85e1-c1a31b57a074/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-15.png" data-image-xs="https://ucarecdn.com/7b562165-f920-4687-85e1-c1a31b57a074/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-15.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692059909060" class="gf_row" data-icon="gpicon-row" data-id="1692059909060"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058951094" data-id="1692058951094"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692059909118" class="gf_row" data-icon="gpicon-row" data-id="1692059909118"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058973857" data-id="1692058973857"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1692059909124" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1692059909124"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Tees</h1></div></div></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692576699991" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692576699991" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058717436" data-id="1692058717436"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1692576699986" class="module-wrap" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1692576699986"><div class="module " data-image="https://ucarecdn.com/0bf5ada0-1624-4917-a643-ef119c0f36a0/-/format/auto/-/preview/3000x3000/-/quality/lighter/Father_s%20day%20gift%20page-21.png" data-image-lg="https://ucarecdn.com/7b562165-f920-4687-85e1-c1a31b57a074/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-15.png" data-image-md="https://ucarecdn.com/7b562165-f920-4687-85e1-c1a31b57a074/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-15.png" data-image-sm="https://ucarecdn.com/7b562165-f920-4687-85e1-c1a31b57a074/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-15.png" data-image-xs="https://ucarecdn.com/0bf5ada0-1624-4917-a643-ef119c0f36a0/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-21.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692576700041" class="gf_row" data-icon="gpicon-row" data-id="1692576700041"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058951094" data-id="1692058951094"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692576699950" class="gf_row" data-icon="gpicon-row" data-id="1692576699950"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058973857" data-id="1692058973857"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1692576699980" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1692576699980"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Tees</h1></div></div></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692060374545" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692060374545" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1691646750849" data-id="1691646750849"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692060374534" class="gf_row" data-icon="gpicon-row" data-id="1692060374534"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1691646761010" data-id="1691646761010"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1692060374478" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1692060374478"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>100% cotton locally printed in our studio in Meanjin.</p></div></div><div data-label="Product List" data-key="product-list" data-atomgroup="product-list" id="m-1692060374511" class="module-wrap" data-icon="gpicon-product-list" data-ver="1.0" data-id="1692060374511" style="min-height: auto;"><div class="module " data-cid="269902151814" data-chandle="fathers-day-tees" data-limit="8" data-collg="4" data-colmd="3" data-colsm="3" data-colxs="2"><div class="item-content item-content-no-placeholder"><div class="gf_row gf_row-no-width gf_row_no_tools gf_row-no-padding">{% assign colXsClass = ' gf_col-xs-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colXsClass = colXsClass | append:cols %}{% assign colSmClass = ' gf_col-sm-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colSmClass = colSmClass | append:cols %}{% assign colMdClass = ' gf_col-md-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colMdClass = colMdClass | append:cols %}{% assign colLgClass = ' gf_col-lg-' %}{% assign cols = 12 | divided_by: 4 %}{% assign colLgClass = colLgClass | append:cols %}{% assign colClass = 'gf_column gf_col_no_tools' %}{% assign colClass = colClass | append: colXsClass %}{% assign colClass = colClass | append: colSmClass %}{% assign colClass = colClass | append: colMdClass %}{% assign colClass = colClass | append: colLgClass %}{% assign gPLCount = 1 %}{% paginate collections["fathers-day-tees"].products by 8 %}{% for product in collections["fathers-day-tees"].products %}<div class="{{colClass}}" style="padding: 20px !important"><div data-label="Product" data-key="product" id="m-1692060374511-child{{forloop.index}}" data-ver="3.1" class="module-wrap" data-id="1692060374511-child{{forloop.index}}" data-index="1" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="39669987967110">{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1692060374511-child{{forloop.index}}-0" data-id="1692060374511-child{{forloop.index}}-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1692060374511-child{{forloop.index}}-1" data-id="1692060374511-child{{forloop.index}}-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1692060374511-child{{forloop.index}}-2" data-id="1692060374511-child{{forloop.index}}-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1692060374511-child{{forloop.index}}-3" data-id="1692060374511-child{{forloop.index}}-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div>{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-xs"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-sm"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-md"></div>{% endif %}{% assign modulo = gPLCount | modulo: 4 %}{% if modulo == 0 %}<div class="gf_clearfix visible-lg"></div>{% endif %}{% assign gPLCount = gPLCount | plus: 1 %}{% endfor %}{% endpaginate %}</div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692234744672" class="gf_row" data-icon="gpicon-row" data-id="1692234744672"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692233924802" data-id="1692233924802"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1692234744685" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1692234744685"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/mens-t-shirts-graphic-printed-aboriginal-art-clothes" target="" data-scroll-speed="2000" data-exc="" aria-label="Go to https://www.yarn.com.au/collections/mens-t-shirts-graphic-printed-aboriginal-art-clothes"><span>SHOP ALL</span></a></div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692060422978" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692060422978" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058717436" data-id="1692058717436"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1692060422971" class="module-wrap" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1692060422971"><div class="module " data-image="https://ucarecdn.com/a8448858-0d8f-4d66-9066-0253b6c88413/-/format/auto/-/preview/3000x3000/-/quality/lighter/Father_s%20day%20gift%20page-10.png" data-image-lg="https://ucarecdn.com/a8448858-0d8f-4d66-9066-0253b6c88413/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-10.png" data-image-md="https://ucarecdn.com/a8448858-0d8f-4d66-9066-0253b6c88413/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-10.png" data-image-sm="https://ucarecdn.com/a8448858-0d8f-4d66-9066-0253b6c88413/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-10.png" data-image-xs="https://ucarecdn.com/a8448858-0d8f-4d66-9066-0253b6c88413/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-10.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692060422940" class="gf_row" data-icon="gpicon-row" data-id="1692060422940"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058951094" data-id="1692058951094"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692060422991" class="gf_row" data-icon="gpicon-row" data-id="1692060422991"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058973857" data-id="1692058973857"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1692060422968" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1692060422968"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Headwear</h1></div></div></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692576792911" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692576792911" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058717436" data-id="1692058717436"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1692576792904" class="module-wrap" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1692576792904"><div class="module " data-image="https://ucarecdn.com/2f1b0b42-5c8c-40d1-837b-c106bf337272/-/format/auto/-/preview/3000x3000/-/quality/lighter/Father_s%20day%20gift%20page-22.png" data-image-lg="https://ucarecdn.com/a8448858-0d8f-4d66-9066-0253b6c88413/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-10.png" data-image-md="https://ucarecdn.com/a8448858-0d8f-4d66-9066-0253b6c88413/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-10.png" data-image-sm="https://ucarecdn.com/a8448858-0d8f-4d66-9066-0253b6c88413/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-10.png" data-image-xs="https://ucarecdn.com/2f1b0b42-5c8c-40d1-837b-c106bf337272/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-22.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692576792985" class="gf_row" data-icon="gpicon-row" data-id="1692576792985"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058951094" data-id="1692058951094"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692576792961" class="gf_row" data-icon="gpicon-row" data-id="1692576792961"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058973857" data-id="1692058973857"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1692576792951" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1692576792951"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Headwear</h1></div></div></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692060487488" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692060487488" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1691646750849" data-id="1691646750849"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692060487539" class="gf_row" data-icon="gpicon-row" data-id="1692060487539"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1691646761010" data-id="1691646761010"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1692060487553" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1692060487553"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>Hats to keep dad cool in the sun.</p></div></div><div data-label="Product List" data-key="product-list" data-atomgroup="product-list" id="m-1692060487535" class="module-wrap" data-icon="gpicon-product-list" data-ver="1.0" data-id="1692060487535" style="min-height: auto;"><div class="module " data-cid="269902184582" data-chandle="fathers-day-headwear" data-limit="8" data-collg="4" data-colmd="3" data-colsm="3" data-colxs="2"><div class="item-content item-content-no-placeholder"><div class="gf_row gf_row-no-width gf_row_no_tools gf_row-no-padding">{% assign colXsClass = ' gf_col-xs-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colXsClass = colXsClass | append:cols %}{% assign colSmClass = ' gf_col-sm-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colSmClass = colSmClass | append:cols %}{% assign colMdClass = ' gf_col-md-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colMdClass = colMdClass | append:cols %}{% assign colLgClass = ' gf_col-lg-' %}{% assign cols = 12 | divided_by: 4 %}{% assign colLgClass = colLgClass | append:cols %}{% assign colClass = 'gf_column gf_col_no_tools' %}{% assign colClass = colClass | append: colXsClass %}{% assign colClass = colClass | append: colSmClass %}{% assign colClass = colClass | append: colMdClass %}{% assign colClass = colClass | append: colLgClass %}{% assign gPLCount = 1 %}{% paginate collections["fathers-day-headwear"].products by 8 %}{% for product in collections["fathers-day-headwear"].products %}<div class="{{colClass}}" style="padding: 20px !important"><div data-label="Product" data-key="product" id="m-1692060487535-child{{forloop.index}}" data-ver="3.1" class="module-wrap" data-id="1692060487535-child{{forloop.index}}" data-index="1" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="40159097094278">{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1692060487535-child{{forloop.index}}-0" data-id="1692060487535-child{{forloop.index}}-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1692060487535-child{{forloop.index}}-1" data-id="1692060487535-child{{forloop.index}}-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1692060487535-child{{forloop.index}}-2" data-id="1692060487535-child{{forloop.index}}-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1692060487535-child{{forloop.index}}-3" data-id="1692060487535-child{{forloop.index}}-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div>{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-xs"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-sm"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-md"></div>{% endif %}{% assign modulo = gPLCount | modulo: 4 %}{% if modulo == 0 %}<div class="gf_clearfix visible-lg"></div>{% endif %}{% assign gPLCount = gPLCount | plus: 1 %}{% endfor %}{% endpaginate %}</div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692234797971" class="gf_row" data-icon="gpicon-row" data-id="1692234797971"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692233924802" data-id="1692233924802"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1692234797906" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1692234797906"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/headwear-1?pf_t_style=TYPE_Beanies&pf_t_style=TYPE_Caps&pf_t_style=TYPE_Fishing+Snoods&pf_t_style=TYPE_Hats+%28Bucket%29" target="" data-scroll-speed="2000" data-exc="" aria-label="Go to https://www.yarn.com.au/collections/headwear-1?pf_t_style=TYPE_Beanies&pf_t_style=TYPE_Caps&pf_t_style=TYPE_Fishing+Snoods&pf_t_style=TYPE_Hats+%28Bucket%29"><span>SHOP ALL</span></a></div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692060526164" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692060526164" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058717436" data-id="1692058717436"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1692060526195" class="module-wrap" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1692060526195"><div class="module " data-image="https://ucarecdn.com/4d0944e6-8090-45bb-9466-56bd42486121/-/format/auto/-/preview/3000x3000/-/quality/lighter/Father_s%20day%20gift%20page-14.png" data-image-lg="https://ucarecdn.com/4d0944e6-8090-45bb-9466-56bd42486121/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-14.png" data-image-md="https://ucarecdn.com/4d0944e6-8090-45bb-9466-56bd42486121/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-14.png" data-image-sm="https://ucarecdn.com/4d0944e6-8090-45bb-9466-56bd42486121/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-14.png" data-image-xs="https://ucarecdn.com/4d0944e6-8090-45bb-9466-56bd42486121/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-14.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692060526218" class="gf_row" data-icon="gpicon-row" data-id="1692060526218"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058951094" data-id="1692058951094"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692060526178" class="gf_row" data-icon="gpicon-row" data-id="1692060526178"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058973857" data-id="1692058973857"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1692060526156" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1692060526156"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Accessories</h1></div></div></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692576834299" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692576834299" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058717436" data-id="1692058717436"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1692576834358" class="module-wrap" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1692576834358"><div class="module " data-image="https://ucarecdn.com/40288e44-0bb5-40f0-be69-291cf5aaa041/-/format/auto/-/preview/3000x3000/-/quality/lighter/Father_s%20day%20gift%20page-23.png" data-image-lg="https://ucarecdn.com/4d0944e6-8090-45bb-9466-56bd42486121/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-14.png" data-image-md="https://ucarecdn.com/4d0944e6-8090-45bb-9466-56bd42486121/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-14.png" data-image-sm="https://ucarecdn.com/4d0944e6-8090-45bb-9466-56bd42486121/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-14.png" data-image-xs="https://ucarecdn.com/40288e44-0bb5-40f0-be69-291cf5aaa041/-/format/auto/-/preview/3000x3000/-/quality/lighter/father_s%20day%20gift%20page-23.png" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692576834234" class="gf_row" data-icon="gpicon-row" data-id="1692576834234"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058951094" data-id="1692058951094"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692576834293" class="gf_row" data-icon="gpicon-row" data-id="1692576834293"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692058973857" data-id="1692058973857"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1692576834326" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1692576834326"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Accessories</h1></div></div></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692060553466" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692060553466" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1691646750849" data-id="1691646750849"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692060553374" class="gf_row" data-icon="gpicon-row" data-id="1692060553374"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1691646761010" data-id="1691646761010"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1692060553435" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1692060553435"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>Practical pieces for Dad this Father's Day.</p></div></div><div data-label="Product List" data-key="product-list" data-atomgroup="product-list" id="m-1692060553428" class="module-wrap" data-icon="gpicon-product-list" data-ver="1.0" data-id="1692060553428" style="min-height: auto;"><div class="module " data-cid="269902217350" data-chandle="fathers-day-accessories" data-limit="8" data-collg="4" data-colmd="3" data-colsm="3" data-colxs="2"><div class="item-content item-content-no-placeholder"><div class="gf_row gf_row-no-width gf_row_no_tools gf_row-no-padding">{% assign colXsClass = ' gf_col-xs-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colXsClass = colXsClass | append:cols %}{% assign colSmClass = ' gf_col-sm-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colSmClass = colSmClass | append:cols %}{% assign colMdClass = ' gf_col-md-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colMdClass = colMdClass | append:cols %}{% assign colLgClass = ' gf_col-lg-' %}{% assign cols = 12 | divided_by: 4 %}{% assign colLgClass = colLgClass | append:cols %}{% assign colClass = 'gf_column gf_col_no_tools' %}{% assign colClass = colClass | append: colXsClass %}{% assign colClass = colClass | append: colSmClass %}{% assign colClass = colClass | append: colMdClass %}{% assign colClass = colClass | append: colLgClass %}{% assign gPLCount = 1 %}{% paginate collections["fathers-day-accessories"].products by 8 %}{% for product in collections["fathers-day-accessories"].products %}<div class="{{colClass}}" style="padding: 20px !important"><div data-label="Product" data-key="product" id="m-1692060553428-child{{forloop.index}}" data-ver="3.1" class="module-wrap" data-id="1692060553428-child{{forloop.index}}" data-index="1" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="40159097127046">{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1692060553428-child{{forloop.index}}-0" data-id="1692060553428-child{{forloop.index}}-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1692060553428-child{{forloop.index}}-1" data-id="1692060553428-child{{forloop.index}}-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1692060553428-child{{forloop.index}}-2" data-id="1692060553428-child{{forloop.index}}-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1692060553428-child{{forloop.index}}-3" data-id="1692060553428-child{{forloop.index}}-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div>{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-xs"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-sm"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-md"></div>{% endif %}{% assign modulo = gPLCount | modulo: 4 %}{% if modulo == 0 %}<div class="gf_clearfix visible-lg"></div>{% endif %}{% assign gPLCount = gPLCount | plus: 1 %}{% endfor %}{% endpaginate %}</div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692234864147" class="gf_row" data-icon="gpicon-row" data-id="1692234864147"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692233924802" data-id="1692233924802"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1692234864089" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1692234864089"><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/fathers-day-accessories" target="" data-scroll-speed="2000" data-exc="" aria-label="Go to https://www.yarn.com.au/collections/fathers-day-accessories"><span>SHOP ALL</span></a></div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692229109308" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1692229109308" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692229109325" data-id="1692229109325"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692229133709" class="gf_row" data-icon="gpicon-row" data-id="1692229133709" data-layout-lg="4+4+4" data-extraclass="" data-layout-md="4+4+4" data-layout-sm="12+12+12" data-layout-xs="12+12+12"><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-12 gf_col-xs-12" id="c-1692229133665" data-id="1692229133665"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1692229146448" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1692229146448"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Gift by Price&nbsp;</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1692229247818" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1692229247818"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>A selection of gifts everyone will love at prices you'll love too.</p></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692229324199" class="gf_row" data-icon="gpicon-row" data-id="1692229324199" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="12+12"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1692229324181" data-id="1692229324181"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1692229331507" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1692229331507"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p><u><a href="https://www.yarn.com.au/collections/gifts-under-50" class="" aria-label="Go to https://www.yarn.com.au/collections/gifts-under-50"><strong>Gifts Under $50</strong></a></u></p></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1692229325318" data-id="1692229325318"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1692229390629" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1692229390629"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p><u><a href="https://www.yarn.com.au/collections/gifts-under-30" class="" aria-label="Go to https://www.yarn.com.au/collections/gifts-under-30"><strong>Gifts Under $30</strong></a></u></p></div></div></div></div></div><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-12 gf_col-xs-12" id="c-1692229138158" data-id="1692229138158"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1692229209575" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1692229209575"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Gift Bundles</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1692229281580" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1692229281580"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>Take the guesswork out of gifting with a our curated gifting bundles.</p><p><br></p><p></p></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692229450734" class="gf_row" data-icon="gpicon-row" data-id="1692229450734" data-layout-lg="12" data-extraclass="" data-layout-md="12" data-layout-sm="12" data-layout-xs="12"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692229324181" data-id="1692229324181"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1692229450677" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1692229450677"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p><u><strong><a href="https://www.yarn.com.au/collections/gift-bundles" class="" aria-label="Go to https://www.yarn.com.au/collections/gift-bundles">Gifts Bundles</a></strong></u></p></div></div></div></div></div><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-12 gf_col-xs-12" id="c-1692229138150" data-id="1692229138150"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1692229214029" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1692229214029"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Polos</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1692229287355" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1692229287355"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>Our most popular product. In fitted, unisex and kids sizes.</p><p><br></p><p></p></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1692229569785" class="gf_row" data-icon="gpicon-row" data-id="1692229569785" data-layout-lg="12" data-extraclass="" data-layout-md="12" data-layout-sm="12" data-layout-xs="12"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1692229324181" data-id="1692229324181"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1692229569849" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1692229569849"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p><b><u><a href="https://www.yarn.com.au/collections/polo-shirts-australia-aboriginal-art-clothes" class="" aria-label="Go to https://www.yarn.com.au/collections/polo-shirts-australia-aboriginal-art-clothes">Shop Polos</a></u></b></p></div></div></div></div></div></div></div></div><!--gfsplit--><script>window.__gemStoreData = {};window.__gemStoreData.gemKeyValid = {};window.__gemStoreData.gemKeyValid.bestWayAddToCart = 1;</script></div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		"https://d1um8515vdn9kb.cloudfront.net/libs/js/owl.carousel.min.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv3product.js",
		 "https://www.youtube.com/player_api",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv2herobanner.js",
		'{{ 'gem-page-***********.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->