{% comment %}
	GEMPAGES BUILDER (https://apps.shopify.com/gempages)

	You SHOULD NOT modify source code in this file because
	It is automatically generated from GEMPAGES BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->


<link rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/libs/css/fontawesome-4.6.3.1.min.css" class="gf-style">
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-page-85002485894.css' | asset_url }}" class="gf_page_style">
<link data-instant-track class="gf_fonts" data-fonts="Gochi Hand" href="//fonts.googleapis.com/css2?family=Gochi Hand:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant Garamond" href="//fonts.googleapis.com/css2?family=Cormorant Garamond:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant" href="//fonts.googleapis.com/css2?family=Cormorant:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Bebas Neue" href="//fonts.googleapis.com/css2?family=Bebas Neue:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Anton" href="//fonts.googleapis.com/css2?family=Anton:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Aladin" href="//fonts.googleapis.com/css2?family=Aladin:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Acme" href="//fonts.googleapis.com/css2?family=Acme:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Alfa Slab One" href="//fonts.googleapis.com/css2?family=Alfa Slab One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Allan" href="//fonts.googleapis.com/css2?family=Allan:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Boogaloo" href="//fonts.googleapis.com/css2?family=Boogaloo:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Dancing Script" href="//fonts.googleapis.com/css2?family=Dancing Script:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Albert Sans" href="//fonts.googleapis.com/css2?family=Albert Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Dela Gothic One" href="//fonts.googleapis.com/css2?family=Dela Gothic One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Gasoek One" href="//fonts.googleapis.com/css2?family=Gasoek One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track rel="stylesheet" type="text/css" href= "https://d1um8515vdn9kb.cloudfront.net/libs/css/owl.carousel.min.css" class="gf_libs">
<!--GEM_HEADER_END-->
<!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor"><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1680743660181" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1680743660181" style="display: block;"><div class="module main-slider owl-carousel owl-theme " data-collg="1" data-colmd="1" data-colsm="1" data-colxs="1" data-marginlg="5px" data-marginmd="5px" data-marginsm="5px" data-marginxs="5px" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="0" data-navmd="0" data-navsm="0" data-navxs="0" data-navspeed="2500" data-autoplay="1" data-autoplaytimeout="8000" data-autoplayhoverpause="0" data-loop="1"><div class="item"><div data-index="1" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1693969161917" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1693969161917" data-resolution="3000x3000" style=""><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/69c9e8a2-639c-4b12-88ca-cd0a3ad01712/-/format/auto/-/preview/3000x3000/-/quality/lighter/Polos.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="3000" height="938" natural-width="3000" natural-height="938"></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div><!--gfsplit--><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1694563464622" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1694563464622"><div class="module main-slider owl-carousel owl-theme " data-collg="7" data-colmd="6" data-colsm="6" data-colxs="1" data-marginlg="0px" data-marginmd="0px" data-marginsm="0px" data-marginxs="5px" data-dotslg="0" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="0" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="0" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="0"><div class="item"><div data-index="1" class="item-content"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1694571003179" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1694571003179"><div class="elm gf-elm-center gf-elm-center-sm gf-elm-center-xs gf-elm-center-md gf-elm-center-lg" data-stretch-lg="1" data-stretch-md="1"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/pages/yarn-corp" target="" data-scroll-speed="2000" data-exc="" data-scroll-speed-md="2000"><span>Home</span></a></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1694570954848" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1694570954848"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-lg="1" data-stretch-md="1" data-stretch-sm="1"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/pages/yarn-corp-about" target="" data-scroll-speed="2000" data-exc="" data-scroll-speed-md="2000" data-scroll-speed-sm="2000"><span>About Yarn Corp</span></a></div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1694570870110" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1694570870110"><div class="elm gf-elm-center gf-elm-center-sm gf-elm-center-xs gf-elm-center-md gf-elm-center-lg" data-stretch-lg="1" data-stretch-md="1"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/pages/yarn-corp-standard-polos" target="" data-scroll-speed="2000" data-exc="" data-scroll-speed-md="2000"><span>Polos</span></a></div></div></div></div><div class="item"><div data-index="4" class="item-content"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1707456270276" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1707456270276"><div class="elm gf-elm-center gf-elm-center-sm gf-elm-center-xs gf-elm-center-md gf-elm-center-lg" data-stretch-lg="1" data-stretch-md="1"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/pages/yarn-corp-naidoc-2024" target="" data-scroll-speed="2000" data-exc="" data-scroll-speed-md="2000"><span>NAIDOC 2024</span></a></div></div></div></div><div class="item"><div data-index="5" class="item-content"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1680656128972" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1680656128972"><div class="elm gf-elm-center gf-elm-center-sm gf-elm-center-xs gf-elm-center-md gf-elm-center-lg" data-stretch-lg="0" data-stretch-md="1"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/pages/yarn-corp-scrubs" target="" data-scroll-speed="2000" data-exc="" data-scroll-speed-md="2000"><span>Scrubs</span></a></div></div></div></div><div class="item"><div data-index="6" class="item-content"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1694571042694" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1694571042694"><div class="elm gf-elm-center gf-elm-center-sm gf-elm-center-xs gf-elm-center-md gf-elm-center-lg" data-stretch-lg="1" data-stretch-md="1"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/pages/yarn-corp-hi-vis-workwear" target="" data-scroll-speed="2000" data-exc="" data-scroll-speed-md="2000"><span>HI VIS Workwear</span></a></div></div></div></div><div class="item"><div data-index="7" class="item-content"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1694571064927" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1694571064927"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-lg="1" data-stretch-md="1"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/pages/yarn-corp-design-your-own" target="" data-scroll-speed="2000" data-exc="" data-scroll-speed-md="2000"><span>Design Your Polos</span></a></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div><!--gfsplit--><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1707182398677" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1707182398677"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">CLASSIC CONTRAST POLOS</h1></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1695617912305" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1695617912305" data-layout-lg="12" data-extraclass="" data-layout-md="12" data-layout-sm="12" data-layout-xs="12"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1695616301163" data-id="1695616301163"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1695790305222" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1695790305222" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/9b8f668e-66cf-4452-a1a9-d14c00a3307f/-/format/auto/-/preview/3000x3000/-/quality/lighter/Mobile-7.jpg" alt="Yarn Marketplace image--25" class="gf_image" data-gemlang="en" width="2880" height="2250" data-width="100%" data-height="auto" title="" natural-width="2880" natural-height="2250" loading="lazy"></div></div><div data-label="Product List" data-key="product-list" data-atomgroup="product-list" id="m-1695617912387" class="module-wrap" data-icon="gpicon-product-list" data-ver="1.0" data-id="1695617912387" style="min-height: auto;"><div class="module " data-cid="270023753862" data-chandle="yarn-corporate-all-custom-bamboo-classic-polo-for-gempages" data-limit="30" data-collg="4" data-colmd="3" data-colsm="3" data-colxs="2"><div class="item-content item-content-no-placeholder"><div class="gf_row gf_row-no-width gf_row_no_tools gf_row-no-padding">{% assign colXsClass = ' gf_col-xs-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colXsClass = colXsClass | append:cols %}{% assign colSmClass = ' gf_col-sm-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colSmClass = colSmClass | append:cols %}{% assign colMdClass = ' gf_col-md-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colMdClass = colMdClass | append:cols %}{% assign colLgClass = ' gf_col-lg-' %}{% assign cols = 12 | divided_by: 4 %}{% assign colLgClass = colLgClass | append:cols %}{% assign colClass = 'gf_column gf_col_no_tools' %}{% assign colClass = colClass | append: colXsClass %}{% assign colClass = colClass | append: colSmClass %}{% assign colClass = colClass | append: colMdClass %}{% assign colClass = colClass | append: colLgClass %}{% assign gPLCount = 1 %}{% paginate collections["yarn-corporate-all-custom-bamboo-classic-polo-for-gempages"].products by 30 %}{% for product in collections["yarn-corporate-all-custom-bamboo-classic-polo-for-gempages"].products %}<div class="{{colClass}}" style="padding: 20px !important"><div data-label="Product" data-key="product" id="m-1695617912387-child{{forloop.index}}" data-ver="3.1" class="module-wrap" data-id="1695617912387-child{{forloop.index}}" data-index="1" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="40178272632966" style="">{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1695617912387-child{{forloop.index}}-0" data-id="1695617912387-child{{forloop.index}}-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="90%" data-height="auto" style="width: 90%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="90%" data-height="auto" style="width: 90%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="90%" data-height="auto" style="width: 90%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="90%" data-height="auto" style="width: 90%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="90%" data-height="auto" style="width: 90%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1695617912387-child{{forloop.index}}-1" data-id="1695617912387-child{{forloop.index}}-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1695617912387-child{{forloop.index}}-7" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1695617912387-child{{forloop.index}}-7"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>From $50.00</p></div></div><div data-label="(P) View More" data-key="p-view-more" data-atomgroup="child-product" id="m-1695617912387-child{{forloop.index}}-12" class="module-wrap" data-icon="gpicon-product-cartbutton" data-ver="1.0" data-id="1695617912387-child{{forloop.index}}-12"><div data-phandle="{{product.handle}}" class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '0' == '1' %}{% assign tg = '_blank' %}{% else %}{% assign tg = '' %}{% endif %}{% assign current_variant = product.selected_or_first_available_variant %}{% if '' != '' %}<a href="{{ product.url }}?variant={{current_variant.id}}" target="{{tg}}" class="gf_view-more  gf_gs-button-view-more gf_gs-button---large"><img src="" alt=""></a>{% else %}<a href="{{ product.url }}?variant={{current_variant.id}}" target="{{tg}}" class="gf_view-more button btn  gf_gs-button-view-more gf_gs-button---large"><span>Add Your Logo</span></a>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div>{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-xs"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-sm"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-md"></div>{% endif %}{% assign modulo = gPLCount | modulo: 4 %}{% if modulo == 0 %}<div class="gf_clearfix visible-lg"></div>{% endif %}{% assign gPLCount = gPLCount | plus: 1 %}{% endfor %}{% endpaginate %}</div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1695686461700" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1695686461700" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1695686461624" data-id="1695686461624"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1695686470270" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1695686470270"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">FULL COLOUR POLOS</h1></div></div><div data-label="Product List" data-key="product-list" data-atomgroup="product-list" id="m-1695686515453" class="module-wrap" data-icon="gpicon-product-list" data-ver="1.0" data-id="1695686515453" style="min-height: auto;"><div class="module " data-cid="************" data-chandle="yarn-corporate-all-custom-standard-polo-for-gempages" data-limit="30" data-collg="4" data-colmd="3" data-colsm="3" data-colxs="2"><div class="item-content item-content-no-placeholder"><div class="gf_row gf_row-no-width gf_row_no_tools gf_row-no-padding">{% assign colXsClass = ' gf_col-xs-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colXsClass = colXsClass | append:cols %}{% assign colSmClass = ' gf_col-sm-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colSmClass = colSmClass | append:cols %}{% assign colMdClass = ' gf_col-md-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colMdClass = colMdClass | append:cols %}{% assign colLgClass = ' gf_col-lg-' %}{% assign cols = 12 | divided_by: 4 %}{% assign colLgClass = colLgClass | append:cols %}{% assign colClass = 'gf_column gf_col_no_tools' %}{% assign colClass = colClass | append: colXsClass %}{% assign colClass = colClass | append: colSmClass %}{% assign colClass = colClass | append: colMdClass %}{% assign colClass = colClass | append: colLgClass %}{% assign gPLCount = 1 %}{% paginate collections["yarn-corporate-all-custom-standard-polo-for-gempages"].products by 30 %}{% for product in collections["yarn-corporate-all-custom-standard-polo-for-gempages"].products %}<div class="{{colClass}}" style="padding: 20px !important"><div data-label="Product" data-key="product" id="m-1695686515453-child{{forloop.index}}" data-ver="3.1" class="module-wrap" data-id="1695686515453-child{{forloop.index}}" data-index="1" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="40178271944838">{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1695686515453-child{{forloop.index}}-0" data-id="1695686515453-child{{forloop.index}}-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="90%" data-height="auto" style="width: 90%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="90%" data-height="auto" style="width: 90%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="90%" data-height="auto" style="width: 90%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="90%" data-height="auto" style="width: 90%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="90%" data-height="auto" style="width: 90%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1695686515453-child{{forloop.index}}-1" data-id="1695686515453-child{{forloop.index}}-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1695686515453-child{{forloop.index}}-7" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1695686515453-child{{forloop.index}}-7"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>From $45.00</p></div></div><div data-label="(P) View More" data-key="p-view-more" data-atomgroup="child-product" id="m-1695686515453-child{{forloop.index}}-10" class="module-wrap" data-icon="gpicon-product-cartbutton" data-ver="1.0" data-id="1695686515453-child{{forloop.index}}-10"><div data-phandle="{{product.handle}}" class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '0' == '1' %}{% assign tg = '_blank' %}{% else %}{% assign tg = '' %}{% endif %}{% assign current_variant = product.selected_or_first_available_variant %}{% if '' != '' %}<a href="{{ product.url }}?variant={{current_variant.id}}" target="{{tg}}" class="gf_view-more  gf_gs-button-view-more gf_gs-button---large"><img src="" alt=""></a>{% else %}<a href="{{ product.url }}?variant={{current_variant.id}}" target="{{tg}}" class="gf_view-more button btn  gf_gs-button-view-more gf_gs-button---large"><span>Add Your Logo</span></a>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div>{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-xs"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-sm"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-md"></div>{% endif %}{% assign modulo = gPLCount | modulo: 4 %}{% if modulo == 0 %}<div class="gf_clearfix visible-lg"></div>{% endif %}{% assign gPLCount = gPLCount | plus: 1 %}{% endfor %}{% endpaginate %}</div></div></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1707182462030" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1707182462030" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1695686461624" data-id="1695686461624"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1707182462037" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1707182462037"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">SIMPSON CONTRAST POLOS</h1></div></div><div data-label="Product List" data-key="product-list" data-atomgroup="product-list" id="m-1707182462031" class="module-wrap" data-icon="gpicon-product-list" data-ver="1.0" data-id="1707182462031" style="min-height: auto;"><div class="module " data-cid="************" data-chandle="yarn-corporate-all-custom-bamboo-simpson-polo-for-gempages" data-limit="30" data-collg="4" data-colmd="3" data-colsm="3" data-colxs="2"><div class="item-content item-content-no-placeholder"><div class="gf_row gf_row-no-width gf_row_no_tools gf_row-no-padding">{% assign colXsClass = ' gf_col-xs-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colXsClass = colXsClass | append:cols %}{% assign colSmClass = ' gf_col-sm-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colSmClass = colSmClass | append:cols %}{% assign colMdClass = ' gf_col-md-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colMdClass = colMdClass | append:cols %}{% assign colLgClass = ' gf_col-lg-' %}{% assign cols = 12 | divided_by: 4 %}{% assign colLgClass = colLgClass | append:cols %}{% assign colClass = 'gf_column gf_col_no_tools' %}{% assign colClass = colClass | append: colXsClass %}{% assign colClass = colClass | append: colSmClass %}{% assign colClass = colClass | append: colMdClass %}{% assign colClass = colClass | append: colLgClass %}{% assign gPLCount = 1 %}{% paginate collections["yarn-corporate-all-custom-bamboo-simpson-polo-for-gempages"].products by 30 %}{% for product in collections["yarn-corporate-all-custom-bamboo-simpson-polo-for-gempages"].products %}<div class="{{colClass}}" style="padding: 20px !important"><div data-label="Product" data-key="product" id="m-1707182462031-child{{forloop.index}}" data-ver="3.1" class="module-wrap" data-id="1707182462031-child{{forloop.index}}" data-index="1" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="40178273353862">{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1707182462031-child{{forloop.index}}-0" data-id="1707182462031-child{{forloop.index}}-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="90%" data-height="auto" style="width: 90%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="90%" data-height="auto" style="width: 90%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="90%" data-height="auto" style="width: 90%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="90%" data-height="auto" style="width: 90%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="90%" data-height="auto" style="width: 90%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1707182462031-child{{forloop.index}}-1" data-id="1707182462031-child{{forloop.index}}-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1707182462031-child{{forloop.index}}-7" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1707182462031-child{{forloop.index}}-7"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>From $50.00</p></div></div><div data-label="(P) View More" data-key="p-view-more" data-atomgroup="child-product" id="m-1707182462031-child{{forloop.index}}-10" class="module-wrap" data-icon="gpicon-product-cartbutton" data-ver="1.0" data-id="1707182462031-child{{forloop.index}}-10"><div data-phandle="{{product.handle}}" class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '0' == '1' %}{% assign tg = '_blank' %}{% else %}{% assign tg = '' %}{% endif %}{% assign current_variant = product.selected_or_first_available_variant %}{% if '' != '' %}<a href="{{ product.url }}?variant={{current_variant.id}}" target="{{tg}}" class="gf_view-more  gf_gs-button-view-more gf_gs-button---large"><img src="" alt=""></a>{% else %}<a href="{{ product.url }}?variant={{current_variant.id}}" target="{{tg}}" class="gf_view-more button btn  gf_gs-button-view-more gf_gs-button---large"><span>Add Your Logo</span></a>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</div></div></div>{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-xs"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-sm"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-md"></div>{% endif %}{% assign modulo = gPLCount | modulo: 4 %}{% if modulo == 0 %}<div class="gf_clearfix visible-lg"></div>{% endif %}{% assign gPLCount = gPLCount | plus: 1 %}{% endfor %}{% endpaginate %}</div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1695686567732" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1695686567732" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1695686567689" data-id="1695686567689"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1695686574041" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1695686574041" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/75e00b0b-bf39-4444-b95c-1f4e837feeef/-/format/auto/-/preview/3000x3000/-/quality/lighter/Header-12.jpg" alt="Yarn Marketplace image--37" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="3000" height="1172" natural-width="3000" natural-height="1172" loading="lazy"></div></div></div></div><!--gfsplit--><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1695790175726" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1695790175726" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/126565f7-af79-4c60-9aa6-b2fd901d3c25/-/format/auto/-/preview/3000x3000/-/quality/lighter/Mobile-3.jpg" alt="Yarn Marketplace image--63" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="2115" height="3000" natural-width="2115" natural-height="3000" loading="lazy"></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1694570710737" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1694570710737" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1694570710720" data-id="1694570710720"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1694575812008" class="gf_row" data-icon="gpicon-row" data-id="1694575812008" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="12+12"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1694575812063" data-id="1694575812063"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1694575799801" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1694575799801"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-left-lg" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">CONTACT US</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1694575849187" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1694575849187"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>Our sales team can provide you with expert advice and bring your concept to life! We have an extensive catalogue of products and artworks, so if you don’t see the perfect fit offered above contact for more options.</p><p></p><p></p><p></p><p></p></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1695885013516" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1695885013516"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p><strong>Email</strong>: <EMAIL></p><p><strong>Phone</strong>: 07 35062894</p><p><strong>Support Hours</strong>: 9am - 4pm AEST</p><p></p><p></p><p></p><p></p><p></p><p></p></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1694577307475" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1694577307475"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs gf_gs-text-heading-2" data-gemlang="en" data-exc=""></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1694583829817" data-id="1694583829817"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1695885040297" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1695885040297"><div class="elm text-edit gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-left-lg" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">MENU</h1></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1695885051883" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1695885051883"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/pages/yarn-corp" target="" data-scroll-speed="2000" data-exc="" aria-label="Go to https://www.yarn.com.au/pages/yarn-corp"><span>Home</span></a></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1695885101196" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1695885101196"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/pages/yarn-corp-about" target="" data-scroll-speed="2000" data-exc="" aria-label="Go to https://www.yarn.com.au/pages/yarn-corp-about"><span>About Yarn Corp</span></a></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1695885121097" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1695885121097"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/pages/yarn-corp-standard-polos" target="" data-scroll-speed="2000" data-exc="" aria-label="Go to https://www.yarn.com.au/pages/yarn-corp-standard-polos"><span>Standard Polos</span></a></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1695885140968" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1695885140968"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/pages/yarn-corp-classic-contrast" target="" data-scroll-speed="2000" data-exc="" aria-label="Go to https://www.yarn.com.au/pages/yarn-corp-classic-contrast"><span>Classic Contrast</span></a></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1695885158969" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1695885158969"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/pages/yarn-corp-simpson-contrast-polos" target="" data-scroll-speed="2000" data-exc="" aria-label="Go to https://www.yarn.com.au/pages/yarn-corp-simpson-contrast-polos"><span>Simpson Contrast</span></a></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1695885174867" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1695885174867"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/pages/yarn-corp-design-your-own" target="" data-scroll-speed="2000" data-exc="" aria-label="Go to https://www.yarn.com.au/pages/yarn-corp-design-your-own"><span>Design Your Polos</span></a></div></div></div></div></div></div><!--gfsplit--><script>window.__gemStoreData = {};window.__gemStoreData.gemKeyValid = {};window.__gemStoreData.gemKeyValid.bestWayAddToCart = 1;</script></div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		"https://d1um8515vdn9kb.cloudfront.net/libs/js/owl.carousel.min.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv3product.js",
		 "https://www.youtube.com/player_api",
		'{{ 'gem-page-85002485894.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->