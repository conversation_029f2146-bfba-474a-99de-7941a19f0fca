{% comment %}
	GEMPAGES BUILDER (https://apps.shopify.com/gempages)

	You SHOULD NOT modify source code in this file because
	It is automatically generated from GEMPAGES BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->


<link rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/libs/css/fontawesome-4.6.3.1.min.css" class="gf-style">
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-page-86363537542.css' | asset_url }}" class="gf_page_style">
<link data-instant-track class="gf_fonts" data-fonts="Gochi Hand" href="//fonts.googleapis.com/css2?family=Gochi Hand:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant Garamond" href="//fonts.googleapis.com/css2?family=Cormorant Garamond:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant" href="//fonts.googleapis.com/css2?family=Cormorant:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Bebas Neue" href="//fonts.googleapis.com/css2?family=Bebas Neue:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Anton" href="//fonts.googleapis.com/css2?family=Anton:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Aladin" href="//fonts.googleapis.com/css2?family=Aladin:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Acme" href="//fonts.googleapis.com/css2?family=Acme:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Alfa Slab One" href="//fonts.googleapis.com/css2?family=Alfa Slab One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Allan" href="//fonts.googleapis.com/css2?family=Allan:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Boogaloo" href="//fonts.googleapis.com/css2?family=Boogaloo:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Dancing Script" href="//fonts.googleapis.com/css2?family=Dancing Script:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Albert Sans" href="//fonts.googleapis.com/css2?family=Albert Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Dela Gothic One" href="//fonts.googleapis.com/css2?family=Dela Gothic One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Gasoek One" href="//fonts.googleapis.com/css2?family=Gasoek One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track rel="stylesheet" type="text/css" href= "https://d1um8515vdn9kb.cloudfront.net/libs/css/owl.carousel.min.css" class="gf_libs">
<!--GEM_HEADER_END-->
<!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor"><div data-label="Vimeo" data-key="vimeo" data-atomgroup="module" id="m-1704403099461" class="module-wrap" data-icon="gpicon-vimeo" data-ver="1" data-id="1704403099461"><div class="module " data-url="https://vimeo.com/899944555" data-autopause="1" data-autoplay="1" data-badge="" data-videoloop="1" data-byline="0" data-showtitle="0" data-showportrait="0" data-videomute="0"><div class="vimeo_video videoFullScreen"></div></div><div class="gf_vimeo-overlay"></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1704260685688" class="gf_row" data-icon="gpicon-row" data-id="1704260685688" data-layout-md="12" data-extraclass="" data-layout-sm="12" data-layout-xs="12"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1704260685717" data-id="1704260685717"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1704260695597" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1704260695597" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-lg gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/5715ded7-c9a3-4732-980c-28c69b6ed026/-/format/auto/-/preview/3000x3000/-/quality/lighter/country%20connection-18.png" alt="Yarn Marketplace image--28" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="3000" height="1477" natural-width="3000" natural-height="1477" loading="lazy"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1704260795923" class="gf_row" data-icon="gpicon-row" data-id="1704260795923" data-layout-lg="4+8" data-extraclass="" data-layout-md="4+8" data-layout-sm="4+8" data-layout-xs="12+12"><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-4 gf_col-xs-12" id="c-1704260795952" data-id="1704260795952"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1704260828070" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1704260828070" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/2758f6eb-1543-4d55-887d-687290772dfa/-/format/auto/-/preview/3000x3000/-/quality/lighter/country%20connection-10.jpg" alt="Yarn Marketplace image--29" class="gf_image" data-gemlang="en" width="927" height="1429" data-width="100%" data-height="auto" title="" natural-width="927" natural-height="1429" loading="lazy"></div></div></div><div class="gf_column gf_col-lg-8 gf_col-md-8 gf_col-sm-8 gf_col-xs-12" id="c-1704260823711" data-id="1704260823711"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1704260832447" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1704260832447" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/72312ad8-3847-4b44-9130-dfb5ccf8a973/-/format/auto/-/preview/3000x3000/-/quality/lighter/country%20connection-09.jpg" alt="Yarn Marketplace image--30" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="2252" height="1429" natural-width="2252" natural-height="1429" loading="lazy"></div></div></div></div><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1704261029283" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1704261029283"><div class="module main-slider owl-carousel owl-theme " data-collg="1" data-colmd="3" data-colsm="3" data-colxs="1" data-marginlg="5px" data-marginmd="5px" data-marginsm="5px" data-marginxs="5px" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="1" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="1"><div class="item"><div data-index="1" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1704261042768" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1704261042768" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-lg gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/1c5437ae-bace-45db-aaac-34cf9b99a77d/-/format/auto/-/preview/3000x3000/-/quality/lighter/country%20connection-08.jpg" alt="" class="gf_image" data-gemlang="en" data-width="120%" data-height="auto" title="" width="3000" height="1937" natural-width="3000" natural-height="1937"></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div></div></div><!--gfsplit--><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1704330515209" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1704330515209"><div class="module main-slider owl-carousel owl-theme " data-collg="1" data-colmd="3" data-colsm="3" data-colxs="1" data-marginlg="5px" data-marginmd="5px" data-marginsm="5px" data-marginxs="5px" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="1" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="1"><div class="item"><div data-index="1" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1704330581032" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1704330581032" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/7f2fe813-d749-4e17-b885-4f7e0cc712f2/-/format/auto/-/preview/3000x3000/-/quality/lighter/country%20connection-04.jpg" alt="" class="gf_image" data-gemlang="en" data-width="auto" data-height="auto" title="" width="2675" height="1872" natural-width="2675" natural-height="1872"></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1704330598730" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1704330598730" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/b0ac55de-b8b9-4b48-bafe-8af485d04797/-/format/auto/-/preview/3000x3000/-/quality/lighter/country%20connection-03.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="2402" height="1685" natural-width="2402" natural-height="1685"></div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1704330605314" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1704330605314" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/cc1564bf-68bc-4847-84d4-ed389ac789a5/-/format/auto/-/preview/3000x3000/-/quality/lighter/country%20connection-05.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="2403" height="1685" natural-width="2403" natural-height="1685"></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1704330820884" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1704330820884" data-extraclass="" data-row-gap="0px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1704330820788" data-id="1704330820788"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1704330893121" class="gf_row gf_row-gap-3" data-icon="gpicon-row" data-id="1704330893121" data-layout-lg="3+3+3+3" data-extraclass="" data-layout-md="3+3+3+3" data-layout-sm="3+3+3+3" data-layout-xs="3+3+3+3" data-row-gap="3px"><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-3 gf_col-xs-3" id="c-1704330893093" data-id="1704330893093"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1704331030982" class="gf_row" data-icon="gpicon-row" data-id="1704331030982" style="display: block;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1704331031003" data-id="1704331031003"></div></div><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1704330937479" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1704330937479" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/dd72774f-cc99-4e0f-90ac-f570f1402ab8/-/format/auto/-/preview/3000x3000/-/quality/lighter/country%20connection-12.jpg" alt="Yarn Marketplace image--71" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="892" height="1162" natural-width="892" natural-height="1162" loading="lazy"></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-3 gf_col-xs-3" id="c-1704330930486" data-id="1704330930486"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1704330940646" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1704330940646" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/6a680e9b-a449-4499-a129-3f79a0849b95/-/format/auto/-/preview/3000x3000/-/quality/lighter/country%20connection-13.jpg" alt="Yarn Marketplace image--72" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="892" height="1162" natural-width="892" natural-height="1162" loading="lazy"></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-3 gf_col-xs-3" id="c-1704330930495" data-id="1704330930495"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1704331039183" class="gf_row" data-icon="gpicon-row" data-id="1704331039183" style="display: block;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1704331031003" data-id="1704331031003"></div></div><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1704330943096" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1704330943096" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/974dd609-3033-4440-bc75-2fefb45da971/-/format/auto/-/preview/3000x3000/-/quality/lighter/country%20connection-14.jpg" alt="Yarn Marketplace image--73" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="892" height="1162" natural-width="892" natural-height="1162" loading="lazy"></div></div></div><div class="gf_column gf_col-lg-3 gf_col-md-3 gf_col-sm-3 gf_col-xs-3" id="c-1704330930449" data-id="1704330930449"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1704330946173" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1704330946173" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/f5d9b4bb-14da-45e6-9159-b21092673c43/-/format/auto/-/preview/3000x3000/-/quality/lighter/country%20connection-15.jpg" alt="Yarn Marketplace image--74" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="892" height="1162" natural-width="892" natural-height="1162" loading="lazy"></div></div></div></div><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1704331092881" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1704331092881" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/217df7e9-d00d-408e-87cc-1251c6ee394b/-/format/auto/-/preview/3000x3000/-/quality/lighter/country%20connection-19.png" alt="Yarn Marketplace image--76" class="gf_image" data-gemlang="en" data-width="60%" data-height="auto" title="" width="3000" height="1007" natural-width="3000" natural-height="1007" loading="lazy"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1704340063520" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1704340063520" data-extraclass="" data-row-gap="0px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1704340063520" data-id="1704340063520"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1704340110373" class="gf_row" data-icon="gpicon-row" data-id="1704340110373"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1704340110352" data-id="1704340110352"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1704340411371" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1704340411371" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/fc9ed11a-**************-b94f5ff11740/-/format/auto/-/preview/3000x3000/-/quality/lighter/country-connection.png" alt="Yarn Marketplace image--77" class="gf_image" data-gemlang="en" data-width="50%" data-height="auto" title="" width="1476" height="255" natural-width="1476" natural-height="255" loading="lazy"></div></div><div data-label="Product List" data-key="product-list" data-atomgroup="product-list" id="m-1704340124785" class="module-wrap" data-icon="gpicon-product-list" data-ver="1.0" data-id="1704340124785" style="min-height: auto;"><div class="module " data-cid="270785937542" data-chandle="caitlyn-davies-plummer-fashion-range" data-limit="20" data-collg="4" data-colmd="3" data-colsm="3" data-colxs="2"><div class="item-content item-content-no-placeholder"><div class="gf_row gf_row-no-width gf_row_no_tools gf_row-no-padding">{% assign colXsClass = ' gf_col-xs-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colXsClass = colXsClass | append:cols %}{% assign colSmClass = ' gf_col-sm-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colSmClass = colSmClass | append:cols %}{% assign colMdClass = ' gf_col-md-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colMdClass = colMdClass | append:cols %}{% assign colLgClass = ' gf_col-lg-' %}{% assign cols = 12 | divided_by: 4 %}{% assign colLgClass = colLgClass | append:cols %}{% assign colClass = 'gf_column gf_col_no_tools' %}{% assign colClass = colClass | append: colXsClass %}{% assign colClass = colClass | append: colSmClass %}{% assign colClass = colClass | append: colMdClass %}{% assign colClass = colClass | append: colLgClass %}{% assign gPLCount = 1 %}{% paginate collections["caitlyn-davies-plummer-fashion-range"].products by 20 %}{% for product in collections["caitlyn-davies-plummer-fashion-range"].products %}<div class="{{colClass}}" style="padding: 15px !important"><div data-label="Product" data-key="product" id="m-1704340124785-child{{forloop.index}}" data-ver="3.1" class="module-wrap" data-id="1704340124785-child{{forloop.index}}" data-index="1" style="min-height: auto;"><div class="module" data-variant="auto" style="" data-current-variant="40213577793670"><product-form class="product-form">{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1704340124785-child{{forloop.index}}-0" data-id="1704340124785-child{{forloop.index}}-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '1' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="100%" data-height="auto" style="width: 100%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="100%" data-height="auto" style="width: 100%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1704340124785-child{{forloop.index}}-1" data-id="1704340124785-child{{forloop.index}}-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1704340124785-child{{forloop.index}}-2" data-id="1704340124785-child{{forloop.index}}-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1704340124785-child{{forloop.index}}-3" data-id="1704340124785-child{{forloop.index}}-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</product-form></div></div></div>{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-xs"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-sm"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-md"></div>{% endif %}{% assign modulo = gPLCount | modulo: 4 %}{% if modulo == 0 %}<div class="gf_clearfix visible-lg"></div>{% endif %}{% assign gPLCount = gPLCount | plus: 1 %}{% endfor %}{% endpaginate %}</div></div></div></div><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1704343901852" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1704343901852" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-lg gf-elm-center-xs" data-exc=""><a href="https://www.yarn.com.au/collections/caitlyn-davies-plummer-fashion-range" target="" aria-label="Go to https://www.yarn.com.au/collections/caitlyn-davies-plummer-fashion-range"><img src="https://ucarecdn.com/1a34926f-daee-4362-abb9-5e0d055c8bc2/-/format/auto/-/preview/3000x3000/-/quality/lighter/country%20connection-17.png" alt="Yarn Marketplace image--83" class="gf_image" data-gemlang="en" data-width="70%" data-height="auto" title="" width="3000" height="519" natural-width="3000" natural-height="519" loading="lazy"></a></div></div></div></div></div></div><!--gfsplit--><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1704403587209" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1704403587209"><div class="module main-slider owl-carousel owl-theme " data-collg="1" data-colmd="3" data-colsm="3" data-colxs="1" data-marginlg="5px" data-marginmd="5px" data-marginsm="5px" data-marginxs="5px" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="1" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="1"><div class="item"><div data-index="1" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1704403587189" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1704403587189" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/b3958d47-2b58-461f-8da6-787b7daf5503/-/format/auto/-/preview/3000x3000/-/quality/lighter/Untitled-4-02.jpg" alt="" class="gf_image" data-gemlang="en" data-width="auto" data-height="auto" title="" width="3000" height="2111" natural-width="3000" natural-height="2111"></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1704404782220" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1704404782220" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/7c6d4126-4dd8-4c71-ae3c-c762996814d1/-/format/auto/-/preview/3000x3000/-/quality/lighter/Untitled-4-03.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="3000" height="2111" natural-width="3000" natural-height="2111"></div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1704403587292" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1704403587292" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/940425fb-52fc-4194-8373-fb1677735926/-/format/auto/-/preview/3000x3000/-/quality/lighter/Untitled-4-01.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="3000" height="2111" natural-width="3000" natural-height="2111"></div></div></div></div><div class="item"><div data-index="4" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1704403587253" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1704403587253" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/1bec1a58-727b-4b8b-92dd-7ed302899f81/-/format/auto/-/preview/3000x3000/-/quality/lighter/Untitled-4-04.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="3000" height="2111" natural-width="3000" natural-height="2111"></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div><!--gfsplit--><script>window.__gemStoreData = {};window.__gemStoreData.gemKeyValid = {};window.__gemStoreData.gemKeyValid.bestWayAddToCart = 1;</script></div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		"https://player.vimeo.com/api/player.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/owl.carousel.min.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv3product.js",
		 "https://www.youtube.com/player_api",
		'{{ 'gem-page-86363537542.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->