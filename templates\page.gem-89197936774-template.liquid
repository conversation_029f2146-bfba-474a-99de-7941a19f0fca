{% comment %}
	GEMPAGES BUILDER (https://apps.shopify.com/gempages)

	You SHOULD NOT modify source code in this file because
	It is automatically generated from GEMPAGES BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->


<link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.6.3/css/font-awesome.min.css" class="gf-style">
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-page-89197936774.css' | asset_url }}" class="gf_page_style">
<!--GEM_HEADER_END-->
<!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor">
<div data-label="Image" data-key="image" data-atomgroup="element" id="e-1718934612749" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1718934612749" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/8eca1f30-e44d-4abe-9b59-5ae4326e9bde/-/format/auto/-/preview/3000x3000/-/quality/lighter/Caitlyn-mobile-1.2.jpg" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="3000" height="966" natural-width="3000" natural-height="966"></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1718935065487" class="gf_row gf_equal-height" data-icon="gpicon-row" data-id="1718935065487" data-extraclass="" data-layout-lg="6+6" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1718935065518" data-id="1718935065518" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1718935086755" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1718935086755"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2"><strong>About Caitlyn's Artwork</strong></h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1718935082989" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1718935082989"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>'Guiding Light' is a painting about strength, perseverance and a tribute to the strong community Caitlyn is lucky to be surrounded by. This year's NAIDOC theme of, 'Keep the Fire Burning, Blak, Loud and Proud' is represented throughout this painting.</p><p>This painting is a reminder that after everything we have been through, the light and spark within us still shines bright. Caitlyn has been fortunate to learn and grow from leaders who show her their light and fire in everything they do. The fire within us can never be put out, we are forever 'Blak, Loud and Proud.'</p></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1718935076650" data-id="1718935076650" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Youtube" data-key="youtube" data-atomgroup="module" id="m-1718935103830" class="module-wrap" data-icon="gpicon-youtube" data-ver="1" data-id="1718935103830"><div class="module gf_module- " data-url="https://www.youtube.com/watch?v=OQBlWco72c4" data-width="" data-height="" data-responsive="1" data-sound="0" data-autoplay="0" data-loop="0" data-controls="1" data-showinfo="" data-modestbranding="0" data-fs="1" data-rel="" data-hd="" data-start="" data-end=""></div><div class="gf_youtube-overlay"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1718935131436" class="gf_row" data-icon="gpicon-row" data-id="1718935131436"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1718935131424" data-id="1718935131424"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1718935240322" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1718935240322"><div class="elm text-edit gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">SHOP CAITLYN'S NAIDOC COLLECTION</h1></div></div><div data-label="Product List" data-key="product-list" data-atomgroup="product-list" id="m-1718935142004" class="module-wrap" data-icon="gpicon-product-list" data-ver="1.0" data-id="1718935142004" style="min-height: auto;"><div class="module " data-cid="274417025158" data-chandle="naidoc-2024-caitlyn-davies-plumme" data-limit="9" data-collg="3" data-colmd="3" data-colsm="3" data-colxs="2"><div class="item-content item-content-no-placeholder"><div class="gf_row gf_row-no-width gf_row_no_tools gf_row-no-padding">{% assign colXsClass = ' gf_col-xs-' %}{% assign cols = 12 | divided_by: 2 %}{% assign colXsClass = colXsClass | append:cols %}{% assign colSmClass = ' gf_col-sm-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colSmClass = colSmClass | append:cols %}{% assign colMdClass = ' gf_col-md-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colMdClass = colMdClass | append:cols %}{% assign colLgClass = ' gf_col-lg-' %}{% assign cols = 12 | divided_by: 3 %}{% assign colLgClass = colLgClass | append:cols %}{% assign colClass = 'gf_column gf_col_no_tools' %}{% assign colClass = colClass | append: colXsClass %}{% assign colClass = colClass | append: colSmClass %}{% assign colClass = colClass | append: colMdClass %}{% assign colClass = colClass | append: colLgClass %}{% assign gPLCount = 1 %}{% paginate collections["naidoc-2024-caitlyn-davies-plumme"].products by 9 %}{% for product in collections["naidoc-2024-caitlyn-davies-plumme"].products %}<div class="{{colClass}}" style="padding: 20px !important"><div data-label="Product" data-key="product" id="m-1718935142004-child{{forloop.index}}" data-ver="3.1" class="module-wrap" data-id="1718935142004-child{{forloop.index}}" data-index="1" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="40603834253446" style=""><product-form>{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1718935142004-child{{forloop.index}}-0" data-id="1718935142004-child{{forloop.index}}-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="default" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="85%" data-height="auto" style="width: 85%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="85%" data-height="auto" style="width: 85%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="85%" data-height="auto" style="width: 85%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="85%" data-height="auto" style="width: 85%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'default' == 'hover' %}{% if 'last' != 'last' %}{% assign nth = last | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="85%" data-height="auto" style="width: 85%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1718935142004-child{{forloop.index}}-1" data-id="1718935142004-child{{forloop.index}}-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1718935142004-child{{forloop.index}}-2" data-id="1718935142004-child{{forloop.index}}-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1718935142004-child{{forloop.index}}-3" data-id="1718935142004-child{{forloop.index}}-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</product-form></div></div></div>{% assign modulo = gPLCount | modulo: 2 %}{% if modulo == 0 %}<div class="gf_clearfix visible-xs"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-sm"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-md"></div>{% endif %}{% assign modulo = gPLCount | modulo: 3 %}{% if modulo == 0 %}<div class="gf_clearfix visible-lg"></div>{% endif %}{% assign gPLCount = gPLCount | plus: 1 %}{% endfor %}{% endpaginate %}</div></div></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1718935984425" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1718935984425"><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>SHOP FULL COLLECTION</span></a></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1718935310526" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1718935310526" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1718935310517" data-id="1718935310517"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1718935320147" class="gf_row gf_equal-height" data-icon="gpicon-row" data-id="1718935320147" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="12+12" style="display: flex; flex-wrap: wrap; visibility: visible;"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1718935320146" data-id="1718935320146" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1718935664671" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1718935664671"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">About The Artist</h1></div></div><div data-label="Heading" data-key="heading" data-atomgroup="element" id="e-1718935669078" class="element-wrap" data-icon="gpicon-heading" data-ver="2" data-id="1718935669078"><div class="elm text-edit gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><h1 class="gf_gs-text-heading-2">Caitlyn Davies-Plummer</h1></div></div><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1718935336137" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1718935336137"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1" data-gemlang="en"><p>Barkindji Woman Caitlyn lives on Kaurna Country in Adelaide, with her husband Dale and beautiful little boy Dusty. She is an up and coming contemporary artist who uses her art to connect with her Country and culture.</p></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1718935361022" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1718935361022"><div class="elm gf-elm-center gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>View Artist Profile</span></a></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1718935331221" data-id="1718935331221" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1718935726428" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1718935726428" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/f88decd3-9136-420e-95c8-0acef0f0753f/-/format/auto/-/preview/3000x3000/-/quality/lighter/Img-3.png" alt="" class="gf_image" data-gemlang="en" width="800" height="800" data-width="80%" data-height="auto" title="" natural-width="800" natural-height="800"></div></div></div></div></div></div><!--gfsplit--><script>window.__gemStoreData = {};window.__gemStoreData.gemKeyValid = {};window.__gemStoreData.gemKeyValid.bestWayAddToCart = 1;</script>
</div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		"https://d1um8515vdn9kb.cloudfront.net/libs/js/gfyoutube.js",
		 "https://www.youtube.com/player_api",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv3product.js",
		'{{ 'gem-page-89197936774.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->