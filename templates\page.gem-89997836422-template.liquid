{% comment %}
	GEMPAGES BUILDER (https://apps.shopify.com/gempages)

	You SHOULD NOT modify source code in this file because
	It is automatically generated from GEMPAGES BUILDER
	Try to edit page with the live editor.
{% endcomment %}
<!--GEM_HEADER-->


<link rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/libs/css/fontawesome-4.6.3.1.min.css" class="gf-style">
<link data-instant-track rel="stylesheet" type="text/css" href="https://d1um8515vdn9kb.cloudfront.net/files/vendor.css?refresh=1" class="gf-style" />
<link data-instant-track rel="stylesheet" type="text/css" href="{{ 'gem-page-89997836422.css' | asset_url }}" class="gf_page_style">
<link data-instant-track class="gf_fonts" data-fonts="Gochi Hand" href="//fonts.googleapis.com/css2?family=Gochi Hand:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant Garamond" href="//fonts.googleapis.com/css2?family=Cormorant Garamond:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Cormorant" href="//fonts.googleapis.com/css2?family=Cormorant:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Bebas Neue" href="//fonts.googleapis.com/css2?family=Bebas Neue:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Anton" href="//fonts.googleapis.com/css2?family=Anton:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Aladin" href="//fonts.googleapis.com/css2?family=Aladin:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Acme" href="//fonts.googleapis.com/css2?family=Acme:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Alfa Slab One" href="//fonts.googleapis.com/css2?family=Alfa Slab One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Allan" href="//fonts.googleapis.com/css2?family=Allan:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Boogaloo" href="//fonts.googleapis.com/css2?family=Boogaloo:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Dancing Script" href="//fonts.googleapis.com/css2?family=Dancing Script:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Albert Sans" href="//fonts.googleapis.com/css2?family=Albert Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Dela Gothic One" href="//fonts.googleapis.com/css2?family=Dela Gothic One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Gasoek One" href="//fonts.googleapis.com/css2?family=Gasoek One:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track class="gf_fonts" data-fonts="Lora" href="//fonts.googleapis.com/css2?family=Lora:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900" rel="stylesheet" type="text/css" />
<link data-instant-track rel="stylesheet" type="text/css" href= "https://d1um8515vdn9kb.cloudfront.net/libs/css/owl.carousel.min.css" class="gf_libs">
<!--GEM_HEADER_END-->
<!--Gem_Page_Main_Editor--><div class="clearfix"></div><div class="gryffeditor"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723436416939" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1723436416939" data-extraclass="" data-row-gap="0px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723436416934" data-id="1723436416934"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723436426984" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723436426984" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/9eb4b425-4f1d-401c-8be6-2dea46be4170/-/format/auto/-/preview/3000x3000/-/quality/lighter/mobile-01.jpg" alt="Yarn Marketplace image--20" class="gf_image" data-gemlang="en" width="1080" height="1566" data-width="100%" data-height="auto" title="" natural-width="1080" natural-height="1566" loading="lazy"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723445657539" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1723445657539" data-extraclass="" data-row-gap="0px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723436416934" data-id="1723436416934"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723445657550" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723445657550" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/ca2df5c3-9b0a-420d-8422-da99d78f781e/-/format/auto/-/preview/3000x3000/-/quality/lighter/HEADER.jpg" alt="Yarn Marketplace image--20" class="gf_image" data-gemlang="en" width="3000" height="1319" data-width="100%" data-height="auto" title="" natural-width="3000" natural-height="1319" loading="lazy"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723436482372" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1723436482372" data-extraclass="" data-row-gap="0px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723436482366" data-id="1723436482366"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723436490376" class="gf_row" data-icon="gpicon-row" data-id="1723436490376"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723436490349" data-id="1723436490349"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1723436549670" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1723436549670"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>The perfect collection to celebrate dad for Father’s Day, Yarn is privileged to present a legacy collection as Brayden Chambers - son of long-time&nbsp;<span style="color: inherit; font-family: inherit; font-size: inherit; font-weight: inherit; text-align: inherit; letter-spacing: 0px;">collaborating artist Charlie Chambers Jnr - makes his debut.</span></p></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723445723880" class="gf_row gf_row-fluid gf_row-no-padding lazyb" data-icon="gpicon-row" data-id="1723445723880" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723436482366" data-id="1723436482366"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723445723902" class="gf_row" data-icon="gpicon-row" data-id="1723445723902"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723436490349" data-id="1723436490349"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1723445723924" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1723445723924"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-gemlang="en" data-exc=""><p>The perfect collection to celebrate dad for Father’s Day, Yarn is privileged to present a legacy collection as Brayden Chambers - son of long-time</p><p>collaborating artist Charlie Chambers Jnr - makes his debut.</p></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723436640475" class="gf_row gf_row-fluid gf_row-no-padding lazyb" data-icon="gpicon-row" data-id="1723436640475" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723436640436" data-id="1723436640436"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723436650904" class="gf_row" data-icon="gpicon-row" data-id="1723436650904"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723436650969" data-id="1723436650969"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723437085701" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723437085701" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/f38b9a59-03df-482c-93f3-20c39988c62e/-/format/auto/-/preview/3000x3000/-/quality/lighter/<EMAIL>" alt="Yarn Marketplace image--21" class="gf_image" data-gemlang="en" data-width="20%" data-height="auto" title="" width="1622" height="447" natural-width="1622" natural-height="447" loading="lazy"></div></div><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1723436763935" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1723436763935"><div class="module main-slider owl-carousel owl-theme " data-collg="3" data-colmd="3" data-colsm="3" data-colxs="1" data-marginlg="31px" data-marginmd="5px" data-marginsm="5px" data-marginxs="5px" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="1" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="0"><div class="item"><div data-index="1" class="item-content"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1723436789653" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1723436789653" data-assigned-ver="4" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="41217412071558" style=""><product-form>{% assign product = all_products['a-fathers-teachings-unisex-polo-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1723436789653-0" data-id="1723436789653-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="hover" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="60%" data-height="auto" style="width: 60%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'hover' == 'hover' %}{% if '3' != 'last' %}{% assign nth = 3 | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1723436789653-1" data-id="1723436789653-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1723436789653-2" data-id="1723436789653-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1723436789653-3" data-id="1723436789653-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</product-form></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1723436824626" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1723436824626" data-assigned-ver="4" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="41217543176326" style=""><product-form>{% assign product = all_products['a-fathers-teachings-black-cotton-crew-neck-unisex-t-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1723436824626-0" data-id="1723436824626-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="hover" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="60%" data-height="auto" style="width: 60%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'hover' == 'hover' %}{% if '3' != 'last' %}{% assign nth = 3 | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1723436824626-1" data-id="1723436824626-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1723436824626-2" data-id="1723436824626-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1723436824626-3" data-id="1723436824626-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</product-form></div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1723436832866" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1723436832866" data-assigned-ver="4" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="41217412432006" style=""><product-form>{% assign product = all_products['a-fathers-teachings-blue-bamboo-simpson-unisex-polo-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1723436832866-0" data-id="1723436832866-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="hover" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="60%" data-height="auto" style="width: 60%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'hover' == 'hover' %}{% if '2' != 'last' %}{% assign nth = 2 | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1723436832866-1" data-id="1723436832866-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1723436832866-2" data-id="1723436832866-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1723436832866-3" data-id="1723436832866-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</product-form></div></div></div></div><div class="item"><div data-index="4" class="item-content"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1723444654772" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1723444654772" data-assigned-ver="4" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="41217543438470" style=""><product-form>{% assign product = all_products['a-fathers-teachings-cobalt-cotton-crew-neck-unisex-t-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1723444654772-0" data-id="1723444654772-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="hover" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="60%" data-height="auto" style="width: 60%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'hover' == 'hover' %}{% if '2' != 'last' %}{% assign nth = 2 | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1723444654772-1" data-id="1723444654772-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1723444654772-2" data-id="1723444654772-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1723444654772-3" data-id="1723444654772-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</product-form></div></div></div></div><div class="item"><div data-index="5" class="item-content"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1723444655830" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1723444655830" data-assigned-ver="4" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="41222873677958" style=""><product-form>{% assign product = all_products['a-fathers-teachings-black-front-trucker-cap'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1723444655830-0" data-id="1723444655830-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="hover" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="60%" data-height="auto" style="width: 60%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'hover' == 'hover' %}{% if '2' != 'last' %}{% assign nth = 2 | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1723444655830-1" data-id="1723444655830-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1723444655830-2" data-id="1723444655830-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1723444655830-3" data-id="1723444655830-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</product-form></div></div></div></div><div class="item"><div data-index="6" class="item-content"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1723444656731" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1723444656731" data-assigned-ver="4" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="41222873612422" style=""><product-form>{% assign product = all_products['a-fathers-teachings-ceramic-coffee-mug'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1723444656731-0" data-id="1723444656731-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="hover" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="60%" data-height="auto" style="width: 60%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'hover' == 'hover' %}{% if '2' != 'last' %}{% assign nth = 2 | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1723444656731-1" data-id="1723444656731-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1723444656731-2" data-id="1723444656731-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1723444656731-3" data-id="1723444656731-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</product-form></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1723437147461" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1723437147461"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/brayden-chambers-a-fathers-teachings-collection" target="_blank" data-scroll-speed="2000" data-exc="" aria-label="Go to https://www.yarn.com.au/collections/brayden-chambers-a-fathers-teachings-collection" role="button"><span>VIEW FULL COLLECTION</span></a></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723445812890" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1723445812890" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723436640436" data-id="1723436640436"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723445812992" class="gf_row" data-icon="gpicon-row" data-id="1723445812992"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723436650969" data-id="1723436650969"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723445813011" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723445813011" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/f38b9a59-03df-482c-93f3-20c39988c62e/-/format/auto/-/preview/3000x3000/-/quality/lighter/<EMAIL>" alt="Yarn Marketplace image--21" class="gf_image" data-gemlang="en" data-width="45%" data-height="auto" title="" width="1622" height="447" natural-width="1622" natural-height="447" loading="lazy"></div></div><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1723445812952" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1723445812952"><div class="module main-slider owl-carousel owl-theme " data-collg="3" data-colmd="3" data-colsm="3" data-colxs="1" data-marginlg="31px" data-marginmd="5px" data-marginsm="5px" data-marginxs="5px" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="1" data-navspeed="1200" data-autoplay="1" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="0"><div class="item"><div data-index="1" class="item-content"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1723445812963" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1723445812963" data-assigned-ver="4" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="41217412071558" style=""><product-form>{% assign product = all_products['a-fathers-teachings-unisex-polo-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1723445812963-0" data-id="1723445812963-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="hover" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="60%" data-height="auto" style="width: 60%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'hover' == 'hover' %}{% if '3' != 'last' %}{% assign nth = 3 | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1723445812963-1" data-id="1723445812963-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1723445812963-2" data-id="1723445812963-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1723445812963-3" data-id="1723445812963-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</product-form></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1723445813003" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1723445813003" data-assigned-ver="4" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="41217543176326" style=""><product-form>{% assign product = all_products['a-fathers-teachings-black-cotton-crew-neck-unisex-t-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1723445813003-0" data-id="1723445813003-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="hover" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="60%" data-height="auto" style="width: 60%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'hover' == 'hover' %}{% if '3' != 'last' %}{% assign nth = 3 | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1723445813003-1" data-id="1723445813003-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1723445813003-2" data-id="1723445813003-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1723445813003-3" data-id="1723445813003-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</product-form></div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1723445812967" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1723445812967" data-assigned-ver="4" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="41217412432006" style=""><product-form>{% assign product = all_products['a-fathers-teachings-blue-bamboo-simpson-unisex-polo-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1723445812967-0" data-id="1723445812967-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="hover" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="60%" data-height="auto" style="width: 60%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'hover' == 'hover' %}{% if '2' != 'last' %}{% assign nth = 2 | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1723445812967-1" data-id="1723445812967-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1723445812967-2" data-id="1723445812967-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1723445812967-3" data-id="1723445812967-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</product-form></div></div></div></div><div class="item"><div data-index="4" class="item-content"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1723445812996" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1723445812996" data-assigned-ver="4" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="41217543438470" style=""><product-form>{% assign product = all_products['a-fathers-teachings-cobalt-cotton-crew-neck-unisex-t-shirt'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1723445812996-0" data-id="1723445812996-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="hover" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="60%" data-height="auto" style="width: 60%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'hover' == 'hover' %}{% if '2' != 'last' %}{% assign nth = 2 | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1723445812996-1" data-id="1723445812996-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1723445812996-2" data-id="1723445812996-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1723445812996-3" data-id="1723445812996-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</product-form></div></div></div></div><div class="item"><div data-index="5" class="item-content"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1723445813013" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1723445813013" data-assigned-ver="4" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="41222873677958" style=""><product-form>{% assign product = all_products['a-fathers-teachings-black-front-trucker-cap'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1723445813013-0" data-id="1723445813013-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="hover" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="60%" data-height="auto" style="width: 60%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'hover' == 'hover' %}{% if '2' != 'last' %}{% assign nth = 2 | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1723445813013-1" data-id="1723445813013-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1723445813013-2" data-id="1723445813013-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1723445813013-3" data-id="1723445813013-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</product-form></div></div></div></div><div class="item"><div data-index="6" class="item-content"><div data-label="Product" data-key="product" data-atomgroup="product" id="m-1723445812979" class="module-wrap" data-icon="gpicon-product" data-ver="3.1" data-id="1723445812979" data-assigned-ver="4" style="min-height: auto;"><div class="module" data-variant="auto" data-current-variant="41222873612422" style=""><product-form>{% assign product = all_products['a-fathers-teachings-ceramic-coffee-mug'] %}{% unless product.empty?  %}{% form 'product', product, id: "", class: "AddToCartForm ", data-productid: product.id %}<input name="id" type="hidden" value="{{product.selected_or_first_available_variant.id}}" data-productid="{{product.id}}"><div data-index="1" class="item-content"><div class="module-wrap" id="m-1723445812979-0" data-id="1723445812979-0" data-label="(P) Image"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs" data-effect="hover" data-pid="{{product.id}}" data-image-type="variant" data-default-variant="{{defaultVariant}}" data-select-text="{{selectText}}" data-zoom-level="1.2" data-ori-size="1024x1024" data-displaytype="percentage" data-displayunit="%" data-badgemod="0">{% assign modelMedia = false %}{% assign videoMedia = false %}{% if 'variant' == 'model' %}{% assign modelMedia = product.media | where: "media_type", "model" | first %}{%endif%}{% if 'variant' == 'video' %}{% assign videoMedia = product.media | where: "media_type", "video" | first %}{%endif%}{% if 'variant' == 'first' %}{% assign current_variant = product.selected %}{% else %}{% assign current_variant = product.selected_or_first_available_variant %}{% endif %}{% assign featured_image = current_variant.featured_image | default: product.featured_image %}{% if '0' == '1' %}{% assign anchorClass = 'active' %}{%else%}{% assign anchorClass = 'hide' %}{%endif%}{%if '0'=='1' %}{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if currentPrice and currentPrice %}{% if currentPrice < comparePrice %}{% assign diff = comparePrice | minus: currentPrice %}{%else%}{% assign diff = currentPrice | minus: comparePrice %}{%endif%}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% if comparePriceFloat !=0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% endif %}{% assign diff=diff | times: 100.0 %}{% assign saleOffValue = diff |round %}{%endif%}{%else%}{% assign saleOffValue = '' %}{%endif%}{% assign imageCapture = '' %}{% assign isVideo = 0 %}{% if videoMedia and videoMedia.sources and 'variant' == 'video' %}{% assign isVideo = 1 %}{% endif %}{% assign elementImgHolderOpen  = '<div class="img-holder">' %}{% assign elementImgHolderClose = '</div>' %}{% if '0' == '1' %}{% capture elementImgHolderOpen %}<a href="{{ product.url }}" class="img-holder">{% endcapture %}{% capture elementImgHolderClose %}</a>{% endcapture %}{% endif %}{% if modelMedia and modelMedia.sources %}{% assign media3DSource=modelMedia.sources | where: "format" , "glb" | first %}{% capture imageCapture %}<model-viewer poster="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" class="gf_product-3DImage gf_product-image gf_product-model-thumb" src="{{ media3DSource.url }}" auto-rotate="" camera-controls="" ar-status="not-presenting" style="position: absolute; width: 100%; height: 100%; z-index: 1; top: 0; left: 0"></model-viewer><img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="{{ modelMedia.preview_image.src | img_url: '1024x1024' }}" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto; opacity: 0;">{% endcapture %}{% elsif isVideo == 1 %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image gf_hidden-important" width="{{media.preview_image.width}}" height="{{media.preview_image.height}}" alt="{{ altTag }}">{% endcapture %}{% elsif featured_image %}{% assign altTag = featured_image.alt %}{% unless altTag %}{% assign altTag = product.title %}{% endunless %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" src="{{ featured_image | img_url: '1024x1024' }}" data-zoom="{{ featured_image | img_url: '2048x2048' }}" alt="{{ altTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% else %}{% capture imageCapture %}<img class="gf_product-image gf_featured-image" data-zoom="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" src="https://cdn.shopify.com/s/images/admin/no-image-2048x2048.gif" alt="{{ product.title }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endcapture %}{% endif %}{% if isVideo == 1 %}{% assign firstSource = videoMedia.sources | first %}<div class="gf_image-item gf_image-video gf_first_video" data-source="{{ firstSource.url }}" data-loaded="1" style="position: static; top: 0px; left: 0px; opacity: 1;"><video id="gf_image-item-0-video" controls="" data-width="60%" data-height="auto" style="width: 60%; height: auto;">{% for source in videoMedia.sources %}<source src="{{ source.url }}" type="{{ source.mime_type }}">{% endfor %}</video></div>{% endif %}{{elementImgHolderOpen}}<div class="gf_product-badge-anchor gf_pb_top-right {{anchorClass}}">{% if 'text' == "text" %}<div class="gf_badge-text-wrap gf_pb_sheild gf_gs-text-paragraph-2"><span>Sale Off </span>{%if '0'=='1' %}<span><span class="data-saleoffvalue" style="margin-left: 5px!important;">{{saleOffValue}}</span><span class="data-saleoffunit"> %</span></span>{%endif%}</div>{% elsif 'text' == "png" %}<div class="gf_badge-png-wrap"> PNG image </div>{% elsif 'text' == "gif" %}<div class="gf_badge-gif-wrap"> GIF image </div>{%endif%}</div>{{ imageCapture }}{% if 'hover' == 'hover' %}{% if '2' != 'last' %}{% assign nth = 2 | minus: 1 %}{% assign limit = product.images | size | minus: 1 %}{% assign nth = nth | at_most: limit %}{% assign hoverImage = product.images[nth] %}{% else %}{% assign hoverImage = product.images | last %}{% endif %}{% assign hoverImgAltTag = hoverImage.alt %}{% unless hoverImgAltTag %}{% assign hoverImgAltTag = product.title %}{% endunless %}<img class="gf_product-image gf_product-image-hover" src="{{ hoverImage.src | img_url: '1024x1024' }}" alt="{{ hoverImgAltTag }}" natural-width="{{ featured_image.width }}" natural-height="{{ featured_image.height }}" width="{{ featured_image.width }}" height="{{ featured_image.height }}" data-width="60%" data-height="auto" style="width: 60%; height: auto">{% endif %}{{elementImgHolderClose}}</div></div><div class="module-wrap" id="m-1723445812979-1" data-id="1723445812979-1" data-label="(P) Title"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}"><h3 itemprop="name" class="product-single__title">{% if '1' == '1' %}<a href="{{ product.url }}" class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</a>{% else %}<span class="gf_product-title gf_gs-text-heading-3">{{ product.title }}</span>{% endif %}</h3></div></div><div class="module-wrap" id="m-1723445812979-2" data-id="1723445812979-2" data-label="(P) Price"><div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-round-decimals="0" data-round-to="99">{% assign current_variant = product.selected_or_first_available_variant %}{% assign dataOldFormat = '' %}{% if shop.money_format %}{% capture dataOldFormat %}{{ shop.money_format | replace: '"', "'" }}{% endcapture %}{% endif %}<div class="gf_product-prices" data-oldformat="{{ dataOldFormat }}" data-oldcurrency="{{ cart.currency.iso_code }}">{% assign currentPrice = current_variant.price | plus: 0 %}{% assign comparePrice = current_variant.compare_at_price | plus: 0 %}{% if '1' == '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-right: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}<span class="gf_product-price money gf_gs-text-paragraph-1" itemprop="price" data-price="">{{ current_variant.price | money }}</span>{% if '1' != '1' and '1' == '1' %}{% assign hide_style = '' %}{% if current_variant.compare_at_price == null %}{% assign hide_style = 'display:none;' %}{% endif %}<span class="gf_product-compare-price money gf_gs-text-paragraph-1" style="margin-left: 5px!important;{{hide_style}}" itemprop="price" data-price-compare-at="">{{ current_variant.compare_at_price | money }}</span>{% endif %}{% if '0' == '1' %}{% assign hide_style = '' %}{% if comparePrice <= currentPrice or comparePrice <= 0 %}{% assign comparePrice = 1 %}{% assign hide_style = 'display:none' %}{% endif %}<span class="gf_pq-discount-selector" style="{{hide_style}}"><span class="gf_pq-discount gf_gs-text-paragraph-1">{% assign diff = comparePrice | minus: currentPrice %}{% assign comparePriceFloat = comparePrice | times: 1.0 %}{% assign diff = diff | divided_by: comparePriceFloat %}{% assign diff = diff | times: 100.0 %}{% assign diff = diff | round %}<span></span><span class="gf_pq-percent">{{ diff }}%</span><span>OFF</span></span></span>{% endif %}</div></div></div><div class="module-wrap" id="m-1723445812979-3" data-id="1723445812979-3" data-label="(P) Cart Button">{% capture pickLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}{% capture continueLink %}{% if '' == empty %}{% else %}{% if '' contains 'https://' or '' contains 'http://' %}{% elsif routes.root_url != null %}{{ shop.url }}{{ routes.root_url | split: '/' | join: '/' }}{% endif %}{% endif %}{% endcapture %}<div class="module gf_module-center gf_module-center-lg gf_module--md gf_module--sm gf_module--xs " data-pid="{{product.id}}" data-text="Add To Cart" data-soldouttext="Sold Out" data-ajaxcart="0" data-cbto="custom" data-editlink="{{pickLink}}" data-ajaxtext="Adding..." data-thankyoutext="Thank you!" data-successmessage="Added to cart! [cart label=View Cart] or [continue label=Continue Shopping]." data-continue="{{continueLink}}" data-effect="" data-ani="gf_ani-shakeLeftRight" data-interval="4000" data-stretch-lg="0" data-stretch-md="0" data-stretch-sm="0" data-stretch-xs="0">{% if '' != '' %}<input type="image" src="" class="gf_add-to-cart product-form-product-template  gf_gs-button-cart-button gf_gs-button---large">{% else %}{% if '' != '' %}<button type="submit" name="add" id="" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% else %}</button><button type="submit" name="add" class="gf_add-to-cart product-form-product-template button btn  gf_gs-button-cart-button gf_gs-button---large">{% endif %}<span class="AddToCartText">Add To Cart</span></button>{% endif %}</div></div></div><div class="gf_clearfix"></div>{% unless product == empty %}<script type="text/plain" class="product-json" id="product-json{{product.id}}">{{ product | json }}</script>{% endunless %}{% endform %}{% endunless %}</product-form></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1723445812955" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1723445812955"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-lg gf-elm-center-xs" data-stretch-lg="0" data-stretch-xs="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/brayden-chambers-a-fathers-teachings-collection" target="_blank" data-scroll-speed="2000" data-exc="" aria-label="Go to https://www.yarn.com.au/collections/brayden-chambers-a-fathers-teachings-collection" role="button" data-scroll-speed-xs="2000"><span>VIEW FULL COLLECTION</span></a></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723503370584" class="gf_row gf_row-gap-0 gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1723503370584" data-extraclass="" data-row-gap="0px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723503370589" data-id="1723503370589"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723503407009" class="gf_row" data-icon="gpicon-row" data-id="1723503407009"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723503407148" data-id="1723503407148"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723503447457" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723503447457" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/9122c7a3-a178-41e0-93b9-749eecaa9fde/-/format/auto/-/preview/3000x3000/-/quality/lighter/mobile-02.jpg" alt="Yarn Marketplace image--84" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1080" height="47" natural-width="1080" natural-height="47" loading="lazy"></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723437624731" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1723437624731" data-extraclass="" data-row-gap="0px" style="display: block;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723437624789" data-id="1723437624789"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723437635115" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723437635115" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/18942d05-42ad-4f80-a09a-2353b0975bde/-/format/auto/-/preview/3000x3000/-/quality/lighter/SPLIT.png" alt="Yarn Marketplace image--52" class="gf_image" data-gemlang="en" width="3000" height="68" data-width="100%" data-height="auto" title="" natural-width="3000" natural-height="68" loading="lazy"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723440095403" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1723440095403" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="12+12"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1723440095529" data-id="1723440095529"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723440148715" class="gf_row" data-icon="gpicon-row" data-id="1723440148715"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723440148739" data-id="1723440148739"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723440160450" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723440160450" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-lg gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/7990c33e-e4de-4037-96af-0365684891e2/-/format/auto/-/preview/3000x3000/-/quality/lighter/FRAME-01Asset%203.png" alt="Yarn Marketplace image--53" class="gf_image" data-gemlang="en" width="1372" height="1385" data-width="85%" data-height="auto" title="" natural-width="1372" natural-height="1385" loading="lazy"></div></div></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1723440131765" data-id="1723440131765"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723440152309" class="gf_row" data-icon="gpicon-row" data-id="1723440152309"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723440152363" data-id="1723440152363"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1723440175163" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1723440175163"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><p>Brayden’s painting celebrates the invaluable lessons, knowledge, and Culture that is passed down by fathers and father figures.  Using the emu father as a mirror and symbol of nurturing, supportive parenting, Brayden tells the story of how knowledge is shared and preserved across generations.</p></div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723438988237" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1723438988237" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723438988310" data-id="1723438988310"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723439000754" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723439000754" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/237263ee-6cea-4b6c-89c8-47ed398b58fe/-/format/auto/-/preview/3000x3000/-/quality/lighter/mobile-04.jpg" alt="Yarn Marketplace image--54" class="gf_image" data-gemlang="en" width="1080" height="1566" data-width="100%" data-height="auto" title="" natural-width="1080" natural-height="1566" loading="lazy"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723447529313" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1723447529313" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723438988310" data-id="1723438988310"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723447529388" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723447529388" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-exc=""><img src="https://ucarecdn.com/7da027ae-a1fa-41cc-b73c-f721c502998f/-/format/auto/-/preview/3000x3000/-/quality/lighter/BANNER.jpg" alt="Yarn Marketplace image--54" class="gf_image" data-gemlang="en" width="3000" height="1322" data-width="100%" data-height="auto" title="" natural-width="3000" natural-height="1322" loading="lazy"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723439124307" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0 lazyb" data-icon="gpicon-row" data-id="1723439124307" data-extraclass="" data-row-gap="0px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723439124310" data-id="1723439124310"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723439283347" class="gf_row" data-icon="gpicon-row" data-id="1723439283347" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="12+12"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1723439283340" data-id="1723439283340"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1723439472159" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1723439472159"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><p>“This is for any fatherly figure, those guiding hands that teach you things that you need to be taught at a young age. You can be taught many different things by many different figures in your life: uncles, grandfathers, any fatherly figure that teaches you the lessons that you need to be taught.”</p></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1723439286201" data-id="1723439286201"><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1723439393830" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1723439393830"><div class="module main-slider owl-carousel owl-theme " data-collg="1" data-colmd="3" data-colsm="3" data-colxs="1" data-marginlg="5px" data-marginmd="5px" data-marginsm="5px" data-marginxs="5px" data-dotslg="0" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="0" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="0"><div class="item"><div data-index="1" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723439415043" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723439415043" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/c63ab9f4-dba0-4ed1-95a0-52799f5461b4/-/format/auto/-/preview/3000x3000/-/quality/lighter/FRAMESAsset%207.png" alt="" class="gf_image" data-gemlang="en" width="1166" height="1139" data-width="100%" data-height="auto" title="" natural-width="1166" natural-height="1139"></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723439434603" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723439434603" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/e5155063-3785-4baf-80c2-e5ae37435ed1/-/format/auto/-/preview/3000x3000/-/quality/lighter/FRAMESAsset%206.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1166" height="1139" natural-width="1166" natural-height="1139"></div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723439449560" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723439449560" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/cabb0aa6-7111-4b98-882b-fb336c0380bc/-/format/auto/-/preview/3000x3000/-/quality/lighter/FRAMESAsset%205.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1166" height="1139" natural-width="1166" natural-height="1139"></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723530586647" class="gf_row gf_row-fluid gf_row-no-padding lazyb" data-icon="gpicon-row" data-id="1723530586647" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723530586687" data-id="1723530586687"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723530616670" class="gf_row" data-icon="gpicon-row" data-id="1723530616670"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723530616634" data-id="1723530616634"><div data-label="Youtube" data-key="youtube" data-atomgroup="module" id="m-1723530745155" class="module-wrap" data-icon="gpicon-youtube" data-ver="1" data-id="1723530745155"><div class="module gf_module- " data-url="https://www.youtube.com/watch?v=Orc57fsqeOk" data-width="" data-height="" data-responsive="1" data-sound="0" data-autoplay="1" data-loop="0" data-controls="0" data-showinfo="" data-modestbranding="0" data-fs="1" data-rel="" data-hd="" data-start="" data-end=""></div><div class="gf_youtube-overlay"></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723531497807" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1723531497807" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723530586687" data-id="1723530586687"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723531497838" class="gf_row" data-icon="gpicon-row" data-id="1723531497838"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723530616634" data-id="1723530616634"><div data-label="Youtube" data-key="youtube" data-atomgroup="module" id="m-1723531497901" class="module-wrap" data-icon="gpicon-youtube" data-ver="1" data-id="1723531497901"><div class="module gf_module- " data-url="https://www.youtube.com/watch?v=Orc57fsqeOk" data-width="" data-height="" data-responsive="1" data-sound="0" data-autoplay="1" data-loop="0" data-controls="0" data-showinfo="" data-modestbranding="0" data-fs="1" data-rel="" data-hd="" data-start="" data-end=""></div><div class="gf_youtube-overlay"></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723507342715" class="gf_row gf_row-fluid gf_row-no-padding gf_equal-height gf_row-gap-0 lazyb" data-icon="gpicon-row" data-id="1723507342715" data-extraclass="" style="display: flex; flex-wrap: wrap; visibility: visible;" data-row-gap="0px"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1723507342748" data-id="1723507342748" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723507398036" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723507398036" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/5edadf81-81ac-42ce-98af-9ff3f1c6f733/-/format/auto/-/preview/3000x3000/-/quality/lighter/FRAME-04.jpg" alt="Yarn Marketplace image--92" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1500" height="1315" natural-width="1500" natural-height="1315" loading="lazy"></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1723507389989" data-id="1723507389989" style="display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723507758375" class="gf_row" data-icon="gpicon-row" data-id="1723507758375" style="min-height: auto;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723507758332" data-id="1723507758332"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723507817045" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723507817045" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-exc=""><img src="https://ucarecdn.com/174b9617-4dad-43cf-8b57-dce700aaff4d/-/format/auto/-/preview/3000x3000/-/quality/lighter/TITLE-02Asset%208.png" alt="Yarn Marketplace image--93" class="gf_image" data-gemlang="en" data-width="60%" data-height="auto" title="" width="654" height="206" natural-width="654" natural-height="206" loading="lazy"></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723507812638" class="gf_row" data-icon="gpicon-row" data-id="1723507812638"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723507812660" data-id="1723507812660"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1723507898523" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1723507898523"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><p>Brayden Chambers is a young Wakka Wakka and Jarrawoir man. Both his tribes are from the Toowoomba, Dalby and Bunya Mountains region but he was born and raised in Ipswich. He is the youngest of six siblings and takes after his father, long-time Yarn collaborating artist Charlie Chambers Jnr, as an artist and storyteller.</p></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723507294018" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1723507294018" data-extraclass="" style="display: block;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723507294118" data-id="1723507294118"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723507301233" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723507301233" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/578a3760-5bef-4f64-96a8-20a4e396e2e4/-/format/auto/-/preview/3000x3000/-/quality/lighter/dasdsa.jpg" alt="Yarn Marketplace image--94" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="3000" height="155" natural-width="3000" natural-height="155" loading="lazy"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723447564780" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1723447564780" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723439124310" data-id="1723439124310"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723447564778" class="gf_row" data-icon="gpicon-row" data-id="1723447564778" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="12+12"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1723439283340" data-id="1723439283340"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1723447564772" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1723447564772"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><p>“This is for any fatherly figure, those guiding hands that teach you things that you need to be taught at a young age. You can be taught many different things by many different figures in your life: uncles, grandfathers, any fatherly figure that teaches you the lessons that you need to be taught.”</p></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1723439286201" data-id="1723439286201"><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1723447564680" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1723447564680"><div class="module main-slider owl-carousel owl-theme " data-collg="1" data-colmd="3" data-colsm="3" data-colxs="1" data-marginlg="5px" data-marginmd="5px" data-marginsm="5px" data-marginxs="5px" data-dotslg="0" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="0" data-navspeed="1200" data-autoplay="0" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="0"><div class="item"><div data-index="1" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723447564783" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723447564783" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/c63ab9f4-dba0-4ed1-95a0-52799f5461b4/-/format/auto/-/preview/3000x3000/-/quality/lighter/FRAMESAsset%207.png" alt="" class="gf_image" data-gemlang="en" width="1166" height="1139" data-width="100%" data-height="auto" title="" natural-width="1166" natural-height="1139"></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723447564695" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723447564695" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/e5155063-3785-4baf-80c2-e5ae37435ed1/-/format/auto/-/preview/3000x3000/-/quality/lighter/FRAMESAsset%206.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1166" height="1139" natural-width="1166" natural-height="1139"></div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723447564754" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723447564754" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/cabb0aa6-7111-4b98-882b-fb336c0380bc/-/format/auto/-/preview/3000x3000/-/quality/lighter/FRAMESAsset%205.png" alt="" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1166" height="1139" natural-width="1166" natural-height="1139"></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723440727371" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1723440727371" data-extraclass=""><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1723440727355" data-id="1723440727355"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723440751333" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723440751333" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/3fb646d0-0b47-432d-8287-cf8accd50e77/-/format/auto/-/preview/3000x3000/-/quality/lighter/FRAME-04.jpg" alt="Yarn Marketplace image--58" class="gf_image" data-gemlang="en" width="1500" height="1315" data-width="100%" data-height="auto" title="" natural-width="1500" natural-height="1315" loading="lazy"></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1723440731469" data-id="1723440731469"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723440780477" class="gf_row" data-icon="gpicon-row" data-id="1723440780477" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723440780471" data-id="1723440780471"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723440823860" class="gf_row" data-icon="gpicon-row" data-id="1723440823860" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723440823881" data-id="1723440823881"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723440830612" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723440830612" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/df2acf55-d18e-4e9f-9435-4fc184031f87/-/format/auto/-/preview/3000x3000/-/quality/lighter/TITLE-02Asset%208.png" alt="Yarn Marketplace image--59" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="654" height="206" natural-width="654" natural-height="206" loading="lazy"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723440837607" class="gf_row" data-icon="gpicon-row" data-id="1723440837607"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723440837518" data-id="1723440837518"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1723441506128" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1723441506128"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><p>Brayden’ painting celebrates the invaluable lessons, knowledge, and Culture that is passed down by fathers and father figures.  Using the emu father as a mirror and symbol of nurturing, supportive parenting, Brayden tells the story of how knowledge is shared and preserved across generations</p></div></div></div></div></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723441856507" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1723441856507" data-extraclass="" style="display: block;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723441856620" data-id="1723441856620"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723443505817" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1723443505817" data-extraclass="" data-row-gap="0px" style="display: block; flex-wrap: unset; visibility: visible;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723443505793" data-id="1723443505793" style="display: block; flex-direction: unset; justify-content: unset; min-height: auto;"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723507124405" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1723507124405" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723507124430" data-id="1723507124430"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723505440507" class="gf_row gf_row-fluid gf_row-no-padding lazyb" data-icon="gpicon-row" data-id="1723505440507" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723505440424" data-id="1723505440424"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723506148198" class="gf_row" data-icon="gpicon-row" data-id="1723506148198" data-layout-lg="6+6" data-extraclass="" data-layout-md="6+6" data-layout-sm="6+6" data-layout-xs="12+12"><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1723506148125" data-id="1723506148125"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723506158454" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723506158454" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/f9ab4406-cca5-40e1-a5f9-7e77e9eb6a3b/-/format/auto/-/preview/3000x3000/-/quality/lighter/fdsfdsaaaafsAsset%2013.png" alt="Yarn Marketplace image--100" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1443" height="894" natural-width="1443" natural-height="894" loading="lazy"></div></div></div><div class="gf_column gf_col-lg-6 gf_col-md-6 gf_col-sm-6 gf_col-xs-12" id="c-1723506150501" data-id="1723506150501"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723506174019" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723506174019" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/ce4d9835-6cd9-4702-b897-cac187e3b25d/-/format/auto/-/preview/3000x3000/-/quality/lighter/fdsfdsaaaaaafsAsset%2014.png" alt="Yarn Marketplace image--101" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1147" height="1131" natural-width="1147" natural-height="1131" loading="lazy"></div></div></div></div></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723500831037" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1723500831037" data-extraclass="" style="display: block;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723500830980" data-id="1723500830980"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723500848271" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723500848271" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/fa39cd2e-3cc2-4832-86ca-aeec42b227b6/-/format/auto/-/preview/3000x3000/-/quality/lighter/mobile-05.jpg" alt="Yarn Marketplace image--102" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1080" height="1087" natural-width="1080" natural-height="1087" loading="lazy"></div></div></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723503943693" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1723503943693" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723501511990" data-id="1723501511990"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723503943701" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723503943701" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/cda16d19-4cf3-4189-8941-156e87b855bc/-/format/auto/-/preview/3000x3000/-/quality/lighter/mobile-09.jpg" alt="Yarn Marketplace image--103" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1080" height="58" natural-width="1080" natural-height="58" loading="lazy"></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723503967633" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1723503967633" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723503967562" data-id="1723503967562"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723503980196" class="gf_row" data-icon="gpicon-row" data-id="1723503980196"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723503980140" data-id="1723503980140"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723504078065" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723504078065" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/5abaa5f6-228b-4415-8d2c-f979866627ce/-/format/auto/-/preview/3000x3000/-/quality/lighter/TITLE-03Asset%2010.png" alt="Yarn Marketplace image--104" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="855" height="331" natural-width="855" natural-height="331" loading="lazy"></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723504073051" class="gf_row" data-icon="gpicon-row" data-id="1723504073051"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723504073102" data-id="1723504073102"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1723504538695" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1723504538695"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-xs" data-gemlang="en" data-exc=""><p>“This is for any fatherly figure, those guiding hands that teach you things that you need to be taught at a young age. You can be taught many different things by many different figures in your life: uncles, grandfathers, any fatherly figure that teaches you the lessons that you need to be taught.”</p></div></div></div></div></div></div></div></div><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723503942599" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1723503942599" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723501511990" data-id="1723501511990"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723503942590" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723503942590" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/a8eeef39-924d-40cf-b13c-eb8af5e16807/-/format/auto/-/preview/3000x3000/-/quality/lighter/mobile-07.jpg" alt="Yarn Marketplace image--105" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="1080" height="59" natural-width="1080" natural-height="59" loading="lazy"></div></div></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723506322018" class="gf_row gf_row-fluid gf_row-no-padding" data-icon="gpicon-row" data-id="1723506322018" data-extraclass=""><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723506321999" data-id="1723506321999"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-1723506390100" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="1723506390100" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/ca2aa949-b793-4200-8b48-1e05c95df04f/-/format/auto/-/preview/3000x3000/-/quality/lighter/split-02.jpg" alt="Yarn Marketplace image--106" class="gf_image" data-gemlang="en" data-width="100%" data-height="auto" title="" width="3000" height="70" natural-width="3000" natural-height="70" loading="lazy"></div></div></div></div><!--gfsplit--><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1723447735261" class="module-wrap gf_hero-fixed-mode lazyb" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1723447735261"><div class="module " data-image="https://ucarecdn.com/9380f67a-1cc7-4ff6-acfb-7734168b584b/-/format/auto/-/preview/3000x3000/-/quality/lighter/bg-beige.jpg" data-image-lg="https://ucarecdn.com/9380f67a-1cc7-4ff6-acfb-7734168b584b/-/format/auto/-/preview/3000x3000/-/quality/lighter/bg-beige.jpg" data-image-md="https://ucarecdn.com/9380f67a-1cc7-4ff6-acfb-7734168b584b/-/format/auto/-/preview/3000x3000/-/quality/lighter/bg-beige.jpg" data-image-sm="https://ucarecdn.com/9380f67a-1cc7-4ff6-acfb-7734168b584b/-/format/auto/-/preview/3000x3000/-/quality/lighter/bg-beige.jpg" data-image-xs="https://ucarecdn.com/9380f67a-1cc7-4ff6-acfb-7734168b584b/-/format/auto/-/preview/3000x3000/-/quality/lighter/bg-beige.jpg" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="1"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723447735264" class="gf_row" data-icon="gpicon-row" data-id="1723447735264" data-layout-lg="4+4+4" data-extraclass="" data-layout-md="4+4+4" data-layout-sm="12+12+12" data-layout-xs="12+12+12"><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-12 gf_col-xs-12" id="c-1723442650282" data-id="1723442650282"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-*************" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="*************"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><p>The bottom left of the painting depicts the ways a father emu takes care of his young (father emu with his eggs in the bottom left circle) taking his newly-hatched chicks down to the river bank for a drink (shown with emu tracks).</p></div></div></div><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-12 gf_col-xs-12" id="c-*************" data-id="*************"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-*************" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="*************" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/b05b76b7-afaa-45b7-ad16-63551a483d25/-/format/auto/-/preview/3000x3000/-/quality/lighter/fdsfdsfsAsset%2012.png" alt="" class="gf_image" data-gemlang="en" data-width="150%" data-height="auto" title="" width="688" height="1045" natural-width="688" natural-height="1045"></div></div></div><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-12 gf_col-xs-12" id="c-*************" data-id="*************"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-1723447735234" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="1723447735234"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-lg gf-elm-left-md gf-elm-left-sm gf-elm-left-xs" data-gemlang="en" data-exc=""><p>The father emu will also teach his young how to avoid predators, how to eat, how to navigate the bush and lastly carry on the life of the species.”</p></div></div></div></div></span></div><picture class="gf_hero-bg-wrap"><source media="(min-width: 1200px)" srcset="https://ucarecdn.com/9380f67a-1cc7-4ff6-acfb-7734168b584b/-/format/auto/-/preview/3000x3000/-/quality/lighter/bg-beige.jpg"><source media="(min-width: 992px)" srcset="https://ucarecdn.com/9380f67a-1cc7-4ff6-acfb-7734168b584b/-/format/auto/-/preview/3000x3000/-/quality/lighter/bg-beige.jpg"><source media="(min-width: 768px)" srcset="https://ucarecdn.com/9380f67a-1cc7-4ff6-acfb-7734168b584b/-/format/auto/-/preview/3000x3000/-/quality/lighter/bg-beige.jpg"><img src="https://ucarecdn.com/9380f67a-1cc7-4ff6-acfb-7734168b584b/-/format/auto/-/preview/3000x3000/-/quality/lighter/bg-beige.jpg" alt=""></picture><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723443907784" class="gf_row gf_row-gap-0" data-icon="gpicon-row" data-id="1723443907784" data-extraclass="" style="display: block; transform: none; z-index: 50;" data-row-gap="0px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723443907691" data-id="1723443907691" style="transform: none; z-index: 50;"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723501647412" class="gf_row gf_row-fluid gf_row-no-padding gf_equal-height gf_row-gap-0" data-icon="gpicon-row" data-id="1723501647412" data-extraclass="" style="transform: none; z-index: 50; display: flex; flex-wrap: wrap; visibility: visible;" data-row-gap="0px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723501647519" data-id="1723501647519" style="transform: none; z-index: 50; display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723501655920" class="gf_row gf_equal-height" data-icon="gpicon-row" data-id="1723501655920" data-extraclass="" style="transform: none; z-index: 50; display: flex; flex-wrap: wrap; visibility: visible; min-height: auto;"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723501656021" data-id="1723501656021" style="transform: none; z-index: 50; display: flex; flex-direction: column; justify-content: center; min-height: auto;"><div data-label="Carousel" data-key="carousel" data-atomgroup="module" id="m-1723501780230" class="module-wrap" data-icon="gpicon-carousel" data-ver="1.0" data-id="1723501780230" style="transform: none; z-index: 50;"><div class="module main-slider owl-carousel owl-theme " data-collg="3" data-colmd="3" data-colsm="3" data-colxs="1" data-marginlg="5px" data-marginmd="5px" data-marginsm="5px" data-marginxs="5px" data-dotslg="1" data-dotsmd="1" data-dotssm="1" data-dotsxs="1" data-navlg="1" data-navmd="1" data-navsm="0" data-navxs="1" data-navspeed="1200" data-autoplay="0" data-autoplaytimeout="5000" data-autoplayhoverpause="1" data-loop="0"><div class="item"><div data-index="1" class="item-content"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-*************" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="*************"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-xs" data-gemlang="en" data-exc=""><p>The bottom left of the painting depicts the ways a father emu takes care of his young (father emu with his eggs in the bottom left circle) taking his newly-hatched chicks down to the river bank for a drink (shown with emu tracks).</p></div></div></div></div><div class="item"><div data-index="2" class="item-content"><div data-label="Image" data-key="image" data-atomgroup="element" id="e-*************" class="element-wrap" data-icon="gpicon-image" data-ver="1.0" data-id="*************" data-resolution="3000x3000"><div class="elm gf-elm-center gf_elm-left-xs gf-elm-center-xs" data-exc=""><img src="https://ucarecdn.com/70e0c0b6-6102-4d22-a4b4-75cbff42b5ce/-/format/auto/-/preview/3000x3000/-/quality/lighter/fdsfdsfsAsset%2012.png" alt="" class="gf_image" data-gemlang="en" data-width="50%" data-height="auto" title="" width="688" height="1045" natural-width="688" natural-height="1045"></div></div></div></div><div class="item"><div data-index="3" class="item-content"><div data-label="Text Block" data-key="text-block" data-atomgroup="element" id="e-*************" class="element-wrap" data-icon="gpicon-textblock" data-ver="1" data-id="*************"><div class="elm text-edit gf-elm-left gf_gs-text-paragraph-1 gf-elm-left-xs" data-gemlang="en" data-exc=""><p>The father emu will also teach his young how to avoid predators, how to eat, how to navigate the bush and lastly carry on the life of the species.”</p></div></div></div></div></div><div class="gf-carousel-loading"><div class="gf-caousel-ripple"><div></div><div></div></div></div></div></div></div></div></div><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1723443946408" class="module-wrap lazyb" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1723443946408"><div class="module " data-image="https://ucarecdn.com/4725adb5-f8a5-447a-8f71-86e4c480324a/-/format/auto/-/preview/3000x3000/-/quality/lighter/BANNER-02-v2.jpg" data-image-lg="https://ucarecdn.com/4725adb5-f8a5-447a-8f71-86e4c480324a/-/format/auto/-/preview/3000x3000/-/quality/lighter/BANNER-02-v2.jpg" data-image-md="https://ucarecdn.com/4725adb5-f8a5-447a-8f71-86e4c480324a/-/format/auto/-/preview/3000x3000/-/quality/lighter/BANNER-02-v2.jpg" data-image-sm="https://ucarecdn.com/4725adb5-f8a5-447a-8f71-86e4c480324a/-/format/auto/-/preview/3000x3000/-/quality/lighter/BANNER-02-v2.jpg" data-image-xs="https://ucarecdn.com/4725adb5-f8a5-447a-8f71-86e4c480324a/-/format/auto/-/preview/3000x3000/-/quality/lighter/BANNER-02-v2.jpg" data-height="" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723444008651" class="gf_row gf_row-gap-68" data-icon="gpicon-row" data-id="1723444008651" data-layout-lg="8+4" data-extraclass="" data-layout-md="8+4" data-layout-sm="8+4" data-layout-xs="12+12" data-row-gap="68px"><div class="gf_column gf_col-lg-8 gf_col-md-8 gf_col-sm-8 gf_col-xs-12" id="c-1723444008698" data-id="1723444008698"></div><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-4 gf_col-xs-12" id="c-1723444012410" data-id="1723444012410"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1723444191971" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1723444191971"><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="#" target="" data-scroll-speed="2000" data-exc=""><span>SHOP NOW</span></a></div></div></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1723502174663" class="module-wrap gf_hero-fixed-mode" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1723502174663"><div class="module " data-image="https://ucarecdn.com/0e109208-2438-4b02-a437-f5d4e03e0d5e/-/format/auto/-/preview/3000x3000/-/quality/lighter/mobile-08.jpg" data-image-lg="https://ucarecdn.com/4725adb5-f8a5-447a-8f71-86e4c480324a/-/format/auto/-/preview/3000x3000/-/quality/lighter/BANNER-02-v2.jpg" data-image-md="https://ucarecdn.com/4725adb5-f8a5-447a-8f71-86e4c480324a/-/format/auto/-/preview/3000x3000/-/quality/lighter/BANNER-02-v2.jpg" data-image-sm="https://ucarecdn.com/4725adb5-f8a5-447a-8f71-86e4c480324a/-/format/auto/-/preview/3000x3000/-/quality/lighter/BANNER-02-v2.jpg" data-image-xs="https://ucarecdn.com/0e109208-2438-4b02-a437-f5d4e03e0d5e/-/format/auto/-/preview/3000x3000/-/quality/lighter/mobile-08.jpg" data-height="" data-effect="none" data-transition="0.5" data-fixedmode="1"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723502174740" class="gf_row gf_row-gap-68" data-icon="gpicon-row" data-id="1723502174740" data-layout-lg="4+8" data-extraclass="" data-layout-md="4+8" data-layout-sm="4+8" data-layout-xs="12+12" data-row-gap="68px"><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-4 gf_col-xs-12" id="c-1723444008698" data-id="1723444008698"></div><div class="gf_column gf_col-lg-8 gf_col-md-8 gf_col-sm-8 gf_col-xs-12" id="c-1723444012410" data-id="1723444012410"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1723502174684" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1723502174684"><div class="elm gf-elm-center gf-elm-center-lg gf-elm-center-md gf-elm-center-sm gf-elm-center-xs" data-stretch-lg="0" data-stretch-xs="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/brayden-chambers-a-fathers-teachings-collection" target="_blank" data-scroll-speed="2000" data-exc="" data-scroll-speed-xs="2000"><span>SHOP NOW</span></a></div></div></div></div></span></div><picture class="gf_hero-bg-wrap"><source media="(min-width: 1200px)" srcset="https://ucarecdn.com/4725adb5-f8a5-447a-8f71-86e4c480324a/-/format/auto/-/preview/3000x3000/-/quality/lighter/BANNER-02-v2.jpg"><source media="(min-width: 992px)" srcset="https://ucarecdn.com/4725adb5-f8a5-447a-8f71-86e4c480324a/-/format/auto/-/preview/3000x3000/-/quality/lighter/BANNER-02-v2.jpg"><source media="(min-width: 768px)" srcset="https://ucarecdn.com/4725adb5-f8a5-447a-8f71-86e4c480324a/-/format/auto/-/preview/3000x3000/-/quality/lighter/BANNER-02-v2.jpg"><img src="https://ucarecdn.com/0e109208-2438-4b02-a437-f5d4e03e0d5e/-/format/auto/-/preview/3000x3000/-/quality/lighter/mobile-08.jpg" alt=""></picture><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723502653126" class="gf_row gf_row-fluid gf_row-no-padding gf_row-gap-0" data-icon="gpicon-row" data-id="1723502653126" data-extraclass="" data-row-gap="0px"><div class="gf_column gf_col-lg-12 gf_col-md-12 gf_col-sm-12 gf_col-xs-12" id="c-1723502653085" data-id="1723502653085"><div data-label="Hero Banner" data-key="hero-banner" data-atomgroup="module" id="m-1723502674139" class="module-wrap lazyb" data-icon="gpicon-herobanner" data-ver="1.1" data-id="1723502674139"><div class="module " data-image="https://ucarecdn.com/cfd5dd8d-18cd-473b-acc3-d37d6c446f3c/-/format/auto/-/preview/3000x3000/-/quality/lighter/BANNER-02-v2.jpg" data-image-lg="https://ucarecdn.com/cfd5dd8d-18cd-473b-acc3-d37d6c446f3c/-/format/auto/-/preview/3000x3000/-/quality/lighter/BANNER-02-v2.jpg" data-image-md="https://ucarecdn.com/cfd5dd8d-18cd-473b-acc3-d37d6c446f3c/-/format/auto/-/preview/3000x3000/-/quality/lighter/BANNER-02-v2.jpg" data-image-sm="https://ucarecdn.com/cfd5dd8d-18cd-473b-acc3-d37d6c446f3c/-/format/auto/-/preview/3000x3000/-/quality/lighter/BANNER-02-v2.jpg" data-image-xs="https://ucarecdn.com/cfd5dd8d-18cd-473b-acc3-d37d6c446f3c/-/format/auto/-/preview/3000x3000/-/quality/lighter/BANNER-02-v2.jpg" data-height="auto" data-effect="none" data-transition="0.5" data-fixedmode="0"><span data-index="1" class="item-content align-middle"><div data-label="Row" data-key="row" data-atomgroup="row" id="r-1723502701871" class="gf_row" data-icon="gpicon-row" data-id="1723502701871" data-layout-lg="8+4" data-extraclass="" data-layout-md="8+4" data-layout-sm="8+4" data-layout-xs="12+12"><div class="gf_column gf_col-lg-8 gf_col-md-8 gf_col-sm-8 gf_col-xs-12" id="c-1723502701867" data-id="1723502701867"></div><div class="gf_column gf_col-lg-4 gf_col-md-4 gf_col-sm-4 gf_col-xs-12" id="c-1723502705045" data-id="1723502705045"><div data-label="Button" data-key="button" data-atomgroup="element" id="e-1723502713513" class="element-wrap" data-icon="gpicon-gpicon-button" data-ver="1.0" data-id="1723502713513"><div class="elm gf-elm-center gf-elm-center-md gf-elm-center-sm gf-elm-center-xs gf-elm-center-lg" data-stretch-lg="0" data-stretch-xs="0"><a class="button btn gf_button gf_gs-button-element gf_gs-button---large" href="https://www.yarn.com.au/collections/brayden-chambers-a-fathers-teachings-collection" target="_blank" data-scroll-speed="2000" data-exc="" data-scroll-speed-xs="2000"><span>SHOP NOW</span></a></div></div></div></div></span></div><div class="gf_hero-bg-wrap"><div class="gf_hero-bg"></div></div><div class="gf_hero-overlay" style="background:transparent;opacity:0.2"></div></div></div></div><!--gfsplit--><script>window.__gemStoreData = {};window.__gemStoreData.gemKeyValid = {};window.__gemStoreData.gemKeyValid.bestWayAddToCart = 1;</script></div><div id="divContentBk"></div><!--End_Gem_Page_Main_Editor-->
<!--GEM_FOOTER-->
{% capture GEM_FOOTER_SCRIPT %}
<script data-instant-track type="text/javascript">
	var pageLibs = [		"https://d1um8515vdn9kb.cloudfront.net/libs/js/owl.carousel.min.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv3product.js",
		 "https://www.youtube.com/player_api",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfyoutube.js",
		 "https://d1um8515vdn9kb.cloudfront.net/libs/js/gfv2herobanner.js",
		'{{ 'gem-page-89997836422.js' | asset_url }}',	];
</script>
<script data-instant-track type="text/javascript">
	var GEMVENDOR = GEMVENDOR || (function(){		var _js = {};		return {			init: function(Args) {				_js = Args;			},			getLibs: function() {				return _js;			}		};	}());	GEMVENDOR.init(pageLibs);</script>
<script data-instant-track type="text/javascript" src="https://d1um8515vdn9kb.cloudfront.net/files/gempagev2.js?v=1.0" class="gf-script" defer></script>
{% endcapture %}
<!--GEM_FOOTER_END-->