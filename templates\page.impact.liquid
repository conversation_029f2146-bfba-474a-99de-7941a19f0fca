<style>
  .impact-banner {
    height: 500px;
    background: url(https://cdn.shopify.com/s/files/1/0247/4021/files/yarn-team.jpg?v=1652838442) no-repeat center center;
    background-size: cover;
  }
</style>

<div class="impact-banner">
  
</div>

<style>
  .top-grad {
    height: 150px;
    background: rgb(104,161,170);
    background: linear-gradient(0deg, rgba(104,161,170,1) 0%, rgba(104,161,170,0) 100%);
    margin-top: -150px;
    }
</style>

<div class="top-grad">
  
</div>


<style>
  
  .impact-body {
    background: rgb(104,161,170);
    background: linear-gradient(0deg, rgba(104,161,170,1) 0%, rgba(16,101,114,1) 49%, rgba(104,161,170,1) 100%);
  
    color: white;
  }
  
  .impact-body .Container {
    width: 1000px;
    margin: 0 auto;
    max-width: 100%;
    padding: 0 20px;
  }
  
  .impact-intro {
    text-align: center;
  }
  
  .impact-intro p {
    font-size: 17px;
    line-height: 1.6em;
  }
  
  .impact-intro h3 {
    font-size: 14px;
    line-height: 1.7em;
    letter-spacing: 2px;
  }
  
  .impact-amount span {
    display: inline-block;
    position: relative;
    
  }
  
  .impact-amount {
    font-size: 90px;
    font-family: Qualy;
    line-height: 90px;
    margin: 40px 0;
  }
  
  .impact-amount span:after {
    content: '';
    border: 1px solid white;
    position: absolute;
    left: -5px;
    top: -12px;
    right: -5px;
    bottom: 7px;
    border-radius: 5px;
  }
  
  @media (max-width: 767px) {
    .impact-amount {
      zoom: .7;
    }
  }
  
  @media (max-width: 520px) {
    .impact-amount {
      zoom: .5;
    }
    
    .impact-intro p {
      font-size: 15px;
      line-height: 1.6em;
      margin-bottom: 30px;
    }
    
    .impact-intro h3 {
      font-size: 13px;
      line-height: 1.7em;
      letter-spacing: 2px;
      margin-top: 20px;
    }
    
  }
  
  @media (max-width: 400px) {
    .impact-amount {
      zoom: .4;
    }
  }
  
</style>


<div class="impact-body">

<div class="impact-intro">
  <div class="Container">
    
    <h1><svg xmlns="http://www.w3.org/2000/svg" width="124.993" height="121.017" viewBox="0 0 124.993 121.017">
      <g id="Group_1047" data-name="Group 1047" transform="translate(10659.006 -13737.487)">
        <path id="Path_177882" data-name="Path 177882" d="M836.347,128.116a3.426,3.426,0,0,1,3.145,3.662,3.542,3.542,0,0,1-3.861,3.177,3.361,3.361,0,0,1-3.09-3.685,3.6,3.6,0,0,1,3.807-3.154" transform="translate(-11472 13641.311)" fill="#fff"/>
        <path id="Path_177883" data-name="Path 177883" d="M885.606,105.243a3.342,3.342,0,0,1-2.986-3.674,3.546,3.546,0,0,1,3.789-3.17,3.375,3.375,0,0,1,2.994,3.681,3.545,3.545,0,0,1-3.8,3.162" transform="translate(-11482.207 13647.368)" fill="#fff"/>
        <path id="Path_177884" data-name="Path 177884" d="M933.578,185.285a3.343,3.343,0,0,1,3.14,3.649,3.409,3.409,0,0,1-3.77,3.033,3.356,3.356,0,1,1,.63-6.681" transform="translate(-11491.839 13633.638)" fill="#fff"/>
        <path id="Path_177885" data-name="Path 177885" d="M836.753,192.492a3.407,3.407,0,0,1-3.084-3.707,3.406,3.406,0,1,1,3.084,3.707" transform="translate(-11472.229 13633.531)" fill="#fff"/>
        <path id="Path_177886" data-name="Path 177886" d="M883.98,215.269A3.326,3.326,0,0,1,887,218.918a3.447,3.447,0,0,1-3.8,3.007,3.359,3.359,0,0,1-2.974-3.609,3.417,3.417,0,0,1,3.755-3.047" transform="translate(-11481.721 13627.527)" fill="#fff"/>
        <path id="Path_177887" data-name="Path 177887" d="M936.883,132.462a3.326,3.326,0,1,1,.626-6.621,3.325,3.325,0,0,1-.626,6.621" transform="translate(-11492.647 13641.774)" fill="#fff"/>
        <path id="Path_177888" data-name="Path 177888" d="M892.561,182.062a34.97,34.97,0,0,1-10.143,3.841,30.378,30.378,0,0,1-15.133-1.54,17.938,17.938,0,0,1-6.166-4.016,37.722,37.722,0,0,1-9.972-14c-2.727-7.07-3.406-14.283-.976-21.6a27.56,27.56,0,0,1,4-7.911,37.043,37.043,0,0,1,7.385-7.908,27.532,27.532,0,0,1,6.22-3.514,18.56,18.56,0,0,1,2-.508c2.674-.732,5.323-1.592,8.143-1.533,5.523.115,10.892.841,15.833,3.6a26.109,26.109,0,0,1,8.552,7.631,34.372,34.372,0,0,1,6.288,13.772c1.966,10.609-.857,19.9-8.051,27.9a28.741,28.741,0,0,1-7.982,5.791m-3.06-1.323c3.124-1.682,5.5-2.833,7.41-4.633,8.229-7.764,11.244-17.225,8.912-28.313a30.659,30.659,0,0,0-4.115-9.46,24.688,24.688,0,0,0-10.629-9.77c-4.12-1.963-8.509-2.428-12.973-2.556-2.8-.08-5.427.8-8.072,1.565a16.774,16.774,0,0,0-2.4.758c-6.069,2.793-10.278,7.549-13.318,13.3-4.238,8.02-3.833,16.292-.463,24.489a34.39,34.39,0,0,0,8.679,11.941,18.783,18.783,0,0,0,10.434,5.053c3.82.538,7.617.8,11.344-.389,2.058-.654,4.041-1.543,5.189-1.989" transform="translate(-11475.292 13646.256)" fill="#fff"/>
        <path id="Path_177889" data-name="Path 177889" d="M856.8,226.367a43.479,43.479,0,0,1-5.576,1.364,8.994,8.994,0,0,1-10.2-8.13,20.324,20.324,0,0,1-.013-4.465,28.978,28.978,0,0,1,5.307-13.575,5.2,5.2,0,0,1,5.525-1.694,3.916,3.916,0,0,1,2.684,4.046A14.8,14.8,0,0,1,853.386,208c-.569,1.37-1.443,2.611-2.111,3.945-.4.792-1.074,1.657-.193,2.533a2.461,2.461,0,0,0,3.12.017,20.936,20.936,0,0,0,2.688-2.816c.791-.955,1.378-2.085,2.2-3.006a3.547,3.547,0,0,1,5.85.574,6.324,6.324,0,0,1,.825,2.253,6.057,6.057,0,0,1-.047,2.581c-1.519,5.1-4.007,9.508-8.923,12.286m-3.357-3.261c.74-.455,1.166-.71,1.585-.975,3.234-2.043,5.117-5.384,7.708-8.045a.993.993,0,0,0-.079-1.506,1.026,1.026,0,0,0-1.512.026c-.826.84-1.58,1.748-2.391,2.6-1.533,1.619-3.029,3.279-4.65,4.8a5.49,5.49,0,0,1-3.543,1.5,2.657,2.657,0,0,1-3.015-2.392,13.216,13.216,0,0,1-.127-2.783c.2-3.852,1.593-7.389,2.974-10.925.112-.287.3-.546.417-.833a1.075,1.075,0,0,0-.437-1.458,1,1,0,0,0-1.446.391,6.691,6.691,0,0,0-.794,1.472c-.877,2.655-1.818,5.3-2.519,8a14,14,0,0,0-.218,6.836,4.865,4.865,0,0,0,5.511,3.916,17.081,17.081,0,0,0,2.536-.635" transform="translate(-11473.705 13630.706)" fill="#fff"/>
        <path id="Path_177890" data-name="Path 177890" d="M908.44,89.055a9.412,9.412,0,0,1,10.652,1.375,12.6,12.6,0,0,1,4.589,8.678,20.6,20.6,0,0,1-3.115,12.826,6.868,6.868,0,0,1-4.677,3.414c-2.408.443-4.282-.684-4.653-3.1a12.689,12.689,0,0,1,.243-4.035c.291-1.647.929-3.237,1.17-4.888a15.7,15.7,0,0,0,.019-4.26c-.2-1.51-1.344-1.82-2.351-.653a11.961,11.961,0,0,0-1.582,2.713c-.83,1.737-1.479,3.568-2.4,5.25a12.468,12.468,0,0,1-2.414,3.044,3.9,3.9,0,0,1-4.688.411c-1.6-.947-2.673-2.963-2.191-4.55,1.647-5.43,4.01-10.46,8.529-14.164.909-.745,1.913-1.373,2.873-2.056m1.94,2.818c-.589.428-1.381.928-2.088,1.528-3.874,3.283-5.572,8.016-8.075,12.211a1.085,1.085,0,0,0,.393,1.621c.791.432,1.261-.057,1.632-.722,1.06-1.9,2.107-3.81,3.212-5.684a35.994,35.994,0,0,1,2.517-3.917,9.129,9.129,0,0,1,4.889-3.556c1.878-.478,3.066.074,3.6,1.952a20.475,20.475,0,0,1,.721,4.576,27.327,27.327,0,0,1-2.036,10.746,3.272,3.272,0,0,0-.367,1.235,1.373,1.373,0,0,0,.593,1.016,1.3,1.3,0,0,0,1.159-.2,2.962,2.962,0,0,0,.709-1.28,45.02,45.02,0,0,0,1.567-5.177c.76-3.865,1.162-7.76-.209-11.6-1.13-3.169-3.416-4.328-6.616-3.338-.474.146-.932.346-1.6.6" transform="translate(-11485.127 13649.483)" fill="#fff"/>
        <path id="Path_177891" data-name="Path 177891" d="M949.9,168.1a13.022,13.022,0,0,1-12.535.187,6.412,6.412,0,0,1-3.193-3,2.768,2.768,0,0,1,.688-3.693,11.177,11.177,0,0,1,3.2-1.814c1.691-.6,3.489-.885,5.215-1.395a6.439,6.439,0,0,0,2.149-1.045,1.608,1.608,0,0,0,.061-2.713,6.737,6.737,0,0,0-2.69-1.076c-1.517-.3-3.087-.328-4.609-.605a8.185,8.185,0,0,1-4.589-2.678,2.6,2.6,0,0,1-.481-3.253,4.006,4.006,0,0,1,3.248-2.4,25.57,25.57,0,0,1,17.56,4.9,11.809,11.809,0,0,1,2.04,1.9c3.158,3.67,3.263,7.53.315,11.37a19.094,19.094,0,0,1-6.38,5.316m.127-5.384c.278-.14.562-.268.832-.422,2.149-1.225,3.946-2.823,3.956-5.469.01-2.712-1.728-4.455-4.01-5.612a32.212,32.212,0,0,0-5.625-2.47,48.7,48.7,0,0,0-6.6-1.068,1.035,1.035,0,0,0-1.261.849,1.082,1.082,0,0,0,1,1.323,20.2,20.2,0,0,0,2.225.225,21.13,21.13,0,0,1,9.912,3.551c2.874,1.779,2.868,4.373.059,6.231a19.351,19.351,0,0,1-3.863,1.826,31.048,31.048,0,0,1-8.2,1.671c-.769.072-1.368.408-1.279,1.271.092.888.78,1,1.529.947a32.455,32.455,0,0,0,11.329-2.853" transform="translate(-11492.428 13641.945)" fill="#fff"/>
        <path id="Path_177892" data-name="Path 177892" d="M907,212.232c.707,1.088,1.35,2.224,2.143,3.244a1.783,1.783,0,0,0,2.1.846,1.987,1.987,0,0,0,1.2-1.964,21.723,21.723,0,0,0-.492-4.214c-.339-1.443-.973-2.816-1.428-4.235a7.214,7.214,0,0,1-.354-1.822,3.836,3.836,0,0,1,5.4-3.885,8.2,8.2,0,0,1,4.069,3.7,24.344,24.344,0,0,1,3.133,15.253,11.454,11.454,0,0,1-.457,2,7.526,7.526,0,0,1-8.149,5.651,11.427,11.427,0,0,1-6.688-2.552,43.624,43.624,0,0,1-10.3-12.878c-.808-1.455-.605-2.325.518-3.539,1.924-2.081,3.607-2.252,5.9-.619A10.307,10.307,0,0,1,907,212.232m9.548-5.754-.115.037c-.2-.389-.378-.784-.591-1.163-.293-.521-.7-.929-1.349-.715-.666.219-.733.8-.6,1.383a12.046,12.046,0,0,0,.487,1.4c.748,2.041,1.615,4.049,2.2,6.136a14.3,14.3,0,0,1,.474,4.22,2.793,2.793,0,0,1-2.465,2.864,5.787,5.787,0,0,1-5.318-.879,19.134,19.134,0,0,1-3.063-2.951c-1.256-1.614-2.3-3.394-3.437-5.1a11.072,11.072,0,0,0-.974-1.358,1.08,1.08,0,0,0-1.64-.217,1.06,1.06,0,0,0-.062,1.518,90.355,90.355,0,0,0,5.523,7.951c2.428,2.861,5.612,4.248,9.513,3.215a5.039,5.039,0,0,0,4.117-4.494,11.147,11.147,0,0,0-.19-4.055c-.694-2.636-1.66-5.2-2.513-7.8" transform="translate(-11485.074 13630.659)" fill="#fff"/>
        <path id="Path_177893" data-name="Path 177893" d="M848.17,112.29a23.369,23.369,0,0,1-3.06-11.97,9.951,9.951,0,0,1,3.29-7.666c3.1-2.686,6.666-2.859,10.318-1.274a19.148,19.148,0,0,1,7.656,6.384,18.172,18.172,0,0,1,3.273,6.451,5.958,5.958,0,0,1-.181,3.845,3.048,3.048,0,0,1-3.892,2.028A9.988,9.988,0,0,1,862,108.2a48.28,48.28,0,0,1-3.744-4.135,31.691,31.691,0,0,0-2.72-2.789,1.768,1.768,0,0,0-1.52-.213,1.138,1.138,0,0,0-.429,1.627,25.27,25.27,0,0,0,1.592,2.947,13.058,13.058,0,0,1,2.2,6.648,4.886,4.886,0,0,1-1.775,4.129,3.007,3.007,0,0,1-2.78.627,4.6,4.6,0,0,1-3.748-2.916,17.213,17.213,0,0,0-.912-1.838m2.65-1.28c.364.721.711,1.451,1.1,2.161a1.007,1.007,0,0,0,1.412.506,1.021,1.021,0,0,0,.529-1.412c-.239-.638-.562-1.244-.862-1.858a24.288,24.288,0,0,1-2.51-8.148,7.412,7.412,0,0,1,.912-5.03c1.155-1.774,2.362-2.175,4.046-.95a22.847,22.847,0,0,1,4.06,3.784c1.628,1.948,3,4.107,4.515,6.154.623.843,1.322,1.026,1.883.549.7-.6.393-1.217-.04-1.8-1.926-2.588-3.782-5.236-5.827-7.727a14.358,14.358,0,0,0-3.388-2.873c-2.911-1.854-5.489-1.144-7.252,1.827a9.819,9.819,0,0,0-1.153,6.3,26.8,26.8,0,0,0,2.578,8.518" transform="translate(-11474.564 13649.001)" fill="#fff"/>
        <path id="Path_177894" data-name="Path 177894" d="M823.82,157.915c.834.467,1.638,1,2.509,1.38a16.471,16.471,0,0,0,2.149.612c.717.2,1.443.389,2.142.643a3.823,3.823,0,0,1,2.785,3.2,3.647,3.647,0,0,1-2.193,3.608,10.1,10.1,0,0,1-3.088,1.244,22.593,22.593,0,0,1-15.806-2.835A10.442,10.442,0,0,1,809.35,163a6.169,6.169,0,0,1,.3-8.065,14.907,14.907,0,0,1,8.395-5.217,42.145,42.145,0,0,1,11.5-.674c.061,0,.123.018.185.026,2.722.35,4.092,1.926,4.065,4.712a2.68,2.68,0,0,1-2.062,2.781,29.969,29.969,0,0,1-4.4.648c-1.1.113-2.213.1-3.321.139q-.1.279-.2.56m-7.183-4.018a27.505,27.505,0,0,0-2.846,2.074,3.836,3.836,0,0,0-.4,5.532,7.663,7.663,0,0,0,2.454,1.931c3.9,1.935,8.127,2.162,12.384,2.127a1.1,1.1,0,0,0,1.246-1.1c.033-.785-.51-1.086-1.206-1.118-1.057-.047-2.115-.044-3.171-.087a20.478,20.478,0,0,1-7.81-1.6,7.065,7.065,0,0,1-1.867-1.2,1.807,1.807,0,0,1-.014-2.92,10.885,10.885,0,0,1,2.493-1.6,21.253,21.253,0,0,1,10.152-1.982,4.785,4.785,0,0,0,1.659-.041,1.432,1.432,0,0,0,.856-.993c.01-.353-.448-.783-.8-1.057-.206-.162-.6-.095-.908-.116a25.475,25.475,0,0,0-12.229,2.154" transform="translate(-11467 13641.043)" fill="#fff"/>
        <path id="Path_177895" data-name="Path 177895" d="M891.295,178.971a30.872,30.872,0,0,1-9.6,2.814,16.58,16.58,0,0,1-6.189-.4,25.292,25.292,0,0,1-15.1-10.714,30.422,30.422,0,0,1-3.552-8.987,24.387,24.387,0,0,1,1.765-17.763,25.875,25.875,0,0,1,9.944-10.974c3.342-2.009,6.945-2.323,10.649-2.17a34.159,34.159,0,0,1,5.482.792,24.721,24.721,0,0,1,13.294,7.657,25.539,25.539,0,0,1,4.482,5.848c4.365,8.775,3.885,17.328-1.357,25.579-2.384,3.752-4.82,5.776-9.818,8.315m-.65-3.193a18.586,18.586,0,0,0,8.649-8.068,21.54,21.54,0,0,0,2.478-15.5,21.2,21.2,0,0,0-5.56-10.343,22.7,22.7,0,0,0-12.1-7.224,39.647,39.647,0,0,0-5.373-.768,15.394,15.394,0,0,0-10.53,3.042,26.082,26.082,0,0,0-4.655,4.944,21.6,21.6,0,0,0-4.322,10.81c-.549,3.961.352,7.738,1.6,11.455a19.33,19.33,0,0,0,2.6,5.523,22.986,22.986,0,0,0,11.946,8.486,14.648,14.648,0,0,0,6.172.525,28.287,28.287,0,0,0,9.09-2.879" transform="translate(-11476.771 13644.752)" fill="#fff"/>
        <path id="Path_177896" data-name="Path 177896" d="M898.85,150.2c1.592,3.578,2.689,7.853,1.459,12.46a13.633,13.633,0,0,1-3.042,5.776,26.35,26.35,0,0,1-5.095,4.771c-10.737,6.89-22.05,2.466-25.929-7.892a20.206,20.206,0,0,1-1.143-7.674,26.685,26.685,0,0,1,1.519-8.316,15.266,15.266,0,0,1,6.076-7.842,8.679,8.679,0,0,1,4.023-1.417,18.431,18.431,0,0,0,1.994-.313c9.467-1.936,16.318,2.9,20.139,10.447m-11.016,4.849a4.981,4.981,0,0,0-3.071-2.642,8.444,8.444,0,0,0-5.027-.08,3.284,3.284,0,0,0-2.454,2.741,8.116,8.116,0,0,0,.282,4.244,6.553,6.553,0,0,0,5.707,4.644,5.033,5.033,0,0,0,5.391-4.339,8.372,8.372,0,0,0-.828-4.569m10.5-1.5a12.045,12.045,0,0,0-2.743-5.43l-5.448,3.511c.626,1.243,1.163,2.308,1.759,3.491l6.431-1.572m-12.191-4.564,3.7-6.31a10.916,10.916,0,0,0-5.447-1.613c-.171,2.541-.332,4.94-.5,7.365l2.24.559m1.061.444,2.088,1.426,5.789-3.762a17.331,17.331,0,0,0-4.235-3.878l-3.642,6.214M867.14,153.71l6.406,1.255.6-2.244-5.405-4.437a21.281,21.281,0,0,0-1.6,5.427m8.586,9.833-.916-1.338-6.251,4.161a8.564,8.564,0,0,0,2.937,4.052c1.758-2.057,2.529-4.721,4.229-6.874m3.582-15a60.1,60.1,0,0,0-1.576-6.925,7.139,7.139,0,0,0-4.287,1.326l3.787,6.49,2.075-.891M876.2,150c-1.164-2.147-2.338-4.267-3.717-6.362a12.888,12.888,0,0,0-3.1,3.687l5.314,4.357L876.2,150m16.185,8.73,6.815.51a11.777,11.777,0,0,0-.56-4.571l-6.521,1.557c.087.816.166,1.561.267,2.5m-13.434-17.3c.531,2.412,1.028,4.672,1.531,6.959l2.331-.034c-.006-2.525.475-4.9.334-7.35a10.8,10.8,0,0,0-4.2.426m-5.24,18.3a33.449,33.449,0,0,0-6.826.951,11.444,11.444,0,0,0,1.08,4.574,10.335,10.335,0,0,0,1-.51q2.242-1.48,4.463-2.99c1-.687,1-.71.285-2.025m-6.729-4.893a15.319,15.319,0,0,0-.223,4.583l6.828-.943c-.042-.861-.078-1.6-.115-2.367l-6.49-1.273m9.415,9.622-4.034,6.99a7.452,7.452,0,0,0,2.913,1.832c.986-2.489,1.936-4.892,2.9-7.327l-1.779-1.5m5.216,2.959a58.9,58.9,0,0,0-.154,7.57l3.92-.489c-.62-2.389-1.2-4.619-1.775-6.834l-1.992-.247m-2.475-.858c-.968,2.458-1.908,4.845-2.912,7.4l4,1.056c.065-2.791.125-5.359.186-7.989l-1.275-.467m15.212,2.8-4.932-3.935-1.8,1.324,3.391,5.437,3.338-2.826m-2.405-7.525a19.285,19.285,0,0,0,6.38,2.068l.842-3.532-6.822-.488-.4,1.951m-1.666,2.838,4.794,3.762a13.189,13.189,0,0,0,2.614-3.377l-6.3-1.963-1.1,1.578m-.226,8.141-3.459-5.608-1.814.489c.571,2.189,1.108,4.246,1.686,6.462l3.587-1.342" transform="translate(-11478.638 13643.004)" fill="#fff"/>
        <path id="Path_177897" data-name="Path 177897" d="M889.819,159.659a3.646,3.646,0,0,1-1.552,5.044,3.7,3.7,0,0,1-4.919-1.711,3.757,3.757,0,0,1,1.658-5.044,3.614,3.614,0,0,1,4.813,1.711" transform="translate(-11482.28 13639.279)" fill="#fff"/>
      </g>
    </svg>
   </h1>
    
    <p>Yarn Marketplace is a non-Indigenous owned business dedicated to creating an inclusive marketplace for and in collaboration with the Indigenous community. Our aim is to provide a platform for Indigenous business owners and artists to share their culture and unique stories with our customers.</p>
    
    <div class="impact-amount">
      
      {% section "impact-number" %}
      
      
    </div>
    
    <h3>
    DONATED OVER THE LAST FOUR YEARS TO INDIGENOUS<br>
    EMPLOYMENT AND TRAINING, ARTIST AND MODEL SPONSORSHIPS<br>
    AND DIRECT COMMUNITY FUNDING
    </h3>
    
  </div>
</div>


<style>
  .impact-block  {
    margin: 50px 0;
  }
  
  .impact-block .block-img {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: top center;
  }
  
  .impact-block .blockwrap {
    display: flex;
    flex-wrap: wrap;
    border-radius: 20px;
    overflow: hidden;
    margin: 0 auto;
    width: 900px;
    max-width: 100%;
  }
  
  .impact-block .blockwrap .block {
    width: 50%;
  }
  
  .impact-block .blockwrap .block-content {
    padding: 45px 30px 45px;
    background: white;
  }
  
  .impact-block .blockwrap .block-content input {
    display: none;
  }
  
  .impact-block .blockwrap .block-content input:checked + label + .block-text {
    display: none;
  }
  
  .impact-block .blockwrap .block-content label {
    text-transform: uppercase;
    font-weight: 600;
    color: #106572;
    padding-bottom: 3px;
    letter-spacing: 1px;
    display: block;
  }
  
  .impact-block .blockwrap .block-content label span {
    color: #106572;
    margin-bottom: 10px;
    border-bottom: 2px solid #106572;
    display: inline-block;
    padding-bottom: 3px;
  }
  
  .impact-block .blockwrap .block-content p {
    color: #106572;
    line-height: 1.8em;
  }
  
  @media (min-width: 768px) {
    .impact-block .blockwrap .block-content .block-text {
      display: block!important;
    }
  }
  
  
  
  @media (max-width: 767px) {
    
    .impact-block .blockwrap .block-content label {
      cursor: pointer;
      position: relative;
      display: block;
    }
    
    .impact-block .blockwrap .block-content {
      padding: 30px 20px 20px;
    }
    
    .impact-block .blockwrap .block-content label:before {
      content: '';
      position: absolute;
      right: 0;
      top: 5px;
      background: url(https://cdn.shopify.com/s/files/1/0247/4021/files/arrowblue.svg?v=1653449288) no-repeat center center;
      width: 18px;
      height: 12px;
      background-size: contain;
    }
    
    .impact-block .blockwrap .block-content input + label:before {
      transform: rotate(180deg);
    }
    
    .impact-block .blockwrap .block-content input:checked + label:before {
      transform: rotate(0deg);
    }
    
    
    
    .impact-block .blockwrap .block {
      width: 100%;
    }
    
    .impact-block .block-img {
      height: 300px;
      order: -1;
    }
    
  }
  
</style>

<div class="impact-block">
  <div class="Container">
    <div class="blockwrap">
      
      <div class="block block-img" style="background-image: url(https://cdn.shopify.com/s/files/1/0247/4021/files/block11.jpg?v=**********)">
        
      </div>
      
      <div class="block block-content">
        <input id="block1" type="checkbox">
        <label for="block1"><span>WHAT WE DO</span></label>
        
        <div class="block-text">
          <p>We support Indigenous-owned brands, ally-friendly brands and Indigenous artists build their businesses and grow through our platform. With our partner brands we share our social reach (thousands of visits a month + over 150k followers on FB and IG), warehouse space, shipping system, marketing resources, marketing insight, and general expertise to help build commercially successful businesses.</p>
        </div>
      </div>
      
    </div>
  </div>
</div>
  
<div class="impact-block">
  <div class="Container">
    <div class="blockwrap">

      <div class="block block-content">
        <input id="block2" type="checkbox">
        <label for="block2"><span>WHO WE SUPPORT</span></label>
        
        <div class="block-text">
          <p>We support First Nations artists and non-for-profits that make important contributions to Indigenous communities, their health and wellbeing. We currently provide ongoing support to four Community Partners: Gunawirra, Nutrition Plus, Surat Aboriginal Corporation and the Australian Literacy and Numeracy Foundation. Each year Yarn also supports and donates to a number of other organisations. Every product sale directly financially supports the significant work of these non-for-profits.</p>
        </div>
      </div>
      
      <div class="block block-img" style="background-image: url(https://cdn.shopify.com/s/files/1/0247/4021/files/block22.jpg?v=**********)">
        
      </div>
      
      
    </div>
  </div>
</div>
  
<div class="impact-block">
  <div class="Container">
    <div class="blockwrap">
      
      <div class="block block-img" style="background-image: url(https://cdn.shopify.com/s/files/1/0247/4021/files/block33.jpg?v=**********)">
        
      </div>
      
      <div class="block block-content">
        <input id="block3" type="checkbox">
        <label for="block3"><span>WHAT WE BELIEVE IN</span></label>
        
        <div class="block-text">
          <p>Our goal goes beyond being yet another e-commerce store. It is our mission to build opportunities, and grow alongside our partner brands and collaborating artists. We believe in developing a strong inclusive platform that can help overcome some of the current boundaries and prejudices that exist within our society. Through sharing First Nations art and knowledge we hope to build a more understanding and inclusive Australia.</p>
        </div>
      </div>
      
    </div>
  </div>
</div>
  

<style>
  
  .impact-achieve {
    text-align: center;
  }
  
  .impact-achieve h2 {
    color: white;
    margin-bottom: 30px;
    letter-spacing: 2px;
    font-size: 16px;
  }
  
  .impact-achieve .blocks {
    display: flex;
    flex-wrap: wrap;
    margin-left: -20px;
  }
  
  .impact-achieve .block {
    
    align-items: center;
    
    margin-bottom: 20px;
    padding-left: 20px;
  }
  
  .impact-achieve .block .blockinner {
    padding: 20px;
    display: flex;
    flex-wrap: wrap;
    border-radius: 10px;
    height: 100%;
  }
  
  
  
  
  .impact-achieve .blocks .block-4 {
    width: 25%;
  }
  
  .impact-achieve .blocks .block-2 {
    width: 50%;
  }
  
  .impact-achieve .blocks .block-white .blockinner  {
    background: white;
    color: #106572;
  }
  
  .impact-achieve .blocks .block-green .blockinner  {
    background: #106572;
    color: white;
  }
  
  .impact-achieve .block h3, .impact-achieve .block span {
    margin: 0px;
    line-height: 1.1em;
    display: block;
    width: 100%;
  }
  
  .impact-achieve .block h3 {
    font-size: 110px;
    font-family: Qualy;
    margin-top: 10px;
    margin-bottom: -10px;
    line-height: .9em;
  }
  
  @media (max-width: 767px) {
    .impact-achieve .blocks .block-4 {
      width: 50%;
    }

    .impact-achieve .blocks .block-2 {
      width: 100%;
    }
  }
  
  @media (max-width: 500px) {
    .impact-achieve .block h3 {
      font-size: 80px;
    }
    
    .impact-achieve .block span {
      font-size: 13px;
    }
    
    .impact-achieve .block .blockinner {
      padding: 20px 10px;
    }
    
  }
  
  
    
</style>
  
<div class="impact-achieve">
  <div class="Container">
    
    <h2>ACHIEVEMENTS SINCE LAUNCH OF YARN</h2>
    
    <div class="blocks">
      
      <div class="block block-4 block-green">
        <div class="blockinner">
          <span>WE HAVE WELCOMED</span>
          <h3>16</h3>
          <span>INDIGENOUS-OWNED<br>PARTNER BRANDS ONBOARD</span>
        </div>
      </div>
      
      <div class="block block-4 block-white">
        <div class="blockinner">
        <span>OUR TEAM IS<br>COMPOSED OF</span>
        <h3>40</h3>
        <span>CULTURALLY DIVERSE<br>INDIGENOUS & NON-INDIGENOUS TEAM MEMBERS</span>
        </div>
      </div>
      
      <div class="block block-4 block-green">
        <div class="blockinner">
        <span>WE HAVE WELCOMED</span>
        <h3>73</h3>
        <span>NEW INDIGENOUS<br>ARTISTS ONBOARD</span>
        </div>
      </div>
      
      <div class="block block-4 block-white">
        <div class="blockinner">
        <span>WE HAVE SUPPORTED</span>
        <h3>11</h3>
        <span>NOT-FOR-PROFIT<br>ORGANISATIONS</span>
        </div>
      </div>
      
      
      
      <div class="block block-2 block-white">
        <div class="blockinner">
        <span>LAUNCHED YARN RAISE WITH</span>
        <h3>100%</h3>
        <span>OF PROFITS GOING DIRECTLY BACK TO INDIGENOUS AND ALLY-FRIENDLY NOT-FOR-PROFITS</span>
        </div>
        </div>
      
      <div class="block block-2 block-green">
        <div class="blockinner">
        <span>DISPATCHED OVER</span>
        <h3>8,178</h3>
        <span>PRODUCTS FROM OUR INCREDIBLE PARTNER BRANDS!</span>
        </div>
      </div>
      
    </div>
    
  </div>
</div>
  
</div><!-- .impact-body -->
  
<style>
  .bot-grad {
    height: 260px;
    background: rgb(104,161,170);
    background: linear-gradient(0deg, rgba(104,161,170,0.3) 0%, rgba(104,161,170,1) 100%);
    position: relative;
  }
</style>

<div class="bot-grad">
  
</div>
  
  <style>
    .impact-footer {
      background: url(https://cdn.shopify.com/s/files/1/0247/4021/files/impact-bottom.jpg?v=1653364102) no-repeat center bottom;
      height: 260px;
      margin-top: -260px;
      background-size: cover;
    }
  </style>
  
<div class="impact-footer">
  
</div>
  


<script>
  
  $( document ).ready(function() {
    if ($(window).width() < 768) {
      $( ".impact-block .blockwrap .block-content input" ).each(function( index ) {
        $(this).click();
      });
    }
  });
  
</script>