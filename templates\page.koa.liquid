<style>

  #timer {
    font-size: 4em;
    line-height: 1em;
    font-weight: 600;
    color: white;
    text-align: center;
  }

  #timer div {
    display: inline-block;
    min-width: 90px;
    padding: 0 10px;
    text-align: center;
  }

  #timer div:not(:last-child) {
    border-right: 2px solid white;
  }

  #timer div span {
    color: #ffffff;
    display: block;
    font-size: .25em;
    font-weight: 600;
    line-height: 1.2em;
    letter-spacing: 1px;
    margin: 5px 0;
  }
  
  .koa-section {
    padding: 60px 0;
    background: url(https://cdn.shopify.com/s/files/1/0247/4021/files/countdown-bg1.jpg?v=1663650318) no-repeat top center;
    //background-size: cover;
    color: white;
  }

  .koa-section p {
    font-size: 17px;
  }

  .koa-section:after {
    content: '';
    display: table;
    clear: both;
  }

  .koa-section .container {
    width: 1200px;
    max-width: 100%;
    padding: 0 20px;
    margin: 0 auto;
    position: relative;
  }

  .koa-content {
    width: 550px;
    max-width: 100%;
    float: right;
  }

  .cd-pic {
    display: none;
  }

  .koa-brand {
      position: relative;
      left: auto;
      top: auto;
      text-align: center;
      width: 100%;
    }

    .koa-brand img {
    }

  @media (max-width: 1140px) {
    .koa-content {
      width: 450px;
      float: right;
    }

    
  }

   @media (min-width: 1140px) {
    .koa-section { 
      background-size: cover;
    }
  }


  @media (max-width: 940px) {
    .koa-content {
      width: 600px;
      margin: 0 auto;
      float: none;
    }

    .cd-pic {
      display: block;
    }

    .koa-section {
      background: url(https://cdn.shopify.com/s/files/1/0247/4021/files/countdown-bg1.jpg?v=1663650318) no-repeat top right;
      background-size: cover;
    }

    #timer {
      font-size: 3em;
    }

    #timer div {
      min-width: 75px;
      padding: 0 5px;
    }
  }
  
</style>

<section class="koa-section">
  <div class="container">

    <div class="koa-content">
      <p style="text-align: center; margin: 0"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/cd-title.png?v=1663647848"></p>
      <div class="koa-brand" style="margin-bottom: 20px"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/logo_9b2e65f8-2ee7-4417-9cd9-9b7018859855.png?v=1663717184"></div>
      
      
      <div id="timer" style="margin-bottom: 20px"></div>

      <p class="cd-pic"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/cd-pic.png?v=1663647844"></p>

      <p>We’re very excited to introduce <strong>“Back to Country”</strong> a polo collection created in collaboration with Barkindji artist, <strong>Caitlyn Davies-Plummer aka Dustin-Koa Art</strong>. This collection reminds us about the importance of feeling connected and caring for Country.</p>
      <p>Composed of 100% post-consumer recycled polyester the collection not only highlights the importance of connecting with Country but also caring for the land.</p>     
      <p><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/cd-launching.png?v=1663658988"></p>
      <p>Sign up to Yarn’s newsletter today and be the first to gain access to this gorgeous new collection + go into the draw to <strong><span style="color: #FDB6B6">WIN THE FULL COLLECTION!</span></strong></p>

      <div style="margin: 25px 0"><div class="klaviyo-form-YfiBht"></div></div>
    </div>

  </div>
</section>



<script type="text/javascript">
function updateTimer() {
    future = Date.parse("sep 28, 2022 17:00:00");
    now = new Date();
    diff = future - now;

    days = Math.floor(diff / (1000 * 60 * 60 * 24));
    hours = Math.floor(diff / (1000 * 60 * 60));
    mins = Math.floor(diff / (1000 * 60));
    secs = Math.floor(diff / 1000);

    d = days;
    h = hours - days * 24;
    m = mins - hours * 60;
    s = secs - mins * 60;

    document.getElementById("timer")
        .innerHTML =
        '<div>' + d + '<span>DAYS</span></div>' +
        '<div>' + h + '<span>HOURS</span></div>' +
        '<div>' + m + '<span>MINS</span></div>' +
        '<div>' + s + '<span>SECS</span></div>';
}
setInterval('updateTimer()', 1000);
</script>