<div class="Container">
  <header class="PageHeader">
    <div class="SectionHeader SectionHeader--center">
      <h1 class="SectionHeader__Heading Heading u-h1">{{ page.title }}</h1>
    </div>
  </header>

  <div class="PageContent Rte">
    {{ page.content }}
  </div>
</div>


{% if page.handle == 'bundles' %}

<script>

  $(function() {
  console.log("bundles was added?");
  // define a new observer
  var obs = new MutationObserver(function(mutations, observer) {
      // look through all mutations that just occured
      for(var i=0; i<mutations.length; ++i) {
          // look through all added nodes of this mutation
          for(var j=0; j<mutations[i].addedNodes.length; ++j) {
              // was a child added with ID of 'bar'?
              if(mutations[i].addedNodes[j].id == "revy-bundles-wrapper") {
                  console.log("bundles was added!");
                
                  
                  setTimeout(function(){ 
                    bundlesButton()
                  }, 1500);
                  
                  
              }
          }
      }
  });

  // have the observer observe foo for changes in children
  obs.observe($(".PageContent").get(0), {
    childList: true
  });
    
    function bundlesButton() {
      
      $('.revy-bundle-button-line.second').each(function() {
        
        let butText = $(this).text();
        $(this).closest('.revy-bundle-action').append('<span class="ProductMeta__Price Price Text--subdued u-h4">' + butText + '</span>');
        
      });
      
    }
  
  });
  
  
</script>

{% endif %}