<style>
  .Button.Button--inverse {
    color: black;
    border-color: white;
    
  }
  
  .Button.Button--inverse:before {
    background-color: white;
  }
</style>

<div class="mmain-custom-page-width">
  
  <style>
    .banner-loyalty {
      background-image: url(https://cdn.shopify.com/s/files/1/0247/4021/files/banner-img.jpg)
    }
    
    @media (max-width: 680px) {
      .banner-loyalty {
        background-image: url(https://cdn.shopify.com/s/files/1/0247/4021/files/mobile_header_eda14196-b819-42b9-b67f-593915675bbb.jpg)
      }
      
      .banner-loyalty .col-content {
        padding: 120px 0!important;
      }
      
      .loyalty-banner2 {
        display: none;
      }
    }
    
    
  </style>

  
   <div class="banner-loyalty full-bg" style="">
     <div class="cols">
       <div class="col">
         
       </div>
       <div class="col col-content">
         <img src="https://cdn.shopify.com/s/files/1/0247/4021/files/rewards.png">
         <p class="loyalty-p text-white">Join our Yarn Rewards program today and start earning every time you shop! It’s easy to join, and you will score your first Yarn coins just for signing up.</p>
         <a href="/account/register" class="Button Button--inverse" style="">Join Now</a>
         <a href="/account/login" class="Button Button--primary">Log in</a>
       </div>
     </div>
   </div>
  
  
  
   <div class="loyalty-hiw" style="text-align: center">
     <div class="loyalty-wrap">
       <h2 class="loyalty-title text-white">How it works</h2>
       <p class="loyalty-p text-white">In just three easy steps start earning discounts and rewards. Simply sign up, select your membership level and receive Yarn Coins for every purchase you make (Easy right?)</p>
       
       <div class="loyalty-hiw-cols">
         <div class="col">
           <div class="loyalty-icon" style="background-image: url(https://cdn.shopify.com/s/files/1/0247/4021/files/Group_771_2x_e9522837-6643-4a5b-8c9f-b24a18a8f388.png?v=**********)"></div>
           <p class="step text-lb">STEP ONE</p>
           <h4 class="text-white">JOIN</h4>
           <p class="loyalty-p text-white">To start earning points</p>
         </div>
         <div class="col">
           <div class="loyalty-icon" style="background-image: url(https://cdn.shopify.com/s/files/1/0247/4021/files/Group_770_2x_d7ddc2f2-b334-4597-ab1d-a967877c115c.png?v=**********)"></div>
           <p class="step text-lb">STEP TWO</p>
           <h4 class="text-white">EARN</h4>
           <p class="loyalty-p text-white">From your purchases</p>
         </div>
         <div class="col">
           <div class="loyalty-icon" style="background-image: url(https://cdn.shopify.com/s/files/1/0247/4021/files/Group_768_2x_e2df1f34-9a71-4c4a-b35f-08480a7f934e.png?v=**********)"></div>
           <p class="step text-lb">STEP THREE</p>
           <h4 class="text-white">REDEEM</h4>
           <p class="loyalty-p text-white">Redeem your coins for discounts and freebies</p>
         </div>
       </div>
     </div>
   </div>
    
  {% comment %}
   <div class="full-bg loyalty-banner2" style="background-image: url(https://cdn.shopify.com/s/files/1/0247/4021/files/bg2.jpg?v=1622767703)">

   </div>
  {% endcomment %}
  
  
   <div class="loyalty-phone">
      <div class="loyalty-wrap">
        <h2 class="loyalty-title text-db" style="text-align: center">Purchase the brands you love<br>and get rewarded.</h2>
        <div class="cols">
          <div class="col text--right">
            <h3>SAVE MONEY</h3>
            <p class="loyalty-p">Redeem your hard-earned coins for money off your next purchase or collect your coins and unlock one of our exclusive products.</p>
            <h3>FREE SHIPPING<br>AND returns</h3>
            <p class="loyalty-p">Free standard shipping on every single purchase, for our Yarn gold members.</p>
          </div>
          <div class="col col-50">
            <img src="https://cdn.shopify.com/s/files/1/0247/4021/files/mobile_mockup.jpg">
          </div>
          <div class="col text--left">
            <h3>Access to exclusive deals and competitions</h3>
            <p class="loyalty-p">Being part of Yarn Rewards comes with plenty of extra perks and exclusive offers. Such as exclusive access to new products and collections, announcements and limited edition products.</p>
            <h3>Birthday Perks</h3>
            <p class="loyalty-p">Just tell us when the big day is, and we’ll mark our calendars to give you a fun gift.</p>
          </div>
        </div>
      </div>
   </div>
  
  <style>
    /*
    .loyalty-perks .cols {
      display: none;
    }
    
    @media (max-width: 600px) {
      .loyalty-perks .cols {
		display: flex;
      }
      
      .loyalty-perks .lge-img {
        display: none;
        
      }
    }
    */
    
    .loyalty-perks .loyalty-p {
      margin-left: auto;
      margin-right: auto;
      max-width: 100%;
      width: 660px;
    }
    
    @media (min-width: 540px) {
      .loyalty-perks .cols .col:last-child {
        mmargin-top: -6%;
      }
      
    }
  </style>
  
  <div class="loyalty-perks" style="text-align: center">
    <div class="loyalty-wrap">
      <h2 class="loyalty-title text-white">Perks at every level</h2>
      <p class="loyalty-p text-white" style="">Check out the awesome benefits you’ll be getting. Level up your membership status to get points faster and enjoy exclusive perks such as FREE shipping on every order!</p>
      
      {% comment %}
      <div class="lge-img">
        <a href="/account/register"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/tiers2.png?v=**********"></a>
      </div>
      {% endcomment %}
      
      <div class="cols">
        {% comment %}
        <div class="col">
          <a href="/account/register"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/silver-landing-page.png?v=**********"></a>
        </div>
        <div class="col">
          <a href="/account/register"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/gold-landing-page.png?v=**********"></a>
        </div>
        {% endcomment %}
        
        <style>
          .col__perk .col__inner {
            border-radius: 30px;
            background-color: white;
            text-align: center;
          }
          
          .perk__header {
            color: #b2b2b2;
            font-size: 24px;
            font-weight: 600;
            padding: 15px 10px;
          }
          
          .col--silver .perk__header {
            color: #b2b2b2;
          }
          
          .col--gold .perk__header {
            color: #d89d14;
          }
          
          .perk__price {
            color: white;
            font-size: 18px;
            padding: 10px 10px;
            letter-spacing: 2px;
          }
          
          .col--silver .perk__price {
            background-color: #b2b2b2;
            color: white;
          }
          
          .col--gold .perk__price {
            background-color: #d89d14;
            color: white;
          }
          
          .perk__benefits {
            color: rgb(40,40,40);
            font-size: 18px;
            padding: 25px 10px;
            letter-spacing: 2px;
          }
          
          .perk__benefits span {
            display: block;
            padding: 10px 0;
            
          }
          
          
          
          
        </style>
        
        <div class="col col__perk" style="">
          {% section 'loyalty-perks' %}
        </div>
        
        <div class="col col__perk" style="">
          {% section 'loyalty-perks-gold' %}
        </div>
        
      </div>
      
      <a href="/account/register" class="Button Button--inverse text--center" style="margin: 40px 20px ">JOIN NOW</a>
      
      
       {% comment %}
      <div class="cols">
        <div class="col">
          <a href="/account/register"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/silver.png?v=**********"></a>
        </div>
        <div class="col">
          <a href="/account/register"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/Gold_membership_1.png?v=**********"></a>
        </div>
      </div>
      {% endcomment %}
      
      
      
      
    </div>
  </div>
  
  
  
  <div class="loyalty-hte" style="text-align: center">
    <div class="loyalty-wrap">
      <h2 class="loyalty-title text-db">How to Earn</h2>
      <p class="loyalty-p">Earning Yarn coins is simple. Get them everytime you shop. Plus you can earn additional coins just by signing up, telling your friends about Yarn and even celebrating your birthday.</p>
    		
      <div class="cols">
        <div class="col">
          <img src="https://cdn.shopify.com/s/files/1/0247/4021/files/earn11.jpg">
        </div>
        <div class="col">
          <img src="https://cdn.shopify.com/s/files/1/0247/4021/files/earn22.jpg">
        </div>
      </div>
      
    </div>
  </div>
  
  
  <div class="loyalty-cta">
    <div class="loyalty-wrap">
      <p class="loyalty-title text-white">Start earning coins today</p>
      <a href="/account/register" class="Button Button--inverse text--right" style="margin: 0 20px">JOIN NOW</a>
    </div> 
  </div>
  
  
  <div class="loyalty-faq">
    <div class="loyalty-wrap">
      <h2 class="loyalty-title text-white" style="text-align: center">FREQUENTLY ASKED QUESTIONS</h2>
      
      <div class="faq">
        
        
        
        
<style>
  
    .accordion {
      box-sizing: border-box;
      display: flex;
      font-family: 'Visby CF', 'Source Sans Pro', sans-serif;
      overflow: hidden;
      width: 90%;
      margin-left: auto;
      margin-right: auto;
    }

    .accordion-select {
      cursor: pointer;
      margin: 0;
      opacity: 0;
      z-index: 1;
    }

    .accordion-title {
      position: relative;
      display: flex;
      align-items: center;
    }

    .accordion-title:not(:nth-last-child(2))::after {
      border: 1px solid transparent;
      bottom: 0;
      content: '';
      left: 0;
      position: absolute;
      right: 0;
      top: 0;
    }

    .accordion-title span {
      bottom: 0px;
      box-sizing: border-box;
      display: block;
    
      line-height: 1em;
      width: 100%;
    }

    .accordion-content {
      box-sizing: border-box;
      overflow: auto;
      position: relative;
      transition: margin 0.3s ease 0.1s;
      text-align: left;
    }

    .accordion-select:checked + .accordion-title + .accordion-content {
      margin-bottom: 0;
      margin-right: 0;
    }

    /* Generated styles starts here */

    .accordion.yarn-accordion {
      border-color: #dedede;
      border-radius: 6px;
      border-style: solid;
      border-width: 1px;
      flex-direction: column;
      height: auto;
    } 

    .yarn-accordion.yarn-accordion .accordion-title,
    .yarn-accordion.yarn-accordion .accordion-select  {
      background-color: #106572;
      color: #ffffff;
      width: 100%;
      min-height: 52px;
      font-size: 20px;
      font-weight: bold;
    }

    .yarn-accordion.yarn-accordion .accordion-select {
      margin-bottom: -52px;
      margin-right: -52px;
    }

    .yarn-accordion.yarn-accordion .accordion-title:not(:nth-last-child(2))::after {
      border-bottom-color: rgb(219, 219, 219);
      border-right-color: transparent;
    } 

    .yarn-accordion.yarn-accordion .accordion-select:hover + .yarn-accordion.yarn-accordion .accordion-title,
    .yarn-accordion.yarn-accordion .accordion-select:checked + .yarn-accordion.yarn-accordion .accordion-title {
      background-color: #ffffff;
    } 

     .yarn-accordion.yarn-accordion .accordion-title span  {	
        transform: rotate(0deg);
        -ms-writing-mode: lr-tb;
        filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=0);
        padding-left: 26px;
        padding-right: 26px;
        line-height: 1em;
    } 

    .yarn-accordion.yarn-accordion .accordion-content {
      background-color: #fafafa;
      color: #5c5c5c;
      height: 430px;
      margin-bottom: -430px;
      margin-right: 0;
      padding: 30px;
      padding-left: 10%;
      padding-right: 10%;
      width: 100%;
      font-size: 16px;
    } 
  
  @media (max-width: 600px) {
    .yarn-accordion.yarn-accordion .accordion-title {
      padding: 10px 0;
      font-size: 16px;
    }
  }
  
</style>
        
  {% section 'rewards-faq' %}

        {% comment %}
  <div class="accordion yarn-accordion">
    
    
    
    <input type="radio" name="select" class="accordion-select" checked>
    <div class="accordion-title"><span>How do I view my coin balance?</span></div>
    <div class="accordion-content">
      Head to the Yarn website and click on the person icon at the top right next to the search bar. Login into your account and you will be able to view your Yarn Coin balance. 
    </div> 
   
    <input type="radio" name="select" class="accordion-select">
    <div class="accordion-title"><span>How do I redeem my coins?</span></div>
    <div class="accordion-content">
      First of all when you reach the Yarn website make sure that you log into your account. Next add the items you would like to purchase to your cart and go to checkout. On the right hand side of the checkout page you will see a redeem your coins section, select how many points you would like to use for the purchase and proceed to payment. 
    </div> 
   
    <input type="radio" name="select" class="accordion-select">
    <div class="accordion-title"><span>Why did my account balance go down?</span></div>
    <div class="accordion-content">
      This would be because you have made a purchase recently and your coins went towards this purchase. In the future if you would like to make purchases without using your coins please check the redeem coins section on the right hand side at checkout and make sure the points slider is set at 0. 
    </div> 
   
    <input type="radio" name="select" class="accordion-select">
    <div class="accordion-title"><span>Is there a limit to how many coins I can use at one time?</span></div>
    <div class="accordion-content">
      There is no limit you may use as many coins as you like for any purchase.
    </div>
    
    <input type="radio" name="select" class="accordion-select">
    <div class="accordion-title"><span>Why is the option to spend my coins not appearing at checkout?</span></div>
    <div class="accordion-content">
      You have to have a minimum of 400 coins in your account before you can spend coins on purchases.
    </div>
    
    <input type="radio" name="select" class="accordion-select">
    <div class="accordion-title"><span>Can I use my coins to purchase a Gold Membership?</span></div>
    <div class="accordion-content">
      No you can not. The Gold Membership can only be purchased Australian dollars.
    </div>
    
    <input type="radio" name="select" class="accordion-select">
    <div class="accordion-title"><span>How do I cancel my membership?</span></div>
    <div class="accordion-content">
      If you have a Silver Membership there’s no need to cancel this as it is free. If you have a Gold Membership that you would like to cancel, simply log into your account and click on the manage subscription button, from here you will be able to cancel your Gold Membership. Please be aware that if you paid for your subscription on a yearly basis you can only cancel your membership once the year is up, you will not get a refund. And if you pay for your subscription on a monthly basis your subscription will be cancelled once the month is up. 
    </div>
    
  </div>
        
        {% endcomment %}
        
        
      </div>
      
    </div>
  </div>
  
  
  

  
  
  

</div>