<script> var wk_type_url = '/';</script><style>#shopify-section-site-footer, #shopify-section-footer, #shopify-section-static-footer{ float:left; width:100%; }.mp-loader { position: fixed; background: rgba(255, 255, 255, 0.6); top: 0; left: 0; bottom: 0; right: 0; z-index: 1100} .mp-loader .mp-spinner { width: 52px; height: 52px; border: 4px solid #cccccc; border-radius: 50%; animation: mp-spin 460ms infinite linear; -o-animation: mp-spin 460ms infinite linear; -ms-animation: mp-spin 460ms infinite linear; -webkit-animation: mp-spin 460ms infinite linear; -moz-animation: mp-spin 460ms infinite linear; border-color: #555555 #cccccc; position: absolute; left: 50%; top: 50%; margin-left: -26px; margin-top: -26px; } .mp-loader .title-text { position: absolute; text-align: center !important; top: 56%; width: 100%; } @keyframes mp-spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } } @-o-keyframes mp-spin { from { -o-transform: rotate(0deg); } to { -o-transform: rotate(360deg); } } @-ms-keyframes mp-spin { from { -ms-transform: rotate(0deg); } to { -ms-transform: rotate(360deg); } } @-webkit-keyframes mp-spin { from { -webkit-transform: rotate(0deg); } to { -webkit-transform: rotate(360deg); } } @-moz-keyframes mp-spin { from { -moz-transform: rotate(0deg); } to { -moz-transform: rotate(360deg); } }</style><div class="rte"><div class="mp_sp_page" data-cust="{{ customer.id }}"></div><div class="wk_cstm_email" data-cust="{{ customer.email }}"></div><div class="wk_cstm_name" data-cust="{{ customer.name }}"></div></div><div class='mp-loader'><div class="mp-spinner"></div><div class="title-text">Please Wait...</div></div><script type="text/javascript" src="https://sp-seller.webkul.com/js/vc_seller_profile.min.js"></script>