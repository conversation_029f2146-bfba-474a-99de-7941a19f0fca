<style>
  @font-face {
    font-family: 'CN Postal';
    src: url('https://cdn.shopify.com/s/files/1/0247/4021/files/CNPostalRegular.woff2?v=1642988043') format('woff2'),
        url('https://cdn.shopify.com/s/files/1/0247/4021/files/CNPostalRegular.woff?v=1642988035') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
    colour: '#ffffff';
}

  @font-face {
    font-family: 'Allenoire';
    src: url('https://cdn.shopify.com/s/files/1/0247/4021/files/Allenoire_Font.eot?v=1672874847'); /* IE9 Compat Modes */
    src: url('https://cdn.shopify.com/s/files/1/0247/4021/files/Allenoire_Font.eot?v=1672874847?#iefix') format('embedded-opentype'), /* IE6-IE8 */
         url('https://cdn.shopify.com/s/files/1/0247/4021/files/Allenoire_Font.woff?v=1672874847') format('woff'), /* Modern Browsers */
         url('https://cdn.shopify.com/s/files/1/0247/4021/files/Allenoire_Font.ttf?v=1672874900') format('truetype'); /* Safari, Android, iOS */
             font-style: normal;
    font-weight: normal;
    text-rendering: optimizeLegibility;
    colour: 'F7F1D5';
}
  
.SectionHeader__Heading.Heading, .banner-head {
  font-family: 'Allenoire'!important;
}
  
#shopify-section-naidoc-sections-template .page-blocks .block-custom-html:first-child .index-section {
  background: url(https://cdn.shopify.com/s/files/1/0247/4021/files/Image-2_0b293d6d-51ec-467b-a619-fcd4eec46925.jpg?v=1737671245) no-repeat 0 0!important;
  background-size: cover!important;
  background-position: center;
}
  
</style>

<main id="main" role="main">

    
    {% section 'naidoc-sections-template' %}
    

</main>

<script src="https://cdnjs.cloudflare.com/ajax/libs/sticky-js/1.2.2/sticky.min.js"></script> 

<script>
  
  $(document).ready( function() {
  
    $('.carousel-naidoc').flickity({
      // options
      cellAlign: 'left',
      contain: true,
      autoPlay: 3000,
      pageDots: false,
      wrapAround: true,
      pauseAutoPlayOnHover: false
    });
    
    let headrh = $('#section-header').outerHeight() + 50;
    //console.log(headrh);
    
    $('a[href="/pages/naidoc-2020"] + .DropdownMenu .Link--secondary').each(function( index ) {
      
      $(this).addClass('scrollme');
      let href = $(this).attr('href');
      href = href.split("#").pop();
      $(this).attr("href", "#" + href);
      
      
    });
    
    $('.SidebarMenu__Nav a[href*="/pages/naidoc-2020"]').click( function( e ) {
        e.preventDefault();
        //do some other stuff here
        $('.Drawer__Close').trigger('click');
        let hreff = $(this).attr('href');
        window.location.replace(hreff);
        //window.location.href = hreff;
    });


    
    $('.scrollme').on('click', function (el) {
      el.preventDefault();

      $('html, body').animate({
        scrollTop: $($(this).attr('href')).offset().top
      }, 700, 'linear');
      
     // $('.naidoc-submenu-list.n-mobile ul').css( "display", "none" );
     
    });
    
    
    //stickybits('.naidoc-submenu');
    var stickyz = new Sticky('.naidoc-submenu');
    //var scroll = new SmoothScroll('[data-scroll]');
    
    
    
    $('.Section--bundles .ProductForm__AddToCart').click( function(e) {
        e.preventDefault();
        // .btn animation
      
      console.log(1);

        let bundleItems = [];

        $(this).closest('.ProductList').find('.product-form__variants option:selected').each( function(){
            let variantId = $(this).val();

            bundleItems.push({
                quantity: 1,
                id: variantId
            });
          
            console.log(2);
        });
      
        $(this).closest('.ProductList').find('.bundle-input').each( function(){
            let variantId = $(this).val();

            bundleItems.push({
                quantity: 1,
                id: variantId
            });
          
          console.log(bundleItems);
        });



        jQuery.post('/cart/add.js', {
            items: bundleItems
        }, function( data ) {
            console.log(4);
            window.location.href = '/cart';
        }, "json");


    });
    
    
    
  
  });
  
  
</script>


