{% if page.content contains "split" %}
{% assign my_description = page.content | split: '<!-- split -->'  %}
{{ my_description[0] }}
{% endif %}
<div class="main-custom-page-width">

 
    <div class="SectionHeader SectionHeader">
      <h1 class="SectionHeader__Heading Heading u-h1">{{ page.title }}</h1>
    </div>


  <div class="PageContent--narrow Rte {% if page.handle contains "promotional-products"%} enquiry_custom{% endif %}">
    
    {% if page.content contains "split" %}{{ my_description[1] }}{% else %}{{page.content}}{% endif %}
    
    
        <form id="trackingForm" action="" method="POST">
          <p><label>Email</label><input type="email" required name="email" class="Form__Input"></p>
          <p><label>Order Number (without # symbol)</label><input type="†ext" required name="order_id" class="Form__Input"></p>
          <button class="But<PERSON> Button--primary">Submit</button>
        </form>
        <ul id="tracking_urls"></ul>
        <span id="error-message"></span>
      
 
  </div>

</div>

<script type="text/javascript">
  $(document).ready(function() {
      $(document).on("submit", "#trackingForm", function(e) {
          e.preventDefault();
          e.stopPropagation();
        
          $("#tracking_urls").html("");
          $("#error-message").html("searching...");

          $.ajax({
              url: 'https://responsivemedia.com.au/tracking/get_tracking.php',
              type: 'POST',
              data: $(this).serializeArray(),
              dataType: 'json',
              success: function(response) {
                
              $("#tracking_urls,#error-message").html("");
                
                  if(response.status == "success") {
                    if(response.data && response.data.length > 0) {
                      $(response.data).each(function(i, v) {
                        
                        if(v.includes("google")) {
                          $("#error-message").html("Your order contains a face mask. Unfortunately we can't provide explicit tracking for your order, however, <b>your order is on its way.</b>");
                        } else {
                          $("#tracking_urls").append('<li><a href="'+v+'" target="_blank">'+v+'</a></li>');
                        }
                      
                      
                      });
                    } else {
                     	 $("#error-message").html("Your order is still being processed by our warehouse.");
                         console.log(response.data);
                    }
                  } else {
					$("#error-message").html("Oops, looks like your data isn't correct. Please check you entry.");
                    console.log(response.data);
                  }
              },
              error: function() {

              }
          });
      });
  });
</script>