<style>
  
  .style-blocks {
    display: flex;
    flex-wrap: wrap;
  }
  
  .style-blocks .style-block {
    width: 50%;
    padding: 15px;
  }
  
  .block2 .content{
    padding: 25px 0;
  }
  
  .block2 .content h2{
    margin: 0;
    color: black;
    font-size: 26px;
    margin-bottom: 15px;
    line-height: 1.1em;
  }
  
  .block2 .content .Button{
    border: 1px solid black;
  }
  
  .block2 .styleimg {
    padding-bottom: 100%;
    width: 100%;
  }
  
  .block1 {
    height: 100%;
  }
  
  .SectionHeader.SectionHeader .SectionHeader__Heading {
    text-align: center;
    color: black;
    text-transform: uppercase;
    font-size: 40px;
    line-height: 1.1em;
  }
  
  .SectionHeader.SectionHeader {
    margin: 0;
  }
  
  @media (max-width: 500px) {
    .style-blocks .style-block {
      width: 100%;
    }
    
    .style-blocks .style-block.style1 {
      order: -1;
    }
    
    .style-blocks .block2 .styleimg {
      display: none;
    }
    
    .block1 {
      height: auto;
      padding-bottom: 133%;
    }
    
    .SectionHeader.SectionHeader .SectionHeader__Heading {
      font-size: 30px;
    }
    
    .block2 .content h2{
      font-size: 22px;
    }
  }
  
</style>


{% if page.content contains "split" %}
{% assign my_description = page.content | split: '<!-- split -->'  %}
{{ my_description[0] }}
{% endif %}
<div class="" style="padding: 40px 0">

 
    <div class="SectionHeader SectionHeader">
      <h1 class="SectionHeader__Heading Heading u-h1 playfair">{{ page.title }}</h1>
    </div>


  <div class="PageContent" style="margin-top: 20px">
    
    {% section 'styles-trends-section' %}
    
    {% if page.content contains "split" %}{{ my_description[1] }}{% else %}{{page.content}}{% endif %}
 
  </div>

</div>