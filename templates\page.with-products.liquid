<style>
  
  body .Rte h2.style-header {
    font-size: 18px;
    font-weight: bold;
    color: black;
  }
  
  .shop-style {
    display: flex;
    flex-wrap: wrap;
  }
  
  .shop-style .prod-block{
    width: 33%;
    position: relative;
    text-align: center;
    padding: 5px;
  }
  
  .shop-style .prod-block img{
    margin: 5px 0;
  }
  
  .shop-style .prod-block h3{
    margin-bottom: 5px;
    font-size: 15px;
  }
  
  .shop-style .prod-block a{
    position: absolute;
    top: 0; bottom: 0; left: 0; right: 0;
  }
  
  @media (max-width: 600px) {
    .shop-style .prod-block{
      width: 50%;
    }
  }
  
  @media (max-width: 400px) {
    .shop-style .prod-block{
      width: 100%;
    }
  }
  
</style>


{% assign my_description = page.content | split: '<!-- split -->'  %}
{% assign prods = my_description[1] | split: ',' %}


<div class="main-custom-page-width">

  <div class="PageContent--narrow Rte">
    
    {{ my_description[0] }}
    
    <h2 class="style-header">SHOP THIS STYLE</h2>

    <div class="shop-style">
      
      {% for prod in prods %}
      
      <div class="prod-block">
        {%- assign image_url = all_products[prod].featured_image.src | img_url: '300x400' -%}
        <img src="{{ image_url }}">
        <h3>{{ all_products[prod].title }}</h3>
        <p>{{ all_products[prod].price | money }}</p>
        <a href="{{ all_products[prod].url }}"></a>
      </div>

      {% endfor %}
      
    </div>
  
  </div>

</div>