<script src="https://cdn.rawgit.com/scottschiller/Snowstorm/master/snowstorm-min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/clipboard@2.0.8/dist/clipboard.min.js"></script>


{%- assign xmasday = "now" | date: "%d" -%}

<script>
  console.log('{{ xmasday }}');
  snowStorm.followMouse = false;
  snowStorm.flakesMaxActive = 182; 
  snowStorm.start();
</script>

{% assign pimage = "" %}
{% assign dlink = "" %}

{% if xmasday == "01" %}
  {% assign product = all_products["mawurji-dreaming-earrings"] %}
  {% assign gifttype = "product" %}
  {% assign title = "Free Gift!" %}
  {% assign message = "Minimum Spend $75" %}
  {% assign pimage = "https://cdn.shopify.com/s/files/1/0247/4021/files/Mawurji_Dreaming_Earrings.png?v=1669865667" %}
  

{% elsif xmasday == "02" %}
  {%- assign product = all_products["artists-of-yarn-2023-calendar"] -%}
  {% assign gifttype = "product" %}
  {% assign title = "Free Gift!" %}
  {% assign message = "Minimum Spend $75" %}
  {% assign pimage = "https://cdn.shopify.com/s/files/1/0247/4021/files/calendar.png?v=1669870431" %}


{% elsif xmasday == "03" %}
  {%- assign product = all_products["the-time-is-now-long-handle-natural-cotton-tote-bag"] -%}
  {% assign gifttype = "product" %}
  {% assign title = "Free Gift!" %}
  {% assign message = "Minimum Spend $75" %}
  {% assign pimage = "https://cdn.shopify.com/s/files/1/0247/4021/files/tote_bag.png?v=1669957924" %}

{% elsif xmasday == "04" %}
  {% assign product = "" %}
  {% assign gifttype = "discount" %}
  {% assign title = "20% Off Gift Sets/Bundles" %}
  {% assign message = "XMAS-20" %}
  {% assign pimage = "" %}
  {% assign dlink = "https://www.yarn.com.au/collections/gift-bundles" %}



{% elsif xmasday == "05" %}
  {%- assign product = all_products["empower-naidoc-week-2022-rpet-fold-up-bag"] -%}
  {% assign gifttype = "product" %}
  {% assign title = "Free Gift!" %}
  {% assign message = "" %}
  {% assign pimage = "https://cdn.shopify.com/s/files/1/0247/4021/files/bag_1-empower.png?v=1670146236" %}

{% elsif xmasday == "06" %}
  {%- assign product = all_products["ngapa-puyurru-necklace"] -%}
  {% assign gifttype = "product" %}
  {% assign title = "Free Gift!" %}
  {% assign message = "Minimum Spend $100" %}
  {% assign pimage = "https://cdn.shopify.com/s/files/1/0247/4021/files/necklace_1.png?v=1670226480" %}

{% elsif xmasday == "07" %}
  {%- assign product = all_products["shallows-small-rectangular-pencil-case"] -%}
  {% assign gifttype = "product" %}
  {% assign title = "Free Gift!" %}
  {% assign message = "Minimum Spend $100" %}
  {% assign pimage = "https://cdn.shopify.com/s/files/1/0247/4021/files/pencil_case_1.png?v=1670356360" %}

{% elsif xmasday == "08" %}
  {%- assign product = all_products["aboriginal-art-mouse-pad-best-gaming-desk-desert-seed"] -%}
  {% assign gifttype = "product" %}
  {% assign title = "Free Gift!" %}
  {% assign message = "Minimum Spend $100" %}
  {% assign pimage = "https://cdn.shopify.com/s/files/1/0247/4021/files/mouse_pad.png?v=1670405898" %}
  

{% elsif xmasday == "09" %}
  {%- assign product = all_products["aboriginal-clothes-coloured-ankle-socks-for-men-cotton-snakes"] -%}
  {% assign gifttype = "product" %}
  {% assign title = "Free Gift!" %}
  {% assign message = "Minimum Spend $100" %}
  {% assign pimage = "https://cdn.shopify.com/s/files/1/0247/4021/files/socks_78c6b146-e75b-44a4-90e9-5093bc25e2c7.png?v=1670405899" %}

{% elsif xmasday == "10" %}
  {%- assign product = all_products["warlu-hair-set-blue"] -%}
  {% assign gifttype = "product" %}
  {% assign title = "Free Gift!" %}
  {% assign message = "Minimum Spend $100" %}
  {% assign pimage = "https://cdn.shopify.com/s/files/1/0247/4021/files/hairset-blue.png?v=1670405897" %}

{% elsif xmasday == "11" %}
  {% assign product = "" %}
  {% assign gifttype = "discount" %}
  {% assign title = "20% off Gifts for Him" %}
  {% assign message = "GIFTS-FOR-HIM" %}
  {% assign pimage = "" %}
  {% assign dlink = "https://www.yarn.com.au/pages/gifts-for-him" %}

{% elsif xmasday == "12" %}
  {% assign product = "" %}
  {% assign gifttype = "discount" %}
  {% assign title = "Free Standard Shipping (min spend $75)" %}
  {% assign message = "XMAS-SHIP" %}
  {% assign pimage = "" %}
  {% assign dlink = "" %}

{% elsif xmasday == "13" %}
  {% assign product = "" %}
  {% assign gifttype = "discount" %}
  {% assign title = "Extra 10% off Clearance Collection" %}
  {% assign message = "XMAS-10" %}
  {% assign pimage = "" %}
  {% assign dlink = "https://www.yarn.com.au/collections/clearance" %}

{% elsif xmasday == "14" %}
  {% assign product = "" %}
  {% assign gifttype = "discount" %}
  {% assign title = "50% off the Retail Price of Mugs" %}
  {% assign message = "MUGS-50" %}
  {% assign pimage = "" %}
  {% assign dlink = "https://www.yarn.com.au/collections/drinkware" %}

{% elsif xmasday == "15" %}
  {% assign product = "" %}
  {% assign gifttype = "discount" %}
  {% assign title = "Free Express Shipping (Minimum Spend $100)" %}
  {% assign message = "XMAS-EXPRESS" %}
  {% assign pimage = "" %}
  {% assign dlink = "" %}


{% elsif xmasday == "16" %}
  {%- assign product = all_products["together-we-rise-naidoc-week-2022-rpet-fold-up-bag"] -%}
  {% assign gifttype = "product" %}
  {% assign title = "Free Gift!" %}
  {% assign message = "Minimum Spend $100" %}
  {% assign pimage = "https://cdn.shopify.com/s/files/1/0247/4021/files/bag_2_1.png?v=1671095808" %}

  
{% elsif xmasday == "17" %}
  {%- assign product = all_products["shallows-full-colour-printed-pen"] -%}
  {% assign gifttype = "product" %}
  {% assign title = "Free Gift!" %}
  {% assign message = "Minimum Spend $100" %}
  {% assign pimage = "https://cdn.shopify.com/s/files/1/0247/4021/files/pen_1.jpg?v=1671223338" %}


  {% elsif xmasday == "18" %}
  {% assign product = "" %}
  {% assign gifttype = "discount" %}
  {% assign title = "30% off ALL polos" %}
  {% assign message = "" %}
  {% assign pimage = "" %}
  {% assign dlink = "https://www.yarn.com.au/collections/polo-shirts-australia-aboriginal-art-clothes" %}
  

{% elsif xmasday == "19" %}
  {% assign product = "" %}
  {% assign gifttype = "discount" %}
  {% assign title = "20% off Gifts for Her" %}
  {% assign message = "" %}
  {% assign pimage = "" %}
  {% assign dlink = "https://www.yarn.com.au/pages/gifts-for-her" %}

{% elsif xmasday == "20" %}
  {%- assign product = all_products["my-mother-earth-cut-out-earrings"] -%}
  {% assign gifttype = "product" %}
  {% assign title = "Free Gift!" %}
  {% assign message = "Minimum Spend $100" %}
  {% assign pimage = "https://cdn.shopify.com/s/files/1/0247/4021/files/my-mother-earrings.jpg?v=1671343343" %}

{% elsif xmasday == "21" %}
  {%- assign product = all_products["warlu-hair-set-orange-blue"] -%}
  {% assign gifttype = "product" %}
  {% assign title = "Free Gift!" %}
  {% assign message = "Minimum Spend $100" %}
  {% assign pimage = "https://cdn.shopify.com/s/files/1/0247/4021/files/hair-set-orange.jpg?v=1671343590" %}

{% elsif xmasday == "22" %}
  {%- assign product = all_products["artists-of-yarn-2023-calendar"] -%}
  {% assign gifttype = "product" %}
  {% assign title = "Free Gift!" %}
  {% assign message = "Minimum Spend $100" %}
  {% assign pimage = "https://cdn.shopify.com/s/files/1/0247/4021/files/calendar.png?v=1669870431" %}

 {% elsif xmasday == "23" %}
  {%- assign product = all_products["ngapa-puyurru-earrings"] -%}
  {% assign gifttype = "product" %}
  {% assign title = "Free Gift!" %}
  {% assign message = "Minimum Spend $100" %}
  {% assign pimage = "" %}

{% elsif xmasday == "24" %}
  {%- assign product = all_products["dreamy-daze-full-colour-printed-pen"] -%}
  {% assign gifttype = "product" %}
  {% assign title = "Free Gift!" %}
  {% assign message = "Minimum Spend $100" %}
  {% assign pimage = "" %}
  
{% endif %}




{%- assign selected_variant = product.selected_or_first_available_variant -%}

<style>
  
@font-face {
  font-family: 'Glamour Absolute';
  src: url('https://cdn.shopify.com/s/files/1/0247/4021/files/GlamourAbsolute-regular.woff2?v=1637286784') format('woff2'),
       url('https://cdn.shopify.com/s/files/1/0247/4021/files/GlamourAbsolute-regular.woff?v=1637286784') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
  
@font-face {
  font-family: 'TRY Vesterbro';
  src: url('https://cdn.shopify.com/s/files/1/0247/4021/files/TRYVesterbro-Medium.woff?v=1606435135') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'TRY Vesterbro';
  src: url('https://cdn.shopify.com/s/files/1/0247/4021/files/TRYVesterbro-Regular.woff?v=1606435135') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'TRY Vesterbro';
  src: url('https://cdn.shopify.com/s/files/1/0247/4021/files/TRYVesterbro-Bold.woff?v=1606435135') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}
  
#xmas-wrap {
  background-color: #c0d5c9;
  padding: 40px 0 80px;
  background: url(https://cdn.shopify.com/s/files/1/0247/4021/files/bg-green.png?v=1669846984) no-repeat top center; 
  background-size: cover;
  position: relative;
  overflow: hidden;
}
  
@media (max-width: 600px) {
  #xmas-wrap {
    background: url(https://cdn.shopify.com/s/files/1/0247/4021/files/BG-Mobile-top.jpg?v=1669866137) no-repeat top center; 
    background-size: contain;
    background-color: #c0d5c9;
    padding-bottom: 0!important;
  }

  #xmas-foot {
    background: url(https://cdn.shopify.com/s/files/1/0247/4021/files/BG-Mobile-bot.jpg?v=1669866232) no-repeat bottom center; 
    background-size: contain;
    background-color: #c0d5c9;
    height: 200px;
  } 
}
  
.xmas-bg-tl {
  position: absolute;
  top: -20px;
  left: -20px;
  text-align: left;
}
  
.xmas-bg-bl {
  position: absolute;
  bottom: 0;
  left: 0;
  text-align: left;
}
  
.xmas-bg-tr {
  position: absolute;
  top: 20%;
  right: 0;
  text-align: right;
}
  
.xmas-bg-br {
  position: absolute;
  bottom: -20px;
  right: -20px;
  text-align: right;
}
  
.xmas-bg-tl img, .xmas-bg-tr img, .xmas-bg-bl img, .xmas-bg-br img {
  width: 80%;
}
  
@media (max-width: 1000px) {
  .xmas-bg-tl img, .xmas-bg-tr img, .xmas-bg-bl img, .xmas-bg-br img {
    width: 40%;
  }
  .xmas-bg-tr {
    top: 120px;
  }
  #xmas-wrap {
    padding-bottom: 80px;
  }
}
  
.xmas-grid {
  display: flex;
  flex-wrap: wrap;
  position: relative;
}
  
#xmas-wrap .intro {
  position: relative;
  padding: 0 10px;
  margin-bottom: 15px;
}
  
#xmas-wrap .intro h1 {
  color: #516a3e;
  font-size: 60px;
  line-height: 1.1em;
  font-family: 'Glamour Absolute', 'TRY Vesterbro', playfair-display, serif;
  font-style: normal;
}

#xmas-wrap .intro {
  text-align: center;
}

#xmas-wrap .intro img {
  width: 600px;
  max-width: 100%;
}
  
#xmas-wrap .intro p, #xmas-wrap .isintro p{
  color: #516a3e;
  margin-bottom: 25px;
  font-family: 'Glamour Absolute','TRY Vesterbro', playfair-display, serif;
  font-style: normal;
  font-size: 22px;
}
  
.xmas-grid .blox {
  width: calc((100% / 6) - 20px);
  margin: 10px;
  border-radius: 10px;
  position: relative;
  z-index: 0;
}
  
.xmas-grid .blox.isintro {
  width: calc((100% / 3) - 20px);
}
  
  @media (max-width: 1200px) {
    .xmas-grid .blox {
      width: calc((100% / 5) - 20px);
    }
    
    #xmas-wrap .intro:after {
      right: 10px;
      top: 0;
      width: 120px;
      height: 120px;
    }
  }
  
  @media (max-width: 900px) {
    .xmas-grid .blox {
      width: calc((100% / 4) - 20px);
    }
    
    .xmas-grid .blox.isintro {
       width: calc((100% / 2) - 20px);
     }
    
    #xmas-wrap .intro:after {
      right: 0px;
      top: 0;
      width: 90px;
      height: 90px;
    }
  }
  
  @media (max-width: 600px) {
    
    .xmas-grid .blox {
      width: calc((100% / 3) - 20px);
    }
    
    .xmas-grid .blox.isintro {
      width: 100%;
    }
    
    #xmas-wrap .intro:after {
      top: -25px;
    }
    #xmas-wrap .intro {
      padding-top: 50px;
    }
    
    #xmas-wrap .intro h1 {
      font-size: 40px;
      text-align: center;
    }
  
    #xmas-wrap .intro p, #xmas-wrap .isintro p {
      font-size: 18px;
      text-align: center;
    }
  }
  
  @media (max-width: 400px) {
    .xmas-grid .blox {
      width: calc((100% / 2) - 20px);
    }
    
     .xmas-grid .blox.isintro {
      width: 100%;
    }
    
  }
  
  .xmas-grid .blox.is-active  {
     background: #516a3e;
  }
  
  .xmas-grid .blox.is-active #xmashere {
    position: absolute;
    top: 0;
    z-index: 99;
  }
  
  .hitheretop {
    z-index: 9!important;;
  }
  
  .xmas-grid .blox.is-active a {
    position: absolute;
    top: 0; bottom: 0; left: 0; right: 0;
  }
  
  
  .xmas-grid .block {
    border-radius: 10px;
    border: 2px solid #516a3e;
    background-color: #c0d5c9;
    padding: 15px 10px;
    text-align: center;
    //box-shadow: 0px 1px 7px 1px #516a3e4f;
    position: relative;
    z-index: 1;
    width: 100%;
    height: 100%;
  }
  
  .xmas-grid .block h3{
    font-size: 60px;
    color: #516a3e;
    font-family: 'Glamour Absolute','TRY Vesterbro', playfair-display, serif;
    font-style: normal;
    //font-weight: 700;
    line-height: 1em;
    margin-bottom: 5px;
    margin-top: 0px;
    
  }
  
  .xmas-grid .block span {
    color: #516a3e;
    text-transform: uppercase;
    //font-family: 'Glamour Absolute','TRY Vesterbro', playfair-display, serif;
    font-style: normal;
    font-size: 20px;
    font-weight: bold;
  }
  
  .xmas-grid .blox.is-active .block {
    //transform: skew(0deg,4deg) translateY(5px) translateX(0px);
    //width: 95%;
     // background: rgb(250,241,228);
    //background: linear-gradient(270deg, rgba(242,231,214,1) 0%, rgba(242,231,214,1) 100%);
    transition: all ease .3s;
    
    background: #cce2d6;
    //background: linear-gradient(270deg, rgba(250,241,228,1) 0%, rgba(242,231,214,1) 100%);
  }
  
  .xmas-grid .blox.is-active .block:hover {
    //transform: skew(0deg,10deg) translateY(8px) translateX(0px);
    transform: skew(0deg,4deg) translateY(5px) translateX(0px);
    width: 92%;
    cursor: pointer;
    //background: rgb(250,241,228);
    //background: linear-gradient(270deg, rgba(250,241,228,1) 0%, rgba(242,231,214,1) 100%);
   // background: linear-gradient(270deg, #f5edeb 0%, #f2e2df 100%);
  }
  
  #xmas-modal {
    padding: 0;
  }
  
  #xmas-modal .xmas-topbar {
    background: #516a3e;
    color: white;
    padding: 5px 0;
  }
  
  #xmas-modal .xmas-wrap {
    display: flex;
    flex-wrap: wrap;
  }
  
  #xmas-modal .xmas-wrap .block{
    width: 50%;
    padding: 10px;
    color: #516a3e;
  }
  
  #xmas-modal .xmas-wrap .block h3{
    font-weight: 600;
    font-size: 18px;
  }
  
  #xmas-modal .xmas-wrap .block .Button--primary{
    border-color: #516a3e;
    
  }
  
  #xmas-modal .xmas-wrap .block .Button--primary::before {
    background-color: #516a3e;
  }
  
  #xmas-modal .xmas-wrap .block .Button--primary:not([disabled]):hover {
    color: #516a3e;
  }

  .xmas-badge {
    position: absolute;
    right: 100%;
    top: -100px;
    width: 180px;
    margin-right: 40px;
  }

  .xmas-badge img {
    width: 100%;
    height: auto;
  }

  .xmas-badge-intro {
    display: none;
    text-align: center;
    margin-bottom: 40px;
  }

  .xmas-badge-intro img {
    width: 140px!important;
  }

  @media (max-width: 1500px) {
    .xmas-badge {
      display: none;
    }

    .xmas-badge-intro {
      display: block;
    }
  }
  
  
</style>

<div id="xmas-wrap">
<div class="main-custom-page-widtht">

  
    <div class="PageContent">

      <div class="intro">
        <div class="xmas-badge-intro"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/Asset_1_2x_f7558ae8-c680-4c04-80a7-34e609e0bb55.png?v=1669859086"></div>
        <h1><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/Asset_4_2x_034351b9-bd10-4358-be84-6a764fe330b6.png?v=1669846613"></h1>
      </div>
      
      <div class="xmas-grid">

        <div class="xmas-badge"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/Asset_1_2x_f7558ae8-c680-4c04-80a7-34e609e0bb55.png?v=1669859086"></div>
        
        <div class="blox {% if xmasday == "01" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">thu</span>
            <h3 class="xmas-no">1</h3>
          </div>
          
        </div>
        
        <div class="blox {% if xmasday == "02" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">fri</span>
            <h3 class="xmas-no">2</h3>
          </div>
        </div>
        
        <div class="blox {% if xmasday == "03" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">sat</span>
            <h3 class="xmas-no">3</h3>
          </div>
        </div>
        
        <div class="blox {% if xmasday == "04" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">sun</span>
            <h3 class="xmas-no">4</h3>
          </div>
        </div>
        
        <div class="blox {% if xmasday == "05" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">mon</span>
            <h3 class="xmas-no">5</h3>
          </div>
        </div>
        
        <div class="blox {% if xmasday == "06" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">tue</span>
            <h3 class="xmas-no">6</h3>
          </div>
        </div>
        
        <div class="blox {% if xmasday == "07" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">wed</span>
            <h3 class="xmas-no">7</h3>
          </div>
        </div>
        
        <div class="blox {% if xmasday == "08" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">thu</span>
            <h3 class="xmas-no">8</h3>
          </div>
        </div>
        
        <div class="blox {% if xmasday == "09" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">fri</span>
            <h3 class="xmas-no">9</h3>
          </div>
        </div>
        
        <div class="blox {% if xmasday == "10" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">sat</span>
            <h3 class="xmas-no">10</h3>
          </div>
        </div>
        
        <div class="blox {% if xmasday == "11" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">sun</span>
            <h3 class="xmas-no">11</h3>
          </div>
        </div>
        
        <div class="blox {% if xmasday == "12" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">mon</span>
            <h3 class="xmas-no">12</h3>
          </div>
        </div>
        
        <div class="blox {% if xmasday == "13" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">tue</span>
            <h3 class="xmas-no">13</h3>
          </div>
        </div>
        
        <div class="blox {% if xmasday == "14" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">wed</span>
            <h3 class="xmas-no">14</h3>
          </div>
        </div>
        
        <div class="blox {% if xmasday == "15" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">thu</span>
            <h3 class="xmas-no">15</h3>
          </div>
        </div>
        
        <div class="blox {% if xmasday == "16" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">fri</span>
            <h3 class="xmas-no">16</h3>
          </div>
        </div>
        
        <div class="blox {% if xmasday == "17" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">sat</span>
            <h3 class="xmas-no">17</h3>
          </div>
        </div>
        
        <div class="blox {% if xmasday == "18" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">sun</span>
            <h3 class="xmas-no">18</h3>
          </div>
        </div>
        
        <div class="blox {% if xmasday == "19" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">mon</span>
            <h3 class="xmas-no">19</h3>
          </div>
        </div>

        <div class="blox {% if xmasday == "20" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">sun</span>
            <h3 class="xmas-no">20</h3>
          </div>
        </div>

        <div class="blox {% if xmasday == "21" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">mon</span>
            <h3 class="xmas-no">21</h3>
          </div>
        </div>

        <div class="blox {% if xmasday == "22" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">tue</span>
            <h3 class="xmas-no">22</h3>
          </div>
        </div>

        <div class="blox {% if xmasday == "23" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">wed</span>
            <h3 class="xmas-no">23</h3>
          </div>
        </div>

        <div class="blox {% if xmasday == "24" %}is-active{% endif %}">
          <div class="block">
            <span class="xmas-day">thu</span>
            <h3 class="xmas-no">24</h3>
          </div>
        </div>

        
        
      </div>
      
    </div>
  </div>

  {% comment %}
  <div class="xmas-bg-tl"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/xmas-bg-tl_2x_765f5541-0863-41aa-b13f-a6aa76c621a6.png?v=1606423952"></div>
  <div class="xmas-bg-bl"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/xmas-bg-bl_2x_416e99c4-c5ba-4c86-88a4-8557d5554538.png?v=1606423952"></div>
  <div class="xmas-bg-tr"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/xmas-bg-tr_2x_2315cb98-21ab-4dd1-89ed-0303ab407c7e.png?v=1606423952"></div>
  <div class="xmas-bg-br"><img src="https://cdn.shopify.com/s/files/1/0247/4021/files/xmas-bg-br_2x_9e9cf0d2-9b78-40ec-b9a4-2cfdda4c3add.png?v=1606423952"></div>
  {% endcomment %}
<div id="xmas-foot"></div> 
</div>




<div id="xmas-modal" style="display: none">
  <div class="xmas-topbar" style="text-align: center">
    {{ "now" | date: "%A %d %B" }}
  </div>

  
  <div class="xmas-wrap"style="">
    
    <div class="block" style="text-align: center">
      {% if gifttype == "discount" %}
        {% if pimage != blank %}
          <img src="{{ pimage }}">
        {% else %}
          <img src="https://cdn.shopify.com/s/files/1/0247/4021/files/xmas-discount.jpg?v=1606694214">
        {% endif %}
      {% else %}
        {% if pimage != blank %}
          <img src="{{ pimage }}">
        {% else %}
          <img src="{{ product.featured_image | img_url: "medium" }}">
        {% endif %}
      {% endif %}
    </div>
    
    <div class="block" style="padding: 30px 10px">
      
      {% if gifttype == "product" %}
      
          <h3>{{ title }}</h3>

          <p>{{ product.title }}</p>

      
          {% if cart.total_price < 10000 %}
      
            <p><i><small>Add a minimum of $100 worth of products to your cart then return to this page in order to gain access to this gift.</small></i></p>
            <button class="Button Button--secondary" style="margin: 10px 0" disabled="disabled">Min $100 spend in cart</button>

          {% else %}
            {% form "product", product %}
      
            <input type="hidden" name="id" data-sku="{{ selected_variant.sku }}" value="{{ selected_variant.id }}">
            <button type="submit" class="Button Button--primary" style="margin: 10px 0">Add to cart</button>
            {% endform %}

          {% endif %}
      
      
      {% endif %}
      
      
      {% if gifttype == "discount" %}
      
      {% if xmasday == "18" %}
      
      <h3>30% off ALL polos</h3>
       <a class="Button Button--primary" href="https://www.yarn.com.au/collections/polo-shirts-australia-aboriginal-art-clothes" style="margin: 10px 0">Shop Now</a>
      {% else %}
      
          <h3>{{ title }}</h3>

          <p style="margin-bottom: 0">Discount code:</p>
          <h3 style="margin-top: 0">{{ message }}</h3>
 
          <p><button class="clip-btn btn" data-clipboard-text="{{ message }}" style="padding: 2px 15px; border-radius: 10px;margin-top: -10px; margin-bottom: 20px; border: dashed #516a3e 2px;">
              Copy code
          </button></p>
      
          {% if dlink != blank %}
             <a class="Button Button--primary" href="{{ dlink }}" style="margin: 10px 0">Shop Now</a>
          {% endif %}
      {% endif %}
      
      <script>
        var clipboard = new ClipboardJS('.clip-btn');

        clipboard.on('success', function(e) {
            document.querySelector(".clip-btn").innerHTML = "Copied!";
        });
      </script>

      {% endif %}
      
      
      
    </div>
    
    
  </div>
</div>



<script>
/*

var myCanvas = document.createElement('canvas');
document.body.appendChild(myCanvas);

var myConfetti = confetti.create(myCanvas, {
  resize: true,
  useWorker: true
});
myConfetti({
  particleCount: 100,
  spread: 160
  // any other options from the global
  // confetti function
});
  
*/
  
// blox is-active
  
jQuery(function($) {

        $('.blox.is-active').on('click', function(e) {
          e.preventDefault();
          
          $('#xmas-modal').modal();
        });

});

  
</script>