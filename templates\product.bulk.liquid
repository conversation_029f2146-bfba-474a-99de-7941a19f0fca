<style>
  .Button--primary::before {
  background-color: #637381!important;
  }

  .Button--primary:not([disabled]):hover {
    color: #637381!important;
  }

  .Button--primary {
    border-color: #637381!important;
  }
  .Price--compareAt:before {
    background: none!important;
  }
</style>

{% section 'product-template' %}

{% comment %}
{% section 'related-products' %}
{% endcomment %}

{% section 'product-recommendations' %}
{% section 'recently-viewed-products' %}


<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/shopify-cartjs/0.4.3/cart.min.js"></script>

<script type="text/javascript">

jQuery(function() {
  CartJS.init({{ cart | json }});
  
  $('.size-guide').on('click', function() {
      $('#ex1').modal();
  });
  
  /*
  // define a new observer
  var obs = new MutationObserver(function(mutations, observer) {
      // look through all mutations that just occured
      for(var i=0; i<mutations.length; ++i) {
          // look through all added nodes of this mutation
          for(var j=0; j<mutations[i].addedNodes.length; ++j) {
              // was a child added with ID of 'bar'?
              if(mutations[i].addedNodes[j].id == "revy-upsell-popup-block") {
                  console.log("bar was added!");
                  setTimeout(function(){ 
                    revyShip();
                    revyShipAhoy();
                  
                  }, 1500);
                  
              }
          }
      }
  });

  // have the observer observe foo for changes in children
  obs.observe($("body").get(0), {
    childList: true
  });
  
  
  function revyShipAhoy() {
    let rfield = document.querySelector('.revy-upsell-cart-summary-total-value-final');
    let roptions = {childList: true};
    let robserver = new MutationObserver(mCallback);
    
    function mCallback(mutations) {
    for (let mutation of mutations) {
      if (mutation.type === 'childList') {
        setTimeout(function(){ revyShip(); }, 1500);
      }
    }
   }

    robserver.observe(rfield, roptions);
  }


  function revyShip() {
      let newcarttotal = $('.revy-upsell-cart-summary-total-value-final.money').text();
      newcarttotal = newcarttotal.replace("$","");
      //newcarttotal = parseInt(newcarttotal);
      newcarttotal =  parseFloat(newcarttotal).toFixed(2);
      
      //let thecarttotal = CartJS.cart.total_price;
      console.log(newcarttotal);

      if($('#rev-shipping').length < 1) {
          $('.revy-upsell-divider').before('<div id="rev-shipping"></div>');
          console.log("rev ship div added!");
      }

      if(newcarttotal < 120) {
          console.log("rev less than needed");
          let remaining = 120 - newcarttotal;
          let dollars = parseFloat(remaining).toFixed(2);

          $('#rev-shipping').html('Spend $' + dollars + ' more and get FREE shipping!');

      } else {
          console.log("rev more than needed");
          $('#rev-shipping').html('You have activated Free Shipping!');
      }
  }
  
  */
  
  
  setTimeout(function(){
    $('.carousel-nav').flickity('resize');
  }, 1500);
  
  setTimeout(function(){
  $('.GiftList').flickity({
      // options
      "prevNextButtons": true,
      "pageDots": false,
      "wrapAround": false,
      "contain": true,
      "cellAlign": "center",
      "dragThreshold": 8,
      "groupCells": true,
      "arrowShape": {"x0": 20, "x1": 60, "y1": 40, "x2": 60, "y2": 35, "x3": 25}
    
    });
  $('.GiftList').addClass('flickity-enabled');
  }, 2000);
  
  
/*
$('.ProductForm__QuantitySelector .QuantitySelector__CurrentQuantity').on("change", function(e) {
  e.preventDefault(); 
  
  var target = document.body.querySelector('.ProductForm__QuantitySelector .QuantitySelector__CurrentQuantity');
  target.value = Math.min(Math.max(parseInt(target.value), 10), 999);

});


  
$('.ProductForm__QuantitySelector [data-action="decrease-quantity"]').on("click", function(e) {
  e.preventDefault();
  
  var element = document.body.querySelector('.QuantitySelector__Button[data-action="decrease-quantity"]');
  element.nextElementSibling.value = Math.max(parseInt(element.nextElementSibling.value), 11);

  updateBulkPrice(31, 21);
  
}); 

  
$('.ProductForm__QuantitySelector [data-action="increase-quantity"]').on("click", function(e) {
  e.preventDefault();

 // var element = document.body.querySelector('.QuantitySelector__Button[data-action="increase-quantity"]');
 // element.previousElementSibling.value = parseInt(element.previousElementSibling.value) + 1;
  updateBulkPrice(29, 19);
  

});
  
 
  
  
  function updateBulkPrice(price1, price2) {
    
    var target = document.body.querySelector('.ProductForm__QuantitySelector .QuantitySelector__CurrentQuantity'); 
    var price = $('.Price.Price--compareAt').text().substr(1);

    if(target.value >= price1) {

      price = parseFloat(price) * 0.85;
      price = price.toFixed(2);
      $('.bulkprice').text('$' + price);
      $('.ProductForm__AddToCart span[data-money-convertible]').text('$' + price);

    } else if(target.value >= price2) {

      price = parseFloat(price) * 0.90;
      price = price.toFixed(2);
      $('.bulkprice').text('$' + price);
      $('.ProductForm__AddToCart span[data-money-convertible]').text('$' + price);

    } else {
      price = parseFloat(price) * 0.95;
      price = price.toFixed(2);
      $('.bulkprice').text('$' + price);
      $('.ProductForm__AddToCart span[data-money-convertible]').text('$' + price);
    }  
    
  }
  
  
  */ 
  
  //updateBulkPrice(29, 19);
  
  function updateBulkPrice(discount, button) {

    var price = $('.Price.Price--compareAt').text().substr(1);

    price = parseFloat(price) * discount;
    price = price.toFixed(2);
    $('.bulkprice').text('$' + price);
    let qtyTotal = getQtyTotal();
    
    if(button) {
      $('.ProductForm__AddToCart span[data-money-convertible]').text('MIN QTY 10');
    } else {
      $('.ProductForm__AddToCart span[data-money-convertible]').text('$' + price);
      $('[data-btnqty]').html(qtyTotal + ' x&nbsp;');
      
    }
    
    

  }
  
  function getQtyTotal() {
    let qtyTotal = 0;
    
      $('.ProductForm__QuantitySelector .QuantitySelector__CurrentQuantity').each( function() {
        let qtyVal = $(this).val();
        console.log('qtyVal');
        qtyTotal = qtyTotal + parseInt(qtyVal);
        
      });
    
     return qtyTotal;
  }
  
  $('.QuantitySelector__Button').on('click', function() {
    setTimeout(function(){ 

     let qtyTotal = getQtyTotal();
     console.log(qtyTotal);

      if(qtyTotal > 9) {
        $('.ProductForm__AddToCart').prop("disabled", false).removeClass('Button--secondary');
          if($('.ProductForm__AddToCart.Button--primary').length == 0) {
            $('.ProductForm__AddToCart').addClass('Button--primary');
          }

      } else if($('.ProductForm__AddToCart.Button--primary').length > 0) {
        $('.ProductForm__AddToCart').prop("disabled", true).removeClass('Button--primary').addClass('Button--secondary');
      }

      if(qtyTotal > 49) {
        updateBulkPrice(0.7);
        
       
      } else if(qtyTotal > 19) {
        updateBulkPrice(0.8);
        
      } else if(qtyTotal > 9) {
        updateBulkPrice(0.9);
        
      } else {
        updateBulkPrice(0.9);
        
      }

      
    }, 500);
  });
  
  let BulkProdList = [];
  
  $('[data-action="add-to-cart-bulk"]').on('click', function() {
    
    $('.bulk-flex-row').each( function() {
                     
      let prodId = $(this).find("[data-value]").attr("data-value");
      let prodQty = $(this).find(".QuantitySelector__CurrentQuantity").val();
      prodQty = parseInt(prodQty);
      
      if (prodQty > 0) {
        
        let prod = {
          quantity: prodQty,
    	  id: prodId
        }
        
        BulkProdList.push(prod);
        //return BulkProdList;
        
        
      }
      
      
     
    });
    
    console.log(BulkProdList);
    
    jQuery.post('/cart/add.js', {
        items: BulkProdList
      }, function() {
   
  })
  .done(function() {
    window.location.href = '/cart';
  })
  .fail(function() {
    
  })
  .always(function() {
    
  });
   
  })
  
});
</script>