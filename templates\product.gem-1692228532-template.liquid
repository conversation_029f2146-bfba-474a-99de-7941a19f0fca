<style>
.blocker{position:fixed;top:0;right:0;bottom:0;left:0;width:100%;height:100%;overflow:auto;z-index:1;padding:20px;box-sizing:border-box;background-color:#000;background-color:rgba(0,0,0,0.75);text-align:center}.blocker:before{content:"";display:inline-block;height:100%;vertical-align:middle;margin-right:-0.05em}.blocker.behind{background-color:transparent}.modal{display:none;vertical-align:middle;position:relative;z-index:2;max-width:500px;box-sizing:border-box;width:90%;background:#fff;padding:15px 30px;-webkit-border-radius:8px;-moz-border-radius:8px;-o-border-radius:8px;-ms-border-radius:8px;border-radius:8px;-webkit-box-shadow:0 0 10px #000;-moz-box-shadow:0 0 10px #000;-o-box-shadow:0 0 10px #000;-ms-box-shadow:0 0 10px #000;box-shadow:0 0 10px #000;text-align:left}.modal a.close-modal{position:absolute;top:-12.5px;right:-12.5px;display:block;width:30px;height:30px;text-indent:-9999px;background-size:contain;background-repeat:no-repeat;background-position:center center;background-image:url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAAAXNSR0IArs4c6QAAA3hJREFUaAXlm8+K00Acx7MiCIJH/yw+gA9g25O49SL4AO3Bp1jw5NvktC+wF88qevK4BU97EmzxUBCEolK/n5gp3W6TTJPfpNPNF37MNsl85/vN/DaTmU6PknC4K+pniqeKJ3k8UnkvDxXJzzy+q/yaxxeVHxW/FNHjgRSeKt4rFoplzaAuHHDBGR2eS9G54reirsmienDCTRt7xwsp+KAoEmt9nLaGitZxrBbPFNaGfPloGw2t4JVamSt8xYW6Dg1oCYo3Yv+rCGViV160oMkcd8SYKnYV1Nb1aEOjCe6L5ZOiLfF120EjWhuBu3YIZt1NQmujnk5F4MgOpURzLfAwOBSTmzp3fpDxuI/pabxpqOoz2r2HLAb0GMbZKlNV5/Hg9XJypguryA7lPF5KMdTZQzHjqxNPhWhzIuAruOl1eNqKEx1tSh5rfbxdw7mOxCq4qS68ZTjKS1YVvilu559vWvFHhh4rZrdyZ69Vmpgdj8fJbDZLJpNJ0uv1cnr/gjrUhQMuI+ANjyuwftQ0bbL6Erp0mM/ny8Fg4M3LtdRxgMtKl3jwmIHVxYXChFy94/Rmpa/pTbNUhstKV+4Rr8lLQ9KlUvJKLyG8yvQ2s9SBy1Jb7jV5a0yapfF6apaZLjLLcWtd4sNrmJUMHyM+1xibTjH82Zh01TNlhsrOhdKTe00uAzZQmN6+KW+sDa/JD2PSVQ873m29yf+1Q9VDzfEYlHi1G5LKBBWZbtEsHbFwb1oYDwr1ZiF/2bnCSg1OBE/pfr9/bWx26UxJL3ONPISOLKUvQza0LZUxSKyjpdTGa/vDEr25rddbMM0Q3O6Lx3rqFvU+x6UrRKQY7tyrZecmD9FODy8uLizTmilwNj0kraNcAJhOp5aGVwsAGD5VmJBrWWbJSgWT9zrzWepQF47RaGSiKfeGx6Szi3gzmX/HHbihwBser4B9UJYpFBNX4R6vTn3VQnez0SymnrHQMsRYGTr1dSk34ljRqS/EMd2pLQ8YBp3a1PLfcqCpo8gtHkZFHKkTX6fs3MY0blKnth66rKCnU0VRGu37ONrQaA4eZDFtWAu2fXj9zjFkxTBOo8F7t926gTp/83Kyzzcy2kZD6xiqxTYnHLRFm3vHiRSwNSjkz3hoIzo8lCKWUlg/YtGs7tObunDAZfpDLbfEI15zsEIY3U/x/gHHc/G1zltnAgAAAABJRU5ErkJggg==')}.modal-spinner{display:none;position:fixed;top:50%;left:50%;transform:translateY(-50%) translateX(-50%);padding:12px 16px;border-radius:5px;background-color:#111;height:20px}.modal-spinner>div{border-radius:100px;background-color:#fff;height:20px;width:2px;margin:0 1px;display:inline-block;-webkit-animation:sk-stretchdelay 1.2s infinite ease-in-out;animation:sk-stretchdelay 1.2s infinite ease-in-out}.modal-spinner .rect2{-webkit-animation-delay:-1.1s;animation-delay:-1.1s}.modal-spinner .rect3{-webkit-animation-delay:-1.0s;animation-delay:-1.0s}.modal-spinner .rect4{-webkit-animation-delay:-0.9s;animation-delay:-0.9s}@-webkit-keyframes sk-stretchdelay{0%,40%,100%{-webkit-transform:scaleY(0.5)}20%{-webkit-transform:scaleY(1.0)}}@keyframes sk-stretchdelay{0%,40%,100%{transform:scaleY(0.5);-webkit-transform:scaleY(0.5)}20%{transform:scaleY(1.0);-webkit-transform:scaleY(1.0)}}
.variant-btn .size-guide {
    position: relative;
    display: inline-block;
    padding: 7px 15px 7px;
    margin: 0 8px 12px 0;
    font-style: normal;
    font-size: 12px;
    text-transform: uppercase;
    background-color: #1065724d;
    font-family: Futura,sans-serif;
    letter-spacing: .1em;
    line-height: 1.6;
    text-decoration: none;
    color: #106572;
    border: 2px solid #106572;
    font-weight: 600;
}

.variant-btn .variant-input label {
  color: black;
}

.ProductMeta {
    padding-bottom: 0;
}
</style>

<script>
  let yarnproduct;
  jQuery.getJSON('/products/{{ product.handle }}.js', function(product) {
    yarnproduct = product;
  });
</script>

{% if product.handle == 'splash-gel-hand-sanitiser' %}
  <script>
    window.location.replace("https://www.yarn.com.au/pages/mask-set");
  </script>
{% endif %}

<script>
      var isIE11 = /*@cc_on!@*/false || !!document.documentMode;
      if (isIE11) {
        window.location.replace("https://www.yarn.com.au{{ product.url }}?view=dropdown");
                                
      } 
</script>

{% if product.handle contains 'our-beautiful-country-naidoc-tote-bag-limited-edition' %}
  <script>
    window.location.replace("https://www.yarn.com.au/pages/outlet");
  </script>
{% endif %}

{% if product.tags contains "hideme" and product.handle != "yarn-gold" and product.handle != "blank-product" and product.handle != "yarn-gold-monthly" and product.handle != "mystery-box-1" %}
  
  {% unless product.handle contains "mystery-box" %}
  <script>
    window.location.replace("https://www.yarn.com.au/cart");
  </script>
{% endunless %}

{% endif %}


{% section 'product-template' %}

<div id="amazon-desk"></div>

{%- for tag in product.tags -%}

  {%- if tag contains ':artist' -%}
    {%- assign artist_page = tag | split: ':' | last -%}
    {%- assign artist_page = pages[artist_page] -%}
    {%- assign artist_title = artist_page.title -%}
    {%- assign artist_content = artist_page.content -%}
    {%- assign has_artist = true -%}
  {%- endif -%}


  {% unless has_artist %}

    {%- if tag contains 'ARTIST_' -%}
      {%- assign artist_page = tag | remove_first: "ARTIST_" -%}
      {%- assign artist_page = artist_page | split: ' ' -%}
      {%- capture artist_handle -%}artist-{{ artist_page | join: "-" }}{%- endcapture -%}
      
      {%- assign artist_page = pages[artist_handle] -%}
      {%- assign artist_title = artist_page.title -%}
      {%- assign artist_content = artist_page.content -%}
      {%- assign has_artist = true -%}
    {%- endif -%}

  {% endunless %}


  {%- if tag contains '__tab1' -%}
  {%- unless tag contains ':artist' -%}
    {%- assign first_custom_page = tag | split: ':' | last -%}
    {%- assign first_custom_page = pages[first_custom_page] -%}

    {%- assign unique_tab_1_title = first_custom_page.title -%}
    {%- assign unique_tab_1_title = unique_tab_1_title | split: ' - ' | first -%}
    {%- assign unique_tab_1_content = first_custom_page.content -%}
    {%- assign has_unique_tab_1 = true -%}
  {% endunless %}
  {%- endif -%}

  {%- if tag contains '__tab2' -%}
  {%- unless tag contains ':artist' -%}
    {%- assign second_custom_page = tag | split: ':' | last -%}
    {%- assign second_custom_page = pages[second_custom_page] -%}

    {%- assign unique_tab_2_title = second_custom_page.title -%}
    {%- assign unique_tab_2_title = unique_tab_2_title | split: ' - ' | first -%}
    {%- assign unique_tab_2_content = second_custom_page.content -%}
    {%- assign has_unique_tab_2 = true -%}
  {% endunless %}
  {%- endif -%}

  {%- if tag contains '__tab3' -%}
  {%- unless tag contains ':artist' -%}
    {%- assign third_custom_page = tag | split: ':' | last -%}
    {%- assign third_custom_page = pages[third_custom_page] -%}

    {%- assign unique_tab_3_title = third_custom_page.title -%}
    {%- assign unique_tab_3_title = unique_tab_3_title | split: ' - ' | first -%}

    {%- if tag contains 'customise' -%}
      {%- assign unique_tab_3_title = unique_tab_3_title | split: ' ' | first -%}
    {%- endif -%}
   
    
    {%- assign unique_tab_3_content = third_custom_page.content -%}
    {%- assign has_unique_tab_3 = true -%}
  {% endunless %}
  {%- endif -%}

{%- endfor -%}

{% assign productTags = product.tags | join: ', ' %}


{% section 'section-new-artist' %}



{% section 'product-recommendations' %}


{% comment %}
{% section 'recently-viewed-products' %}
{% endcomment %}

<script type="text/javascript">
  var _learnq = _learnq || [];

  var item = {
    Name: {{ product.title|json }},
    ProductID: {{ product.id|json }},
    Categories: {{ product.collections|map:'title'|json }},
    ImageURL: "https:{{ product.featured_image.src|img_url:'grande' }}",
    URL: "{{ shop.secure_url }}{{ product.url }}",
    Brand: {{ product.vendor|json }},
    Price: {{ product.price|money|json }},
    CompareAtPrice: {{ product.compare_at_price_max|money|json }}
  };

  _learnq.push(['track', 'Viewed Product', item]);
  _learnq.push(['trackViewedItem', {
    Title: item.Name,
    ItemId: item.ProductID,
    Categories: item.Categories,
    ImageUrl: item.ImageURL,
    Url: item.URL,
    Metadata: {
      Brand: item.Brand,
      Price: item.Price,
      CompareAtPrice: item.CompareAtPrice
    }
  }]);
</script>

<script type="text/javascript">
	var _learnq = _learnq || [];
	document.querySelector('.ProductForm__AddToCart').addEventListener('click',function (){
 		_learnq.push(['track', 'Added to Cart', item]);
	});
</script>

<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/shopify-cartjs/0.4.3/cart.min.js"></script>

<script type="text/javascript">
  jQuery(function() {
    CartJS.init({{ cart | json }});
  });
</script>


<!-- Giftlist + new Variant Button update -->

<script type="text/javascript">
  
  
  
  
    function updateQueryString(key, value, url) {
    if (!url) url = window.location.href;

    let updated = ''
    var re = new RegExp("([?&])" + key + "=.*?(&|#|$)(.*)", "gi"),
        hash;

    if (re.test(url)) {
        if (typeof value !== 'undefined' && value !== null) {
            updated = url.replace(re, '$1' + key + "=" + value + '$2$3');
        } 
        else {
            hash = url.split('#');
            url = hash[0].replace(re, '$1$3').replace(/(&|\?)$/, '');
            if (typeof hash[1] !== 'undefined' && hash[1] !== null) {
                url += '#' + hash[1];
            }
            updated = url;
        }
    }
    else {
        if (typeof value !== 'undefined' && value !== null) {
            var separator = url.indexOf('?') !== -1 ? '&' : '?';
            hash = url.split('#');
            url = hash[0] + separator + key + '=' + value;
            if (typeof hash[1] !== 'undefined' && hash[1] !== null) {
                url += '#' + hash[1];
            }
            updated = url;
        }
        else {
            updated = url;
        }
    }

    window.history.replaceState({ path: updated }, '', updated);
}
  
  var ezz = document.querySelector(".Select--primary select");
  
  if(ezz) {
    var valuezz = ezz.options[ezz.selectedIndex].value;
    updateQueryString('variant', valuezz);
  }
  
  
  
  jQuery(function($) {
    
  function showLabel(inv) {
    if(inv == 1) {
        $('.inv-labels span.label-one').css("display", "block");
      } else if (inv <= 5) {
        $('.inv-labels span.label-few').css("display", "block");
    }
  }
      
    const invv = $('.ProductForm__Variants .variant-input input:checked').attr('data-inventory'); 
    
    showLabel(invv);

    $('.variant-btn label').on('click', function() {

        if($(this).prev('input:checked')) {

            let varId = $(this).prev().val();
            let obj = meta.product.variants.find(o => o.id == varId);
            let obj2 = yarnproduct.variants.find(o => o.id == varId);
            let price = obj.price;
            let comprice = obj2.compare_at_price;


            var dollars = (price / 100).toFixed(2);
            var comdollars = (comprice / 100).toFixed(2);
            var afterdollars = (dollars / 4).toFixed(2);
            
            
			dollars = parseFloat(dollars, 10).toFixed(2);
            comdollars = parseFloat(comdollars, 10).toFixed(2);

            

          $('.ProductMeta__PriceList .ProductMeta__Price:first-child').html('$' + dollars);
            if(comdollars > dollars) {
          $('.ProductMeta__PriceList .ProductMeta__Price.Price--compareAt s').html('$' + comdollars);
              
          } else {
            $('.ProductMeta__PriceList .ProductMeta__Price.Price--compareAt s').html('');
             
          }
          
          $('#yarncoinspan').html(price);
          

          $('.ProductForm__AddToCart [data-money-convertible]').html('$' + dollars);
          $('.afterpay-instalments').html('$' + afterdollars);
          
        }

    });
    
    

    $('.size-guide').on('click', function() {
        $('#ex1').modal();
    });

    setTimeout(function(){
      $('.carousel-nav').flickity('resize');
    }, 1500);

    setTimeout(function(){
    $('.GiftList').flickity({
        // options
        "prevNextButtons": true,
        "pageDots": false,
        "wrapAround": false,
        "contain": true,
        "cellAlign": "center",
        "dragThreshold": 8,
        "groupCells": true,
        "draggable": true,
        "arrowShape": {"x0": 20, "x1": 60, "y1": 40, "x2": 60, "y2": 35, "x3": 25}

      });
    $('.GiftList').addClass('flickity-enabled');
    }, 2000);

    $('.variant-btn label').on( 'click', function() {
      
      $('.inv-labels span').css("display", "none");
      
      let myVal = $(this).prev().val();
      let myInv = $(this).prev().attr('data-inventory');
      
    
      
      
      $('.Select--primary select').attr('selected', '');
      $('.Select--primary select').val(myVal);
      
     updateQueryString('variant', myVal);
      
     showLabel(myInv);
      
      /*
      if(myInv == 1) {
        $('.inv-labels span.label-one').css("display", "block");
      } else if (myInv <= 3) {
        $('.inv-labels span.label-few').css("display", "block");
      }
      */
      
    });

  });
  
  
    window.addEventListener("load", function() {
	// store tabs variable
	var myTabs = document.querySelectorAll("ul.nav-tabs > li a");
    function myTabClicks(tabClickEvent) {
        
		for (var i = 0; i < myTabs.length; i++) {
			myTabs[i].classList.remove("active");
		}
		var clickedTab = tabClickEvent.currentTarget;
		clickedTab.classList.add("active");
		tabClickEvent.preventDefault();
		var myContentPanes = document.querySelectorAll(".tab-pane");
		for (i = 0; i < myContentPanes.length; i++) {
			myContentPanes[i].classList.remove("active");
		}
		var anchorReference = tabClickEvent.target;
      
        var activePaneId = anchorReference.dataset.tab;
        
		//var activePaneId = anchorReference.getAttribute("href");
		var activePane = document.querySelector(activePaneId);
		activePane.classList.add("active");
	}
	for (i = 0; i < myTabs.length; i++) {
		myTabs[i].addEventListener("click", myTabClicks)
	}
});
  
</script>
