{% section 'product-template' %}

{% comment %}
{% section 'related-products' %}
{% endcomment %}

{% section 'product-recommendations' %}
{% section 'recently-viewed-products' %}

{% comment %}
{% section 'product-bundle' %}
{% endcomment %}



<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/shopify-cartjs/0.4.3/cart.min.js"></script>

<script type="text/javascript">
  jQuery(function() {
    CartJS.init({{ cart | json }});
  });
</script>


{% comment %}

<!-- Mothers Day Promo -->

{% unless product.has_only_default_variant %}

{% for collection in product.collections %}
  {% if collection.handle == 'mothers-day-collection' %}

    <script>
      jQuery(function() {
        let hasGift = false;
        
        jQuery.each(CartJS.cart.items, function(index, item) {
          if(item.id == 31481703891002) {
            hasGift = true;
          }
        });
        
        if(!hasGift) {
          
          $('.ProductForm__AddToCart').on('click', function(e) {
          e.preventDefault();
          const prodVarId = $('.ProductForm__Variants input:checked').val();
          const prodQty = $('.QuantitySelector__CurrentQuantity').val();

          jQuery.post('/cart/add.js', {
            items: [
              {
                quantity: 1,
                id: 31481703891002,
                properties: {
                  'Free Gift': 'Mothers Day'
                }
              },
              {
                quantity: prodQty,
                id: prodVarId
              }
            ]
          }).done(function() {
             window.location.replace("https://www.bundarra.org/cart");
          });
            
          });
           
        }
            
        
      });
    </script>

  {% endif %} 
{% endfor %}

{% endunless %}



{% if product.has_only_default_variant %}

{% for collection in product.collections %}
  {% if collection.handle == 'mothers-day-collection' %}

    <script>
      jQuery(function() {
        let hasGift = false;
        
        jQuery.each(CartJS.cart.items, function(index, item) {
          if(item.id == 31481703891002) {
            hasGift = true;
          }
        });
        
        if(!hasGift) {
          
          $('.ProductForm__AddToCart').on('click', function(e) {
          e.preventDefault();
          const prodVarId = $('.ProductForm__Variants input').val();
          const prodQty = $('.QuantitySelector__CurrentQuantity').val();

          jQuery.post('/cart/add.js', {
            items: [
              {
                quantity: 1,
                id: 31481703891002,
                properties: {
                  'Free Gift': 'Mothers Day'
                }
              },
              {
                quantity: prodQty,
                id: prodVarId
              }
            ]
          }).done(function() {
             window.location.replace("https://www.bundarra.org/cart");
          });
            
          });
           
        }
            
        
      });
    </script>

  {% endif %} 
{% endfor %}

{% endif %}

{% endcomment %}

<!-- Giftlist + new Variant Button update -->

<script type="text/javascript">
  jQuery(function() {
    

    $('.size-guide').on('click', function() {
        $('#ex1').modal();
    });

    setTimeout(function(){
      $('.carousel-nav').flickity('resize');
    }, 1500);

    setTimeout(function(){
    $('.GiftList').flickity({
        // options
        "prevNextButtons": true,
        "pageDots": false,
        "wrapAround": false,
        "contain": true,
        "cellAlign": "center",
        "dragThreshold": 8,
        "groupCells": true,
        "draggable": false,
        "arrowShape": {"x0": 20, "x1": 60, "y1": 40, "x2": 60, "y2": 35, "x3": 25}

      });
    $('.GiftList').addClass('flickity-enabled');
    }, 2000);

    $('.variant-btn label').on( 'click', function() {
      let myVal = $(this).prev().val();
      $('.Select--primary select').attr('selected', '');
      $('.Select--primary select').val(myVal);
    });

  });
</script>

{% comment %}
<!--Start Stamped.io Auto Installation--><div id="stamped-main-widget" class="stamped-main-widget" data-widget-style="standard" data-product-id="{{ product.id }}" data-name="{{ product.title | escape }}" data-url="{{ shop.url }}{{ product.url }}" data-image-url="{{ product.featured_image | product_img_url: "large" |replace: '?', '%3F' | replace: '&','%26'}}" data-description="{{ product.description | escape }}" data-product-sku="{{product.handle}}" data-product-type="{{product.type}}">{{ product.metafields.stamped.reviews }}</div><!--End Stamped.io Auto Installation-->
{% endcomment %}