{%- comment -%}
	SEO Manager - product meta search v3.0
	usage: /search?view=SEOManagerProductMeta&q=1000&page=1
	Copyright (c) venntov
	https://venntov.com / https://SEOManager.com
	Josh Highland

	NOTICE: All information contained herein is property of venntov.
	The intellectual and technical concepts contained herein are proprietary
	to venn<PERSON> and are protected by trade secret and copyright law.
	Reproduction of this code is strictly forbidden unless prior written
	permission is obtained from venn<PERSON>. If violated, legal action
	will be taken. Just don't do it.
{%- endcomment -%}

{%- layout none -%}
{%- paginate collections.all.products by search.terms -%}
	{%- assign productCount = collections.all.all_products_count -%}
	{%- capture results -%}
		{%- for item in collections.all.products  -%}
			{%- assign product = item -%}
			{%- assign meta_description = product.metafields.global.description_tag -%}
			{%- assign meta_title		= product.metafields.global.title_tag -%}
			{%- assign seoDone			= product.metafields.SEOMetaManager.seoDone -%}
			{%- assign seoScore			= product.metafields.SEOMetaManager.seoScore -%}

			{%- if meta_description == null -%}
				{%- capture meta_description -%}{{ product.description | strip_html | strip_newlines | truncate: 160 }} {% endcapture %}
			{%- endif -%}

			{%- if meta_title == null -%}
				{%- assign meta_title = product.title -%}
			{%- endif -%}

			{%- if seoDone == null -%}
				{%- assign seoDone = 0 -%}
			{%- endif -%}

			{%- if seoScore == null -%}
				{%- assign seoScore = 0 -%}
			{%- endif -%}

			{
				"productID"			: {{ product.id | JSON }},
				"title"				: {{ product.title | json }},
				"meta_title"		: {{ meta_title | json }},
				"meta_description"	: {{ meta_description | json }},
				"seo_done"			: {{ seoDone | json }},
				"seo_score"			: {{ seoScore | json }},
				"thumbnail"			: {{ product.featured_image.src | img_url : '100x100' | json }},
				"handle"			: {{ product.handle | json }}
			}
			{%- unless forloop.last -%},{%- endunless -%}
		{% endfor %}
	{%- endcapture -%}
{%- endpaginate -%}
{
	"productCount"	: {{ productCount }},
	"productData"	: [{{ results }}]
}