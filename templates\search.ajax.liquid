{% layout none %}

<div class="Segment">
  {% comment %}
  <div class="Segment__Title Segment__Title--flexed">
    {%- if search.types contains 'product' -%}
      <span class="Heading Text--subdued u-h7">{{ 'search.general.results_count' | t: count: search.results_count }}</span>
    {%- else -%}
      {%- if search.types contains 'article' and search.types contains 'page' -%}
        <span class="Heading Text--subdued u-h7">{{ 'search.general.pages_and_articles' | t }}</span>
      {%- elsif search.types contains 'article' -%}
        <span class="Heading Text--subdued u-h7">{{ 'search.general.articles' | t }}</span>
      {%- else -%}
        <span class="Heading Text--subdued u-h7">{{ 'search.general.pages' | t }}</span>
      {%- endif -%}
    {%- endif -%}

    {%- if search.results_count > 0 -%}
      <a class="Heading Link Link--secondary u-h7" href="/search?q={{ search.terms }}&type={{ search.types | join: ',' }}">{{ 'search.general.view_all' | t }}</a>
    {%- endif -%}
  </div>
  {% endcomment %}

  <div class="Segment__Content">
    {%- if search.results_count == 0 -%}
      <p>{{ 'search.general.no_results' | t }}</p>
    {%- else -%}
      {%- if search.types contains 'product' -%}
        {%- if settings.search_mode == 'product' -%}
          {%- assign results_count = 10 -%}
        {%- else -%}
          {%- assign results_count = 3 -%}
        {%- endif -%}

        <div class="Grid Grid--s">
          {%- for result in search.results limit: results_count -%}
          
           {% unless result.tags contains "hideme" %}
            {%- comment -%}The inline onclick is a bit hacky, but it's easier on mobile to have the full line clickable{%- endcomment -%}
            <div class="Grid__Cell 1/4--tablet-and-up" onclick="window.location.href = '{{ result.url }}'">
              {%- include 'product-item', product: result, show_labels: false, show_product_info: true -%}
            </div>
            {% endunless %}
          
          {%- endfor -%}
        </div>
        
        {%- if search.results_count > 0 -%}
         <h4 class="text--center" style="padding-top: 25px">
         <a class="Button Button--primary" href="/search?q={{ search.terms }}&type={{ search.types | join: ',' }}">{{ 'search.general.view_all' | t }}</a> </h4>
        {%- endif -%}
   
      {%- else -%}
        <ul class="Linklist">
          {%- for result in search.results limit: 6 -%}
            <li class="Linklist__Item">
              <a href="{{ result.url }}" class="Link Link--secondary">{{ result.title }}</a>
            </li>
          {%- endfor -%}
        </ul>
      {%- endif -%}
    {%- endif -%}
  </div>
</div>
