<section data-section-id="search" data-section-type="search">
  {%- if search.performed == false or search.results_count == 0 -%}
    <div class="EmptyState">
      <div class="Container">
        {% comment %}
        <h1 class="EmptyState__Title Heading u-h5">{{ 'search.general.title' | t }}</h1>
        
        
        {%- if search.performed -%}
          {%- assign filtered_terms = search.terms|replace:'*','' | replace: '*', '' -%}
          <p>{{ 'search.general.no_results_with_terms' | t: terms: filtered_terms }}</p>
        {%- else -%}
          <p>{{ 'search.general.content' | t }}</p>
        {%- endif -%}
        
        {% endcomment %}
        
        {%- if search.performed -%}
          {%- assign filtered_terms = search.terms|replace:'*','' | replace: '*', '' -%}
          <h1 class="EmptyState__Title Heading u-h5">{{ 'search.general.no_results_with_terms' | t: terms: filtered_terms }}</h1>
        {%- else -%}
          <h1 class="EmptyState__Title Heading u-h5">{{ 'search.general.content' | t }}</h1>
        {%- endif -%}

        <div class="EmptyState__Action" >
          <form method="GET" action="/search" class="Form">
            <input class="Form__Input" type="text" name="q" autocomplete="off" autocorrect="off" aria-label="{{ 'search.general.input_placeholder' | t }}" placeholder="{{ 'search.general.input_placeholder' | t }}">
            <input type="hidden" name="type" value="product">
          </form>
        </div>
      </div>
    </div>
  
  {%- else -%}
    {%- paginate search.results by 36 -%}
      <header class="PageHeader">
        <div class="Container">
          <div class="SectionHeader SectionHeader--center">
            {% comment %}
            <h1 class="SectionHeader__Heading Heading u-h1">{{ 'search.general.title' | t }}</h1>

            <div class="SectionHeader__Description">
              {%- assign filtered_terms = search.terms|replace:'*','' | replace: '*', '' -%}
              {{- 'search.general.results_with_terms_count' | t: count: search.results_count, terms: filtered_terms -}}
            </div>
            {% endcomment %}
            
            <h1 class="SectionHeader__Heading Heading u-h1">{{ search.terms }}</h1>
            
            
          </div>
        </div>
      </header>

      {%- if search.types contains 'product' -%}
        <div class="ProductList ProductList--grid Grid" data-mobile-count="2" data-desktop-count="4">
          {%- for result in search.results -%}
          {% unless result.tags contains "hideme" %}
            <div class="Grid__Cell 1/2 1/3--tablet 1/4--lap-and-up">
              {%- include 'product-item', product: result, show_labels: true, show_product_info: true -%}
            </div>
          {% endunless %}
          {%- endfor -%}
        </div>
      {%- else -%}
        <div class="ArticleListWrapper">
          <div class="ArticleList Grid Grid--m">
            {%- for result in search.results -%}
              <div class="Grid__Cell 1/2--tablet 1/3--lap-and-up">
                {%- if result.object_type == 'article' -%}
                  {%- include 'article-item', article: result -%}
                {%- elsif result.object_type == 'page' -%}
                  <article class="ArticleItem">
                    <div class="ArticleItem__Content">
                      <h2 class="ArticleItem__Title Heading u-h2">
                        <a href="{{ result.url }}">{{ result.title }}</a>
                      </h2>

                      <p class="ArticleItem__Excerpt">{{ result.content | strip_html | truncate: 150 }}</p>
                      <a href="{{ article.url }}" class="ArticleItem__Link Link Link--underline">{{ 'blog.article.read_more' | t }}</a>
                    </div>
                  </article>
                {%- endif -%}
              </div>
            {%- endfor -%}
          </div>
        </div>
      {%- endif -%}

      {%- include 'pagination' -%}
    {%- endpaginate -%}
  {%- endif -%}
</section>