{% if search.types contains 'product' or search.types == 'product' %}

{% comment %}
<section data-section-id="collection-template" data-section-type="collection" data-section-settings='{"collectionUrl": "","currentTags": [],"sortBy": "manual","filterPosition": "sidebar"}'>
	<div id="collection-filter-drawer" class="CollectionFilters Drawer Drawer--secondary Drawer--fromRight" aria-hidden="true">
		<header class="Drawer__Header Drawer__Header--bordered Drawer__Header--center Drawer__Container">
			<span class="Drawer__Title Heading u-h4">Filters</span>

			<button class="Drawer__Close Icon-Wrapper--clickable" data-action="close-drawer" data-drawer-id="collection-filter-drawer" aria-label="Close navigation"><svg class="Icon Icon--close" role="presentation" viewBox="0 0 16 14">
			<path d="M15 0L1 14m14 0L1 0" stroke="currentColor" fill="none" fill-rule="evenodd"></path>
			</svg></button>
		</header>

		<div class="Drawer__Content">
			<div class="Drawer__Main" data-scrollable>
				<div class="boost-pfs-filter-tree boost-pfs-filter-tree-v" data-is-mobile></div>
			</div>
		</div>a
	</div>
	<div id="collection-sort-popover" class="Popover" aria-hidden="true">
		<header class="Popover__Header">
			<button class="Popover__Close Icon-Wrapper--clickable" data-action="close-popover"><svg class="Icon Icon--close" role="presentation" viewBox="0 0 16 14">
			<path d="M15 0L1 14m14 0L1 0" stroke="currentColor" fill="none" fill-rule="evenodd"></path>
			</svg></button>
			<span class="Popover__Title Heading u-h4">Sort</span>
		</header>
		<div class="Popover__Content">
			<div class="Popover__ValueList boost-pfs-filter-top-sorting" data-scrollable="">{% include 'boost-pfs-filter-skeleton', width: 3 %}</div>
		</div>
	</div>
	<section  class="CollectionMain" data-section-id="search" data-section-type="search">
		<div class="CollectionToolbar CollectionToolbar--top CollectionToolbar--reverse boost-pfs-search-panel-product-show">
			<div class="CollectionToolbar__Group">
				<button class="CollectionToolbar__Item CollectionToolbar__Item--filter Heading Text--subdued u-h6 hidden-lap-and-up" data-action="open-drawer" data-drawer-id="collection-filter-drawer" aria-label="Show filters">
					Filter
				</button>
				 <button class="CollectionToolbar__Item CollectionToolbar__Item--sort Heading Text--subdued u-h6"
                          aria-label="{{ 'collection.sorting.show_sort' | t }}"
                          aria-haspopup="true"
                          aria-expanded="false"
                          aria-controls="collection-sort-popover">
                    {{ 'collection.sorting.title' | t }} {% include 'icon' with 'select-arrow' %}
                  </button>
			</div>
			<div class="CollectionToolbar__Item CollectionToolbar__Item--layout">
				<div class="CollectionToolbar__LayoutSwitch hidden-tablet-and-up">
					<button aria-label="Show one product per row" class="CollectionToolbar__LayoutType " data-action="change-layout-mode" data-grid-type="mobile" data-count="1">
						{% include 'icon' with 'wall-1' %}
					</button>
					<button aria-label="Show two products per row" class="CollectionToolbar__LayoutType is-active" data-action="change-layout-mode" data-grid-type="mobile" data-count="2">
						{% include 'icon' with 'wall-2' %}
					</button>
				</div>
				<div class="CollectionToolbar__LayoutSwitch hidden-phone">
					<button aria-label="Show two products per row" class="CollectionToolbar__LayoutType is-active" data-action="change-layout-mode" data-grid-type="desktop" data-count="2">
						{% include 'icon' with 'wall-2' %}
					</button>
					<button aria-label="Show four products per row" class="CollectionToolbar__LayoutType " data-action="change-layout-mode" data-grid-type="desktop" data-count="4">
						{% include 'icon' with 'wall-4' %}
					</button>
				</div>
			</div>
		</div>
		<header class="PageHeader">
			<div class="Container">
				<div class="SectionHeader SectionHeader--center">
					<h1 class="SectionHeader__Heading Heading u-h1 boost-pfs-search-result-header">{{ 'search.general.title' | t }}</h1>
				</div>
			</div>
		</header>
		<div class="Container">
			<form method="GET" action="{{ routes.search_url }}" class="Form">
				<input class="Form__Input" type="text" name="q" autocomplete="off" autocorrect="off" aria-label="{{ 'search.general.input_placeholder' | t }}" placeholder="{{ 'search.general.input_placeholder' | t }}">
				<input type="hidden" name="type" value="product">
			</form>
		</div>

		<div class="boost-pfs-search-result-toolbar Container">
			<div class="boost-pfs-search-result-item">
				<ul class="boost-pfs-search-result-panel-controls">
					<li>{% include 'boost-pfs-filter-skeleton', width: 2 %}</li>
					<li>{% include 'boost-pfs-filter-skeleton', width: 2 %}</li>
					<li>{% include 'boost-pfs-filter-skeleton', width: 1 %}</li>
				</ul>
			</div>
			<div class="boost-pfs-search-result-item">
				<span class="filters-toolbar__product-count boost-pfs-search-total-result">
					{% include 'boost-pfs-filter-skeleton', width: 2 %}
				</span>
			</div>
		</div>

		<div class="CollectionInner boost-pfs-search-panel-product-show">
			<div class="CollectionInner__Sidebar CollectionInner__Sidebar--withTopToolbar hidden-pocket">
				<div class="boost-pfs-filter-tree-mobile-button">{% include 'boost-pfs-filter-skeleton', type: 'button' %}</div>
				<div class="boost-pfs-filter-tree boost-pfs-filter-tree-v">
					{% comment %} Include placeholder template {% endcomment %}
					{% include 'boost-pfs-filter-skeleton', type: 'filter-tree' %}
				</div>
			</div>
			<div class="CollectionInner__Products">
				<div class="ProductListWrapper">
					<div class="boost-pfs-filter-products ProductList ProductList--grid Grid" data-mobile-count="2" data-desktop-count="4">
						{% comment %} Fix Shopify search term report {% endcomment %}
						{% paginate search.results by 2 %}
						  {% if search.performed %}
						    {% for item in search.results %}
						      <div class="grid__item" style="display: none !important"></div>
						    {% endfor %}
						  {% endif %}
						{% endpaginate %}
					</div>
				</div>
				<div class="boost-pfs-filter-bottom-pagination"></div>
				<div class="boost-pfs-filter-load-more"></div>
			</div>
		</div>
		<div class="boost-pfs-search-result-wrap boost-hidden Container">
			<div class="boost-pfs-search-result-collections">
				<div class="boost-pfs-search-result-list-item"><a href="#">{% include 'boost-pfs-filter-skeleton', width: 4 %}</a></div>
				<div class="boost-pfs-search-result-list-item"><a href="#">{% include 'boost-pfs-filter-skeleton', width: 3 %}</a></div>
				<div class="boost-pfs-search-result-list-item"><a href="#">{% include 'boost-pfs-filter-skeleton', width: 4 %}</a></div>
			</div>
			<div class="boost-pfs-search-result-collection-pagination">{% include 'boost-pfs-filter-skeleton', width: 4 %}</div>
		</div>
		<div class="boost-pfs-search-result-wrap boost-hidden Container">
			<div class="boost-pfs-search-result-pages">
				<div class="boost-pfs-search-result-list-item"><a href="#">{% include 'boost-pfs-filter-skeleton', width: 4 %}</a></div>
				<div class="boost-pfs-search-result-list-item"><a href="#">{% include 'boost-pfs-filter-skeleton', width: 3 %}</a></div>
				<div class="boost-pfs-search-result-list-item"><a href="#">{% include 'boost-pfs-filter-skeleton', width: 4 %}</a></div>
			</div>
			<div class="boost-pfs-search-result-page-pagination">{% include 'boost-pfs-filter-skeleton', width: 4 %}</div>
		</div>
	</section>
</section>

<script>
// Declare boostPFSThemeConfig variable
var boostPFSThemeConfig = {
	label: {
		sorting: {% assign temp = 'collections.sorting.title' | t  %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Sorting" {% endunless %},
		sorting_best_selling: {% assign temp = 'collections.sorting.best_selling' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Best Selling" {% endunless %},
		sorting_featured: {% assign temp = 'collections.sorting.featured' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Featured" {% endunless %},
		sorting_title_ascending: {% assign temp = 'collections.sorting.az' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Alphabetically, A-Z" {% endunless %},
		sorting_title_descending: {% assign temp = 'collections.sorting.za' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Alphabetically, Z-A" {% endunless %},
		sorting_price_ascending: {% assign temp = 'collections.sorting.price_ascending' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Lowest Price" {% endunless %},
		sorting_price_descending: {% assign temp = 'collections.sorting.price_descending' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Highest Price" {% endunless %},
		sorting_date_ascending: {% assign temp = 'collections.sorting.date_ascending' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Date, Old to New" {% endunless %},
		sorting_date_descending: {% assign temp = 'collections.sorting.date_descending' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Date, New to Old" {% endunless %},
		sorting_sale_descending: {% assign temp = 'collections.sorting.sale_descending' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "% Off" {% endunless %},
		sorting_relevance: {% assign temp = 'collections.sorting.relevance' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Relevance" {% endunless %},
		view_product: {% assign temp = 'collection.product.view_product' | t  %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "View" {% endunless %},
	
		sale: {% assign temp = 'product.labels.on_sale' | t   %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "On Sale" {% endunless %},
		sold_out: {% assign temp = 'product.labels.sold_out' | t   %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Sold out" {% endunless %},
		from: {% assign temp = 'collection.product.from_price_html' | t   %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "From" {% endunless %},
		
		search_results_zero: "{{ 'search.general.results_with_terms_count.zero' | t  }}",
		search_results_one: "{{ 'search.general.results_with_terms_count.one' | t  }}",
		search_results_other: "{{ 'search.general.results_with_terms_count.other' | t  }}",
    },
	custom: {
		section_id : {{ section.id | json }},
		product_show_price_on_hover: {{ settings.product_show_price_on_hover  | json }},
		product_info_alignment: {{ settings.product_info_alignment | json }},
		product_image_size: {{ settings.product_image_size | json }},
		product_show_secondary_image: {{ settings.product_show_secondary_image | json }},
		use_horizontal: false,
		show_product_info: true,
		show_color_swatch: false,
		show_labels: true,
		mobile_row: '2',
		tablet_row: '3',
		desktop_row: '4',
		filter_position: 'sidebar',
		products_per_page:  16 ,
		products_per_row:  4 ,
		products_per_row_mobile:  2 ,
		show_vendor:  false ,
		show_price:  false ,
		show_sale_label:  false ,
		show_sold_out_label:  false ,
		active_image_swap:  false ,
		show_sorting:  true ,
		show_layout_switch: true,
    	theme_script: "{{ 'theme.min.js' | asset_url }}",
	}
};
</script>

{% endcomment %}


<section data-section-id="collection-template" data-section-type="collection" data-section-settings='{"collectionUrl": "","currentTags": [],"sortBy": "manual","filterPosition": "sidebar"}'>
	<div id="collection-filter-drawer" class="CollectionFilters Drawer Drawer--secondary Drawer--fromRight" aria-hidden="true">
		<header class="Drawer__Header Drawer__Header--bordered Drawer__Header--center Drawer__Container">
			<span class="Drawer__Title Heading u-h4">Filters</span>

			<button class="Drawer__Close Icon-Wrapper--clickable" data-action="close-drawer" data-drawer-id="collection-filter-drawer" aria-label="Close navigation"><svg class="Icon Icon--close" role="presentation" viewBox="0 0 16 14">
			<path d="M15 0L1 14m14 0L1 0" stroke="currentColor" fill="none" fill-rule="evenodd"></path>
			</svg></button>
		</header>

		<div class="Drawer__Content">
			<div class="Drawer__Main" data-scrollable>
				<div class="boost-pfs-filter-tree boost-pfs-filter-tree-v" data-is-mobile></div>
			</div>
		</div>a
	</div>
	<div id="collection-sort-popover" class="Popover" aria-hidden="true">
		<header class="Popover__Header">
			<button class="Popover__Close Icon-Wrapper--clickable" data-action="close-popover"><svg class="Icon Icon--close" role="presentation" viewBox="0 0 16 14">
			<path d="M15 0L1 14m14 0L1 0" stroke="currentColor" fill="none" fill-rule="evenodd"></path>
			</svg></button>
			<span class="Popover__Title Heading u-h4">Sort</span>
		</header>
		<div class="Popover__Content">
			<div class="Popover__ValueList boost-pfs-filter-top-sorting" data-scrollable="">{% include 'boost-pfs-filter-skeleton', width: 3 %}</div>
		</div>
	</div>
	<section  class="CollectionMain" data-section-id="search" data-section-type="search">
		<div class="CollectionToolbar CollectionToolbar--top CollectionToolbar--reverse boost-pfs-search-panel-product-show">
			<div class="CollectionToolbar__Group">
				<button class="CollectionToolbar__Item CollectionToolbar__Item--filter Heading Text--subdued u-h6 hidden-lap-and-up" data-action="open-drawer" data-drawer-id="collection-filter-drawer" aria-label="Show filters">
					Filter
				</button>
				 <button class="CollectionToolbar__Item CollectionToolbar__Item--sort Heading Text--subdued u-h6"
                          aria-label="{{ 'collection.sorting.show_sort' | t }}"
                          aria-haspopup="true"
                          aria-expanded="false"
                          aria-controls="collection-sort-popover">
                    {{ 'collection.sorting.title' | t }} {% include 'icon' with 'select-arrow' %}
                  </button>
			</div>
			<div class="CollectionToolbar__Item CollectionToolbar__Item--layout">
				<div class="CollectionToolbar__LayoutSwitch hidden-tablet-and-up">
					<button aria-label="Show one product per row" class="CollectionToolbar__LayoutType " data-action="change-layout-mode" data-grid-type="mobile" data-count="1">
						{% include 'icon' with 'wall-1' %}
					</button>
					<button aria-label="Show two products per row" class="CollectionToolbar__LayoutType is-active" data-action="change-layout-mode" data-grid-type="mobile" data-count="2">
						{% include 'icon' with 'wall-2' %}
					</button>
				</div>
				<div class="CollectionToolbar__LayoutSwitch hidden-phone">
					<button aria-label="Show two products per row" class="CollectionToolbar__LayoutType is-active" data-action="change-layout-mode" data-grid-type="desktop" data-count="2">
						{% include 'icon' with 'wall-2' %}
					</button>
					<button aria-label="Show four products per row" class="CollectionToolbar__LayoutType " data-action="change-layout-mode" data-grid-type="desktop" data-count="4">
						{% include 'icon' with 'wall-4' %}
					</button>
				</div>
			</div>
		</div>
		<header class="PageHeader">
			<div class="Container">
				<div class="SectionHeader SectionHeader--center">
					<h1 class="SectionHeader__Heading Heading u-h1 boost-pfs-search-result-header">{{ 'search.general.title' | t }}</h1>
				</div>
			</div>
		</header>
		<div class="Container">
			<form method="GET" action="{{ routes.search_url }}" class="Form">
				<input class="Form__Input" type="text" name="q" autocomplete="off" autocorrect="off" aria-label="{{ 'search.general.input_placeholder' | t }}" placeholder="{{ 'search.general.input_placeholder' | t }}">
				<input type="hidden" name="type" value="product">
			</form>
		</div>

		<div class="boost-pfs-search-result-toolbar Container">
			<div class="boost-pfs-search-result-item">
				<ul class="boost-pfs-search-result-panel-controls">
					<li>{% include 'boost-pfs-filter-skeleton', width: 2 %}</li>
					<li>{% include 'boost-pfs-filter-skeleton', width: 2 %}</li>
					<li>{% include 'boost-pfs-filter-skeleton', width: 1 %}</li>
				</ul>
			</div>
			<div class="boost-pfs-search-result-item">
				<span class="filters-toolbar__product-count boost-pfs-search-total-result">
					{% include 'boost-pfs-filter-skeleton', width: 2 %}
				</span>
			</div>
		</div>

		<div class="CollectionInner boost-pfs-search-panel-product-show">
			<div class="CollectionInner__Sidebar CollectionInner__Sidebar--withTopToolbar hidden-pocket">
				<div class="boost-pfs-filter-tree-mobile-button">{% include 'boost-pfs-filter-skeleton', type: 'button' %}</div>
				<div class="boost-pfs-filter-tree boost-pfs-filter-tree-v">
					{% comment %} Include placeholder template {% endcomment %}
					{% include 'boost-pfs-filter-skeleton', type: 'filter-tree' %}
				</div>
			</div>
			<div class="CollectionInner__Products">
				<div class="ProductListWrapper">
					<div class="boost-pfs-filter-products ProductList ProductList--grid Grid" data-mobile-count="2" data-desktop-count="4">
						{% comment %} Fix Shopify search term report {% endcomment %}
						{% paginate search.results by 2 %}
						  {% if search.performed %}
						    {% for item in search.results %}
						      <div class="grid__item" style="display: none !important"></div>
						    {% endfor %}
						  {% endif %}
						{% endpaginate %}
					</div>
				</div>
				<div class="boost-pfs-filter-bottom-pagination"></div>
				<div class="boost-pfs-filter-load-more"></div>
			</div>
		</div>
		<div class="boost-pfs-search-result-wrap boost-hidden Container">
			<div class="boost-pfs-search-result-collections">
				<div class="boost-pfs-search-result-list-item"><a href="#">{% include 'boost-pfs-filter-skeleton', width: 4 %}</a></div>
				<div class="boost-pfs-search-result-list-item"><a href="#">{% include 'boost-pfs-filter-skeleton', width: 3 %}</a></div>
				<div class="boost-pfs-search-result-list-item"><a href="#">{% include 'boost-pfs-filter-skeleton', width: 4 %}</a></div>
			</div>
			<div class="boost-pfs-search-result-collection-pagination">{% include 'boost-pfs-filter-skeleton', width: 4 %}</div>
		</div>
		<div class="boost-pfs-search-result-wrap boost-hidden Container">
			<div class="boost-pfs-search-result-pages">
				<div class="boost-pfs-search-result-list-item"><a href="#">{% include 'boost-pfs-filter-skeleton', width: 4 %}</a></div>
				<div class="boost-pfs-search-result-list-item"><a href="#">{% include 'boost-pfs-filter-skeleton', width: 3 %}</a></div>
				<div class="boost-pfs-search-result-list-item"><a href="#">{% include 'boost-pfs-filter-skeleton', width: 4 %}</a></div>
			</div>
			<div class="boost-pfs-search-result-page-pagination">{% include 'boost-pfs-filter-skeleton', width: 4 %}</div>
		</div>
	</section>
</section>

<script>
// Declare boostPFSThemeConfig variable
var boostPFSThemeConfig = {
	label: {
		sorting: {% assign temp = 'collections.sorting.title' | t  %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Sorting" {% endunless %},
		sorting_best_selling: {% assign temp = 'collections.sorting.best_selling' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Best Selling" {% endunless %},
		sorting_featured: {% assign temp = 'collections.sorting.featured' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Featured" {% endunless %},
		sorting_title_ascending: {% assign temp = 'collections.sorting.az' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Alphabetically, A-Z" {% endunless %},
		sorting_title_descending: {% assign temp = 'collections.sorting.za' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Alphabetically, Z-A" {% endunless %},
		sorting_price_ascending: {% assign temp = 'collections.sorting.price_ascending' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Lowest Price" {% endunless %},
		sorting_price_descending: {% assign temp = 'collections.sorting.price_descending' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Highest Price" {% endunless %},
		sorting_date_ascending: {% assign temp = 'collections.sorting.date_ascending' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Date, Old to New" {% endunless %},
		sorting_date_descending: {% assign temp = 'collections.sorting.date_descending' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Date, New to Old" {% endunless %},
		sorting_sale_descending: {% assign temp = 'collections.sorting.sale_descending' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "% Off" {% endunless %},
		sorting_relevance: {% assign temp = 'collections.sorting.relevance' | t %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Relevance" {% endunless %},
		view_product: {% assign temp = 'collection.product.view_product' | t  %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "View" {% endunless %},
	
		sale: {% assign temp = 'product.labels.on_sale' | t   %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "On Sale" {% endunless %},
		sold_out: {% assign temp = 'product.labels.sold_out' | t   %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "Sold out" {% endunless %},
		from: {% assign temp = 'collection.product.from_price_html' | t   %} {% unless temp contains 'translation missing'  %} "{{ temp }}" {% else %} "From" {% endunless %},
		
		search_results_zero: "{{ 'search.general.results_with_terms_count.zero' | t  }}",
		search_results_one: "{{ 'search.general.results_with_terms_count.one' | t  }}",
		search_results_other: "{{ 'search.general.results_with_terms_count.other' | t  }}",
    },
	custom: {
		section_id : {{ section.id | json }},
		product_show_price_on_hover: {{ settings.product_show_price_on_hover  | json }},
		product_info_alignment: {{ settings.product_info_alignment | json }},
		product_image_size: {{ settings.product_image_size | json }},
		product_show_secondary_image: {{ settings.product_show_secondary_image | json }},
		use_horizontal: false,
		show_product_info: true,
		show_color_swatch: false,
		show_labels: true,
		mobile_row: '2',
		tablet_row: '3',
		desktop_row: '4',
		filter_position: 'sidebar',
		products_per_page:  16 ,
		products_per_row:  4 ,
		products_per_row_mobile:  2 ,
		show_vendor:  false ,
		show_price:  false ,
		show_sale_label:  false ,
		show_sold_out_label:  false ,
		active_image_swap:  false ,
		show_sorting:  true ,
		show_layout_switch: true,
    	theme_script: "{{ 'theme.min.js' | asset_url }}",
	}
};
</script>

{% else %}

<section data-section-id="search" data-section-type="search">
  {%- if search.performed == false or search.results_count == 0 -%}
    <div class="EmptyState">
      <div class="Container">
        {% comment %}
        <h1 class="EmptyState__Title Heading u-h5">{{ 'search.general.title' | t }}</h1>
        
        
        {%- if search.performed -%}
          {%- assign filtered_terms = search.terms|replace:'*','' | replace: '*', '' -%}
          <p>{{ 'search.general.no_results_with_terms' | t: terms: filtered_terms }}</p>
        {%- else -%}
          <p>{{ 'search.general.content' | t }}</p>
        {%- endif -%}
        
        {% endcomment %}
        
        {%- if search.performed -%}
          {%- assign filtered_terms = search.terms|replace:'*','' | replace: '*', '' -%}
          <h1 class="EmptyState__Title Heading u-h5">{{ 'search.general.no_results_with_terms' | t: terms: filtered_terms }}</h1>
        {%- else -%}
          <h1 class="EmptyState__Title Heading u-h5">{{ 'search.general.content' | t }}</h1>
        {%- endif -%}

        <div class="EmptyState__Action" >
          <form method="GET" action="/search" class="Form">
            <input class="Form__Input" type="text" name="q" autocomplete="off" autocorrect="off" aria-label="{{ 'search.general.input_placeholder' | t }}" placeholder="{{ 'search.general.input_placeholder' | t }}">
            <input type="hidden" name="type" value="product">
          </form>
        </div>
      </div>
    </div>
  
  {%- else -%}
    {%- paginate search.results by 36 -%}
      <header class="PageHeader">
        <div class="Container">
          <div class="SectionHeader SectionHeader--center">
            {% comment %}
            <h1 class="SectionHeader__Heading Heading u-h1">{{ 'search.general.title' | t }}</h1>

            <div class="SectionHeader__Description">
              {%- assign filtered_terms = search.terms|replace:'*','' | replace: '*', '' -%}
              {{- 'search.general.results_with_terms_count' | t: count: search.results_count, terms: filtered_terms -}}
            </div>
            {% endcomment %}
            
            <h1 class="SectionHeader__Heading Heading u-h1">{{ search.terms }}</h1>
            
            
          </div>
        </div>
      </header>

      {%- if search.types contains 'product' -%}
        <div class="ProductList ProductList--grid Grid" data-mobile-count="2" data-desktop-count="4">
          {%- for result in search.results -%}
          {% unless result.tags contains "hideme" %}
            <div class="Grid__Cell 1/2 1/3--tablet 1/4--lap-and-up">
              {%- include 'product-item', product: result, show_labels: true, show_product_info: true -%}
            </div>
          {% endunless %}
          {%- endfor -%}
        </div>
      {%- else -%}
        <div class="ArticleListWrapper">
          <div class="ArticleList Grid Grid--m">
            {%- for result in search.results -%}
              <div class="Grid__Cell 1/2--tablet 1/3--lap-and-up">
                {%- if result.object_type == 'article' -%}
                  {%- include 'article-item', article: result -%}
                {%- elsif result.object_type == 'page' -%}
                  <article class="ArticleItem">
                    <div class="ArticleItem__Content">
                      <h2 class="ArticleItem__Title Heading u-h2">
                        <a href="{{ result.url }}">{{ result.title }}</a>
                      </h2>

                      <p class="ArticleItem__Excerpt">{{ result.content | strip_html | truncate: 150 }}</p>
                      <a href="{{ article.url }}" class="ArticleItem__Link Link Link--underline">{{ 'blog.article.read_more' | t }}</a>
                    </div>
                  </article>
                {%- endif -%}
              </div>
            {%- endfor -%}
          </div>
        </div>
      {%- endif -%}

      {%- include 'pagination' -%}
    {%- endpaginate -%}
  {%- endif -%}
</section>


{% endif %}