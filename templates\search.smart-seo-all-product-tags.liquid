{%- layout none -%}
{%- capture output -%}
    {%- for collection in collections -%}
        {%- if forloop.index == 1 -%}{%- endif -%}
            {%- if collection.all_tags.size > 0 -%}
                {%- for tag in collection.all_tags -%}

                    {%- if output contains tag -%}
                    {%- else -%}
                        "{{tag}}"
                        {%- unless forloop.last == true -%},{%- endunless -%}
                    {%- endif -%}

                {%- endfor -%}
            {%- endif -%}
    {%- endfor -%}
{%- endcapture -%}
{{ output | strip_newlines | prepend: '[' | append: ']' }}